import datetime
import json
import logging
import time
import traceback

from django.core.cache import cache

from basic.services import Send<PERSON>essageApi
from basic.third_apis.feishu_api import FeishuApi
from basic.utils import DistributedLockThread, SqlUtil
from report.feishu.doc_parser import <PERSON><PERSON>uDocPars<PERSON>
from report.models import ReportFeishuInfoLog

func_handler = {}


# 定时任务进行扫描
class FeishuReportGenerateThread(DistributedLockThread):
    def __init__(self):
        super().__init__("FeishuReportGenerateThread", name="FeishuReportGenerateThread",
                         switchName="feishu_report_switch", interval=30)
        self.report_lock_name = ""
        self.timeout = 60 * 30

    def before(self):
        # 避免启动
        ReportFeishuInfoLog.objects.filter(status=1).update(status=0)
        time.sleep(100)

    def apply(self):
        result = ReportFeishuInfoLog.objects.filter(status=0, is_delete=0).order_by('id')[0:30]
        for item in result:
            try:
                self.report_lock_name = f"ReportFeishuInfoLog.id:{item.id}"
                if not self.obtain_report_lock():
                    continue
                ReportFeishuInfoLog.objects.filter(id=item.id).update(status=1, update_time=datetime.datetime.now())
                FeishuReportGenerator(item).generate()
                ReportFeishuInfoLog.objects.filter(id=item.id).update(status=2, update_time=datetime.datetime.now())
                self.release_report_lock()
            except Exception as e:
                logging.error(f"{traceback.format_exc()}")
                SendMessageApi(f"生成报告异常！！{traceback.format_exc()}").send_message("系统异常告警分组")
                ReportFeishuInfoLog.objects.filter(id=item.id).update(status=3)
                self.release_report_lock()

        # 如果是手动生成的 ， 那么需要根据参数里面的起止时间
        result = ReportFeishuInfoLog.objects.filter(status='5', is_delete=0, ).exclude(params__icontains='2099').order_by('id')[0:30]
        for item in result:
            try:
                params = json.loads(item.params)
                if params.get("end_time", "") and params.get("end_time", "").startswith("2099"):
                    continue
                if "vin" in params and "end_time" in params:
                    # 起止事件在， 查询文件列表中是否，   如果全都是4， 那么解析
                    next_time = datetime.datetime.strptime(params['end_time'], "%Y-%m-%d %H:%M:%S") -  datetime.timedelta(minutes=10)
                    sql = f""" select count(1) record_count from dataview_planning_thread_speed where vin = %s and record_time > %s  and record_time < %s """
                    result = SqlUtil.query_one_dict(sql, (params['vin'], next_time.strftime("%Y-%m-%d %H:%M:%S"), params['end_time']))
                    if result.get("record_count", 0) > 0:
                        # 等会看看还有没有其他的文件进来。
                        time.sleep(15)
                        # 如果有数据，并且当前没有正在解析的记录， 那么认为解析完成
                        sql = f""" select count(1) inprocess from cron_oss_file_list where current_status <'4' and vin = %s and file_update_time > %s """
                        result = SqlUtil.query_one_dict(sql, (params['vin'], params['end_time']))
                        if result.get("inprocess", -1) == 0:
                            ReportFeishuInfoLog.objects.filter(id=item.id).update(status=0, update_by='sys', update_time=datetime.datetime.now())
            except Exception as e:
                logging.error(f"trigger report error : {traceback.format_exc()}")

    def obtain_report_lock(self):
        if cache.set(self.report_lock_name, 1, nx=True, timeout=60 * 15):
            logging.info(f"FeishuDocParser obtain lock:{self.report_lock_name}")
            return True
        # 使用redis setnx 获取分布式锁 如果没有获取到尝试获取三次，否则就不再尝试获取
        return False

    def release_report_lock(self):
        result = cache.delete(self.report_lock_name)
        logging.info(f"FeishuDocParser release lock:{self.lock_name} {result}")

class FeishuReportGenerator:
    """
    由于飞书的接口调用是有限制的， 这个报告生成不能无穷的调用
    """

    def __init__(self, config):
        self.config = config
        self.lock_name = "report:lock:FeishuReportGenerator"
        self.feishu_api = FeishuApi()
        self.template_space_id = ''
        self.template_token = ''
        self.template_obj_type = ''
        self.template_node = ''
        # 目标文档类型是与template 的类型是一致的，因此不再
        self.target_space_id = ''
        self.target_token = ''
        self.target_obj_type = ''
        self.target_node = ''

    def generate(self):
        # 1. 读取飞书的所有Node， 根据Node 进行文本替换！！！！！！
        report_url = self.config.report_url
        url_info = report_url.split('/')
        node_info = self.feishu_api.wiki_get_node(url_info[-1], url_info[-2])
        self.template_token = node_info.get('data', {}).get("node", {}).get("obj_token", "")
        self.template_obj_type = node_info.get('data', {}).get("node", {}).get("obj_type", "")
        if self.template_obj_type == 'docx':
            FeishuDocParser(self.template_token, json.loads(self.config.params)).execute()

    def prepare(self):
        """
        准备好需要运行的环境
        """
        result = True
        entity = {"status": '3'}
        if not self.check_template(entity):
            result = False
        if result and not self.check_target(entity):
            result = False
        if result and not self.copy_template(entity):
            result = False
        return result, entity

    def copy_template(self, prepare):
        result = True
        try:
            current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M")
            prepare['name'] = f"{self.config.name}_{current_time}"
            resp = self.feishu_api.create_duplicate(self.template_space_id, self.template_node, self.target_node,
                                                    self.target_space_id, prepare['name'])
            self.target_token = resp.get('data', {}).get("node", {}).get("obj_token", "")
            self.target_obj_type = resp.get('data', {}).get("node", {}).get("obj_type", "")
            self.target_node_token = resp.get('data', {}).get("node", {}).get("node_token", "")
        except Exception as e:
            logging.error(f"create template duplicate fail{traceback.format_exc()}")
            prepare['remark'] = f"{prepare.get('remark', '')}\n拷贝模板至目标位置失败"
            self.release_lock()
            return False
        if self.target_token == '' or self.target_obj_type == '':
            prepare['remark'] = f"{prepare.get('remark', '')}\n新生成文档失败"
            result = False
        else:
            type = 'wiki' if self.config.target.startswith('wiki') else self.config.template.split('/')[-2]
            prepare['report_url'] = f"https://yuanlijuhe.feishu.cn/{type}/{self.target_node_token}"
            prepare['feishu_info_id'] = self.config.id
            prepare['status'] = '0'
            prepare['target_token'] = self.target_token
        return result

    def check_template(self, prepare):
        result = True
        template = self.config.template
        # 如果是wiki, 则需要校验是否是支持的类型
        template = template.split("/")
        if len(template) != 2:
            prepare[
                'remark'] = f"{prepare.get('remark', '')}\n模板配置有误，需配置成：wiki/KYJqwt******Tj8car54nQd 的形式"
            result = False
            return result
        self.template_node = template[1]
        node_info = self.feishu_api.wiki_get_node(template[1], template[0])
        self.template_token = node_info.get('data', {}).get("node", {}).get("obj_token", "")
        self.template_obj_type = node_info.get('data', {}).get("node", {}).get("obj_type", "")
        self.template_space_id = node_info.get('data', {}).get("node", {}).get("space_id", "")
        # 目标文档不允许为空！！！！
        if self.template_token == '' or self.template_space_id == '':
            prepare['remark'] = f"{prepare.get('remark', '')}\n模板信息获取失败!"
            result = False
        return result

    def check_target(self, prepare):
        """
        校验目标位置
        """
        result = True
        target = self.config.target
        target = target.split("/")
        if len(target) != 2:
            prepare[
                'remark'] = f"{prepare.get('remark', '')}\n目标位置配置有误，需配置成：wiki/KYJqwt******Tj8car54nQd 的形式"
            result = False
        if result:
            self.target_node = target[1]
            node_info = self.feishu_api.wiki_get_node(target[1], target[0])
            self.target_token = node_info.get('data', {}).get("node", {}).get("obj_token", "")
            self.target_obj_type = node_info.get('data', {}).get("node", {}).get("obj_type", "")
            self.target_space_id = node_info.get('data', {}).get("node", {}).get("space_id", "")
            if self.target_token == '' or self.target_space_id == '':
                prepare['remark'] = f"{prepare.get('remark', '')}\n目标位置获取失败"
                result = False
        return result

    def obtain_lock(self):
        if cache.set(self.lock_name, 1, nx=True, timeout=60):
            logging.info(f"feishu report obtain lock:{self.lock_name}")
            return True
        # 使用redis setnx 获取分布式锁 如果没有获取到尝试获取三次，否则就不再尝试获取
        return False

    def release_lock(self):
        result = cache.delete(self.lock_name)
        logging.info(f"feishu report release lock:{self.lock_name} {result}")
