#!/usr/bin/env python3
"""
改进的API设计
提供更直观、一致的API接口
"""

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Iterator, List, Optional, Union, Callable
from enum import Enum


class MessageType(Enum):
    """标准化的消息类型枚举"""
    LANE_ARRAY_V2 = "deva_perception_msgs/msg/LaneArrayv2"
    ENTRANCE_ARRAY = "deva_perception_msgs/msg/EntranceArray"
    AEB_OBSTACLE_ARRAY = "deva_perception_msgs/msg/AEBObstacleArray"
    LOCALIZATION_ESTIMATE = "deva_localization_msgs/msg/LocalizationEstimate"
    CONTROL_RESULT = "deva_control_msgs/msg/ControlResult"


class ProcessingMode(Enum):
    """处理模式枚举"""
    STANDARD = "standard"
    FAST = "fast"
    STREAMING = "streaming"
    BATCH = "batch"


@dataclass
class FilterCriteria:
    """过滤条件"""
    message_types: Optional[List[Union[str, MessageType]]] = None
    topics: Optional[List[str]] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    max_messages: Optional[int] = None
    min_confidence: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'message_types': [mt.value if isinstance(mt, MessageType) else mt 
                            for mt in (self.message_types or [])],
            'topics': self.topics,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'max_messages': self.max_messages,
            'min_confidence': self.min_confidence
        }


@dataclass
class ProcessingOptions:
    """处理选项"""
    mode: ProcessingMode = ProcessingMode.STANDARD
    batch_size: int = 10000
    max_workers: int = 4
    enable_validation: bool = True
    enable_caching: bool = True
    progress_callback: Optional[Callable[[int, int], None]] = None


class McapParserBuilder:
    """MCAP解析器构建器"""
    
    def __init__(self):
        self._file_path: Optional[Path] = None
        self._filter_criteria = FilterCriteria()
        self._processing_options = ProcessingOptions()
        self._verbose = False
    
    def file(self, file_path: Union[str, Path]) -> 'McapParserBuilder':
        """设置文件路径"""
        self._file_path = Path(file_path)
        return self
    
    def filter_by_types(self, *message_types: Union[str, MessageType]) -> 'McapParserBuilder':
        """按消息类型过滤"""
        self._filter_criteria.message_types = list(message_types)
        return self
    
    def filter_by_topics(self, *topics: str) -> 'McapParserBuilder':
        """按话题过滤"""
        self._filter_criteria.topics = list(topics)
        return self
    
    def filter_by_time(self, start_time: float, end_time: float) -> 'McapParserBuilder':
        """按时间范围过滤"""
        self._filter_criteria.start_time = start_time
        self._filter_criteria.end_time = end_time
        return self
    
    def limit_messages(self, max_messages: int) -> 'McapParserBuilder':
        """限制消息数量"""
        self._filter_criteria.max_messages = max_messages
        return self
    
    def min_confidence(self, confidence: float) -> 'McapParserBuilder':
        """设置最小置信度"""
        self._filter_criteria.min_confidence = confidence
        return self
    
    def processing_mode(self, mode: ProcessingMode) -> 'McapParserBuilder':
        """设置处理模式"""
        self._processing_options.mode = mode
        return self
    
    def batch_size(self, size: int) -> 'McapParserBuilder':
        """设置批处理大小"""
        self._processing_options.batch_size = size
        return self
    
    def max_workers(self, workers: int) -> 'McapParserBuilder':
        """设置最大工作线程数"""
        self._processing_options.max_workers = workers
        return self
    
    def enable_validation(self, enable: bool = True) -> 'McapParserBuilder':
        """启用/禁用数据验证"""
        self._processing_options.enable_validation = enable
        return self
    
    def enable_caching(self, enable: bool = True) -> 'McapParserBuilder':
        """启用/禁用缓存"""
        self._processing_options.enable_caching = enable
        return self
    
    def progress_callback(self, callback: Callable[[int, int], None]) -> 'McapParserBuilder':
        """设置进度回调"""
        self._processing_options.progress_callback = callback
        return self
    
    def verbose(self, enable: bool = True) -> 'McapParserBuilder':
        """启用详细输出"""
        self._verbose = enable
        return self
    
    def build(self) -> 'ImprovedMcapParser':
        """构建解析器"""
        if self._file_path is None:
            raise ValueError("必须指定文件路径")
        
        return ImprovedMcapParser(
            file_path=self._file_path,
            filter_criteria=self._filter_criteria,
            processing_options=self._processing_options,
            verbose=self._verbose
        )


class ImprovedMcapParser:
    """改进的MCAP解析器"""
    
    def __init__(self,
                 file_path: Path,
                 filter_criteria: FilterCriteria,
                 processing_options: ProcessingOptions,
                 verbose: bool = False):
        self.file_path = file_path
        self.filter_criteria = filter_criteria
        self.processing_options = processing_options
        self.verbose = verbose
        
        # 验证文件存在
        if not self.file_path.exists():
            raise FileNotFoundError(f"MCAP文件不存在: {self.file_path}")
    
    def parse(self) -> Iterator[Any]:
        """解析数据"""
        # 这里是实际的解析逻辑
        # 根据processing_options.mode选择不同的解析策略
        if self.processing_options.mode == ProcessingMode.FAST:
            return self._fast_parse()
        elif self.processing_options.mode == ProcessingMode.STREAMING:
            return self._streaming_parse()
        elif self.processing_options.mode == ProcessingMode.BATCH:
            return self._batch_parse()
        else:
            return self._standard_parse()
    
    def _standard_parse(self) -> Iterator[Any]:
        """标准解析"""
        # 实现标准解析逻辑
        pass
    
    def _fast_parse(self) -> Iterator[Any]:
        """快速解析"""
        # 实现快速解析逻辑
        pass
    
    def _streaming_parse(self) -> Iterator[Any]:
        """流式解析"""
        # 实现流式解析逻辑
        pass
    
    def _batch_parse(self) -> Iterator[Any]:
        """批处理解析"""
        # 实现批处理解析逻辑
        pass
    
    def analyze(self) -> Dict[str, Any]:
        """分析文件"""
        # 实现文件分析逻辑
        return {
            "file_size": self.file_path.stat().st_size,
            "message_count": 0,  # 实际计算
            "topics": [],  # 实际提取
            "time_range": None,  # 实际计算
        }


# 便利函数
def parse_mcap(file_path: Union[str, Path]) -> McapParserBuilder:
    """创建MCAP解析器构建器"""
    return McapParserBuilder().file(file_path)


# 使用示例
if __name__ == "__main__":
    # 链式API使用示例
    parser = (parse_mcap("data.mcap")
              .filter_by_types(MessageType.LANE_ARRAY_V2, MessageType.ENTRANCE_ARRAY)
              .filter_by_time(1000.0, 2000.0)
              .limit_messages(100)
              .processing_mode(ProcessingMode.FAST)
              .batch_size(5000)
              .enable_validation()
              .verbose()
              .build())
    
    # 解析数据
    for message in parser.parse():
        print(f"消息: {message.message_type} @ {message.timestamp}")
    
    # 分析文件
    analysis = parser.analyze()
    print(f"文件分析: {analysis}")
