#!/usr/bin/env python3
"""
数据验证和清洗模块
确保解析的数据质量和一致性
"""

import math
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple, Union
from enum import Enum


class ValidationLevel(Enum):
    """验证级别"""
    STRICT = "strict"      # 严格验证，发现问题立即抛出异常
    WARNING = "warning"    # 警告模式，记录问题但继续处理
    SILENT = "silent"      # 静默模式，只修复数据不报告


class ValidationResult(Enum):
    """验证结果"""
    VALID = "valid"
    INVALID = "invalid"
    CORRECTED = "corrected"


@dataclass
class ValidationIssue:
    """验证问题"""
    field_name: str
    issue_type: str
    description: str
    original_value: Any
    corrected_value: Any = None
    severity: str = "warning"


@dataclass
class ValidationReport:
    """验证报告"""
    total_checked: int = 0
    valid_count: int = 0
    corrected_count: int = 0
    invalid_count: int = 0
    issues: List[ValidationIssue] = None
    
    def __post_init__(self):
        if self.issues is None:
            self.issues = []
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_checked == 0:
            return 0.0
        return (self.valid_count + self.corrected_count) / self.total_checked * 100


class CoordinateValidator:
    """坐标验证器"""
    
    def __init__(self, 
                 max_coordinate: float = 1e6,
                 min_coordinate: float = -1e6,
                 precision: int = 3):
        self.max_coordinate = max_coordinate
        self.min_coordinate = min_coordinate
        self.precision = precision
    
    def validate_point(self, point: Any) -> Tuple[ValidationResult, List[ValidationIssue]]:
        """验证3D点"""
        issues = []
        result = ValidationResult.VALID
        
        if not hasattr(point, 'x') or not hasattr(point, 'y') or not hasattr(point, 'z'):
            issues.append(ValidationIssue(
                field_name="point_structure",
                issue_type="missing_fields",
                description="点缺少x、y或z字段",
                original_value=point,
                severity="error"
            ))
            return ValidationResult.INVALID, issues
        
        # 验证坐标范围
        for coord_name in ['x', 'y', 'z']:
            coord_value = getattr(point, coord_name)
            
            if not isinstance(coord_value, (int, float)):
                issues.append(ValidationIssue(
                    field_name=coord_name,
                    issue_type="invalid_type",
                    description=f"{coord_name}坐标不是数值类型",
                    original_value=coord_value,
                    severity="error"
                ))
                result = ValidationResult.INVALID
                continue
            
            if math.isnan(coord_value) or math.isinf(coord_value):
                issues.append(ValidationIssue(
                    field_name=coord_name,
                    issue_type="invalid_value",
                    description=f"{coord_name}坐标是NaN或无穷大",
                    original_value=coord_value,
                    severity="error"
                ))
                result = ValidationResult.INVALID
                continue
            
            if coord_value < self.min_coordinate or coord_value > self.max_coordinate:
                issues.append(ValidationIssue(
                    field_name=coord_name,
                    issue_type="out_of_range",
                    description=f"{coord_name}坐标超出合理范围",
                    original_value=coord_value,
                    severity="warning"
                ))
                # 可以选择修正坐标
                corrected_value = max(self.min_coordinate, min(self.max_coordinate, coord_value))
                issues[-1].corrected_value = corrected_value
                result = ValidationResult.CORRECTED
        
        return result, issues
    
    def validate_waypoints(self, waypoints: List[Any]) -> Tuple[ValidationResult, List[ValidationIssue]]:
        """验证路径点列表"""
        all_issues = []
        overall_result = ValidationResult.VALID
        
        if not waypoints:
            all_issues.append(ValidationIssue(
                field_name="waypoints",
                issue_type="empty_list",
                description="路径点列表为空",
                original_value=waypoints,
                severity="warning"
            ))
            return ValidationResult.INVALID, all_issues
        
        # 验证每个点
        for i, point in enumerate(waypoints):
            result, issues = self.validate_point(point)
            
            # 添加索引信息
            for issue in issues:
                issue.field_name = f"waypoints[{i}].{issue.field_name}"
            
            all_issues.extend(issues)
            
            if result == ValidationResult.INVALID:
                overall_result = ValidationResult.INVALID
            elif result == ValidationResult.CORRECTED and overall_result == ValidationResult.VALID:
                overall_result = ValidationResult.CORRECTED
        
        # 验证路径点连续性
        if len(waypoints) > 1:
            continuity_issues = self._check_waypoint_continuity(waypoints)
            all_issues.extend(continuity_issues)
        
        return overall_result, all_issues
    
    def _check_waypoint_continuity(self, waypoints: List[Any]) -> List[ValidationIssue]:
        """检查路径点连续性"""
        issues = []
        
        for i in range(1, len(waypoints)):
            prev_point = waypoints[i-1]
            curr_point = waypoints[i]
            
            # 计算距离
            distance = math.sqrt(
                (curr_point.x - prev_point.x) ** 2 +
                (curr_point.y - prev_point.y) ** 2 +
                (curr_point.z - prev_point.z) ** 2
            )
            
            # 检查是否有异常大的跳跃
            if distance > 100.0:  # 100米的跳跃可能有问题
                issues.append(ValidationIssue(
                    field_name=f"waypoints[{i-1}]->[{i}]",
                    issue_type="large_gap",
                    description=f"相邻路径点距离过大: {distance:.2f}m",
                    original_value=distance,
                    severity="warning"
                ))
            
            # 检查重复点
            if distance < 0.01:  # 1cm以内认为是重复点
                issues.append(ValidationIssue(
                    field_name=f"waypoints[{i}]",
                    issue_type="duplicate_point",
                    description="路径点与前一个点重复",
                    original_value=curr_point,
                    severity="info"
                ))
        
        return issues


class LaneValidator:
    """车道线验证器"""
    
    def __init__(self):
        self.coord_validator = CoordinateValidator()
    
    def validate_lane(self, lane: Any) -> Tuple[ValidationResult, List[ValidationIssue]]:
        """验证单条车道线"""
        issues = []
        result = ValidationResult.VALID
        
        # 验证基本字段
        required_fields = ['lane_id', 'confidence', 'waypoints']
        for field in required_fields:
            if not hasattr(lane, field):
                issues.append(ValidationIssue(
                    field_name=field,
                    issue_type="missing_field",
                    description=f"车道线缺少必需字段: {field}",
                    original_value=None,
                    severity="error"
                ))
                result = ValidationResult.INVALID
        
        if result == ValidationResult.INVALID:
            return result, issues
        
        # 验证置信度
        if not (0.0 <= lane.confidence <= 1.0):
            issues.append(ValidationIssue(
                field_name="confidence",
                issue_type="out_of_range",
                description=f"置信度超出范围[0,1]: {lane.confidence}",
                original_value=lane.confidence,
                corrected_value=max(0.0, min(1.0, lane.confidence)),
                severity="warning"
            ))
            result = ValidationResult.CORRECTED
        
        # 验证路径点
        waypoint_result, waypoint_issues = self.coord_validator.validate_waypoints(lane.waypoints)
        issues.extend(waypoint_issues)
        
        if waypoint_result == ValidationResult.INVALID:
            result = ValidationResult.INVALID
        elif waypoint_result == ValidationResult.CORRECTED and result == ValidationResult.VALID:
            result = ValidationResult.CORRECTED
        
        return result, issues
    
    def validate_lane_array(self, lane_array: List[Any]) -> ValidationReport:
        """验证车道线数组"""
        report = ValidationReport()
        
        for i, lane in enumerate(lane_array):
            report.total_checked += 1
            
            result, issues = self.validate_lane(lane)
            
            # 添加车道线索引信息
            for issue in issues:
                issue.field_name = f"lane[{i}].{issue.field_name}"
            
            report.issues.extend(issues)
            
            if result == ValidationResult.VALID:
                report.valid_count += 1
            elif result == ValidationResult.CORRECTED:
                report.corrected_count += 1
            else:
                report.invalid_count += 1
        
        return report


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.WARNING):
        self.validation_level = validation_level
        self.lane_validator = LaneValidator()
    
    def clean_lane_data(self, lane_data: Any) -> Tuple[Any, ValidationReport]:
        """清洗车道线数据"""
        if not hasattr(lane_data, 'lane_array'):
            report = ValidationReport(total_checked=1, invalid_count=1)
            report.issues.append(ValidationIssue(
                field_name="lane_array",
                issue_type="missing_field",
                description="数据缺少lane_array字段",
                original_value=lane_data,
                severity="error"
            ))
            return lane_data, report
        
        # 验证车道线数组
        report = self.lane_validator.validate_lane_array(lane_data.lane_array)
        
        # 根据验证级别处理问题
        if self.validation_level == ValidationLevel.STRICT and report.invalid_count > 0:
            raise ValueError(f"数据验证失败: {report.invalid_count}个无效项")
        
        # 应用修正
        cleaned_data = self._apply_corrections(lane_data, report)
        
        return cleaned_data, report
    
    def _apply_corrections(self, data: Any, report: ValidationReport) -> Any:
        """应用数据修正"""
        # 这里应该实际修正数据
        # 为了简化，这里只返回原数据
        return data


if __name__ == "__main__":
    # 测试数据验证器
    validator = LaneValidator()
    
    # 创建模拟车道线数据
    class MockPoint:
        def __init__(self, x, y, z):
            self.x, self.y, self.z = x, y, z
    
    class MockLane:
        def __init__(self, lane_id, confidence, waypoints):
            self.lane_id = lane_id
            self.confidence = confidence
            self.waypoints = waypoints
    
    # 测试数据
    test_lane = MockLane(
        lane_id=1,
        confidence=0.95,
        waypoints=[
            MockPoint(100.0, 200.0, 0.0),
            MockPoint(110.0, 210.0, 0.1),
            MockPoint(120.0, 220.0, 0.2)
        ]
    )
    
    result, issues = validator.validate_lane(test_lane)
    print(f"验证结果: {result}")
    print(f"问题数量: {len(issues)}")
    for issue in issues:
        print(f"  - {issue.description}")
