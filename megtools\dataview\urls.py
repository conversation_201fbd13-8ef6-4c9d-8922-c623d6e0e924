from django.urls import path
from dataview.views import *

urlpatterns = [
    path('queryTopOverview', queryTopOverview),
    path('queryTopCpus', queryTopCpus),
    path('queryTopProcess', queryTopProcess),
    path('queryCommonAnylizeApi', queryCommonAnylizeApi),
    path('queryHostAllCpuMpstatUsageApi', queryHostAllCpuMpstatUsage),
    path('getLogFileListPageApi', getLogFileListPage),
    path('insertOssFile', insertOssFile),
    path('rerunLogParseApi', rerunLogParse),
    path('queryHostProcessInnerApi', queryHostProcessInnerApi),
]