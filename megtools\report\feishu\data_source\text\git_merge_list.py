import logging
import traceback

from basic.third_apis.gitlab_api import GitlabApi
from basic.utils import SqlUtil
from report.feishu.data_source.image.line_chart_image import LineChartImage
from report.feishu.generator.impl.table_handler import TableHandler
from report.feishu.generator.impl.text_handler import TextHandler, BACK_COLOR


class GitMergeList(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.gitlab_api = GitlabApi()
        self.handle_conditions(variables, params)
        self.table_header = ["ID", "标题", "测试人员", "改动点", "修复的JIRA", "测试方法", "MR测试结果", "集成测试结果"]
        self.column_width = [50, 200, 400, 100, 400, 400, 100]

    def apply(self):
        req_entity = {"state": 'merged', "target_branch": 'dev', "per_page": 100, "updated_after": self.condition['start_time'], "updated_before": self.condition['end_time']}
        merge_list = self.gitlab_api.merge_requests(self.condition['project_id'], req_entity)
        merge_list = self.reform_merge_list(merge_list)
        # 查到数据之后落入飞书文档
        TableHandler(merge_list, self.block, self.feishu_api,column_width=self.column_width, table_header=self.table_header).apply()

    def reform_merge_list(self, merge_list):
        result = []
        for item in merge_list:
            row = []
            row.append({"content": f'{item.get("iid")}', "url": f"https://git-core.megvii-inc.com/e2e/zeta/-/merge_requests/{item.get('iid')}"})
            row.append(item.get("title", ""))
            description = item.get("description", "")
            row.append(self.sub_description(description, "## 测试人员【*必填：QA人员/本人】", "## 测试是否通过【*必填：是/否】"))
            row.append(self.sub_description(description, "## 更新内容【*必填】", "## 所属模块【*必填】"))
            row.append("")
            row.append(self.sub_description(description,"## 测试方法【*必填】","## 自测结果【选填：结论，截图】"))
            row.append(self.sub_description(description,"## QA测试结论【*必填：结论，截图，报告】","## 遗留的问题【选填】"))
            row.append("")
            result.append(row)
        return result

    def sub_description(self, description, start, end):
        result = ""
        try:
            result = description[description.index(start) + len(start):description.index(end) - 1].strip()
        except ValueError:
            logging.error(f"description substr fail: {description}{traceback.format_exc()}")
            pass
        return result

    def handle_conditions(self, variables, params):
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.condition['project_id'] = params

