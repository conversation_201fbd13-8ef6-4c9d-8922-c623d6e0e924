import os
import requests
import json
import configparser
from datetime import datetime
from jira import JIRA
import lark_oapi as lark
from lark_oapi.api.drive.v1 import *
import sys
import re
import logging
from typing import List, Tuple, Optional
from openai import OpenAI

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ConfigManager:
    def __init__(self, config_path: Optional[str] = None):
        """配置管理器初始化"""
        self.config_path = self._resolve_config_path(config_path)
        self.parser = configparser.ConfigParser()
        self._load_config()
    
    def _resolve_config_path(self, config_path: Optional[str]) -> str:
        """解析配置文件路径"""
        if config_path:
            return config_path
        
        # 判断是否是打包后的可执行文件
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        return os.path.join(base_dir, 'config.ini')
    
    def _load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            self.create_default_config()
            raise FileNotFoundError(f"配置文件 {self.config_path} 不存在，已创建默认配置")
        
        self.parser.read(self.config_path, encoding='utf-8')
    
    def create_default_config(self):
        """创建默认配置文件"""
        config = configparser.ConfigParser()
        
        # 豆包(ByteDance)配置
        config['Doubao'] = {
            'api_key': 'your_doubao_api_key_here',
            'model_id': 'doubao-1-5-lite-32k-250115',
            'api_url': 'https://ark.cn-beijing.volces.com/api/v3'
        }
        
        # Jira 配置
        config['Jira'] = {
            'url': 'https://your-jira-instance.com',
            'user': 'your_jira_username',
            'password': 'your_jira_password',
            'jql': 'project = YOURPROJECT AND status = "To Do"'
        }
        
        # Feishu 配置
        config['Feishu'] = {
            'app_id': 'your_feishu_app_id',
            'app_secret': 'your_feishu_app_secret',
            'folder_token': 'your_feishu_folder_token',
        }
        
        # Analysis 配置
        config['Analysis'] = {
            'system_prompt': """请基于以下要求进行深度分析：
1. 问题分类统计：统计各分类下的问题数量，计算占比（数量 / 总问题数），用数据呈现问题分布规律，对比不同分类的规模差异。并贴上对应的问题链接。
2. top问题总结：识别最常见（数量最多）、最关键（影响最大/关联核心流程）的问题
需基于具体问题集合展开，确保分类合理、统计准确、总结聚焦，输出清晰可落地的分析结论。"""
        }
        
        with open(self.config_path, 'w', encoding='utf-8') as configfile:
            config.write(configfile)
        logger.info(f"配置文件 {self.config_path} 已创建，请修改其中的配置项")
    
    def get(self, section: str, option: str, default: str = '') -> str:
        """获取配置值"""
        try:
            return self.parser.get(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default


class JiraClient:
    """封装Jira操作"""
    def __init__(self, config: ConfigManager):
        self.config = config
        self.jira = self._connect_jira()
    
    def _connect_jira(self) -> Optional[JIRA]:
        """连接Jira服务器"""
        try:
            jira_url = self.config.get('Jira', 'url')
            jira_user = self.config.get('Jira', 'user')
            jira_password = self.config.get('Jira', 'password')
            
            if not all([jira_url, jira_user, jira_password]):
                logger.error("Jira配置信息不完整")
                return None
                
            return JIRA(
                server=jira_url,
                basic_auth=(jira_user, jira_password),
                options={'verify': True},
                timeout=20
            )
        except Exception as e:
            logger.error(f"连接Jira失败: {str(e)}")
            return None
    
    def get_issues(self) -> List:
        """获取所有匹配的问题"""
        if self.jira is None:
            logger.error("Jira连接未建立，无法获取问题")
            return []
        
        try:
            base_jql = self.config.get('Jira', 'jql')
            logger.info(f"正在执行 JQL 查询: {base_jql}")
            
            all_issues = []
            start_at = 0
            max_results = 100
            total = None
            
            while total is None or start_at < total:
                issues = self.jira.search_issues(
                    base_jql,
                    startAt=start_at,
                    maxResults=max_results,
                    fields="summary,labels,created"
                )
                all_issues.extend(issues)
                if total is None:
                    total = issues.total
                start_at += len(issues)
                logger.info(f"已获取 {len(all_issues)}/{total} 个问题")
            
            logger.info(f"找到 {len(all_issues)} 个匹配问题")
            return all_issues
        
        except Exception as e:
            logger.error(f"获取问题失败: {str(e)}")
            return []
    
    def print_issues(self, issues: List):
        """打印问题列表"""
        if not issues:
            logger.info("没有可用的问题")
            return
        
        logger.info("\n" + "="*80)
        logger.info("所有问题列表".center(80))
        logger.info("="*80)
        jira_url = self.config.get('Jira', 'url')
        for i, issue in enumerate(issues, 1):
            issue_link = f"{jira_url}/browse/{issue.key}"
            logger.info(f"{i}. [{issue.key}] {issue.fields.summary}")
            logger.info(f"   链接: {issue_link}")
        logger.info("="*80)
    
    def extract_mr_labels(self, issues: List) -> List[str]:
        """提取MR标签"""
        if not issues:
            return []
        
        pattern = re.compile(r'^MR_.*', re.IGNORECASE)
        labels = set()
        
        for issue in issues:
            issue_labels = issue.fields.labels or []
            for label in issue_labels:
                if pattern.match(label):
                    labels.add(label)
        
        return sorted(list(labels))


class ReportGenerator:
    """报告生成器"""
    def __init__(self, config: ConfigManager, jira_client: JiraClient):
        self.config = config
        self.jira_client = jira_client
    
    def _generate_common_report_header(self, issues: List) -> str:
        """生成报告通用头部"""
        report = f"# 问题分析报告\n\n"
        report += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        report += f"**分析问题数量**: {len(issues)}\n\n"
        return report
    
    def generate_category_report(self, issues: List) -> str:
        """生成分类报告"""
        if not issues:
            return "没有可用数据生成报告"
        
        report = self._generate_common_report_header(issues)
        report += "## 问题列表\n\n"
        
        # 添加问题表格
        report += "| 序号 | 问题Key | 摘要 | 链接 |\n"
        report += "|------|---------|------|------|\n"
        
        jira_url = self.config.get('Jira', 'url')
        for i, issue in enumerate(issues, 1):
            issue_link = f"{jira_url}/browse/{issue.key}"
            summary = issue.fields.summary
            if len(summary) > 60:
                summary = summary[:57] + "..."
            
            report += f"| {i} | {issue.key} | {summary} | [查看]({issue_link}) |\n"
        
        return report
    
    def generate_local_report(self, issues: List) -> Optional[str]:
        """生成本地报告"""
        if not issues:
            return None
        
        report = self._generate_common_report_header(issues)
        report += "## 所有问题列表\n\n"
        report += "| 序号 | 问题Key | 摘要 | 链接 |\n"
        report += "|------|---------|------|------|\n"
        
        jira_url = self.config.get('Jira', 'url')
        for i, issue in enumerate(issues, 1):
            issue_link = f"{jira_url}/browse/{issue.key}"
            summary = issue.fields.summary
            if len(summary) > 60:
                summary = summary[:57] + "..."
            
            report += f"| {i} | {issue.key} | {summary} | [查看]({issue_link}) |\n"
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"local_analysis_report_{timestamp}.md"
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(report)
            logger.info(f"本地报告已保存到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存本地报告失败: {str(e)}")
            return None
    
    def generate_ai_report(self, issues: List) -> Optional[str]:
        """使用豆包API生成报告"""
        if not issues:
            return self.generate_local_report(issues)
        
        # 准备系统提示
        system_prompt = self.config.get('Analysis', 'system_prompt')
        if not system_prompt:
            system_prompt = """
            你是一位自动测试专家，拥有15年行业经验。请基于以下问题摘要生成一份完整的Markdown格式测试报告，包含以下内容：
            
            ## 1. 执行摘要
            - 简要概述分析结果
            - 主要发现和关键指标
            
            ## 2. 问题分类统计
            - 创建4-8个有意义的问题类别
            - 统计各类问题的数量和比例
            - 使用表格展示分类结果
            
            ## 3. 问题分布分析
            - 识别问题的集中领域和热点区域
            - 分析问题的时间分布趋势（如果可能）
            
            ## 4. 根本原因分析
            - 探究问题产生的深层次原因
            - 分析系统性和重复性问题模式
            
            ## 5. 风险评估
            - 评估各类问题的严重程度
            - 分析潜在影响和风险等级
            - 识别高风险问题
            
            ## 6. 改进建议
            - 针对不同类别的问题提出专业改进建议
            - 提出预防措施和优化方案
            - 建议优先级排序
            
            ## 7. 详细问题列表
            - 列出所有分析的问题（包含链接）
            
            报告应不少于1000字，使用专业的测试术语，并包含必要的表格和数据可视化建议。
            """
        
        # 准备用户输入
        user_input = "以下是问题摘要和链接列表：\n\n"
        jira_url = self.config.get('Jira', 'url')
        for i, issue in enumerate(issues, 1):
            user_input += f"{i}. {issue.fields.summary}\n"
            user_input += f"   链接: {jira_url}/browse/{issue.key}\n"
            if hasattr(issue.fields, 'created'):
                created_date = issue.fields.created[:10]  # 只取日期部分
                user_input += f"   创建日期: {created_date}\n"
        user_input += "\n请生成一份完整的测试报告。"
        
        # 获取豆包API配置
        api_key = self.config.get('Doubao', 'api_key')
        model_id = self.config.get('Doubao', 'model_id', default='doubao-1-5-lite-32k-250115')
        api_url = self.config.get('Doubao', 'api_url', default='https://ark.cn-beijing.volces.com/api/v3')
        
        if not api_key or api_key == 'your_doubao_api_key_here':
            logger.error("豆包API key 未配置")
            return self.generate_local_report(issues)
        
        # 初始化豆包客户端
        try:
            client = OpenAI(
                base_url=api_url,
                api_key=api_key
            )
        except Exception as e:
            logger.error(f"初始化豆包客户端失败: {str(e)}")
            return self.generate_local_report(issues)
        
        logger.info("正在使用豆包API生成测试报告... (这可能需要一些时间)")
        
        try:
            # 调用豆包API
            completion = client.chat.completions.create(
                model=model_id,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_input}
                ],
                extra_body={
                    "thinking": {
                        "type": "disabled"  # 不使用深度思考能力
                        #"type": "enabled" # 使用深度思考能力
                        # "type": "auto" # 模型自行判断是否使用深度思考能力
                    }
                },
                temperature=0.2,
                max_tokens=4000,
                stream=False
            )
            
            # 获取生成的报告内容
            report_content = completion.choices[0].message.content
            
            # 添加问题列表到报告
            category_report = self.generate_category_report(issues)
            full_report = f"{report_content}\n\n{category_report}"
            
            # 保存报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.md"
            try:
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(full_report)
                
                logger.info("\n" + "="*80)
                logger.info("测试报告已生成".center(80))
                logger.info("="*80)
                logger.info(f"报告已保存到: {filename}")
                logger.info("="*80)
                
                return filename
            except Exception as e:
                logger.error(f"保存报告失败: {str(e)}")
                return None
            
        except Exception as e:
            logger.error(f"豆包API调用失败: {str(e)}")
            return self.generate_local_report(issues)


class FeishuClient:
    """飞书客户端"""
    def __init__(self, config: ConfigManager):
        self.config = config
        self.client = self._create_client()
    
    def _create_client(self) -> Optional[lark.Client]:
        """创建飞书客户端"""
        app_id = self.config.get('Feishu', 'app_id')
        app_secret = self.config.get('Feishu', 'app_secret')
        
        if not app_id or not app_secret:
            logger.warning("飞书配置信息不完整")
            return None
            
        try:
            return lark.Client.builder() \
                .app_id(app_id) \
                .app_secret(app_secret) \
                .log_level(lark.LogLevel.INFO) \
                .build()
        except Exception as e:
            logger.error(f"创建飞书客户端失败: {str(e)}")
            return None
    
    def delete_file(self, file_token: str) -> bool:
        """删除飞书文件"""
        if not self.client:
            logger.error("飞书客户端未初始化")
            return False
            
        try:
            request = DeleteFileRequest.builder() \
                .file_token(file_token) \
                .type("file") \
                .build()
            
            response = self.client.drive.v1.file.delete(request)
            
            if not response.success():
                logger.error(f"文件删除失败: code={response.code}, msg={response.msg}")
                return False
            return True
        except Exception as e:
            logger.error(f"删除飞书文件出错: {str(e)}")
            return False
    
    def upload_and_convert(self, file_path: str, mr_labels: List[str]) -> Tuple[bool, Optional[str]]:
        """上传并转换为飞书文档"""
        folder_token = self.config.get('Feishu', 'folder_token')
        
        if not self.client or not folder_token:
            logger.warning("飞书配置不完整，跳过上传和转换")
            return False, None
        
        try:
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            # 生成文档名称
            date_str = datetime.now().strftime("%Y%m%d")
            doc_name = f"{mr_labels[0]}_{date_str}_测试报告" if mr_labels else f"测试报告_{date_str}"
            
            # 上传文件
            with open(file_path, "rb") as file:
                upload_request = UploadAllMediaRequest.builder() \
                    .request_body(UploadAllMediaRequestBody.builder()
                                 .file_name(file_name)
                                 .parent_type("explorer")
                                 .parent_node(folder_token)
                                 .size(str(file_size))
                                 .extra(json.dumps({
                                     "file_extension": "md",
                                     "obj_type": "docx"
                                 }))
                                 .file(file)
                                 .build()) \
                    .build()
                
                logger.info(f"正在上传文件到飞书: {file_name} ({file_size}字节)")
                
                upload_response = self.client.drive.v1.media.upload_all(upload_request)
                
                if not upload_response.success():
                    logger.error(f"文件上传失败: code={upload_response.code}, msg={upload_response.msg}")
                    return False, None
                
                file_token = upload_response.data.file_token
                logger.info(f"文件上传成功! 文件token: {file_token}")
            
            # 创建转换任务
            convert_request = CreateImportTaskRequest.builder() \
                .request_body(ImportTask.builder()
                             .file_extension("md")
                             .file_token(file_token)
                             .type("docx")
                             .file_name(doc_name)
                             .point(ImportTaskMountPoint.builder()
                                    .mount_type(1)
                                    .mount_key(folder_token)
                                    .build())
                             .build()) \
                .build()
            
            logger.info(f"正在创建转换任务: {doc_name}")
            
            convert_response = self.client.drive.v1.import_task.create(convert_request)
            
            if not convert_response.success():
                logger.error(f"创建转换任务失败: code={convert_response.code}, msg={convert_response.msg}")
                return False, file_token
            
            ticket = convert_response.data.ticket
            logger.info(f"转换任务创建成功! 任务ID: {ticket}")
            
            # 清理操作
            self._cleanup_resources(file_path, file_token)
            
            return True, file_token
            
        except Exception as e:
            logger.error(f"飞书操作出错: {str(e)}")
            return False, None
    
    def _cleanup_resources(self, file_path: str, file_token: Optional[str]):
        """清理本地和远程资源"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已删除本地文件: {file_path}")
        except Exception as e:
            logger.error(f"本地文件删除失败: {str(e)}")
        
        if file_token:
            if self.delete_file(file_token):
                logger.info("已删除飞书原始文件")
            else:
                logger.warning("警告：飞书原始文件删除失败，请手动清理")


class AutoTestReportGenerator:
    """主应用类"""
    def __init__(self):
        try:
            self.config = ConfigManager()
            self.jira_client = JiraClient(self.config)
            self.report_generator = ReportGenerator(self.config, self.jira_client)
            self.feishu_client = FeishuClient(self.config)
        except Exception as e:
            logger.error(f"初始化失败: {str(e)}")
            raise
    
    def run(self):
        """运行主流程"""
        logger.info("\n" + "="*60)
        logger.info(f"AutoTest Report Generator v2.0".center(60))
        logger.info(f"{datetime.now().strftime('%Y-%m-%d')}".center(60))
        logger.info("="*60 + "\n")
        
        # 获取Jira问题
        issues = self.jira_client.get_issues()
        # 打印问题列表
        if issues:
            self.jira_client.print_issues(issues)
        
        # 提取MR标签
        mr_labels = self.jira_client.extract_mr_labels(issues)
        logger.info(f"提取到MR标签: {mr_labels}")
        
        # 生成报告
        report_file = self.report_generator.generate_ai_report(issues)
        
        # 上传到飞书
        if report_file:
            self.upload_to_feishu(report_file, mr_labels)
    
    def upload_to_feishu(self, report_file: str, mr_labels: List[str]):
        """上传报告到飞书"""
        if not self.feishu_client.client:
            logger.warning("\n飞书配置缺失，跳过上传")
            return
        
        logger.info("\n尝试上传报告到飞书云文档...")
        
        success, _ = self.feishu_client.upload_and_convert(report_file, mr_labels)
        
        if success:
            logger.info("报告已成功上传并转换")
            folder_token = self.config.get('Feishu', 'folder_token')
            app_id = self.config.get('Feishu', 'app_id')
            if app_id and folder_token:
                doc_link = f"https://{app_id}.feishu.cn/drive/folder/{folder_token}"
                logger.info(f"您可以在飞书云文档中查看: {doc_link}")
        else:
            logger.error("报告上传或转换失败")


if __name__ == "__main__":
    try:
        app = AutoTestReportGenerator()
        app.run()
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        sys.exit(1)