
# 合理生成车道线测试用例
import csv
import random

scene_types = [
    ("直道场景", ["可变车道", "应急车道", "常规车道", "公交车道", "潮汐车道", "超宽车道", "超窄车道", "鱼骨线", "车道线不清晰", "新旧车道线", "禁停区", "导流区", "高架", "停止线", "斑马线", "隧道", "弯道"]),
    ("直道变化场景", ["少分多", "多分少", "分流", "合流", "环岛", "导流区", "主路进入辅路", "辅路进入主路", "匝道", "收费站", "高速反光柱", "弯道", "大曲率弯道", "联排水马", "联排锥桶"]),
    ("路口场景", ["超大路口", "十字路口", "三岔路口", "丁字路口", "多叉路口", "右转专用道", "错位路口", "左转待转区", "直行待行区"]),
    ("车辆行为", ["路口左转", "路口右转", "路口直行", "路口掉头"]),
    ("道路属性", ["高速", "城区", "施工"])
]

target_types = [
    ("车道线", ["黄色实线", "白色实线", "双黄线", "导流线", "黄色虚线", "白色虚线", "左虚右实双黄线", "左实右虚双黄线", "左虚右实双白线", "左实右虚双白线"]),
    ("路沿", ["路沿线", "护栏", "锥桶", "水马"]),
    ("离散目标", ["停止线", "driveline", "地面箭头", "禁停区", "斑马线"])
]

problem_types = [
    ("误检", ["位置错误", "曲率错误", "多线重合", "实例断联", "实例多联", "实例弯折", "实例乱线"]),
    ("漏检", ["漏检道路线", "漏检路沿线", "漏检护栏", "漏检锥桶", "漏检水马", "漏检停止线", "漏检driveline", "漏检地面箭头", "漏检禁停区", "漏检斑马线", "检出距离不足", "漏检导流线"]),
    ("类型错误", ["类型整体错误", "类型部分错误", "类型跳变错误"]),
    ("系统问题", ["可视化问题", "系统性问题"])
]

def make_requirement(scene_type, scene, target_type, target, problem_type, problem):
    # 生成合理的测试要求
    if problem_type == "误检":
        return f"采集{scene_type}-{scene}场景视频，运行算法，人工核查{target_type}{target}是否存在{problem}，并记录误检情况。"
    elif problem_type == "漏检":
        return f"采集{scene_type}-{scene}场景视频，运行算法，人工核查{target_type}{target}是否存在{problem}，并记录漏检情况。"
    elif problem_type == "类型错误":
        return f"采集{scene_type}-{scene}场景视频，运行算法，人工核查{target_type}{target}类型识别是否准确，记录类型识别错误。"
    else:
        return f"采集{scene_type}-{scene}场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。"

def make_expected(problem_type):
    if problem_type == "误检":
        return "无误检，所有目标均正确识别"
    elif problem_type == "漏检":
        return "无漏检，所有目标均被检测"
    elif problem_type == "类型错误":
        return "类型识别准确，无类型错误"
    else:
        return "系统稳定，无异常，无可视化问题"

def make_remark(problem_type, problem):
    if problem_type == "误检":
        return "阈值：误检率<0.1%"
    elif problem_type == "漏检":
        return "阈值：漏检率<0.1%"
    elif problem_type == "类型错误":
        return "类型识别准确率>99%"
    else:
        return problem

def is_reasonable(scene_type, scene, target_type, target, problem_type, problem):
    # 合理性规则举例：
    # 1. 只有“车道线”目标才有“类型错误”
    if problem_type == "类型错误" and target_type != "车道线":
        return False
    # 2. 离散目标不做类型错误测试
    if target_type == "离散目标" and problem_type == "类型错误":
        return False
    # 3. "路沿"目标不做类型错误测试
    if target_type == "路沿" and problem_type == "类型错误":
        return False
    # 4. "系统问题"只与场景相关
    if problem_type == "系统问题" and target_type != "车道线":
        return False
    # 5. "漏检道路线"只针对车道线
    if problem == "漏检道路线" and target_type != "车道线":
        return False
    # 6. "漏检路沿线"只针对路沿
    if problem == "漏检路沿线" and target_type != "路沿":
        return False
    # 7. "漏检斑马线"等只针对离散目标
    if problem in ["漏检斑马线", "漏检停止线", "漏检driveline", "漏检地面箭头", "漏检禁停区"] and target_type != "离散目标":
        return False
    # 8. "漏检导流线"只针对车道线
    if problem == "漏检导流线" and target_type != "车道线":
        return False
    return True

def generate_cases():
    cases = []
    case_id = 1
    random.seed(42)
    # 先生成系统问题
    for scene_type, scenes in scene_types:
        for scene in scenes:
            for problem in ["可视化问题", "系统性问题"]:
                cases.append([
                    f"SYS-{case_id:04d}", scene_type, scene, "车道线", "-", "系统问题", problem,
                    make_requirement(scene_type, scene, "车道线", "-", "系统问题", problem),
                    make_expected("系统问题"),
                    make_remark("系统问题", problem)
                ])
                case_id += 1
    # 生成其它问题
    # 用集合去重，避免重复用例
    case_set = set()
    while len(cases) < 650:
        scene_type, scenes = random.choice(scene_types)
        scene = random.choice(scenes)
        target_type, targets = random.choice(target_types)
        target = random.choice(targets)
        problem_type, problems = random.choice(problem_types)
        problem = random.choice(problems)
        # 合理性校验
        if not is_reasonable(scene_type, scene, target_type, target, problem_type, problem):
            continue
        # 目标与问题子类严格对应
        if problem.startswith("漏检"):
            if "道路线" in problem and target_type != "车道线":
                continue
            if "路沿线" in problem and target_type != "路沿":
                continue
            if problem in ["漏检斑马线", "漏检停止线", "漏检driveline", "漏检地面箭头", "漏检禁停区"] and target_type != "离散目标":
                continue
            if problem == "漏检导流线" and target_type != "车道线":
                continue
        if problem_type == "类型错误" and target_type != "车道线":
            continue
        # 用例唯一性校验
        case_key = (scene_type, scene, target_type, target, problem_type, problem)
        if case_key in case_set:
            continue
        case_set.add(case_key)
        cases.append([
            f"TC-{case_id:04d}", scene_type, scene, target_type, target, problem_type, problem,
            make_requirement(scene_type, scene, target_type, target, problem_type, problem),
            make_expected(problem_type),
            make_remark(problem_type, problem)
        ])
        case_id += 1
    return cases

def save_cases(cases):
    # 先写入 BOM 头，确保 Excel 识别 UTF-8
    with open('lane_detection_test_cases.csv', 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow([
            "测试用例ID", "场景类型", "具体场景", "目标类型", "目标子类", "算法问题类型", "算法问题子类", "测试要求", "预期结果", "备注"
        ])
        writer.writerows(cases)

if __name__ == "__main__":
    cases = generate_cases()
    save_cases(cases)
    print(f"已生成包含{len(cases)}个合理测试用例的CSV文件：lane_detection_test_cases.csv")
