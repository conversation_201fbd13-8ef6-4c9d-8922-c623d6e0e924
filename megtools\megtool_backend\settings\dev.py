# 本地开发使用
from rediscluster import ClusterConnectionPool

from .base import *

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'megtool',
        'USER': 'megtool',
        'PASSWORD': 'r2uKRbps$DV7',
        'HOST': 'tidb.mcd.mc.machdrive.cn',
        'PORT': '4000',
        'TIME_ZONE': 'Asia/Shanghai',
        'POOL_OPTIONS': {  # 连接池的参数
            'POOL_SIZE': 10,  # 项目启动时，连接池预先创建的连接数量  最大连接数=POOL_SIZE+MAX_OVERFLOW，默认两个都是10
            'MAX_OVERFLOW': 10, # 当POOL_SIZE的连接数被使用完，还可以根据系统情况再创建的连接数
            'RECYCLE': 60 * 40, # 连接超时时间，超过就关闭连接替换成新打开的连接。默认是-1，即不超时。
            'PRE_PING': True, # 从连接池取出连接时，是否先ping以下数据库，检测连接是否还存活。默认True
            'ECHO': False, # 是否开启SQLAlchemy的SQL语句日志。默认False，在调试时可以使用。
            'TIMEOUT': 30, # 定义连接池中获取连接的超时时间，单位秒。如果指定时间内无法获取到可用的连接，将会引发超时异常。
        }
    }
}

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": [
            "redis://:<EMAIL>:6479/0",
            "redis://:<EMAIL>:6480/0",
            "redis://:<EMAIL>:6479/0",
            "redis://:<EMAIL>:6480/0",
            "redis://:<EMAIL>:6479/0",
            "redis://:<EMAIL>:6480/0",
        ],
        "OPTIONS": {
            "REDIS_CLIENT_CLASS": "rediscluster.RedisCluster",
            "CONNECTION_POOL_CLASS": "rediscluster.connection.ClusterConnectionPool",
            "PASSWORD": "dsf8sdfQ",
            "SOCKET_TIMEOUT": 5,
            "CONNECTION_POOL_KWARGS": {
                "max_connections": 100,
                "skip_full_coverage_check": True,
            },
        },
        "KEY_PREFIX": "megtool:dev",
    }
}

FEISHU_CONFIG = {
    # 飞书开放平台api
    'url': "https://open.feishu.cn/open-apis",
    # 群机器人发送消息
    'group_url': "https://open.feishu.cn/open-apis/bot/v2/hook/",
    'headers': {"Content-Type": "application/json"},
    "app_id": "cli_a731655b65799013",
    "app_secret": "e5yex8mfKnkKs3TvbDP9XdX1equDs2nr",
}
GITLAB_CONFIG = {
    'PRIVATE-TOKEN': "********************",
    'GIT-URL': "https://git-core.megvii-inc.com/api/v4/",
}
JIRA_CONFIG = {
    # 查询jira链接
    'base_url': 'https://jira.mach-drive-inc.com/rest/',
    'url': "https://jira.mach-drive-inc.com/rest/api/2/search",
    'headers': {
        "Authorization": "Bearer ODgyNjA1MzkyMzI0Oh9nupQ7Z7gUCmG6kf2zrkoJkQOd",
        "Content-Type": "application/json"
    }
}

LOGIN_CONFIG = {
    'base_url': 'https://sso.megvii-inc.com/cas/',
    'JWT_SECRET': 'mach_meg_tool_access_token_secret',
}
TASK_CONFIG = {
    'cron_task_switch': 1,
    'file_parser_switch': 1,
    'feishu_report_switch': 1,
}

AWS_CONFIG = {
    "ak": "a5f1703d2bc1c0fd68bc05c74424ad2a",
    "sk": "1d24d63418fc3621889fd9da4abd247d",
    "endpoint_url": "http://oss.i.machdrive.cn",
}
TEST_PLATFORM_ENV='dev'

DEBUG = False