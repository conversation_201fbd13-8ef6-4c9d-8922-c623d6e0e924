"""MCAP工具函数模块"""

from mcap_ros2.reader import read_ros2_messages
import json
import sys
from collections import defaultdict
from typing import Dict, List, Any, <PERSON><PERSON>


def sanitize_filename(message_type: str) -> str:
    """清理文件名中的特殊字符"""
    return message_type.replace("/", "_")


def extract_message_types(stream_data) -> Dict[str, Any]:
    """从流数据中提取消息类型"""
    message_types = {}
    
    for msg in stream_data:
        # 获取消息类型
        if hasattr(msg, 'message_type'):
            message_type = msg.message_type
        else:
            message_type = "unknown"
            
        topic = getattr(msg, 'topic', '/unknown')
        timestamp = getattr(msg, 'timestamp', 0)
        data = getattr(msg, 'data', {})
        
        if message_type not in message_types:
            message_types[message_type] = {
                'count': 0,
                'samples': [],
                'topics': set(),
                'first_timestamp': timestamp,
                'last_timestamp': timestamp
            }
        
        # 更新统计信息
        message_types[message_type]['count'] += 1
        message_types[message_type]['topics'].add(topic)
        message_types[message_type]['last_timestamp'] = timestamp
        
        # 收集样本数据（最多5个）
        if len(message_types[message_type]['samples']) < 5:
            message_types[message_type]['samples'].append({
                'timestamp': timestamp,
                'topic': topic,
                'data': data
            })
    
    # 转换topics为列表
    for message_type in message_types:
        message_types[message_type]['topics'] = list(message_types[message_type]['topics'])
    
    return message_types


def collect_sample_data(messages, message_type: str, limit: int = 5) -> List[Dict[str, Any]]:
    """收集指定消息类型的样本数据"""
    samples = []
    
    for msg in messages:
        if len(samples) >= limit:
            break
            
        if hasattr(msg, 'message_type') and msg.message_type == message_type:
            samples.append({
                'timestamp': getattr(msg, 'timestamp', 0),
                'topic': getattr(msg, 'topic', '/unknown'),
                'data': getattr(msg, 'data', {})
            })
    
    return samples


def generate_json_files(
    message_types: Dict[str, Any], file_metadata: Dict[str, Any], output_dir: str = "."
) -> int:
    """生成JSON文件"""
    import os
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    success_count = 0

    for message_type, type_info in message_types.items():
        filename = f"{sanitize_filename(message_type)}.json"
        filepath = os.path.join(output_dir, filename)

        output_data = {
            "message_type": message_type,
            "count": type_info.get("count", 0),
            "samples": type_info.get("samples", []),
            "topics": type_info.get("topics", []),
            "first_timestamp": type_info.get("first_timestamp", 0),
            "last_timestamp": type_info.get("last_timestamp", 0),
            "file_metadata": file_metadata
        }

        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(
                    output_data, f, default=str, indent=2, ensure_ascii=False
                )
            success_count += 1
        except Exception as e:
            print(f"❌ 生成 {filename} 时出错: {e}")

    return success_count


def analyze_mcap_file(mcap_file: str) -> Dict[str, Any]:
    """分析MCAP文件"""
    try:
        message_types, message_counts = _extract_message_types_from_mcap(mcap_file)
        
        return {
            "success": True,
            "message_types": message_types,
            "message_counts": dict(message_counts),
            "total_messages": sum(message_counts.values()),
            "unique_types": len(message_types)
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message_types": {},
            "message_counts": {},
            "total_messages": 0,
            "unique_types": 0
        }


def _extract_message_types_from_mcap(mcap_file: str) -> Tuple[Dict[str, Any], Dict[str, int]]:
    """从MCAP文件中提取消息类型（内部函数）"""
    message_types = {}
    message_counts = defaultdict(int)
    processed_count = 0

    try:
        for msg in read_ros2_messages(mcap_file):
            processed_count += 1
            
            # 获取消息类型
            message_type = getattr(msg, 'message_type', 'unknown')
            topic = getattr(msg, 'topic', '/unknown')
            timestamp = getattr(msg, 'timestamp', 0)
            
            message_counts[message_type] += 1
            
            if message_type not in message_types:
                message_types[message_type] = {
                    'count': 0,
                    'samples': [],
                    'topics': set(),
                    'first_timestamp': timestamp,
                    'last_timestamp': timestamp
                }
            
            # 更新统计信息
            message_types[message_type]['count'] += 1
            message_types[message_type]['topics'].add(topic)
            message_types[message_type]['last_timestamp'] = timestamp
            
            # 收集样本数据（最多3个）
            if len(message_types[message_type]['samples']) < 3:
                try:
                    data = getattr(msg, 'data', {})
                    message_types[message_type]['samples'].append({
                        'timestamp': timestamp,
                        'topic': topic,
                        'data': data
                    })
                except Exception:
                    # 如果数据序列化失败，跳过
                    pass
            
            # 限制处理数量以避免内存问题
            if processed_count >= 10000:
                break
                
    except Exception as e:
        print(f"处理MCAP文件时出错: {e}")
        raise
    
    # 转换topics为列表
    for message_type in message_types:
        message_types[message_type]['topics'] = list(message_types[message_type]['topics'])
    
    return message_types, message_counts