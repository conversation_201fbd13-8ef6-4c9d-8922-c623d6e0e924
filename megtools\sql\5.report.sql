drop table if exists report_feishu_info;

create table report_feishu_info
(
    id        bigint unsigned auto_increment primary key comment '主键ID',
    name      varchar(128) not null comment '报告名称',
    template      varchar(64) not null comment '模板ID',
    target      varchar(64) not null comment '模板ID',
    params     text not null comment '报告生成参数',
    remark    varchar(256) not null comment '备注',
    create_by varchar(64)   not null DEFAULT '' comment '创建者',
    create_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    update_by varchar(64)   not null DEFAULT '' comment '更新者',
    update_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    is_delete  varchar(2)  not null DEFAULT '0' comment '是否已经删除 0-未删除 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci  comment 'KeyValue存储';


drop table if exists report_feishu_info_log;

create table report_feishu_info_log
(
    id        bigint unsigned auto_increment primary key comment '主键ID',
    feishu_info_id      bigint not null comment '关联报告配置',
    name      varchar(128) not null comment '报告名称',
    report_url      varchar(64) not null comment '生成的飞书url',
    params     text not null comment '报告生成参数',
    remark    varchar(256) not null comment '备注',
    status    varchar(2) not null comment '生成状态 0-初始状态 1-生成中 2-生成成功 3-生成失败',
    create_by varchar(64)   not null DEFAULT '' comment '创建者',
    create_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    update_by varchar(64)   not null DEFAULT '' comment '更新者',
    update_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    is_delete  varchar(2)  not null DEFAULT '0' comment '是否已经删除 0-未删除 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci  comment 'KeyValue存储';
