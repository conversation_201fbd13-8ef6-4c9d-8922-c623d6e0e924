"""
这里解析TOP相关的日志，
日志长这个样子：
10:26:37 AM  CPU    %usr   %nice    %sys %iowait    %irq   %soft  %steal  %guest  %gnice   %idle
10:26:39 AM  all    5.48    0.85    2.63    0.05    0.00    0.17    0.00    0.00    0.00   90.83
10:26:39 AM    0    6.06    1.52    3.54    0.00    0.00    0.00    0.00    0.00    0.00   88.89
10:26:39 AM    1    1.02    1.02    1.02    0.00    0.00    0.00    0.00    0.00    0.00   96.95
10:26:39 AM    2    6.63    1.02    3.57    0.00    0.00    0.00    0.00    0.00    0.00   88.78
10:26:39 AM    3    1.01    0.51    0.00    0.00    0.00    0.00    0.00    0.00    0.00   98.48
10:26:39 AM    4    4.39    1.46    2.93    0.00    0.00    4.39    0.00    0.00    0.00   86.83
10:26:39 AM    5    1.51    0.50    0.00    0.00    0.00    0.00    0.00    0.00    0.00   97.99
10:26:39 AM    6    8.59    2.02    5.56    0.00    0.00    0.00    0.00    0.00    0.00   83.84
10:26:39 AM    7    0.51    0.51    0.00    0.00    0.00    0.00    0.00    0.00    0.00   98.98
10:26:39 AM    8    6.67    1.03    4.62    0.00    0.00    0.00    0.00    0.00    0.00   87.69
10:26:39 AM    9    1.49    1.00    1.00    0.00    0.00    0.00    0.00    0.00    0.00   96.52
10:26:39 AM   10   68.34    0.00   31.16    0.50    0.00    0.00    0.00    0.00    0.00    0.00
10:26:39 AM   11    1.01    0.51    0.51    0.00    0.00    0.00    0.00    0.00    0.00   97.98
10:26:39 AM   12    6.40    2.46    7.39    0.00    0.00    0.00    0.00    0.00    0.00   83.74
10:26:39 AM   13    1.51    0.50    1.01    0.00    0.00    0.00    0.00    0.00    0.00   96.98
10:26:39 AM   14    6.47    2.49    2.49    0.00    0.00    0.00    0.00    0.00    0.00   88.56
10:26:39 AM   15    1.52    0.51    0.51    0.00    0.00    0.00    0.00    0.00    0.00   97.46
10:26:39 AM   16    7.84    0.98    1.47    0.49    0.00    0.49    0.00    0.00    0.00   88.73
10:26:39 AM   17    6.06    1.01    1.52    0.00    0.00    0.00    0.00    0.00    0.00   91.41
10:26:39 AM   18    3.52    1.01    2.01    0.50    0.00    0.00    0.00    0.00    0.00   92.96
10:26:39 AM   19    3.03    1.01    2.02    0.00    0.00    0.00    0.00    0.00    0.00   93.94
10:26:39 AM   20    5.45    0.00    1.49    0.00    0.00    0.00    0.00    0.00    0.00   93.07
10:26:39 AM   21    1.54    1.54    1.03    0.00    0.00    0.00    0.00    0.00    0.00   95.90
10:26:39 AM   22    2.55    0.51    1.02    0.00    0.00    0.51    0.00    0.00    0.00   95.41
10:26:39 AM   23    2.53    0.51    1.52    0.00    0.00    0.00    0.00    0.00    0.00   95.45
10:26:39 AM   24    3.50    1.00    1.00    0.00    0.00    0.00    0.00    0.00    0.00   94.50
10:26:39 AM   25    3.54    0.51    1.01    0.00    0.00    0.00    0.00    0.00    0.00   94.95
10:26:39 AM   26    2.53    0.51    1.01    0.00    0.00    0.00    0.00    0.00    0.00   95.96
10:26:39 AM   27    2.53    0.00    1.01    0.00    0.00    0.00    0.00    0.00    0.00   96.46
10:26:39 AM   28    2.03    0.00    0.51    0.00    0.00    0.00    0.00    0.00    0.00   97.46
10:26:39 AM   29    1.52    0.51    1.02    0.00    0.00    0.00    0.00    0.00    0.00   96.95
10:26:39 AM   30    2.02    0.51    0.51    0.00    0.00    0.00    0.00    0.00    0.00   96.97
10:26:39 AM   31    1.52    0.51    0.51    0.00    0.00    0.00    0.00    0.00    0.00   97.47
"""
import logging
import traceback
from datetime import datetime

from cron_task.handlers.log_parsers.parser_super import Parser
from cron_task.models import HostAllCpuMpStatLog


def apply(file_path, db_entity):
    TopCpuMpstatParser().parse(db_entity.id, db_entity, file_path)


class TopCpuMpstatParser(Parser):
    def __init__(self):
        super().__init__()
        self.current_day = ""
        self.cpu_cache = []
        self.db_entity = None

    def parse_file(self, id, db_entity, log_path):
        self.db_entity = db_entity
        with open(log_path, "r", encoding="utf-8") as log:
            is_new_line = True
            while True:
                try:
                    line = log.readline()
                    if line == "":
                        break
                    is_new_line = True
                    # 这里需要处理分组
                    self.choose_parse(line)
                    self.check_flash_cache()
                except Exception as e:
                    logging.error(f"top parse error: {line} {traceback.format_exc()}")
                    self.error_info = f"top parse error: {line} {traceback.format_exc()}"
                    if is_new_line is False:
                        ## 这时候说明，第二次进来的时候还是没有读取到新的行，从而破开这个死循环
                        break
                    is_new_line = False
        self.check_flash_cache(True)

    def parse_txt(self, base_info, text):
        self.db_entity = base_info
        self.origin = base_info.origin
        # 直接解析文本信息
        for item in text:
            lines = item.split('\n')
            for line in lines:
                self.choose_parse(line)
                self.check_flash_cache()
        self.check_flash_cache(True)

    # 将缓存数据 刷到数据库里面
    def check_flash_cache(self, is_force=False):
        if len(self.cpu_cache) >= 1000 or is_force:
            self.flush_cache(HostAllCpuMpStatLog, self.cpu_cache)



    def choose_parse(self, line):
        if line.strip() == '':
            return
        if line.startswith("Linux"):
            day = line.split()[3]
            if "年" in line:
                self.current_day = datetime.strptime(day, "%Y年%m月%d日").strftime("%Y-%m-%d")
            else:
                self.current_day = datetime.strptime(day, "%m/%d/%Y").strftime("%Y-%m-%d")
        elif 'CPU' not in line:
            self.parse_cpu(line)

    def parse_cpu(self, cpu_line):
        if self.current_day == "":
            return

        cpu_line_info = cpu_line.split()
        current_time = cpu_line_info[0]
        start = 2
        if "时" in current_time:
            current_time = current_time.replace("时", ":")
            current_time = current_time.replace("分", ":")
            current_time = current_time.replace("秒", "")
            start = 1
        if "PM" == cpu_line_info[1]:
            hour = current_time.split(":")[0]
            if hour == "12":
                current_time = f"{hour}{current_time[2:]}"
            else:
                current_time = f"{int(hour) + 12}{current_time[2:]}"
        if "AM" == cpu_line_info[1]:
            hour = current_time.split(":")[0]
            if hour == "12":
                current_time = f"00{current_time[2:]}"

        self.update_time(f"{self.current_day} {current_time}")
        cpu_info = {
            "vin": self.db_entity.vin,
            "record_time": f"{self.current_day} {current_time}",
            "cpu_idx": cpu_line_info[start],
            "cpu_usr": float(cpu_line_info[start+1]),
            "cpu_nice": float(cpu_line_info[start+2]),
            "cpu_sys": float(cpu_line_info[start+3]),
            "cpu_iowait": float(cpu_line_info[start+4]),
            "cpu_irq": float(cpu_line_info[start+5]),
            "cpu_soft": float(cpu_line_info[start+6]),
            "cpu_steal": float(cpu_line_info[start+7]),
            "cpu_guest": float(cpu_line_info[start+8]),
            "cpu_gnice": float(cpu_line_info[start+9]),
            "cpu_idle": float(cpu_line_info[start+10]),
        }
        self.cpu_cache.append(HostAllCpuMpStatLog(**cpu_info))
