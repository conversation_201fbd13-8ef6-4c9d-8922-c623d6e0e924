
# 示例代码

本目录包含 MegSim API 的使用示例和工具脚本。

## 📋 示例列表

| 文件名 | 描述 | 用途 |
|--------|------|------|
| `api_analysis_report.py` | API分析报告生成器 | 生成详细的API使用分析报告 |

## 🚀 使用方法

### API分析报告生成器
```bash
python api_analysis_report.py
```

这个脚本会：
1. 分析所有已测试的API端点
2. 生成详细的使用统计
3. 提供API调用示例
4. 输出完整的分析报告

## 💡 示例代码片段

### 基础API调用
```python
import requests
import json

# 基础GET请求
response = requests.get('https://megsim.mc.machdrive.cn/multisearch')
data = response.json()

# 基础POST请求
payload = {"name": "test", "project": 4}
response = requests.post(
    'https://megsim.mc.machdrive.cn/api/tasks/',
    json=payload
)
```

### 使用客户端类
```python
from clients.tasks_api_client import MegSimTasksClient

# 创建客户端实例
client = MegSimTasksClient()

# 创建任务
result = client.create_task(
    name="示例任务",
    project=4,
    car_type="Z10"
)

if result["success"]:
    print(f"任务创建成功: {result['data']['id']}")
else:
    print(f"创建失败: {result['error']}")
```

### 错误处理示例
```python
try:
    result = client.create_task_plan(
        name="测试计划",
        project=4,
        task_ids=[424160]
    )
    
    if not result["success"]:
        if result["status_code"] == 400:
            error_analysis = client.analyze_error_response(result["data"])
            print(f"错误类型: {error_analysis['error_type']}")
            print(f"建议: {error_analysis['suggestions']}")
        
except Exception as e:
    print(f"请求异常: {e}")
```

## 🔧 工具脚本

### 批量测试脚本
```python
# 批量运行所有API测试
import subprocess
import os

test_scripts = [
    'test_api_variations.py',
    'test_tasks_api.py', 
    'test_task_plans_api.py',
    'test_batch_jira_api.py'
]

for script in test_scripts:
    print(f"运行测试: {script}")
    result = subprocess.run(['python', f'tests/{script}'], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ 测试通过")
    else:
        print(f"❌ 测试失败: {result.stderr}")
```

## 📝 注意事项

1. 示例代码仅供参考，实际使用时请根据需要调整
2. 确保已安装必要的依赖包：`requests`
3. 某些示例可能需要有效的API访问权限
4. 建议在测试环境中运行示例代码
