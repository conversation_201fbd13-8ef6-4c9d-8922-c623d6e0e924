# 环境变量配置示例
# 复制此文件为 .env 并修改相应配置

# 飞书配置
FEISHU_APP_ID=your_app_id_here
FEISHU_APP_SECRET=your_app_secret_here
FEISHU_APP_TOKEN=your_app_token_here
FEISHU_TABLE_ID=your_table_id_here

# GitLab配置
GITLAB_URL=https://********************.com
GITLAB_TOKEN=your_gitlab_token_here
GITLAB_TIMEOUT=10

# 轮询配置
POLL_INTERVAL=60
MAX_RETRIES=5
RETRY_DELAY=30
BATCH_SIZE=100

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/feishu_monitor.log
LOG_MAX_FILE_SIZE=10485760
LOG_BACKUP_COUNT=5

# 调试模式
DEBUG=false
