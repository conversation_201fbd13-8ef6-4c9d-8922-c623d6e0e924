name: megtool-backend                         # [required] 应用名称，全局唯一，不允许重名

web:                                          # 类型为 web 的组件列表，作用是对外提供 HTTP 服务，内部域名为 ${appName}.mcd.megvii-inc.com
  - name: main                                # [required] 名称，同一个 app 下的 web 不能重名
    cmd: python3 main.py                 # 运行命令，如果没有填写 backend 字段则此字段必须填写
    port:                                     # [required] 端口列表，格式为 servicePort:processPort/protocol
      - 8000
    liveness_probe:
      http_get:
        scheme: http
        path: /misc/ping
        port: 8000
    readiness_probe:
      http_get:
        scheme: http
        path: /actuator/health
        port: 8000
