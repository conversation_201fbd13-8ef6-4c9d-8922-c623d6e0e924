#!/usr/bin/env python3


import time
import bisect
from pathlib import Path
from typing import Any, Dict, Iterator, List, Optional, Union, Tuple
from concurrent.futures import ThreadPoolExecutor
from collections import defaultdict

# MCAP相关导入
try:
    from mcap_ros2.reader import read_ros2_messages
    from mcap.reader import McapReader
    MCAP_AVAILABLE = True
except ImportError:
    MCAP_AVAILABLE = False

# 导入日志系统
try:
    from ..utils.sdk_logger import get_logger, log_info, log_warning, log_error, log_success, log_progress, log_performance, log_file_operation, log_parsing_info, log_data_info, log_lane_info
except ImportError:
    try:
        from ..utils.logger import get_logger, log_info, log_warning, log_error, log_success, log_progress, log_performance, log_file_operation, log_parsing_info, log_data_info, log_lane_info
    except ImportError:
        # 临时日志函数（如果日志系统不可用）
        def log_info(msg, **kwargs): print(f"INFO - {msg}")
        def log_warning(msg, **kwargs): print(f"WARNING - {msg}")
        def log_error(msg, **kwargs): print(f"ERROR - {msg}")
        def log_progress(msg, **kwargs): print(f"PROGRESS - {msg}")
        def log_data_info(msg, **kwargs): print(f"DATA - {msg}")
        def log_success(msg, **kwargs): print(f"SUCCESS - {msg}")

# 导入数据类
try:
    from ..data_structures import MessageData, TimeRange, ProcessingStats, StreamConfig
except ImportError:
    from ..data_structures import MessageData, TimeRange, ProcessingStats, StreamConfig


class MessageIndex:
    """消息索引类，用于快速随机访问"""
    
    def __init__(self):
        self.timestamps = []  # 时间戳列表（有序）
        self.message_offsets = []  # 消息在文件中的偏移量
        self.topic_index = defaultdict(list)  # 话题索引
        self.type_index = defaultdict(list)  # 消息类型索引
        self.built = False
    
    def add_message(self, timestamp: float, offset: int, topic: str, msg_type: str, index: int):
        """添加消息到索引"""
        self.timestamps.append(timestamp)
        self.message_offsets.append(offset)
        self.topic_index[topic].append(index)
        self.type_index[msg_type].append(index)
    
    def build_index(self):
        """构建索引（排序等）"""
        if not self.built:
            # 时间戳通常已经是有序的，但为了确保正确性还是排序一下
            sorted_data = sorted(zip(self.timestamps, self.message_offsets))
            self.timestamps, self.message_offsets = zip(*sorted_data) if sorted_data else ([], [])
            self.timestamps = list(self.timestamps)
            self.message_offsets = list(self.message_offsets)
            self.built = True
    
    def find_time_range(self, start_time: float, end_time: float) -> Tuple[int, int]:
        """查找时间范围内的消息索引范围"""
        if not self.built:
            self.build_index()
        
        start_idx = bisect.bisect_left(self.timestamps, start_time)
        end_idx = bisect.bisect_right(self.timestamps, end_time)
        return start_idx, end_idx
    
    def get_messages_by_topic(self, topic: str) -> List[int]:
        """获取指定话题的所有消息索引"""
        return self.topic_index.get(topic, [])
    
    def get_messages_by_type(self, msg_type: str) -> List[int]:
        """获取指定类型的所有消息索引"""
        return self.type_index.get(msg_type, [])


class FastMcapParser:
    """高速MCAP解析器 - 批量读取、随机访问和内存优化"""
    
    def __init__(self, verbose: bool = False, batch_size: int = 10000, max_workers: int = 4):
        """初始化高速解析器"""
        self.verbose = verbose
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.message_cache = {}  # 消息缓存
        self.index = MessageIndex()  # 消息索引
        self.stats = ProcessingStats()
        
        if not MCAP_AVAILABLE:
            raise ImportError("MCAP支持不可用，请安装: pip install mcap-ros2-support")
    
    def build_index(self, mcap_file: Union[str, Path]) -> MessageIndex:
        """构建消息索引"""
        mcap_file = Path(mcap_file)
        
        if self.verbose:
            log_info(f"🔍 构建消息索引: {mcap_file.name}")
        
        start_time = time.time()
        index = MessageIndex()
        message_count = 0
        
        try:
            # 第一遍扫描：构建索引
            for i, ros_msg in enumerate(read_ros2_messages(str(mcap_file))):
                timestamp = getattr(ros_msg, 'log_time_ns', getattr(ros_msg, 'publish_time_ns', 0)) / 1e9  # 纳秒转秒
                topic = ros_msg.channel.topic if hasattr(ros_msg, 'channel') and hasattr(ros_msg.channel, 'topic') else 'unknown'
                msg_type = ros_msg.schema.name if hasattr(ros_msg, 'schema') and hasattr(ros_msg.schema, 'name') else 'unknown'

                # 添加到索引（这里offset暂时用消息序号代替）
                index.add_message(
                    timestamp=timestamp,
                    offset=i,  # 实际应该是文件偏移量
                    topic=topic,
                    msg_type=msg_type,
                    index=i
                )
                
                message_count += 1
                
                if self.verbose and message_count % 50000 == 0:
                    log_info(f"   已索引 {message_count:,} 条消息...")
            
            # 构建索引
            index.build_index()
            
            build_time = time.time() - start_time
            if self.verbose:
                log_success(f"索引构建完成: {message_count:,} 条消息，耗时 {build_time:.2f}s")
                log_info(f"   索引速率: {message_count / build_time:.0f} 消息/秒")
        
        except Exception as e:
            if self.verbose:
                log_error(f"索引构建失败: {e}")
            raise
        
        self.index = index
        return index
    
    def fast_stream_data(self,
                        mcap_file: Union[str, Path],
                        message_types: Optional[List[str]] = None,
                        topics: Optional[List[str]] = None,
                        time_range: Optional[TimeRange] = None,
                        max_messages: Optional[int] = None,
                        enable_structured_data: bool = False) -> Iterator[MessageData]:
        """高速流式处理MCAP数据，使用索引优化策略"""
        mcap_file = Path(mcap_file)

        if not mcap_file.exists():
            raise FileNotFoundError(f"MCAP文件不存在: {mcap_file}")

        if self.verbose:
            log_success(f" 开始高速处理: {mcap_file.name} (索引优化)")
            if message_types:
                log_info(f"   消息类型过滤: {message_types}")
            if topics:
                log_info(f"   话题过滤: {topics}")
            if time_range:
                log_info(f"   时间范围: {time_range.start_time:.2f}s - {time_range.end_time:.2f}s")

        # 重置统计
        self.stats = ProcessingStats()
        self.stats.start_time = time.time()

        # 使用索引优化策略
        yield from self._indexed_stream_data(mcap_file, message_types, topics, time_range, max_messages, enable_structured_data)

        # 更新统计
        self.stats.end_time = time.time()
        self.stats.processing_time = self.stats.end_time - self.stats.start_time

        if self.verbose:
            log_success(" 高速处理完成")
            log_info(f"   处理消息数: {self.stats.processed_messages:,}")
            log_info(f"   处理时间: {self.stats.processing_time:.2f}s")
            log_info(f"   处理速率: {self.stats.get_processing_rate():.0f} 消息/秒")
    
    def _get_target_indices(self, 
                           message_types: Optional[List[str]] = None,
                           topics: Optional[List[str]] = None,
                           time_range: Optional[TimeRange] = None) -> List[int]:
        """获取目标消息的索引列表"""
        target_indices = set()
        
        # 如果没有任何过滤条件，返回所有消息
        if not message_types and not topics and not time_range:
            return list(range(len(self.index.timestamps)))
        
        # 按时间范围过滤
        if time_range:
            start_idx, end_idx = self.index.find_time_range(time_range.start_time, time_range.end_time)
            target_indices.update(range(start_idx, end_idx))
        else:
            target_indices.update(range(len(self.index.timestamps)))
        
        # 按话题过滤
        if topics:
            topic_indices = set()
            for topic in topics:
                topic_indices.update(self.index.get_messages_by_topic(topic))
            target_indices &= topic_indices
        
        # 按消息类型过滤
        if message_types:
            type_indices = set()
            for msg_type in message_types:
                # 支持简化的消息类型名称
                full_types = [t for t in self.index.type_index.keys() if msg_type in t]
                for full_type in full_types:
                    type_indices.update(self.index.get_messages_by_type(full_type))
            target_indices &= type_indices
        
        return sorted(list(target_indices))
    
    def _batch_read_messages(self, mcap_file: Path, indices: List[int]) -> List[MessageData]:
        """批量读取消息"""
        messages = []
        
        # 这里实现批量读取逻辑
        # 由于mcap_ros2库的限制，我们仍然需要遍历，但可以跳过不需要的消息
        current_index = 0
        target_set = set(indices)
        
        for ros_msg in read_ros2_messages(str(mcap_file)):
            if current_index in target_set:
                timestamp = getattr(ros_msg, 'log_time_ns', getattr(ros_msg, 'publish_time_ns', 0)) / 1e9  # 纳秒转秒
                topic = ros_msg.channel.topic if hasattr(ros_msg, 'channel') and hasattr(ros_msg.channel, 'topic') else 'unknown'
                msg_type = ros_msg.schema.name if hasattr(ros_msg, 'schema') and hasattr(ros_msg.schema, 'name') else 'unknown'

                msg_data = MessageData(
                    data=ros_msg.ros_msg,  # 实际的ROS消息数据
                    timestamp=timestamp,
                    topic=topic,
                    message_type=msg_type
                )
                messages.append(msg_data)
                
                # 如果已经收集到所有目标消息，可以提前退出
                if len(messages) >= len(indices):
                    break
            
            current_index += 1
        
        return messages
    
    def random_access(self, mcap_file: Union[str, Path], timestamp: float,
                     tolerance: float = 0.1) -> Optional[MessageData]:
        """随机访问指定时间点的消息"""
        if not self.index.built:
            self.build_index(mcap_file)
        
        # 查找最接近的时间戳
        closest_idx = bisect.bisect_left(self.index.timestamps, timestamp)
        
        # 检查前后的时间戳，找到最接近的
        candidates = []
        for idx in [closest_idx - 1, closest_idx, closest_idx + 1]:
            if 0 <= idx < len(self.index.timestamps):
                ts = self.index.timestamps[idx]
                if abs(ts - timestamp) <= tolerance:
                    candidates.append((abs(ts - timestamp), idx))
        
        if not candidates:
            return None
        
        # 选择最接近的
        _, best_idx = min(candidates)
        
        # 读取该消息
        messages = self._batch_read_messages(Path(mcap_file), [best_idx])
        return messages[0] if messages else None
    
    def get_statistics(self) -> ProcessingStats:
        """获取处理统计信息"""
        return self.stats

    def _indexed_stream_data(self, mcap_file: Path, message_types: Optional[List[str]],
                            topics: Optional[List[str]], time_range: Optional[TimeRange],
                            max_messages: Optional[int], enable_structured_data: bool) -> Iterator[MessageData]:
        """基于索引的流式处理"""
        # 构建索引（如果还没有构建）
        if not self.index.built:
            self.build_index(mcap_file)

        # 确定要处理的消息索引范围
        target_indices = self._get_target_indices(message_types, topics, time_range)

        if max_messages:
            target_indices = target_indices[:max_messages]

        self.stats.total_messages = len(target_indices)

        if self.verbose:
            log_info(f"   目标消息数: {len(target_indices):,}")

        # 批量处理消息
        processed_count = 0

        try:
            # 分批处理
            for batch_start in range(0, len(target_indices), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(target_indices))
                batch_indices = target_indices[batch_start:batch_end]

                # 批量读取消息
                batch_messages = self._batch_read_messages(mcap_file, batch_indices)

                # 处理批次中的每条消息
                for msg_data in batch_messages:
                    # 结构化数据转换
                    if enable_structured_data:
                        msg_data.structured_data = self._convert_to_structured_data(msg_data)

                    self.stats.processed_messages += 1
                    processed_count += 1

                    yield msg_data

                # 进度显示
                if self.verbose and processed_count % (self.batch_size * 5) == 0:
                    log_info(f"   已处理 {processed_count:,} 条消息...")

        except Exception as e:
            if self.verbose:
                log_error(f" 索引流式处理出错: {e}")
            raise


