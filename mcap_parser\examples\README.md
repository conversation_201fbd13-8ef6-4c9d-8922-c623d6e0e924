# 🚗 车道线解析示例

本目录包含使用MCAP自动驾驶SDK解析车道线数据的示例代码。

## 📁 文件说明

### `quick_lane_print.py` - 快速打印版本 ⭐
使用SDK快速打印车道线数据，无索引创建。

**特点:**
- 🎯 使用SDK，禁用索引创建
- ⚡ 直接流式读取，速度最快
- 📊 打印核心车道线信息
- 🔧 最适合快速查看数据

**使用方法:**
```bash
# 快速打印车道线
python examples/quick_lane_print.py ppl_bag_20250716_200601_0.mcap 3
```

### `direct_lane_print.py` - 详细打印版本
使用SDK详细打印所有车道线数据。

**特点:**
- 🎯 使用SDK，无索引创建
- 📊 详细打印所有车道线属性
- 🛣️ 包括路径点、置信度、ID等
- 🔧 适合深度数据分析

**使用方法:**
```bash
# 详细打印车道线数据
python examples/direct_lane_print.py ppl_bag_20250716_200601_0.mcap 1
```

### `direct_lane_parser.py` - 兼容版本
兼容不同SDK版本的车道线解析。

**使用方法:**
```bash
python examples/direct_lane_parser.py ppl_bag_20250716_200601_0.mcap 5
```

## 🚀 快速开始

### 方法1: 快速打印车道线（推荐）
```bash
# 快速打印车道线核心信息
python examples/quick_lane_print.py ppl_bag_20250716_200601_0.mcap 3
```

### 方法2: 详细打印车道线数据
```bash
# 详细打印所有车道线属性
python examples/direct_lane_print.py ppl_bag_20250716_200601_0.mcap 1
```

### 典型输出示例
```
🚗 快速打印车道线: ppl_bag_20250716_200601_0.mcap

🛣️  车道线消息 1 (时间: 1752667562.137s)
   检测到 25 条车道线:
   车道1: ID=0, 置信度=1.000, 18个点
     起点: (328635.0, 3357994.9, 16.1)
     终点: (328530.2, 3357980.8, 15.6)
   车道2: ID=5, 置信度=1.000, 19个点
     起点: (328641.0, 3357991.9, 16.2)
     终点: (328530.8, 3357977.1, 15.7)
   ... 还有 23 条车道线

✅ 完成，共打印 1 条消息
```

## 📊 输出数据说明

### 车道线数据结构
- **车道ID**: 唯一标识符
- **置信度**: 检测置信度 (0.0-1.0)
- **路径点**: 车道线的3D坐标点序列
- **车道属性**: 车道类型和属性信息

### 坐标系统
- **X, Y**: 平面坐标 (米)
- **Z**: 高度坐标 (米)
- 坐标系通常为车辆坐标系或世界坐标系

## 🔧 自定义解析

可以根据需要修改示例代码：

```python
# 调整显示的车道线数量
for lane in lanes[:10]:  # 显示前10条

# 添加更多数据字段
print(f"车道属性: {lane.lane_property}")
print(f"左车道ID: {lane.left_lane_id}")
print(f"右车道ID: {lane.right_lane_id}")
```

## ⚡ 使用提示

- 限制`max_messages`避免处理过多数据
- SDK已禁用索引创建，直接流式读取
- 支持完整消息类型名：`deva_perception_msgs/msg/LaneArrayv2`
