"""pytest配置文件"""

import pytest
import asyncio
from unittest.mock import Mock
from src.config import Settings, FeishuConfig, GitLabConfig


@pytest.fixture
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_settings():
    """模拟配置"""
    return Settings(
        feishu=FeishuConfig(
            app_id="test_app_id",
            app_secret="test_app_secret",
            app_token="test_app_token",
            table_id="test_table_id"
        ),
        gitlab=GitLabConfig(
            url="https://test-gitlab.com",
            token="test_token"
        )
    )


@pytest.fixture
def mock_feishu_client():
    """模拟飞书客户端"""
    client = Mock()
    client.bitable.v1.app_table_record.list.return_value.success.return_value = True
    client.bitable.v1.app_table_record.update.return_value.success.return_value = True
    return client


@pytest.fixture
def sample_feishu_record():
    """示例飞书记录"""
    from src.models.feishu import FeishuRecord
    return FeishuRecord(
        id="test_record_id",
        fields={
            "版本地址": "https://test-gitlab.com/project/repo/-/merge_requests/123",
            "Commit ID": "",
            "更新内容": "",
            "验证要求": ""
        },
        version="test_version"
    )


@pytest.fixture
def sample_gitlab_mr():
    """示例GitLab MR"""
    from src.models.gitlab import GitLabMR
    return GitLabMR(project_path="project/repo", mr_id=123)
