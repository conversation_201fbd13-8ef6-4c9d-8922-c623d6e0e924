import logging
import os
import shutil
import time
import traceback

from django.db.models import Q

from basic.services import SendMessageApi
from basic.utils import DistributedLockThread, get_ip, OssUtils, FileUtil, generate_md5
from cron_task.models import OssFileList


class LogFileDownloadThread(DistributedLockThread):
    def __init__(self):
        self.host_name = get_ip()
        lock_name = f"log_file_download.lock:{self.host_name}"
        super().__init__(lock_name, name="LogFileDownloadThread", switchName="file_parser_switch")

    def before(self):
        # 如果没有获取到锁，那么等待2分钟，让获取到锁的pod 进行处理， 这个是避免多容器，容器内多线程重复执行的操作！
        if not self.obtain_lock():
            time.sleep(120)
            return
        # 如果获取到锁了， 那么需要重置日志的解析状态。
        OssFileList.objects.filter(Q(current_status__in=[1, 2, 3]) & ~Q(parse_type="mcap_parse")).update(current_status=0, host_name="")

    def apply(self):
        self.interval = 10
        self.need_release = True
        download_cnt = OssFileList.objects.filter(current_status__in=[1,2], host_name=self.host_name).count()
        if download_cnt > 10:
            time.sleep(self.interval)
            return
        result = OssFileList.objects.filter(Q(current_status=0) & ~Q(parse_type="mcap_parse")).order_by('id')[0:5]
        path = []
        resource_path = []
        ids = []
        bucketName = []
        for item in result:
            self.interval = 0.01
            ids.append(item.id)
            resource_path.append(item.oss_path)
            bucketName.append(item.bucket_name)
            dir_name = os.path.join('oss_logs', item.vin, item.oss_path[0:8])
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
            path.append(os.path.join(dir_name, f"{generate_md5(item.oss_path)}_{item.oss_path.split('/')[-1]}"))
        OssFileList.objects.filter(id__in=ids).update(current_status=1, host_name=self.host_name)
        self.release_lock()
        self.need_release = False
        self.download(ids, resource_path, path, bucketName)

    def download(self, ids, resource_urls, path, bucketName):
        bucket_clients = {}
        for idx in range(0, len(resource_urls)):
            table_id = ids[idx]
            oss_path = resource_urls[idx]
            local_path = path[idx]
            bucket_name = bucketName[idx]
            bucket_client = bucket_clients.get(bucket_name, None)
            if bucket_client is None:
                bucket_client = OssUtils(bucket_name)
                bucket_clients[bucket_name] = bucket_client


            is_success = False
            count = 0
            # 下载，需要进行重试
            while True:
                count += 1
                error_info = ""
                try:
                    bucket_client.download(oss_path, local_path)
                    unzip_path = None
                    if "gz" in local_path.split(".")[-1]:
                        unzip_path = self.decompress_gz_file(path[idx])
                    elif "log" in  local_path.split(".")[-1]:
                        # 如果是日志文件没有压缩，直接拷贝下就好了
                        new_path = os.path.join(os.path.dirname(path[idx]), f"{os.path.basename(path[idx])}.log")
                        shutil.copyfile(local_path, new_path)
                        unzip_path = new_path
                    if unzip_path is not None:
                        is_success = True
                        break
                except Exception:
                    logging.info(f"download error! {traceback.format_exc()}")
                    error_info = traceback.format_exc()
                    time.sleep(1)
                if count > 20:
                    break
            # 如果成功了，则更新状态
            if is_success:
                OssFileList.objects.filter(id=table_id).update(current_status=2, host_name=self.host_name)
            else:
                message_entity = {
                    "log_file_path": oss_path,
                    "bucket_name": bucket_name,
                    "log_file_dir": oss_path[0: oss_path.rindex("/")]
                }
                message = {"template_id": "AAqRnxM6gI88O", "template_variable": message_entity}
                SendMessageApi(message).send_message("日志下载失败监控告警")
                OssFileList.objects.filter(id=table_id).update(current_status=5, remark=error_info)

        for bucket_name in bucket_clients:
            bucket_client = bucket_clients[bucket_name]
            bucket_client.close()

    def decompress_gz_file(self, input_file):
        output_path = os.path.join(os.path.dirname(input_file), f"{os.path.basename(input_file)}_dir")
        new_path = os.path.join(os.path.dirname(input_file), f"{os.path.basename(input_file)}.log")
        try:
            file_util = FileUtil()
            file_util.extraTar(input_file, output_path)
            file_list = file_util.all_file(output_path)
            if len(file_list) == 1:
                # 如果只有一个文件， 那么直接移动重命名即可
                file_util.move_file(file_list[0], new_path)
                file_util.clear(output_path)
            else:
                with open(new_path, 'wb') as outfile:
                    # 遍历每个要合并的文件
                    for file_name in file_list:
                        try:
                            # 以读取二进制模式打开当前文件
                            with open(file_name, 'rb') as infile:
                                # 将当前文件内容复制到目标文件
                                shutil.copyfileobj(infile, outfile)
                                # 可以选择在每个文件内容后添加换行符，使不同文件内容分隔开
                                outfile.write(b'\n')
                        except FileNotFoundError:
                            logging.info("file not found")
            file_util.clear(output_path)
            return new_path
        except Exception as e:
            logging.error(f"unzip error: {traceback.format_exc()}")
            return None