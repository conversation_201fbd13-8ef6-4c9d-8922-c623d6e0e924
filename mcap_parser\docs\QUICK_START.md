# 🚀 快速开始指南

## 安装依赖

```bash
pip install psutil
```

## 基本使用

### 1. 查看支持的消息类型

```bash
python mcap_parser_cli.py list
```

### 2. 解析车道线数据

```bash
python mcap_parser_cli.py parse data.mcap --types LaneArrayv2 --max 20
```

### 3. 解析感知数据

```bash
python mcap_parser_cli.py parse data.mcap --types perception --max 50
```

### 4. 分析MCAP文件

```bash
python mcap_parser_cli.py analyze data.mcap
```

## 编程接口

```python
from mcap_parser import Parser<PERSON>anager
from pathlib import Path

# 创建解析器管理器
with ParserManager(max_workers=4) as parser:
    results = list(parser.parse_file(
        mcap_file=Path("data.mcap"),
        message_types=["LaneArrayv2"],
        max_messages=100
    ))
    
    # 获取统计信息
    stats = parser.get_statistics()
    print(f"解析成功率: {stats['success_rate']:.1%}")
```

## 高级功能

### 多线程异步处理

```bash
python mcap_parser_cli.py parse data.mcap --types all --max 100 --workers 8 --async-mode
```

### 自定义解析器

```python
from mcap_parser.parsers.base import BaseParser

class CustomParser(BaseParser):
    def __init__(self):
        super().__init__("Custom", ["CustomMessageType"])
    
    def parse_message(self, ros_msg, timestamp, topic, message_type):
        print(f"解析自定义消息: {timestamp}")
        return True
```

## 性能优化

- 使用多线程: `--workers 8`
- 启用异步: `--async-mode`
- 批量处理: `--batch-size 200`
- 内存限制: 在代码中配置 `max_memory_usage_mb`
