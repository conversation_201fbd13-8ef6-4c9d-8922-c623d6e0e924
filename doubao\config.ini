[DeepSeek]
api_key = ***********************************
api_url = https://api.deepseek.com/v1/chat/completions

[Doubao]
api_key = b99ee2d7-cfd8-42a7-84bf-59d198954e64
model_id = doubao-seed-1-6-250615
api_url = https://ark.cn-beijing.volces.com/api/v3

[Jira]
url = https://jira.mach-drive-inc.com
user = t-majingmiao
password = mach.1234
jql = labels = MR_3693 and issueFunction in hasComments()  

[Feishu]
app_id = cli_a8d028a01ed8900b
app_secret = fA8FpGvqdoZvxBe9tTqiDbp6KkRGWmEF
folder_token = Kf6xfRFZfld96AdEOYrcgJscnEb
user_access_token = u-fUiS7ZZUV2xUCVbNdhnB.jk01rXlgkyPUG00g4Mw0f1l

[Analysis]
system_prompt = 
    一、分析对象
    基于 车道线相关测试问题集合（需提供完整问题列表，包含问题描述、分类判定依据、唯一可访问链接(链接的文本改为jira号,该链接必须可以点击访问)、"是否回归通过" 标记等核心属性）。
    
    二、分类规则
    映射原则：严格匹配指定分类，无歧义归类：
    接管类问题 → 统一归入 功能问题接管；
    闪烁类问题 → 归入对应 漏检 子项（如漏检道路线、漏检路沿等）；
    同一问题仅归属 1 个分类，禁止重复统计。
    
    分类项（按需提取，仅列存在的分类）：
    位置错误、曲率错误、功能问题接管、多线重合、实例断连、实例多连、实例弯折、实例乱线、漏检道路线、漏检路沿、漏检停止线、漏检 driveline(可驶入路口，括号里的是名词解释，列表时删除)、漏检地面箭头、漏检禁停区、漏检斑马线、检出距离不足、漏检锥桶线、类型整体错误、类型部分错误、类型跳变错误
    
    三、输出要求
    (10)问题分类统计表
    问题分类    问题数量（格式：总数量（回归通过数量）,检查数量是否正确，不要出现错误）(如果未提及为回归问题则不需要输出回归通过数量)    占比（= 分类数量 ÷ 总问题数）    完整问题链接（每条问题对应唯一有效链接，用逗号分隔）
        
    四、约束条件
    统计必须基于 真实问题集合，确保分类映射准确、数量无遗漏；
    回归通过问题需在 "数量" 字段显式标注（如 8(3) 代表 8 个问题，其中 3 个已回归通过）如果未提及为回归问题则不需要输出回归通过数量，；
    仅输出 事实性分析结论(分类分布、Top 特征)，无需优化建议，聚焦数据与逻辑呈现。