import os

import boto3

class OssUtils:
    def __init__(self,ak, sk, endpoint, bucket):
        self.client = boto3.client("s3", endpoint_url=endpoint, aws_access_key_id=ak, aws_secret_access_key=sk)
        self.bucket = bucket

    def ls(self, prefix, marker=''):
        while True:
            resp = self.client.list_objects(Prefix=prefix, Bucket=self.bucket, MaxKeys=100, Marker=marker)
            for item in resp.get("Contents", []):
                yield item
            if resp.get("IsTruncated"):
                marker = resp.get("NextMarker")
            else:
                break


    def download(self, source, dest):
        return self.client.download_file(self.bucket, source, dest)

    def upload(self, source, dest):
        self.client.upload_file(source, self.bucket, dest)

    def clear(self, path):
        if not os.path.exists(path):
            return
        for i in os.listdir(path):
            file_data = os.path.join(path, i)
            if os.path.isfile(file_data):
                os.remove(file_data)
            else:
                self.clear(file_data)
        os.rmdir(path)

if __name__ == '__main__':
    ak = "a5f1703d2bc1c0fd68bc05c74424ad2a"
    sk = "1d24d63418fc3621889fd9da4abd247d"
    endpoint = "http://oss.i.machdrive.cn"
    endpoint = "http://oss-internal.i.machdrive.cn"
    step_oss = OssUtils(ak, sk, endpoint, "libingsi-qy")

    ak = "********************************"
    sk = "e90510d83ff60947244654a6c1b2bae0"
    endpoint = "http://oss.i.brainpp.cn"
    endpoint = "http://oss-internal.i.brainpp.cn"
    brain_oss = OssUtils(ak, sk, endpoint,"libingsi")
    for item in brain_oss.ls(prefix="e2emodel-data/mcap_on_car"):
        print(f"{item.get('Key')} start download!")
        dir_name = os.path.join(*item.get('Key').split('/')[:-1])
        os.makedirs(dir_name, exist_ok=True)
        brain_oss.download(item.get("Key"), item.get("Key"))
        print(f"{item.get('Key')} start upload!")
        step_oss.upload(item.get("Key"), item.get("Key"))
        print(f"{item.get('Key')} finish!!!!")
        os.remove(item.get("Key"))

