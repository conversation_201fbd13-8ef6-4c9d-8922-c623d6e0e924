
# API 客户端类

本目录包含所有 MegSim API 的客户端类，提供了简单易用的 Python 接口。

## 📋 客户端列表

| 客户端类 | 文件名 | 功能描述 | API端点 |
|---------|--------|----------|----------|
| `MegSimApiClient` | `megsim_api_client.py` | 多搜索API客户端 | `/multisearch` |
| `MegSimBatchJiraClient` | `batch_jira_client.py` | 批量Jira任务客户端 | `/batch/jira` |
| `MegSimTasksClient` | `tasks_api_client.py` | 任务管理客户端 | `/api/tasks/` |
| `MegSimTaskPlansClient` | `task_plans_api_client.py` | 任务计划客户端 | `/api/task_plans/` |

## 🚀 使用示例

### 多搜索API
```python
from megsim_api_client import MegSimApiClient

client = MegSimApiClient()
result = client.search(vehicle_id=473, limit=10)
print(f"找到 {len(result['data'])} 条记录")
```

### 任务管理API
```python
from tasks_api_client import MegSimTasksClient

client = MegSimTasksClient()
result = client.create_task(
    name="测试任务",
    project=4,
    car_type="Z10"
)
print(f"任务创建成功，ID: {result['data']['id']}")
```

### 任务计划API
```python
from task_plans_api_client import MegSimTaskPlansClient

client = MegSimTaskPlansClient()
result = client.create_task_plan_with_unique_name(
    base_name="测试计划",
    project=4,
    task_ids=[424160]
)
print(f"任务计划创建成功")
```

## ✨ 特性

- **统一接口**: 所有客户端都提供一致的API接口
- **错误处理**: 完善的异常处理和错误信息
- **参数验证**: 自动验证请求参数的有效性
- **cURL生成**: 可以生成等效的cURL命令
- **类型提示**: 完整的Python类型提示支持

## 📝 注意事项

1. 所有客户端都使用相同的基础URL: `https://megsim.mc.machdrive.cn`
2. 大部分API无需认证，但请注意访问频率限制
3. 建议在生产环境中添加适当的重试机制
4. 某些API可能有数据重复检查，注意处理相应的错误响应
