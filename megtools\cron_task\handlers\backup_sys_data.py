from basic.utils import SqlUtil


def apply(task_config):
    sql = """
    insert into cron_task_info_bak (task_name, cron_exp, next_exec_time, is_run, task_config, remark, create_by, create_time, update_by, update_time, is_delete, speed_time)
    select  task_name, cron_exp, next_exec_time, is_run, task_config, '', create_by, now(), update_by, now(), is_delete, speed_time from cron_task_info;
    """
    SqlUtil.exec_update(sql)
    sql = """
    delete from cron_task_info_bak where create_time <  DATE_SUB(CURDATE(), INTERVAL 15 DAY);
    """
    SqlUtil.exec_update(sql)

    sql = """ insert into basic_key_value_bak (name, value, remark, create_by, create_time, update_by, update_time, is_delete)
    select name, value, remark, create_by, now(), update_by, now(), is_delete from basic_key_value; """
    SqlUtil.exec_update(sql)
    sql = """delete from basic_key_value_bak where create_time <  DATE_SUB(CURDATE(), INTERVAL 15 DAY);"""
    SqlUtil.exec_update(sql)

