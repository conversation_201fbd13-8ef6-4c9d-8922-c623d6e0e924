<#
.SYNOPSIS
    飞书报告触发PowerShell脚本

.DESCRIPTION
    用于通过PowerShell触发MegTool Backend的飞书报告生成
    支持单个报告触发、批量触发、配置文件管理等功能

.PARAMETER ReportId
    报告ID (必填，除非使用配置文件)

.PARAMETER Vin
    车辆识别码

.PARAMETER StartTime
    开始时间 (格式: yyyy-MM-dd HH:mm:ss)

.PARAMETER EndTime
    结束时间 (格式: yyyy-MM-dd HH:mm:ss)

.PARAMETER Params
    其他自定义参数

.PARAMETER Status
    触发状态 (默认: 4-手动触发)

.PARAMETER BaseUrl
    MegTool Backend服务地址 (默认: http://localhost:8000)

.PARAMETER Username
    用户名 (默认: admin)

.PARAMETER Password
    密码 (默认: password)

.PARAMETER ConfigFile
    配置文件路径 (JSON格式)

.PARAMETER Batch
    批量模式，触发配置文件中的所有报告

.PARAMETER CreateConfig
    创建示例配置文件

.PARAMETER JsonOutput
    以JSON格式输出结果

.PARAMETER Quiet
    静默模式，只输出错误信息

.EXAMPLE
    .\trigger_report.ps1 -ReportId 1 -Vin "TEST123"
    触发ID为1的报告，指定车辆识别码

.EXAMPLE
    .\trigger_report.ps1 -ReportId 1 -Vin "TEST123" -StartTime "2024-01-01 00:00:00" -EndTime "2024-01-01 23:59:59"
    触发报告并指定时间范围

.EXAMPLE
    .\trigger_report.ps1 -ConfigFile "config.json" -Batch
    使用配置文件批量触发报告

.EXAMPLE
    .\trigger_report.ps1 -CreateConfig
    创建示例配置文件
#>

param(
    [int]$ReportId,
    [string]$Vin = "",
    [string]$StartTime = "",
    [string]$EndTime = "",
    [string]$Params = "",
    [string]$Status = "4",
    [string]$BaseUrl = "http://localhost:8000",
    [string]$Username = "admin",
    [string]$Password = "password",
    [string]$ConfigFile,
    [switch]$Batch,
    [switch]$CreateConfig,
    [switch]$JsonOutput,
    [switch]$Quiet
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    if ($Quiet -and $Level -ne "ERROR") {
        return
    }
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "INFO"  { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage }
    }
    
    # 写入日志文件
    Add-Content -Path "trigger_report.log" -Value $logMessage -Encoding UTF8
}

# 创建示例配置文件
function New-SampleConfig {
    $config = @{
        base_url = "http://localhost:8000"
        username = "admin"
        password = "password"
        reports = @(
            @{
                report_id = 1
                vin = "TEST123"
                start_time = "2024-01-01 00:00:00"
                end_time = "2024-01-01 23:59:59"
                status = "4"
                custom_params = ""
            }
        )
    }
    
    $configJson = $config | ConvertTo-Json -Depth 3
    $configJson | Out-File -FilePath "report_config_sample.json" -Encoding UTF8
    
    Write-Log "示例配置文件已创建: report_config_sample.json"
}

# 登录到MegTool Backend
function Invoke-Login {
    param(
        [string]$BaseUrl,
        [string]$Username,
        [string]$Password
    )
    
    $loginUrl = "$BaseUrl/api/basic/loginApi"
    $loginData = @{
        username = $Username
        password = $Password
    }
    
    try {
        $response = Invoke-RestMethod -Uri $loginUrl -Method Post -Body ($loginData | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 30
        
        if ($response.status -eq 200 -and $response.data.token) {
            Write-Log "登录成功，用户: $Username"
            return $response.data.token
        } else {
            Write-Log "登录失败: $($response.message)" "ERROR"
            return $null
        }
    }
    catch {
        Write-Log "登录请求失败: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# 触发报告生成
function Invoke-TriggerReport {
    param(
        [string]$BaseUrl,
        [string]$Token,
        [int]$ReportId,
        [string]$Vin = "",
        [string]$StartTime = "",
        [string]$EndTime = "",
        [string]$CustomParams = "",
        [string]$Status = "4"
    )
    
    $triggerUrl = "$BaseUrl/api/report/generateFeishuReportApi"
    
    # 构建参数字典
    $paramsDict = @{}
    if ($Vin) { $paramsDict.vin = $Vin }
    if ($StartTime) { $paramsDict.start_time = $StartTime }
    if ($EndTime) { $paramsDict.end_time = $EndTime }
    if ($CustomParams) { $paramsDict.custom = $CustomParams }
    
    # 转换为JSON字符串
    $paramsJson = if ($paramsDict.Count -gt 0) { $paramsDict | ConvertTo-Json -Compress } else { "" }
    
    $triggerData = @{
        id = $ReportId
        params = $paramsJson
        status = $Status
    }
    
    $headers = @{
        "Authorization" = "Bearer $Token"
        "Content-Type" = "application/json"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $triggerUrl -Method Post -Body ($triggerData | ConvertTo-Json) -Headers $headers -TimeoutSec 60
        
        Write-Log "报告触发完成，报告ID: $ReportId, 状态: $($response.status)"
        return @{ success = $true; data = $response }
    }
    catch {
        Write-Log "报告触发请求失败: $($_.Exception.Message)" "ERROR"
        return @{ success = $false; error = $_.Exception.Message }
    }
}

# 主函数
function Main {
    Write-Log "============================================================"
    Write-Log "飞书报告触发PowerShell脚本"
    Write-Log "============================================================"
    
    # 创建示例配置文件
    if ($CreateConfig) {
        New-SampleConfig
        return
    }
    
    # 使用配置文件
    if ($ConfigFile) {
        if (-not (Test-Path $ConfigFile)) {
            Write-Log "配置文件不存在: $ConfigFile" "ERROR"
            exit 1
        }
        
        try {
            $config = Get-Content $ConfigFile -Raw -Encoding UTF8 | ConvertFrom-Json
        }
        catch {
            Write-Log "配置文件格式错误: $($_.Exception.Message)" "ERROR"
            exit 1
        }
        
        # 使用配置文件中的设置
        $BaseUrl = if ($config.base_url) { $config.base_url } else { $BaseUrl }
        $Username = if ($config.username) { $config.username } else { $Username }
        $Password = if ($config.password) { $config.password } else { $Password }
        
        # 登录
        $token = Invoke-Login -BaseUrl $BaseUrl -Username $Username -Password $Password
        if (-not $token) {
            Write-Log "登录失败，退出程序" "ERROR"
            exit 1
        }
        
        # 批量模式
        if ($Batch) {
            if (-not $config.reports -or $config.reports.Count -eq 0) {
                Write-Log "配置文件中没有找到报告配置" "ERROR"
                exit 1
            }
            
            $results = @()
            foreach ($reportConfig in $config.reports) {
                $result = Invoke-TriggerReport -BaseUrl $BaseUrl -Token $token -ReportId $reportConfig.report_id -Vin $reportConfig.vin -StartTime $reportConfig.start_time -EndTime $reportConfig.end_time -CustomParams $reportConfig.custom_params -Status $reportConfig.status
                
                $results += @{
                    report_id = $reportConfig.report_id
                    result = $result
                }
            }
            
            if ($JsonOutput) {
                $results | ConvertTo-Json -Depth 3
            } else {
                $successCount = ($results | Where-Object { $_.result.success }).Count
                Write-Log "批量触发完成，成功: $successCount/$($results.Count)"
            }
        }
        else {
            # 单个报告模式
            if (-not $config.reports -or $config.reports.Count -eq 0) {
                Write-Log "配置文件中没有找到报告配置" "ERROR"
                exit 1
            }
            
            $reportConfig = $config.reports[0]
            $result = Invoke-TriggerReport -BaseUrl $BaseUrl -Token $token -ReportId $reportConfig.report_id -Vin $reportConfig.vin -StartTime $reportConfig.start_time -EndTime $reportConfig.end_time -CustomParams $reportConfig.custom_params -Status $reportConfig.status
            
            if ($JsonOutput) {
                $result | ConvertTo-Json -Depth 3
            } elseif (-not $result.success) {
                exit 1
            }
        }
    }
    else {
        # 命令行参数模式
        if (-not $ReportId) {
            Write-Log "必须指定报告ID (-ReportId) 或使用配置文件 (-ConfigFile)" "ERROR"
            exit 1
        }
        
        # 登录
        $token = Invoke-Login -BaseUrl $BaseUrl -Username $Username -Password $Password
        if (-not $token) {
            Write-Log "登录失败，退出程序" "ERROR"
            exit 1
        }
        
        # 触发报告
        $result = Invoke-TriggerReport -BaseUrl $BaseUrl -Token $token -ReportId $ReportId -Vin $Vin -StartTime $StartTime -EndTime $EndTime -CustomParams $Params -Status $Status
        
        if ($JsonOutput) {
            $result | ConvertTo-Json -Depth 3
        } elseif (-not $result.success) {
            exit 1
        }
    }
    
    Write-Log "============================================================"
    Write-Log "操作完成"
    Write-Log "============================================================"
}

# 执行主函数
Main