import datetime
import re

from django.utils.http import urlencode
from urllib.parse import urlparse, parse_qs

from basic.services import SendMessageApi
from basic.third_apis.feishu_api import FeishuApi
from basic.utils import Jira<PERSON>perate, DateUtil


def apply(task_config):
    Jira2BiTable(task_config).apply()

class Jira2BiTable:
    def __init__(self, task_config):
        self.task_config = task_config
        self.jql = task_config.get("jql", "")
        self.feishu_url = task_config.get("feishu_url", "")
        self.trend_url = task_config.get("trend_url", "")
        self.plan_date_url = task_config.get("plandate_url", "")
        self.model_names = self.task_config.get("model_name", [])

        self.trans_config = task_config.get("trans_config", {})
        self.bitable_fields = [item['bitable_key'] for item in self.trans_config]
        # 对其去重
        self.jira_fields = []
        self.__init_jira_fields()
        self.jira_value_path = [item['jira_value_path'] for item in self.trans_config]
        self.feishuApi = FeishuApi()

    def __init_jira_fields(self):
        for item in self.trans_config:
            field = item['jira_field']
            if isinstance(field, list):
                self.jira_fields.extend(field)
            else:
                self.jira_fields.append(field)
        self.jira_fields = list(dict.fromkeys(self.jira_fields))

    def apply(self):
        self.check()
        feishu_data = self.query_feishu(self.feishu_url)
        feishu_data = self.reform_feishu_data(feishu_data)
        jira_data = self.query_jira()
        result = self.compare_and_save(feishu_data, jira_data)
        self.handle_issue_trend(jira_data)
        self.handle_plan_cnt(jira_data)
        self.send_message(result)

    def reform_feishu_data(self, feishu_data):
        result = {}
        for item in feishu_data:
            result[item.get("fields", {}).get("JiraKey", {}).get("text", "")] = item
        return result

    def handle_plan_cnt(self, jira_data):
        """
        按照天维度，计算各个模块的进度个数
        """
        if len(jira_data) == 0 or len(self.plan_date_url) == 0:
            return

        result = {}
        for jira_info in list(jira_data.values()):
            if not jira_info.get("预计完成时间"):
                continue
            plan_date = DateUtil.timestamp2Day(jira_info.get("预计完成时间") / 1000) * 1000
            auth_group = jira_info.get("算法聚类分组", None)
            status = jira_info.get("状态")
            if auth_group is None or len(auth_group) == 0 or f"{auth_group[0]}," not in "OD,MDriver,车道线,环境模型,红绿灯,":
                self.handle_plan_cnt_value(plan_date, result, "未分类", jira_info)
                continue
            if not (f"{status}," in "分析中,开发中,待测试,测试中,打回,Closed,"):
                self.handle_plan_cnt_value(plan_date, result, "未分类", jira_info)
                continue
            field_name = f"{auth_group[0]}-{status}"
            self.handle_plan_cnt_value(plan_date, result, field_name, jira_info)

        # 格式化数据
        for item in result:
            for field_name in result[item].keys():
                field_value = result[item][field_name]
                field_value["text"] = str(field_value["text"])
                query_condition = {
                    "jql": f"key in ({','.join(field_value["link"] )})"
                }
                field_value["link"] = f"https://jira.mach-drive-inc.com/issues/?{urlencode(query_condition)}"
        bitable_arr = [{"fields":{"日期":item, **result[item]}} for item in result]
        # 3.向飞书里面写！
        self.write_bi_table(bitable_arr, self.plan_date_url)

    def handle_plan_cnt_value(self, plan_date, result, field_name, jira_info):
        if plan_date not in result:
            result[plan_date] = {
                field_name: {"text": 1, "link": [jira_info["JiraKey"]["text"]]}
            }
        else:
            cnt = result[plan_date].get(field_name, {}).get("text", 0) + 1
            link_arr = result[plan_date].get(field_name, {}).get("link", [])
            link_arr.append(jira_info["JiraKey"]["text"])
            result[plan_date][field_name] = {"text": cnt, "link": link_arr}

    def handle_issue_trend(self, jira_data):
        """
        计算每天的issue的数据量， 按照create_date 进行处理, create 如果没有补齐， 那么还需要对日期进行补齐
        """
        if len(jira_data) == 0 or len(self.trend_url) == 0:
            return
        # 1. 计算数据
        result_dict, min_date = self.cal_trend_data(jira_data)
        # 2.基本的计数已经处理完成， 此时需要按照天进行累加处理
        bitable_arr = self.generate_trend_data(result_dict, min_date)
        # 3.向飞书里面写！
        self.write_bi_table(bitable_arr, self.trend_url)

    def cal_trend_data(self, jira_data):
        min_date = list(jira_data.values())[0].get("创建日期")/1000
        result_dict = {}
        for value in list(jira_data.values()):
            create_time = DateUtil.timestamp2Day(value.get("创建日期") / 1000)
            create_temp_entity = result_dict.get(create_time, {})
            if create_time not in result_dict:
                result_dict[create_time] = create_temp_entity
            update_time = DateUtil.timestamp2Day(value.get("已更新") / 1000)
            update_temp_entity = result_dict.get(update_time, {})
            if update_time not in result_dict:
                result_dict[update_time] = update_temp_entity
            # 记录每天的issue 数量
            create_temp_entity['-总数'] = create_temp_entity.get("-总数", 0) + 1
            model_name = value.get("模块")
            for item in model_name:
                if item in self.model_names:
                    # 如果模块在配置的里面，那么需要将模块的计数加进去
                    create_temp_entity[f"{item}-总数"] = create_temp_entity.get(f"{item}-总数", 0) + 1
            # 获取当前问题是否已经解决，按照解决和未解决进行分类处理
            if value.get("状态") == 'Closed':
                update_temp_entity['-解决'] = update_temp_entity.get("-解决", 0) + 1
                for item in model_name:
                    if item in self.model_names:
                        # 如果模块在配置的里面，那么需要将模块的计数加进去
                        update_temp_entity[f"{item}-解决"] = update_temp_entity.get(f"{item}-解决", 0) + 1
            if create_time < min_date:
                min_date = create_time
        return result_dict, min_date

    def generate_trend_data(self, result_dict, min_date):
        current_timestamp = datetime.datetime.now().timestamp()
        bitable_arr = []
        data_cache = {}
        while True:
            if min_date > current_timestamp:
                break
            day_info = result_dict.get(min_date, {})
            bi_entity = {"日期": min_date * 1000}
            for item in self.model_names:
                all = day_info.get(f"{item}-总数", 0)
                resolve = day_info.get(f"{item}-解决", 0)
                data_cache[f'{item}-总数'] = data_cache.get(f'{item}-总数', 0) + all
                data_cache[f'{item}-解决'] = data_cache.get(f'{item}-解决', 0) + resolve
                bi_entity[f'{item}-总数'] = data_cache[f'{item}-总数']
                bi_entity[f'{item}-解决'] = data_cache[f'{item}-解决']

            bitable_arr.append(
                {"fields": bi_entity})
            min_date += 86400
        return bitable_arr

    def write_bi_table(self, bitable_data, feishu_url):
        # 1. 清空飞书里面的数据，
        self.bitable_fields = ['日期']
        result = self.query_feishu(feishu_url)
        del_ids = [item['record_id'] for item in result]
        self.feishuApi.bitable_batch_delete_all(self.bitable_token, self.table_id, del_ids)
        # 2. 写入飞书
        self.feishuApi.bitable_batch_insert_all(self.bitable_token, self.table_id, bitable_data)

    def send_message(self,result):
        open_ids = self.task_config.get("alarm", "")
        if not open_ids or len(result) == 0:
            return
        for item in open_ids.split(";"):
            SendMessageApi(f""" **{self.task_config.get("task_name", "")}**\n {"\n".join(result)} """).send_message(item)

    def compare_and_save(self, feishu_data, jira_data):
        result = []
        # 对比飞书的数据，和jira 的数据，应该删除的加删除标记， 如果需要添加的插入，如果修改的进行修改
        # 1. feishu_data 里面有，jira_data 里面没有
        feishu_need_del = set(feishu_data.keys()) - set(jira_data.keys())
        self.handle_feishu_del(feishu_need_del, feishu_data, result)
        # 2. feishu_data 里面没有， jira_data 里面有，直接插入数据
        feishu_need_insert = set(jira_data.keys()) - set(feishu_data.keys())
        self.handle_feishu_append(feishu_need_insert, jira_data, result)
        # 3. 两个里面如果都一样，那么需要对比进行更新
        feishu_need_compare = set(jira_data.keys()) & set(feishu_data.keys())
        self.handle_compare_update(feishu_need_compare, feishu_data, jira_data, result)
        return result


    def handle_compare_update(self, feishu_need_compare, feishu_data, jira_data, result):
        if len(feishu_need_compare) == 0:
            return
        update_keys = []
        update_entity = []
        for item in feishu_need_compare:
            feishu_entity = feishu_data.get(item)
            jira_entity = jira_data.get(item)
            # 获取到每个元素进行对比
            change_fields = []
            for field_name in self.bitable_fields:
                feishu_text = self.get_feishu_text(field_name, feishu_entity.get("fields"))
                jira_text = self.get_feishu_text(field_name, jira_entity)
                if feishu_text != jira_text:
                    change_fields.append(f"{feishu_text} -> {jira_text}")
            if len(change_fields) > 0:
                update_keys.append(f"-->>{item}: {','.join(change_fields)}")
                update_entity.append({"record_id": feishu_entity.get("record_id"), "fields": jira_entity})
        if len(update_keys) == 0:
            return
        result.append("修改的DPMS数据：")
        result.extend(update_keys)
        self.feishuApi.bitable_batch_update_all(self.bitable_token, self.table_id, update_entity)

    def get_feishu_text(self, field_name, feishu_entity):
        value = feishu_entity.get(field_name, "")
        if isinstance(value, dict):
            return value.get("text", "")
        elif isinstance(value, list):
            temp = ""
            for item in value:
                if isinstance(item, dict):
                    temp = f"{temp}{item.get('text', '')}"
                else:
                    temp = f"{temp}{item}"
            return temp
        elif value is None:
            value = ""
        else:
            value = f"{value}"
        return value

    def handle_feishu_append(self, need_insert, jira_data, result):
        if need_insert is None or len(need_insert) == 0:
            return
        # 向飞书内插入数据
        rows = []
        for item in list(need_insert):
            rows.append({"fields":jira_data.get(item)})
        self.feishuApi.bitable_batch_insert_all(self.bitable_token, self.table_id, rows, index=4)
        result.append(f"新增DPMS数据：{','.join(need_insert)}")


    def handle_feishu_del(self, feishu_need_del, feishu_data, result):
        if feishu_need_del is None or len(feishu_need_del) == 0:
            return
        del_record_id = []
        for item in list(feishu_need_del):
            del_record_id.append(feishu_data.get(item).get('record_id'))
        self.feishuApi.bitable_batch_delete_all(self.bitable_token, self.table_id, del_record_id)
        result.append(f"删除DPMS数据：{','.join(feishu_need_del)}")

    def query_feishu(self, url):
        parsed_url = urlparse(url)
        get_params = parse_qs(parsed_url.query)
        path = parsed_url.path
        self.wiki_token = path.split("/")[-1]
        self.table_id = get_params["table"][0]
        view_id = get_params["view"][0]

        bi_table_info = self.feishuApi.wiki_get_node(self.wiki_token, 'wiki')
        if bi_table_info.get("code", -1) != 0:
            # 没有找到飞书表格直接返回
            return bi_table_info.get("msg", "")
        # 开始查询bit_table 中的数据
        self.bitable_token = bi_table_info.get("data", {}).get("node", {}).get("obj_token", "")
        body = {"view_id": view_id, "field_names": self.bitable_fields}
        bitable_data = self.feishuApi.bitable_records_all(self.bitable_token, self.table_id, body)
        return bitable_data

    def query_jira(self):
        jira_entities = JiraOperate().query_jira(self.jql, self.jira_fields)
        result = {}
        for item in jira_entities:
            result[item.get("key")] = self.trans_jira2bitable(item)
        return result

    def trans_jira2bitable(self, jira_entity):
        result = {}
        for item in self.trans_config:
            bitable_field = item.get("bitable_key", "")
            jira_path = item.get("jira_value_path", "")
            if jira_path:
                # 如果jira_path 存在，通过jira_path 进行取值
                value = self.get_value_by_path(jira_path, jira_entity, item)
            if "fix_value" in item:
                value = item.get("fix_value")
            result[bitable_field] = value
        return result

    # 通过配置的路径
    def get_value_by_path(self, jira_path, jira_entity, field_config):
        path_arr = jira_path.split(".")
        result = jira_entity
        for item in path_arr:
            if result is None or result == "":
                result = ""
                break
            if item == "[*]" and isinstance(result, list):
                temp = []
                for item in result:
                    temp.append(self.get_value_by_path(jira_path[jira_path.index("[*]")+4:], item, {}))
                result = temp
                break
            elif item.startswith("["):
                # 如果当前条目是数组下标则按照下标进行取值, 下标越界啥的都娶不到。
                if isinstance(result, list) and int(item[1:-1]) < len(result):
                    result = result[int(item[1:-1])]
                else:
                    result = ""
            else:
                result = result.get(item, "")
        return self.reform_bitable_type(jira_entity, result, field_config)

    def reform_bitable_type(self, jira_entity, value, field_config):
        bitable_type = field_config.get("bitable_type", "文本")
        if bitable_type == "超链接":
            if value and value.startswith("[") and value.endswith("]") and len(value.split("|")) == 2:
                jira_link = value.split("|")
                return {"text": jira_link[0][1:], "link": jira_link[1][:-1]}
            elif value:
                link_prefix = field_config.get("link_prefix", "")
                return {"text": value, "link": f"{link_prefix}{value}"}
            else:
                return {"text": " ", "link": ""}
        elif bitable_type == "多选":
            if isinstance(value, list):
                return value
            elif value is None:
                return []
            else:
                return [value]
        elif bitable_type == "数字":
            if isinstance(value, int) or isinstance(value, float):
                return value
            else:
                return float(value)
        elif bitable_type == "过滤单选":
            match_arr = self.get_trans_config(jira_entity, field_config, "match_arr")
            if isinstance(value, list):
                for item in value:
                    if item in match_arr:
                        return item
            elif value:
                if value in match_arr:
                    return value
            return ""
        elif bitable_type == "过滤多选":
            match_arr = self.get_trans_config(jira_entity, field_config, "match_arr")
            if isinstance(value, list):
                result = []
                for item in value:
                    if item in match_arr:
                        result.append(item)
                return result
            elif value:
                if value in match_arr:
                    return [value]
            return []
        elif bitable_type == "过滤MR":
            result = ""
            if isinstance(value, list):
                for item in value:
                    if item.startswith("MR_"):
                        result = item
                        break
            if result:
                link_prefix = field_config.get("link_prefix", "")
                return {"text": result, "link": f"{link_prefix}{result[3:]}"}
            return {"text": " ", "link": f""}
        elif bitable_type == "转换多选":
            trans_config = self.get_trans_config(jira_entity, field_config, "trans_key")
            if isinstance(value, list):
                result = []
                for item in value:
                    if item in trans_config:
                        result.extend(trans_config.get(item))
                return list(set(result))
            elif value:
                if value in trans_config:
                    return trans_config.get(value)
            return []
        elif bitable_type == "日期":
            format_type = field_config.get("format_type", "%Y-%m-%d")
            if value is None:
                return None
            date_obj = datetime.datetime.strptime(value, format_type)
            value = int(date_obj.timestamp() * 1000)
            return value
        elif bitable_type == "提取文本":
            reg_str = field_config.get("reg_str", "")
            if not reg_str:
                return value
            extra_result = self.extra_time(value, reg_str)
            return self.format_extra(extra_result, field_config)
        else:
            return value

    def format_extra(self, value:str, field_config):
        if value is None or "operate" not in field_config:
            return value
        for item in field_config["operate"]:
            if item.get("type", "") == "dateformat":
                format_type = item.get("format_type", "%Y-%m-%d")
                if value is None:
                    return None
                date_obj = datetime.datetime.strptime(value, format_type)
                value = int(date_obj.timestamp() * 1000)
            elif item.get("type", "") == "replace":
                old_value = item.get("old", "")
                new_value = item.get("new", "")
                value = value.replace(old_value, new_value)
            elif item.get("type", "") == "to_int":
                value = int(value)
        return value

    def extra_time(self, result, pattern):
        extra = None
        match = re.search(pattern, result)
        if match:
            extra = match.group()
        return extra

    def get_trans_config(self, jira_entity, field_config, trans_key=""):
        if trans_key in field_config:
            return field_config[trans_key]
        # 如果没有匹配到，那么检查匹配规则
        if "trans_config" not in field_config:
            return []

        result = []
        for item in field_config["trans_config"]:
            condition = item.get("condition")
            jira_value = self.get_value_by_path(item.get("jira_value_path"), jira_entity, {})
            match_result = False
            if isinstance(jira_value, list):
                for temp in jira_value:
                    match_result = self.compare_condition(temp, condition)
                    if match_result:
                        break
            else:
                match_result = self.compare_condition(jira_value, condition)
            if match_result:
                result = self.task_config.get(item.get("match_key"))
        return result

    def compare_condition(self, jira_value, condition):
        if isinstance(condition, list):
            for cond in condition:
                if jira_value == cond:
                    # 如果两个当中匹配上了一个， 那么返回为真，直接取值
                    return True
        else:
            if jira_value == condition:
                return True
        return False

    def check(self):
        if not self.jql:
            raise Exception("请配置jql参数")
        if not self.feishu_url:
            return Exception("请配置feishu_url参数")


