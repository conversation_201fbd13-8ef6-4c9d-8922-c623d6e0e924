import os
import sys
import time
from datetime import datetime
from jira import JIRA, JIRAError

# 忽略无关警告
os.environ['PYTHONWARNINGS'] = 'ignore::UserWarning'

# ===== 自托管 Jira 配置 =====
JIRA_URL = "https://jira.mach-drive-inc.com"
JIRA_USER = "t-majingmiao"
JIRA_PASSWORD = "mach.1234"
# ===========================

# 全局变量用于存储进度数据
progress_data = {
    'timestamps': [],
    'percentages': [],
    'total_counts': [],
    'labeled_counts': []
}

# 设置您的 JQL 查询
JQL_QUERY = (
    'labels = MR_3808'
)

# 目标标签
TARGET_LABEL = "local-ads-no-lidar"

def connect_to_jira():
    """连接 Jira 服务器"""
    try:
        # 使用用户名+密码认证
        return JIRA(
            server=JIRA_URL,
            basic_auth=(JIRA_USER, JIRA_PASSWORD),
            options={'verify': True},
            timeout=10
        )
    except Exception as e:
        print(f"[!] 连接错误: {str(e)}")
        return None

def get_issue_counts(jira):
    """使用两次查询获取问题计数"""
    try:
        # 查询总数
        total_result = jira.search_issues(JQL_QUERY, maxResults=0)
        total_count = total_result.total
        
        # 查询包含目标标签的数量 - 使用 "is not EMPTY" 语法
        labeled_jql = f'{JQL_QUERY} AND {TARGET_LABEL} is not EMPTY'
        labeled_result = jira.search_issues(labeled_jql, maxResults=0)
        labeled_count = labeled_result.total
        
        return total_count, labeled_count
    except JIRAError as e:
        print(f"[!] 查询失败 (HTTP {e.status_code}): {e.text}")
        return 0, 0
    except Exception as e:
        print(f"[!] 查询错误: {str(e)}")
        return 0, 0

def update_progress():
    """更新进度数据"""
    jira = connect_to_jira()
    if not jira:
        return None, None, None
    
    try:
        # 获取计数
        total_count, labeled_count = get_issue_counts(jira)
        
        # 计算进度百分比
        progress_percent = (labeled_count / total_count * 100) if total_count > 0 else 0
        
        # 记录当前时间
        current_time = datetime.now()
        
        # 记录数据
        progress_data['timestamps'].append(current_time)
        progress_data['percentages'].append(progress_percent)
        progress_data['total_counts'].append(total_count)
        progress_data['labeled_counts'].append(labeled_count)
        
        return progress_percent, total_count, labeled_count
        
    except Exception as e:
        print(f"[!] 更新进度错误: {str(e)}")
        return None, None, None

def console_monitor():
    """控制台实时监控（终端进度条）"""
    print(f"启动实时监控 @ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"JQL 查询: {JQL_QUERY}")
    print(f"目标字段: '{TARGET_LABEL}'")
    print("每秒更新一次进度...\n")
    
    # 进度条长度
    bar_length = 50
    
    # 初始显示表头
    print("时间      | 进度 (%) | 已标记/总数 | 进度条")
    print("-----------------------------------------------")
    
    try:
        # 初始占位行
        sys.stdout.write("\r" + " " * 100 + "\r")
        sys.stdout.flush()
        
        while True:
            start_time = time.time()
            
            # 更新进度数据
            progress_percent, total_count, labeled_count = update_progress()
            
            if progress_percent is None:
                time.sleep(1)
                continue
            
            # 获取当前时间
            current_time = datetime.now().strftime('%H:%M:%S')
            
            # 创建进度条
            filled_length = int(bar_length * progress_percent // 100)
            bar = '■' * filled_length + ' ' * (bar_length - filled_length)
            
            # 使用回车符覆盖当前行
            line = f"{current_time} | {progress_percent:7.2f}% | {labeled_count:5d}/{total_count:5d} | [{bar}]"
            sys.stdout.write("\r" + line)
            sys.stdout.flush()
            
            # 确保每秒更新一次
            elapsed = time.time() - start_time
            if elapsed < 1:
                time.sleep(1 - elapsed)
                
    except KeyboardInterrupt:
        # 结束时换行
        sys.stdout.write("\n\n")
        sys.stdout.flush()
        print("\n监控已停止")
        if progress_data['timestamps']:
            last_percent = progress_data['percentages'][-1]
            last_total = progress_data['total_counts'][-1]
            last_labeled = progress_data['labeled_counts'][-1]
            print(f"最终进度: {last_percent:.2f}% ({last_labeled}/{last_total})")


if __name__ == "__main__":
    try:
        console_monitor()
    except KeyboardInterrupt:
        print("\n监控已停止")