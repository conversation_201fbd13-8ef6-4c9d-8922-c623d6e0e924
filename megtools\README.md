# MegTool Backend

## 项目概述

MegTool Backend 是一个基于 Django 的后端服务系统，为车辆数据分析、性能监控、任务管理和报告生成提供完整的 REST API 解决方案。

## 主要功能

- 🔐 **用户认证与权限管理** - 完整的用户登录、权限控制系统
- 📊 **数据分析与监控** - 车辆性能数据分析、系统监控
- ⏰ **定时任务管理** - 灵活的定时任务调度和管理
- 📋 **报告生成** - 自动化飞书报告生成和管理
- 🗺️ **地图服务** - 车辆轨迹查询和问题定位
- 🔌 **外部接口** - 性能数据解析和第三方集成
- ⚙️ **配置管理** - 系统配置的统一管理

## 技术栈

- **后端框架**: Django 3.2+
- **数据库**: 支持 MySQL/PostgreSQL
- **认证**: JWT Token
- **API**: RESTful API
- **任务调度**: Cron表达式
- **第三方集成**: 飞书、Jira、高德地图

## 快速开始

### 1. 环境要求

- Python 3.8+
- Django 3.2+
- 数据库 (MySQL/PostgreSQL)

### 2. 安装依赖

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 3. 数据库配置

```bash
# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser
```

### 4. 启动服务

```bash
python manage.py runserver
```

### 5. 健康检查

```bash
curl http://localhost:8000/misc/ping
```

## 文档导航

### 📖 [API 文档](./API_DOCUMENTATION.md)
完整的 API 接口文档，包含：
- 所有接口的详细说明
- 请求参数和响应格式
- 错误码说明
- 接口使用示例

### 📚 [使用说明](./USAGE_GUIDE.md)
详细的使用指南，包含：
- 快速开始教程
- 常见使用场景
- 最佳实践
- 故障排除
- 性能优化建议

## API 模块概览

### 基础服务 (`/api/basic/`)
- 用户认证登录
- 配置管理
- 用户信息管理
- 告警分组管理
- Jira 数据查询
- 飞书消息发送

### 数据视图 (`/api/dataview/`)
- 系统性能监控
- CPU/内存分析
- 进程数据查询
- 日志文件管理

### 定时任务 (`/api/cron/`)
- 任务调度管理
- 日报生成
- Jira 问题克隆
- 批量数据处理

### 报告管理 (`/api/report/`)
- 飞书报告配置
- 自动报告生成
- 报告日志查询
- 报告重新生成

### 地图服务 (`/api/amap/`)
- 车辆轨迹查询
- Jira 问题定位
- 地理位置分析

### 外部接口 (`/api/outer/`)
- 性能数据解析
- OSS 文件处理

### 系统监控
- 健康检查 (`/misc/ping`)
- 系统状态 (`/actuator/health`)

## 项目结构

```
megtool_backend/
├── basic/                 # 基础服务模块
├── dataview/             # 数据视图模块
├── cron_task/            # 定时任务模块
├── report/               # 报告管理模块
├── gaode/                # 地图服务模块
├── outer_interface/      # 外部接口模块
├── megtool_backend/      # 项目配置
├── requirements.txt      # 依赖包列表
├── manage.py            # Django 管理脚本
├── API_DOCUMENTATION.md # API 文档
├── USAGE_GUIDE.md       # 使用说明
└── README.md            # 项目说明
```

## 开发指南

### 代码规范
- 遵循 PEP 8 Python 代码规范
- 使用有意义的变量和函数命名
- 添加必要的注释和文档字符串

### 测试
```bash
# 运行测试
python manage.py test

# 运行特定模块测试
python manage.py test basic.tests
```

### 数据库操作
```bash
# 创建迁移文件
python manage.py makemigrations

# 应用迁移
python manage.py migrate

# 查看迁移状态
python manage.py showmigrations
```

## 部署说明

### 生产环境配置
1. 设置环境变量
2. 配置数据库连接
3. 设置静态文件服务
4. 配置日志记录
5. 启用 HTTPS

### Docker 部署
```bash
# 构建镜像
docker build -t megtool-backend .

# 运行容器
docker run -p 8000:8000 megtool-backend
```

## 安全注意事项

- 🔒 使用 HTTPS 传输敏感数据
- 🔑 定期更换 JWT 密钥
- 🛡️ 实施访问频率限制
- 📝 记录和监控 API 访问日志
- 🔐 遵循最小权限原则

## 性能优化

- 📈 使用数据库索引优化查询
- 💾 实施适当的缓存策略
- 🔄 使用连接池管理数据库连接
- 📊 监控 API 响应时间
- 🚀 考虑异步处理大量数据

## 常见问题

### Q: 如何获取 API 访问权限？
A: 联系系统管理员获取用户名和密码，然后通过登录接口获取访问 Token。

### Q: Token 过期怎么办？
A: Token 过期后需要重新调用登录接口获取新的 Token。

### Q: 如何查看 API 调用日志？
A: 系统会记录所有 API 调用日志，可以联系管理员查看或通过日志管理接口查询。

### Q: 支持哪些数据格式？
A: 系统主要支持 JSON 格式的数据交换，部分接口支持文件上传。

## 更新日志

### v1.0.0
- 初始版本发布
- 基础 API 功能实现
- 用户认证和权限管理
- 数据分析和监控功能

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 📧 邮箱: [开发团队邮箱]
- 💬 技术支持: [支持渠道]
- 📋 问题反馈: [Issue 跟踪系统]

---

**注意**: 请确保在生产环境中正确配置安全设置，包括数据库密码、JWT 密钥等敏感信息。