import time
import logging
import json
import os
from typing import Dict, List, Optional, Tuple, Set
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("feishu_sync")

# 飞书认证配置
CONFIG = {
    "APP_ID": "cli_a8d028a01ed8900b",
    "APP_SECRET": "fA8FpGvqdoZvxBe9tTqiDbp6KkRGWmEF",
    "GITLAB_URL": "https://git-core.megvii-inc.com",
    "SOURCE_APP_TOKEN": "MudQbRICta0KjusHgXWcpCCJnyb",
    "SOURCE_TABLE_ID": "tbli7WPGf9S1xCF0",
    "TARGET_APP_TOKEN": "Xyd9b0yiWahjvxsu8PxcysynnZe",
    "TARGET_TABLE_ID": "tblSh72Uc3SZyXKb",
    "SYNC_INTERVAL": 86400,  # 每天同步一次（秒）
    "PAGE_SIZE": 50,        # 每次获取的记录数
    "STATE_FILE": "sync_state.json"  # 同步状态文件
}

# 初始化飞书客户端
client = lark.Client.builder() \
    .app_id(CONFIG["APP_ID"]) \
    .app_secret(CONFIG["APP_SECRET"]) \
    .enable_set_token(True) \
    .log_level(lark.LogLevel.INFO) \
    .build()

# 同步状态类
class SyncState:
    def __init__(self):
        self.last_sync_time = 0  # 上次同步时间戳
        self.synced_records: Dict[str, Tuple[str, str]] = {}  # {源记录ID: (目标记录ID, 版本号)}
    
    def save(self):
        """保存同步状态到文件"""
        state_data = {
            "last_sync_time": self.last_sync_time,
            "synced_records": self.synced_records
        }
        try:
            with open(CONFIG["STATE_FILE"], 'w') as f:
                json.dump(state_data, f)
            logger.info(f"同步状态已保存到 {CONFIG['STATE_FILE']}")
        except Exception as e:
            logger.error(f"保存同步状态失败: {str(e)}")
    
    @classmethod
    def load(cls):
        """从文件加载同步状态"""
        state = SyncState()
        if os.path.exists(CONFIG["STATE_FILE"]):
            try:
                with open(CONFIG["STATE_FILE"], 'r') as f:
                    state_data = json.load(f)
                state.last_sync_time = state_data.get("last_sync_time", 0)
                state.synced_records = {
                    k: tuple(v) for k, v in state_data.get("synced_records", {}).items()
                }
                logger.info(f"从 {CONFIG['STATE_FILE']} 加载同步状态成功")
            except Exception as e:
                logger.error(f"加载同步状态失败: {str(e)}")
        else:
            logger.info("未找到状态文件，将创建新的同步状态")
        return state

def get_source_records(state: SyncState) -> Tuple[Set[str], List[Dict]]:
    """获取源表格记录（增量获取）"""
    existing_ids = set()
    changed_records = []
    page_token = None  # 分页处理
    
    try:
        while True:
            # 构建请求参数 - 获取所有记录用于删除检测
            req = ListAppTableRecordRequest.builder() \
                .app_token(CONFIG["SOURCE_APP_TOKEN"]) \
                .table_id(CONFIG["SOURCE_TABLE_ID"]) \
                .build()
            
            # 发送API请求
            resp = client.bitable.v1.app_table_record.list(req)
            
            # 处理API响应
            if not resp.success():
                logger.error(f"获取源表格记录失败: {resp.code}, {resp.msg}")
                break
                
            if not resp.data or not resp.data.items:
                logger.info("没有获取到源表格记录")
                break
            
            # 处理所有记录
            for record in resp.data.items:
                source_id = record.record_id
                existing_ids.add(source_id)
                
                # 获取最后修改时间
                modified_time = getattr(record, 'last_modified_time', None) or str(int(time.time()))
                
                # 创建记录标识
                record_version = f"{record.record_id}-{modified_time}"
                
                # 检查记录是否需要更新
                if source_id not in state.synced_records or \
                   record_version != state.synced_records[source_id][1]:
                    changed_records.append({
                        "id": source_id,
                        "fields": record.fields or {},
                        "version": record_version,
                        "modified_time": modified_time
                    })
            
            # 检查是否有更多记录
            if not resp.data.has_more:
                break
                
            page_token = resp.data.page_token
        
        logger.info(f"获取到 {len(existing_ids)} 条源记录，其中 {len(changed_records)} 条需要同步")
        return existing_ids, changed_records
        
    except Exception as e:
        logger.exception(f"获取源表格记录异常: {str(e)}")
        return existing_ids, changed_records

def get_target_records() -> Dict[str, str]:
    """获取目标表格所有记录ID（通过source_id映射）"""
    target_records = {}
    page_token = None
    
    try:
        while True:
            # 构建请求参数
            req = ListAppTableRecordRequest.builder() \
                .app_token(CONFIG["TARGET_APP_TOKEN"]) \
                .table_id(CONFIG["TARGET_TABLE_ID"]) \
                .page_size(CONFIG["PAGE_SIZE"]) \
                .page_token(page_token) \
                .build()
            
            # 发送API请求
            resp = client.bitable.v1.app_table_record.list(req)
            
            # 处理API响应
            if not resp.success():
                logger.error(f"获取目标记录失败: {resp.code}, {resp.msg}")
                break
                
            if not resp.data or not resp.data.items:
                logger.info("没有获取到目标记录")
                break
            
            # 提取所有目标记录ID
            for record in resp.data.items:
                # 源记录ID存储在"source_id"字段中
                if record.fields and "source_id" in record.fields:
                    source_id = record.fields["source_id"]
                    target_records[source_id] = record.record_id
            
            # 检查是否有更多记录
            if not resp.data.has_more:
                break
                
            page_token = resp.data.page_token
        
        logger.info(f"获取到 {len(target_records)} 条目标记录")
        return target_records
        
    except Exception as e:
        logger.exception(f"获取目标记录异常: {str(e)}")
        return {}

def create_target_record(record: Dict) -> Optional[str]:
    """在目标表格中创建记录并返回记录ID"""
    try:
        # 复制字段以避免修改原始数据
        fields = record["fields"].copy() if record["fields"] else {}
        
        # 关键修复：添加源记录ID用于关联
        fields["source_id"] = record["id"]
        
        # 通用处理所有字段
        for field_name, value in fields.items():
            # 处理链接字段（值可能是字典或字典列表）
            if isinstance(value, dict) and "record_id" in value:
                fields[field_name] = [value["record_id"]]
            
            elif isinstance(value, list) and value and isinstance(value[0], dict) and "record_id" in value[0]:
                fields[field_name] = [item["record_id"] for item in value]
        
        # 构建请求参数
        req = CreateAppTableRecordRequest.builder() \
            .app_token(CONFIG["TARGET_APP_TOKEN"]) \
            .table_id(CONFIG["TARGET_TABLE_ID"]) \
            .request_body(AppTableRecord.builder()
                .fields(fields)  # 使用处理后的字段
                .build()) \
            .build()
        
        # 发送API请求
        resp = client.bitable.v1.app_table_record.create(req)
        
        # 处理API响应
        if not resp.success():
            logger.error(f"创建目标记录失败: {record['id']}, 错误码: {resp.code}, 错误信息: {resp.msg}")
            return None
            
        logger.info(f"创建目标记录成功: {record['id']} -> {resp.data.record.record_id}")
        return resp.data.record.record_id
        
    except Exception as e:
        logger.error(f"创建目标记录异常: {str(e)}")
        return None

def update_target_record(target_id: str, record: Dict) -> bool:
    """更新目标表格中的记录"""
    try:
        # 复制字段以避免修改原始数据
        fields = record["fields"].copy() if record["fields"] else {}
        
        # 关键修复：确保保留source_id字段
        fields["source_id"] = record["id"]
        
        # 关键修复：添加链接字段处理（与创建函数一致）
        for field_name, value in fields.items():
            if isinstance(value, dict) and "record_id" in value:
                fields[field_name] = [value["record_id"]]
            elif isinstance(value, list) and value and isinstance(value[0], dict) and "record_id" in value[0]:
                fields[field_name] = [item["record_id"] for item in value]
        
        # 构建请求参数
        req = UpdateAppTableRecordRequest.builder() \
            .app_token(CONFIG["TARGET_APP_TOKEN"]) \
            .table_id(CONFIG["TARGET_TABLE_ID"]) \
            .record_id(target_id) \
            .request_body(AppTableRecord.builder()
                .fields(fields)
                .build()) \
            .build()
        
        # 发送API请求
        resp = client.bitable.v1.app_table_record.update(req)
        
        # 处理API响应
        if not resp.success():
            logger.error(f"更新目标记录失败: {record['id']}, 错误码: {resp.code}, 错误信息: {resp.msg}")
            return False
            
        logger.info(f"更新目标记录成功: {record['id']} ({target_id})")
        return True
        
    except Exception as e:
        logger.error(f"更新目标记录异常: {str(e)}")
        return False

def delete_target_record(target_id: str) -> bool:
    """删除目标表格中的记录"""
    try:
        # 构建请求参数
        req = DeleteAppTableRecordRequest.builder() \
            .app_token(CONFIG["TARGET_APP_TOKEN"]) \
            .table_id(CONFIG["TARGET_TABLE_ID"]) \
            .record_id(target_id) \
            .build()
        
        # 发送API请求
        resp = client.bitable.v1.app_table_record.delete(req)
        
        # 处理API响应
        if not resp.success():
            logger.error(f"删除目标记录失败: {target_id}, 错误码: {resp.code}, 错误信息: {resp.msg}")
            return False
            
        logger.info(f"删除目标记录成功: {target_id}")
        return True
        
    except Exception as e:
        logger.error(f"删除目标记录异常: {str(e)}")
        return False

def sync_records():
    """执行同步操作"""
    # 加载同步状态
    state = SyncState.load()
    logger.info(f"上次同步时间: {time.ctime(state.last_sync_time)}")
    
    # 获取源记录
    existing_source_ids, source_records = get_source_records(state)
    logger.info(f"需要同步的记录数: {len(source_records)}")
    
    # 获取目标记录
    target_records = get_target_records()
    
    # 处理创建和更新
    for record in source_records:
        source_id = record["id"]
        
        if source_id in target_records:
            # 更新现有记录
            target_id = target_records[source_id]
            if update_target_record(target_id, record):
                # 更新同步状态
                state.synced_records[source_id] = (target_id, record["version"])
        else:
            # 创建新记录
            target_id = create_target_record(record)
            if target_id:
                # 更新同步状态
                state.synced_records[source_id] = (target_id, record["version"])
    
    # 处理删除（源中不存在但目标中存在的记录）
    deleted_count = 0
    for source_id, target_id in list(target_records.items()):
        # 只删除有source_id关联的记录
        if source_id not in existing_source_ids:
            if delete_target_record(target_id):
                deleted_count += 1
                # 从同步状态中移除
                if source_id in state.synced_records:
                    del state.synced_records[source_id]
    
    # 更新同步时间
    state.last_sync_time = time.time()
    
    # 保存同步状态
    state.save()
    
    logger.info(f"同步完成: 新增/更新 {len(source_records)} 条, 删除 {deleted_count} 条")

# 主函数
if __name__ == "__main__":
    sync_records()