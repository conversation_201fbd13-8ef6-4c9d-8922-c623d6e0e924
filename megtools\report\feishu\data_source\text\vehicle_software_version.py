import datetime
import json

from basic.utils import SqlUtil
from report.feishu.data_source.image.line_chart_image import LineChartImage


class VehicleSoftware(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.handle_conditions(variables, params)

    def apply(self):
        db_version = self.query_data()
        if not db_version:
            return
        result = {}
        record_time = db_version.get("record_time", "")
        db_version = json.loads(db_version.get("version_info", "{}"))
        if self.params == 'model':
            self.organize_model_version(db_version, "default_model_version",result)
            self.organize_model_version(db_version, "current_model_version",result)
        elif self.params == 'so':
            # 软件版本
            # 新的会覆盖一下默认的。
            self.organize_so_version(db_version, "default_so_version",result)
            self.organize_so_version(db_version, "current_so_version",result)
        else:
            self.organize_so_version(db_version, "default_so_version",result)
            self.organize_so_version(db_version, "current_so_version",result)
            self.organize_model_version(db_version, "default_model_version",result)
            self.organize_model_version(db_version, "current_model_version",result)
        return f'记录时间：{record_time}\n{"\n".join(result.values())}'

    def organize_so_version(self,db_version, key, result):
        if len(result) == 0:
            version_info = db_version.get('version', {})
            result['version'] = f"JOB_ID:{version_info.get('JOB_ID', 'JOB_ID')}\nCOMMIT_SHA:{version_info.get('COMMIT_SHA', 'COMMIT_SHA')}"
        for item in db_version.get(key, {}):
            version = db_version.get(key, {}).get(item, {})
            so_name = version.get("so_name", "")
            version = version.get("version", {}).get("host", "")
            if so_name:
                result[item] = f"{item}/{so_name}:{version}"

    def organize_model_version(self,db_version, key, result):
        for item in db_version.get(key, {}):
            model_version = db_version.get(key, {}).get(item, [])
            if not model_version:
                continue
            if len(model_version) == 1:
                result[item] = f"{item}:{model_version[0]}"
            elif len(model_version) > 1:
                result[item] = f"{item}:\n\t{'\n\t'.join(model_version)}"



    def query_data(self):
        # 只查询6个小时以内的数据！！
        sql = f""" select record_time, version_info  from dataview_vehicle_software_info where vin = %s and record_time > %s and record_time < %s order by record_time desc limit 1 """
        next_time = datetime.datetime.strptime(self.condition['start_time'], "%Y-%m-%d %H:%M:%S") - datetime.timedelta(hours=2)
        next_time = next_time.strftime("%Y-%m-%d %H:%M:%S")
        result = SqlUtil.query_all_dict(sql, (self.condition['vin'], next_time, self.condition["end_time"]))
        if result is None or len(result) == 0:
            return None
        return result[0]


    def handle_conditions(self, variables, params):
        # 如果没有指定VIN，直接生成图片
        self.condition["vin"] = variables.get('vin', '')
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.params = params

