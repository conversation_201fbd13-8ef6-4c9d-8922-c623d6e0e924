#!/usr/bin/env python3
"""
快速打印车道线数据
最简洁的方式使用SDK打印车道线数据，无索引创建
"""

import sys
import os

# 添加SDK路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

# 导入日志系统
try:
    from src.utils.sdk_logger import log_info, log_error, log_success, log_warning, log_lane_info
except ImportError:
    # 回退到print
    def log_info(msg): print(f"INFO - {msg}")
    def log_error(msg): print(f"ERROR - {msg}")
    def log_success(msg): print(f"SUCCESS - {msg}")
    def log_warning(msg): print(f"WARNING - {msg}")
    def log_lane_info(msg): print(f"LANE - {msg}")

try:
    from src.main import McapAutoDriveSDK
    log_success("成功导入SDK")
except ImportError as e:
    log_error(f"无法导入SDK: {e}")
    sys.exit(1)


def quick_print_lanes(mcap_file: str, max_messages: int = 3):
    """快速打印车道线数据"""
    log_info(f"快速打印车道线: {mcap_file}")

    # 创建SDK实例 - 禁用所有可能创建索引的功能，启用详细模式调试
    sdk = McapAutoDriveSDK(
        verbose=True,  # 启用详细模式查看调试信息
        enable_fast_mode=False,
        enable_structured_data=False
    )

    log_info("直接流式读取中...")

    count = 0
    try:
        # 使用完整的消息类型名
        lane_types = ["deva_perception_msgs/msg/LaneArrayv2", "LaneArrayv2"]
        for msg in sdk.stream_data(mcap_file, lane_types, max_messages=max_messages):
            count += 1
            log_lane_info(f"车道线消息 {count} (时间: {msg.timestamp:.3f}s)")

            # 快速打印车道线核心信息
            if hasattr(msg.data, 'lane_array'):
                lanes = msg.data.lane_array
                log_info(f"检测到 {len(lanes)} 条车道线:")

                for i, lane in enumerate(lanes[:5]):  # 只显示前5条
                    waypoints = lane.waypoints if hasattr(lane, 'waypoints') else []
                    log_info(f"车道{i+1}: ID={lane.lane_id}, 置信度={lane.confidence:.3f}, {len(waypoints)}个点")

                    # 打印起点和终点
                    if waypoints:
                        start = waypoints[0]
                        end = waypoints[-1]
                        log_info(f"  起点: ({start.x:.1f}, {start.y:.1f}, {start.z:.1f})")
                        log_info(f"  终点: ({end.x:.1f}, {end.y:.1f}, {end.z:.1f})")

                if len(lanes) > 5:
                    log_info(f"... 还有 {len(lanes)-5} 条车道线")
            else:
                log_warning("无法解析车道线数据")

    except Exception as e:
        log_error(f"读取失败: {e}")
        return

    if count == 0:
        log_warning("未找到车道线数据")
    else:
        log_success(f"完成，共打印 {count} 条消息")


def main():
    if len(sys.argv) < 2:
        log_info("用法: python examples/quick_lane_print.py <mcap_file> [max_messages]")
        return

    mcap_file = sys.argv[1]
    max_messages = int(sys.argv[2]) if len(sys.argv) > 2 else 3

    if not os.path.exists(mcap_file):
        log_error(f"文件不存在: {mcap_file}")
        return

    quick_print_lanes(mcap_file, max_messages)


if __name__ == "__main__":
    main()
