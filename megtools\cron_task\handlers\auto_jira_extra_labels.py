from basic.services import SendMessageApi
from basic.utils import <PERSON><PERSON><PERSON><PERSON>ate, DateUtil


# 根据summary 中的信息，提取标签内容，然后进行打签
def apply(task_config):
    AutoJiraExtraLabels(task_config).apply()

class AutoJiraExtraLabels:
    def __init__(self, task_config):
        self.task_config = task_config
        self.jql = task_config.get("jql", "")
        self.tag_lables = task_config.get("tag_lables", "")

    def apply(self):
        jira_data = self.query_jira(self.task_config.get("before_execute", None))
        # 判断是否符合条件，
        # 修改Labels
        result = self.auto_labels(jira_data)
        self.send_message(result)

    def auto_labels(self, jira_data):
        result = []
        for item in jira_data:
            need_append_labels = []
            old_labels = item.get("fields", {}).get("labels",[])
            summary = item.get("fields", {}).get("summary","")
            for label_name in self.tag_lables:
                if label_name in summary and label_name not in old_labels:
                    need_append_labels.append(label_name)
            if len(need_append_labels) > 0:
                old_labels.extend(need_append_labels)
                self.update_labels(item.get("key", ""), {"labels": old_labels})
                result.append(f'{item.get("key")}: {need_append_labels}')
        return result

    def update_labels(self, jira_key, update_value):
        JiraOperate().modify_values(jira_key, update_value)

    def send_message(self, result):
        open_ids = self.task_config.get("alarm", "")
        if not open_ids or len(result) == 0:
            return
        for item in open_ids.split(";"):
            SendMessageApi(f""" **{self.task_config.get("task_name", "")}**\n {"\n".join(result)} """).send_message(item)

    def query_jira(self, last_execute):
        last_update_time = '2025-01-01 00:00'
        if last_execute:
            last_update_time = last_execute.strftime("%Y-%m-%d %H:%M")
        # 添加SQL的片段
        self.jql = self.jql.replace("{{updated}}", last_update_time)
        jira_entities = JiraOperate().query_jira(self.jql, ["labels", "summary"])
        return jira_entities

    def check(self):
        if not self.jql:
            raise Exception("请配置jql参数")


