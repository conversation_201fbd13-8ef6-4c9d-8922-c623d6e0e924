import json

from cron_task.handlers.log_parsers.parser_super import <PERSON><PERSON><PERSON>
from cron_task.models import  VehicleSoftwareVersionInfo


def apply(file_path, db_entity):
    VehicleSoftwareParser().parse(db_entity.id, db_entity, file_path)


save_key = [
    "version",
    "default_so_version", "default_model_version",
    "current_sys_time",
    "current_so_version", "current_model_version"
]

class VehicleSoftwareParser(Parser):
    def __init__(self):
        super().__init__()
        self.db_entity = None

    def parse_file(self, id, db_entity, log_path):
        self.db_entity = db_entity
        with open(log_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        data_bak = {}
        for key in data.keys():
            if key in save_key:
                data_bak[key] = data[key]

        record_time = data_bak.get("current_sys_time", {}).get("host", "")[0:-4]
        # 存储到数据库里
        entity = {
            "vin": self.db_entity.vin,
            "record_time": record_time,
            "version_info": json.dumps(data_bak),
        }
        VehicleSoftwareVersionInfo(**entity).save()
