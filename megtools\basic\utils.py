import logging
import random
import shutil
import time
import traceback
from decimal import Decimal
from threading import Thread

import boto3
import requests
from croniter import croniter
from django.conf import settings
from django.core.paginator import Paginator
from django.core import serializers
import json
from datetime import date, datetime, timedelta
from django.db.models import Q, Model, QuerySet
from django.db.models.fields import Char<PERSON>ield, DateField, DateTimeField, IntegerField
from django.db import connections
import hashlib
import socket
# import psutil
import os
import tarfile
# import pandas as pd
# import numpy as np
from django.core.cache import cache
# from openpyxl.styles import Alignment

from requests.auth import HTTPBasicAuth


# import openpyxl


class JsonUtils:
    # 数据库查询出来的单一对象无法转化为Json 需要对其进行序列化之后再进行转换
    @staticmethod
    def convert2Json(data):
        return JsonUtils.convertList2Json([data])[0]

    @staticmethod
    def convertList2Json(data):
        jsonObject = json.loads(serializers.serialize("json", data, ensure_ascii=False))
        result = []
        for item in jsonObject:
            item.get("fields")["id"] = item.get("pk")
            result.append(item.get("fields"))

        return result

    @staticmethod
    def convertStr2Json(data):
        if data is None or data == '':
            return None
        return json.loads(data)

    @staticmethod
    def is_valid_json(json_str):
        try:
            return json.loads(json_str)
        except:
            return False

    def flatten_json(data, parent_key='', sep='|'):
        """
        将嵌套的JSON对象扁平化为一个字典，使用下划线来连接键名以表示嵌套关系。

        :param data: 要扁平化的嵌套字典或列表。
        :param parent_key: 父键名，用于递归时构建新的键名。
        :param sep: 分隔符，用于连接键名。
        :return: 扁平化后的字典。
        """
        items = []

        def flatten(x, name=''):
            if isinstance(x, dict):
                for a in x:
                    flatten(x[a], name + (name and sep or '') + a)
            elif isinstance(x, list):
                i = 0
                for a in x:
                    flatten(a, name + (name and sep or '') + str(i))
                    i += 1
            else:
                items.append((name, x))

        flatten(data)
        return {k: v for k, v in items}


# 分页所需要的工具
class PageUtils:

    # 数据库查询的对象是QuerySet 无法直接转换为Json， 因此在这里进行转换为JSON对象
    # 当前方式先将数据库查询对象序列化，然后再转换成JSONObj
    # 再取出来 fields 组装成结果，过程较多可能会存在性能问题
    # 但是未找到更好的办法，后面有更高效的办法可以对其改进。
    @staticmethod
    def page(data, req):
        paginator = Paginator(data, int(req.get("pageSize")))
        cur_page = int(req.get("page"))

        result = []
        if paginator.count != 0:
            max_page = int(paginator.count / int(req.get("pageSize"))) + 1
            cur_page = max_page if cur_page > max_page else cur_page
            pageData = paginator.page(cur_page)
            if type(pageData.object_list) == list:
                result = pageData.object_list
            elif type(pageData.object_list) == QuerySet and pageData.object_list.count() > 0 and type(pageData.object_list[0]) == dict:
                result = list(pageData.object_list)
            else:
                jsonObject = json.loads(serializers.serialize("json", pageData, ensure_ascii=False))
                for item in jsonObject:
                    item.get("fields")["id"] = item.get("pk")
                    result.append(item.get("fields"))
        else:
            cur_page = 1

        return {
            "total": paginator.count,
            "page": cur_page,
            "pageSize": int(req.get("pageSize")),
            "items": result
        }


class JsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y/%m/%d %H:%M:%S')
        elif isinstance(obj, date):
            return obj.strftime('%Y-%m-%d')
        elif isinstance(obj, bytes):
            return obj.decode()
        elif isinstance(obj, Decimal):
            return float(obj.quantize(Decimal('0.00')))
        else:
            return json.JSONEncoder.default(self, obj)


class OrmFilter:
    @staticmethod
    def and_condition(model, condition, like_flag):
        like_pattern = '__contains' if like_flag == 1 else ''
        if isinstance(condition, dict):
            q_obj = Q()
            q_obj.connector = 'and'
            for key in condition:
                if f"({key}," in model.__doc__ or f", {key})" in model.__doc__ or f", {key}," in model.__doc__:
                    if isinstance(model.__dict__[key].field, DateField) \
                            or isinstance(model.__dict__[key].field, DateTimeField):
                        req_time = condition.get(key, [])
                        if req_time and len(req_time) == 2:
                            q_obj.children.append((f"{key}__gte", req_time[0]))
                            q_obj.children.append((f"{key}__lte", req_time[1]))
                        continue
                    if isinstance(model.__dict__[key].field, CharField):
                        if isinstance(condition[key], list):
                            # 如果传入的参数是一个list， 则拼接 in 的形式
                            q_obj.children.append((f"{key}__in", condition[key]))
                            continue
                        q_obj.children.append((f"{key}{like_pattern}", condition[key]))
                        continue
                    if isinstance(model.__dict__[key].field, IntegerField):
                        # 这种是按照等于进行判断。
                        q_obj.children.append((key, condition[key]))
                        continue

            return q_obj

        return None


class SqlUtil:
    @staticmethod
    def query_all_dict(sql, params=None, db='default'):
        cursor = connections[db].cursor()
        try:
            if params:
                cursor.execute(sql, params=params)
            else:
                cursor.execute(sql)

            col_name = [desc[0] for desc in cursor.description]
            row = cursor.fetchall()
            rowList = []
            for item in row:
                tMap = dict(zip(col_name, item))
                rowList.append(tMap)
            return rowList
        finally:
            cursor.close()

    @staticmethod
    def query_one_dict(sql, params=None, db='default'):
        cursor = connections[db].cursor()
        try:
            if params:
                cursor.execute(sql, params=params)
            else:
                cursor.execute(sql)
            col_name = [desc[0] for desc in cursor.description]
            item = cursor.fetchone()
            tMap = dict(zip(col_name, item)) if item is not None else {}
            return tMap
        finally:
            cursor.close()

    @staticmethod
    def query_field_list(sql, params=None, db='default'):
        cursor = connections[db].cursor()
        try:
            if params:
                cursor.execute(sql, params=params)
            else:
                cursor.execute(sql)
            col_name = [desc[0] for desc in cursor.description]
            row = cursor.fetchall()
            rowList = tuple()
            for item in row:
                rowList = rowList.__add__(item)
            return list(rowList)
        finally:
            cursor.close()

    @staticmethod
    def exec_update(sql, params=None, db='default'):
        cursor = connections[db].cursor()
        try:
            if params:
                cursor.execute(sql, params=params)
            else:
                cursor.execute(sql)
        finally:
            cursor.close()

class ParamUtils:
    @staticmethod
    def clear_none(data):
        del_keys = []
        for key in data:
            if data[key] == None:
                del_keys.append(key)
        for item in del_keys:
            del data[item]


class DateEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        else:
            return json.JSONEncoder.default(self, obj)

    def get_date_list(start_date, end_date):
        dates = []
        dt = datetime.strptime(start_date, "%Y-%m-%d")
        date = start_date[:]
        while date <= end_date:
            dates.append(date)
            dt += timedelta(days=1)
            date = dt.strftime("%Y-%m-%d")
        return dates

    def get_hour_list(start_date, end_date):
        dates = []
        dt = datetime.strptime(start_date, "%Y-%m-%d %H")
        date = start_date[:]
        while date <= end_date:
            dates.append(date)
            dt += timedelta(hours=1)
            date = dt.strftime("%Y-%m-%d %H")
        return dates

    def get_current_week(date=None):
        """
        获取指定日期的 周一，周日
        """
        if date:
            duty_date = datetime.strptime(str(date), '%Y-%m-%d')
            monday, sunday = duty_date, duty_date
        else:
            monday, sunday = datetime.date.today(), datetime.date.today()
        one_day = timedelta(days=1)
        while monday.weekday() != 0:
            monday -= one_day
        while sunday.weekday() != 6:
            sunday += one_day

        # return monday, sunday
        # 返回时间字符串
        return datetime.strftime(monday, "%Y-%m-%d"), datetime.strftime(sunday, "%Y-%m-%d")

    def get_weeks(start_date):
        """
        输入一个日期，返回这个日期到当前日期的所有周，
        例如输入 2023-09-25 输出 ['2023/09/25~2023/10/01','2023/10/02~2023/10/08','2023/10/09~2023/10/15']
        """
        # 将输入的日期字符串转换为datetime对象
        start_date = datetime.strptime(start_date, '%Y-%m-%d')

        # 获取当前日期
        end_date = datetime.now()

        # 计算第一个周的起始日期
        first_week_start = start_date
        if start_date.weekday() != 0:
            first_week_start = start_date + timedelta(days=-start_date.weekday())

            # 计算每一个周的起始日期和结束日期，并保存到列表中
        weeks = {}
        weekNums = []
        current_date = first_week_start
        while current_date <= end_date:
            current_week_start = current_date
            week_number = current_week_start.isocalendar()[1]
            current_date += timedelta(days=7)
            week = str(datetime.now().year) + '-' + str(week_number) + '周'
            weekNums.append(week)
            weeks[week] = f"{current_week_start.strftime('%Y-%m-%d')}/{(current_date + timedelta(days=-1)).strftime('%Y-%m-%d')}"
        return weekNums, weeks

    def formatCurrentTime(format_str="%Y-%m-%d %H:%M:%S"):
        now = datetime.now()
        return now.strftime(format_str)


class QueryWrapper(object):
    """查询集包装器。实现django Paginator需要的必要方法，实现和query一样使用Paginator分页"""

    def __init__(self, sql, params=None):
        """
        :param sql: sql语句
        :param params: sql语句的params参数
        """
        self.sql = sql
        self.params = params
        if len(params) == 0:
            self.params = None

    def count(self):
        """计算总页数"""
        sql = """select count(*) from (%s) _count""" % self.sql
        # sql封装方法请参考https://my.oschina.net/watcher/blog/1573503
        return self.fetchone_sql(sql, self.params, flat=True)  # 返回总页数

    def __getitem__(self, k):
        """ self.__getslice(x, y) = self[x:y]"""
        x, y = k.start, k.stop
        sql = self.sql + ' LIMIT {start}, {num}'.format(start=x, num=y - x)
        # sql封装方法请参考https://my.oschina.net/watcher/blog/1573503
        return self.fetchall_to_dict(sql, self.params)  # 字典列表形式返回

    def fetchone_sql(self, sql, params=None, flat=False, db="default"):
        """
        返回一行数据
        :param sql: sql语句
        :param db: 使用的数据库
        :param params: sql语句参数
        :param flat: 如果为True，只返回第一个字段值，例如：id
        :return: 例如：(id, 'username', 'first_name')
        """
        cursor = connections[db].cursor()
        cursor.execute(sql, params)
        fetchone = cursor.fetchone()
        cursor.close()
        if fetchone:
            fetchone = fetchone[0] if flat else fetchone
        return fetchone

    def fetchone_to_dict(self, sql, params=None, db="default"):
        """
        返回一行数据
        :param sql: sql语句
        :param params: sql语句参数
        :return: 例如：{"id": id, "username": 'username', "first_name": 'first_name'}
        """
        cursor = connections[db].cursor()
        cursor.execute(sql, params)
        desc = cursor.description
        row = dict(zip([col[0] for col in desc], cursor.fetchone()))
        cursor.close()
        return row

    def fetchall_to_dict(self, sql, params=None, db="default"):
        """
        返回全部数据
        :param sql: sql语句
        :param db: sql语句
        :param params: sql语句参数
        :return: 例如：[{"id": id, "username": 'username', "first_name": 'first_name'}]
        """
        cursor = connections[db].cursor()
        cursor.execute(sql, params)
        desc = cursor.description
        object_list = [
            dict(zip([col[0] for col in desc], row))
            for row in cursor.fetchall()
        ]
        cursor.close()
        return object_list


class FileUtil:
    @staticmethod
    def calMd5(file_path):
        m = hashlib.md5()  # 创建md5对象
        with open(file_path, 'rb') as fobj:
            while True:
                data = fobj.read(4096)
                if not data:
                    break
                m.update(data)  # 更新md5对象
        return m.hexdigest()

    @staticmethod
    def calMd5Salt( file_path, other):
        file_md5 = FileUtil.calMd5(file_path)
        data = file_md5 + other
        md5 = hashlib.md5()  # 创建md5加密对象
        md5.update(data.encode('utf-8'))  # 指定需要加密的字符串

        return md5.hexdigest()

    # 删除文件夹以及文件夹下的所有内容
    def clear(self, path):
        if not os.path.exists(path):
            return
        for i in os.listdir(path):
            file_data = os.path.join(path, i)
            if os.path.isfile(file_data):
                os.remove(file_data)
            else:
                self.clear(file_data)
        os.rmdir(path)

    # 级联创建多级目录
    @staticmethod
    def createDirs(path):
        path = path.strip()
        path = path.rstrip(os.path.sep)
        dirs = path.split(os.path.sep)
        temp_path = dirs[0]
        for item in dirs:
            if item == temp_path:
                continue
            temp_path = temp_path + os.path.sep + item
            if os.path.isfile(temp_path):
                os.remove(temp_path)
            elif os.path.exists(temp_path):
                continue
            os.mkdir(temp_path)

    # 解压文件到对应的目标目录
    def unzip(self, file_path, target_path):
        self.createDirs(target_path)

    # 解压缩Tar文件
    def extraTar(self, tar_path, target_path):
        self.createDirs(target_path)
        tf = tarfile.open(tar_path)
        tf.extractall(target_path)
        tf.close()

    # 获取目录下所有的文件绝对路径
    def all_file(self, base_dir):
        result = []
        self.__find_file(result, base_dir)
        return result

    def __find_file(self, result, base_dir):
        if os.path.isfile(base_dir):
            result.append(os.path.abspath(base_dir))
            return
        if os.path.isdir(base_dir):
            for item in os.listdir(base_dir):
                self.__find_file(result, os.path.join(base_dir, item))

    def move_file(self, source, target, overwrite=False):
        if not os.path.exists(source):
            return
        if os.path.exists(target) and overwrite is False:
            return
        shutil.move(source, target)



import re

regexp_str = '\\{\\{[a-z,0-9,A-Z,_,.,(,),:]+\\}\\}'
args_pattern = re.compile(regexp_str)


def parse_key(origin):
    result = {}
    for item in origin.keys():
        value = origin[item]
        if isinstance(value, dict):
            dict_params = parse_key(value)
            result.update(**dict_params)
        elif isinstance(value, list):
            for arr_item in value:
                dict_params = parse_key(arr_item)
                result.update(**dict_params)
        elif isinstance(value, str):
            dict_params = parse_single_key(value)
            result.update(**dict_params)
    return result


def parse_single_key(origin):
    result = {}
    find_replace = args_pattern.findall(origin)
    for item in find_replace:
        key = item[2:len(item) - 2]
        # 如果是 env.的环境变量无需处理
        if key.startswith("env."):
            continue
        # 如果是 类似于 now() 的函数 则不进行处理
        if "(" in key and ")" in key:
            continue
        key = key[5:] if key.startswith("item.") else key
        if ":" in key and len(key.split(":")) > 1:
            temp = key.split(":")[0]
            result[temp[0]] = key[len(temp[0]) + 3:]
        else:
            result[key] = ""
    return result


# 获取客户端IP地址
def get_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    s.connect(("*******", 80))
    ip = (s.getsockname()[0])
    s.close()
    return ip


# 获取MAC地址
# def __get_mac__(ip):
#     dic = psutil.net_if_addrs()
#     for adapter in dic:
#         snicList = dic[adapter]
#         mac = '无 mac 地址'
#         ipv4 = '无 ipv4 地址'
#         for snic in snicList:
#             if snic.family.name in {'AF_LINK', 'AF_PACKET'}:
#                 mac = snic.address
#             elif snic.family.name == 'AF_INET':
#                 ipv4 = snic.address
#         if ipv4 == ip:
#             return mac


# 生成客户端信息
# def server_info():
#     result = {'ip': __get_ip__()}
#     result['mac'] = __get_mac__(result['ip'])
#     return result


from django.db import models
from django.db import transaction
class ExpandQuerySet(models.QuerySet):
    """
    拓展querySet
    """

    def bulk_update_or_create(self, common_keys, unique_key_name, unique_key_to_defaults, batch_size=200):
        """
        common_keys: {field_name: field_value} 通用筛选条件
        unique_key_name: field_name # 唯一字段
        unique_key_to_defaults: {field_value: {field_name: field_value}} # 更新值
        """
        with transaction.atomic(using=self.db, savepoint=False):
            filter_kwargs = dict(common_keys)
            filter_kwargs[f"{unique_key_name}__in"] = unique_key_to_defaults.keys()
            existing_objs = {
                getattr(obj, unique_key_name): obj
                for obj in self.filter(**filter_kwargs).select_for_update()
            }
            # 批量创建
            create_data = {
                k: v for k, v in unique_key_to_defaults.items() if k not in existing_objs
            }
            for unique_key_value, obj in create_data.items():
                obj[unique_key_name] = unique_key_value
                obj.update(common_keys)
            creates = [self.model(**obj_data) for obj_data in create_data.values()]
            if creates:
                self.bulk_create(creates, batch_size=batch_size)
            # 如果使用了add_now来自动更新时间，update_fields必须包含此字段
            # 因queryset.update不会自动更新时间，只有save会
            update_fields = {"create_time"}
            # 批量更新
            updates = []
            for key, obj in existing_objs.items():
                for i in unique_key_to_defaults[key].items():
                    setattr(obj, i[0], i[1])
                # 将所有要更新的字段都统计出来
                update_fields.update(unique_key_to_defaults[key].keys())
                updates.append(obj)
            if existing_objs:
                self.bulk_update(updates, update_fields, batch_size=batch_size)
        return len(creates), len(updates)

    def update(self, **kwargs):
        if getattr(self.model, "create_time", None):
            kwargs.update({"create_time": datetime.now()})
        return super().update(**kwargs)


class TemplateUtil:
    @staticmethod
    def replace_params(cur_params, origin) -> str:
        if type(origin) != str:
            return origin
        find_replace = args_pattern.findall(origin)
        for item in find_replace:
            key = item[2:len(item) - 2]
            new_value = TemplateUtil.find_by_path(cur_params, key)
            origin = origin.replace(item, str(new_value), 1)
        return origin

    @staticmethod
    def find_by_path(params, path):
        paths = path.split(".")
        result = None
        num = 1
        try:
            for sub_path in paths:
                if num == 1:
                    result = params[sub_path]
                    num = num + 1
                else:
                    result = result[sub_path]
        except Exception as e:
            return ""

        return result

class DictUtil:
    @staticmethod
    def get_value(entity, field_path: str):
        """
        传入一个字典对象，根据字段路径来查找对应的值，如果找不到，则返回为空
        """
        # 如果是字段路径为空了，则可以返回了
        if field_path == "" or entity is None:
            return entity if entity is not None else ""
        if field_path.startswith("|"):
            length = int(field_path[1:])
            return str(entity)[0:length] if entity is not None else ""
        field_name = field_path[0: field_path.index("|")] if "|" in field_path else field_path
        if "." in field_path:
            field_name = field_path[0:field_path.index(".")]

        # 处理 长度
        if len(field_name) < len(field_path) and field_path[len(field_name)] == "|":
            field_path = field_path[len(field_name):]
        else:
            field_path = field_path[len(field_name) + 1:]
        if field_name.isdigit():
            # 处理数组类型
            arr_idx = int(field_name)
            temp = None
            if arr_idx < len(entity):
                temp = entity[arr_idx]
            return DictUtil.get_value(temp, field_path)
        return DictUtil.get_value(entity.get(field_name, None), field_path)

class DateUtil:
    @staticmethod
    def current(format_str="%Y-%m-%d %H:%M:%S"):
        now = datetime.now()
        return now.strftime(format_str)

    @staticmethod
    def format(date: datetime, format_str="%Y-%m-%d %H:%M:%S"):
        return date.strftime(format_str)

    @staticmethod
    def timestamp2Day(timeStamp):
        """
        一个时间戳 转换成去掉时分秒后的时间戳
        """
        dateArray = datetime.fromtimestamp(timeStamp)
        dayStr = dateArray.strftime("%Y-%m-%d")
        return datetime.strptime(dayStr, "%Y-%m-%d").timestamp()

def parse_crontab(crontab_expr, start_time=None):
    if start_time is None:
        start_time = datetime.now()

    cron = croniter(crontab_expr, start_time)
    next_date = cron.get_next(datetime)
    return next_date

def generate_md5(input_string):
    """
    生成文件md5
    :param input_string:
    :return:
    """
    # 创建一个md5 hash对象
    hash_object = hashlib.md5()
    # 使用输入的字符串更新hash对象
    hash_object.update(input_string.encode('utf-8'))
    # 返回哈希值
    return hash_object.hexdigest()


def doris_stream(table_name, data):
    """
    table_name: 表名
    data: 输入进行 encode('utf-8'), 数据之间使用 ~ 进行分割
    """
    database = settings.DATABASES["doris"]['NAME']
    username = settings.DATABASES["doris"]['USER']
    password = settings.DATABASES["doris"]['PASSWORD']
    host = settings.DATABASES["doris"]['HOST']
    headers = {
        'Content-Type': 'text/plain; charset=UTF-8',
        'format': 'csv',
        "column_separator": '`',
        'Expect': '100-continue',
    }
    auth = HTTPBasicAuth(username, password)
    url = 'http://%s:8030/api/%s/%s/_stream_load' % (host, database, table_name)
    session = requests.sessions.Session()
    session.should_strip_auth = lambda old_url, new_url: False  # Don't strip auth
    resp = session.request(
        'PUT', url=url,
        data=data,
        headers=headers,
        auth=auth
    )
    resp_text = resp.text
    resp.close()
    session.close()
    result = False
    if resp.json()['Status'] != 'Success':
        logging.error(resp_text)
        result = True
    return result, resp_text


def get_tenant_token():
    """
    获取飞书tenant_access_token
    """
    url = settings.FEISHU_CONFIG["url"]
    app_id = settings.FEISHU_CONFIG["app_id"]
    app_secret = settings.FEISHU_CONFIG["app_secret"]
    return _get_tenant_token(url, app_id, app_secret)


def get_doc_tenant_token():
    url = settings.FEISHU_CONFIG["url"]
    app_id = settings.FEISHU_CONFIG["doc_app_id"]
    app_secret = settings.FEISHU_CONFIG["doc_app_secret"]
    return _get_tenant_token(url, app_id, app_secret)


def _get_tenant_token(url, app_id, app_secret):
    headers = {"Content-Type": "application/json"}
    body = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    url1 = f"{url}/auth/v3/tenant_access_token/internal"
    resp = requests.post(url1, headers=headers, json=body)
    return resp.json()["tenant_access_token"]


class JiraOperate:

    def query_jira_by_key(self, jira_key, fields=None):
        if fields is None:
            fields = ["key", "summary"]
        result = self.page_jira(0, f"key={jira_key}", fields=fields)
        if len(result) > 0:
            return result[0]
        return None

    def query_jira(self, jql, fields=None):
        """
        直接在Jira 中查询出来数据，返回body就好
        这里记得配置 jql 的时候需要验证下，不能查出来的数据太多！！！！！
        """
        if fields is None:
            fields = ["key", "summary"]
        page = 0
        jira_entities = []
        while True:
            jira_entity = self.page_jira(page, jql, fields)
            if len(jira_entity) == 0:
                break
            page += 1
            jira_entities.extend(jira_entity)
        return jira_entities

    def page_jira(self, page, jql, fields, count=400):
        jira_entity = []
        start = page * count
        body = {
            "maxResults": count,
            "startAt": start,
            "fields": fields,
            "jql": jql
        }

        logging.info(f"jira req: {body}")
        resp = requests.post(settings.JIRA_CONFIG["url"], headers=settings.JIRA_CONFIG["headers"], json=body)
        logging.info(f"jira response: {resp.text}")
        data = resp.json()
        # 循环所有的问题列表
        for item in data.get('issues', []):
            if "fields" not in item:
                continue
            jira_entity.append(item)
        return jira_entity

    def query_comment(self, jira_key):
        url = f'{settings.JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}/comment'
        resp = requests.get(url, headers=settings.JIRA_CONFIG["headers"])
        return resp.json()

    def add_comment(self, jira_key, comment):
        url = f'{settings.JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}/comment'
        body = { "body": comment }
        resp = requests.post(url, headers=settings.JIRA_CONFIG["headers"], json=body)
        return resp.json()

    def modify_assignee(self, jira_key, assignee):
        self.modify_values(jira_key, {"assignee":  {"name": assignee}})

    def modify_values(self, jira_key, fields):
        url = f'{settings.JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}'
        body = {
            "fields": fields
        }
        resp = requests.put(url, headers=settings.JIRA_CONFIG["headers"], json=body)
        logging.info(f"modify jira fields {resp.text}")

    def get_transitions(self, jira_key):
        url = f'{settings.JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}/transitions'
        resp = requests.get(url, headers=settings.JIRA_CONFIG["headers"])
        logging.info(f"modify jira fields {resp.text}")
        return resp.json()

    def post_transitions(self, jira_key, id):
        url = f'{settings.JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}/transitions'
        resp = requests.post(url, headers=settings.JIRA_CONFIG["headers"], json={
            "transition": {"id": id}
        })
        logging.info(f"modify jira fields {resp.text}")

# class WikiOperate:
#     # Confluence服务器的配置信息
#     base_url = settings.WIKI_CONFIG["base_url"]
#     api_token = settings.WIKI_CONFIG["api_token"]
#     headers = {
#         'Accept': 'application/json',
#         'Authorization': 'Bearer ' + api_token
#     }
#     # space_key = 'JBC'  # 你的空间键
#     # parent_page_id = 796932064  # 父页面的ID
#     def child_page_query(self, parent_page_id):
#         result = {}
#         index = 0
#         limit = 500
#         while True:
#             url = f'{self.base_url}/rest/api/content/{parent_page_id}/child/page?start={index}&limit={limit}'
#             # 发送GET请求
#             response = requests.get(url, headers=self.headers)
#
#             # 检查请求是否成功
#             if response.status_code == 200:
#                 # 打印子页面信息
#                 logging.debug(response.json())
#             else:
#                 print(f'Error retrieving child pages: {response.status_code}')
#
#             index += limit
#             data = response.json()
#             size = data.get("size", 0)
#
#             wiki_results = data.get("results", {})
#             for info in wiki_results:
#                 title = info.get("title")
#                 page_id = info.get("id")
#                 if title:
#                     # result.append([title, page_id])
#                     result[title] = f"https://wiki.jiduauto.com/pages/viewpage.action?pageId={page_id}"
#
#             if not size or size < limit:
#                 break
#
#         return result
#
#     def space_query(self):
#
#         # Confluence服务器的URL
#         # confluence_url = 'https://wiki.jiduauto.com'
#
#         # 获取space列表的API
#         spaces_api = '{}/rest/api/space'.format(self.base_url)
#
#         headers = {
#             'Accept': 'application/json',
#             'Authorization': 'Bearer ' + self.api_token
#         }
#
#         # 使用HTTP基本认证获取API
#         response = requests.get(spaces_api, headers=headers)
#
#         # 检查请求是否成功
#         if response.status_code == 200:
#             spaces = response.json()['results']
#             for space in spaces:
#                 space_key = space['key']
#                 # 打印每个space的key
#                 print(space_key)
#         else:
#             print("Failed to retrieve spaces:", response.status_code)
#
#     def down_pdf(self, space_key, page_id):
#         # https://wiki.jiduauto.com/rest/shdsd-confluenceWatermark/1/export/getExportSetting
#         # https://wiki.jiduauto.com/rest/shdsd-confluenceWatermark/1/export/addExportPdf
#         # https://wiki.jiduauto.com/spaces/flyingpdf/pdfpageexport.action?pageId=820403427
#         # 获取 PDF
#         headers = {
#             'Accept': 'application/json',
#             'Authorization': 'Bearer ' + self.api_token
#         }
#
#         pdf_url = f'{self.base_url}/rest/api/content/{page_id}/export/pdf'
#         response = requests.get(pdf_url, headers=headers, stream=True)
#
#         # 将 PDF 保存到文件
#         with open(f'{space_key}-{page_id}.pdf', 'wb') as pdf_file:
#             for chunk in response.iter_content(chunk_size=1024):
#                 if chunk:
#                     pdf_file.write(chunk)


def get_column_name(col_idx: int):
    if col_idx <= 0:
        return ""
    else:
        next_cal = int(col_idx / 26)
        mod_val = (col_idx) % 26
        if mod_val == 0:
            mod_val = 26
            next_cal -= 1
        mod_val = chr(mod_val + 64)
        cal_val = get_column_name(next_cal)
        return f"{cal_val}{mod_val}"


# class ExcelParse2Model(object):
#     """
#     解析excel存入数据库
#     """
#
#     # 替换列名(key)和值(_value_replace)
#     # 员工管理
#     Employ = {
#             "姓名": "emp_name",
#             "域账号": "domain_account",
#             "入职日期": "join_day",
#             "司龄(天)": "seniority",
#             "工作地点": "work_city",
#             "二级部门": "second_dep",
#             "三级部门": "third_dep",
#             "直接上级": "leader",
#             "三级主管": "manager",
#             "岗位(WB)": "job_title",
#             "供应商(WB)": "supplier",
#             "考核分类": "staff_class",
#         }
#
#     # 违规驾驶信息
#     Illegal = {
#         "车号": "car_number",
#         "车辆归属地": "vehicle_location",
#         "直接上级": "leader",
#         "所属组别": "job_group",
#         "抽查日期": "check_day",
#         "记录仪": "recorder_status",
#         "镜头角度": "dms_status",
#         "异常描述": "abnormal_desc",
#         "图片": "image",
#         "_value_replace": {
#             "记录仪": {'正常': '0', '异常': 1},
#             "镜头角度": {'正常': '0', '异常': 1},
#
#         }
#     }
#
#     # 事故记录 1-多车剐蹭 2-单车剐蹭 3-多车事故 4-多车事故(三方人伤) 5-压线违章 6-与非机动车剐蹭 7-单车事故 8-智驾全责 9-其他
#     Accident = {
#         "姓名": "emp_name",
#         "域账号": "domain_account",
#         "直接上级": "leader",
#         "所属组别": "job_group",
#         "责任划分": "divided",
#         "事故类型": "accident_type",
#         "发生时间": "accident_date",
#         "事故描述": "accident_remark",
#         "_value_replace": {
#             "责任划分": {"全责": "1", "无责": "2", "次责": "3", "智驾全责": "4", "其他": "5"},
#             "事故类型": {"多车剐蹭": "1", "单车剐蹭": "2", "多车事故": "3", "多车事故(三方人伤)": "4", "压线违章": "5",
#                          "与非机动车剐蹭": "6", "单车事故": "7", "智驾全责": "8", "其他": "9"},
#         }
#     }
#
#     @staticmethod
#     def convert_excel_to_json(excel_file):
#         df = pd.read_excel(excel_file, engine='openpyxl', keep_default_na=False)
#         json_data = df.to_json(orient='records')
#         return json_data
#
#     @staticmethod
#     def excel_parse(request,  mapping, is_del=None):
#         file = request.FILES.get('file', None)
#         if file is not None:
#             file_path = f'{settings.FILE_CONFIG["base_path"]}{file.name}'
#             with open(file_path, 'wb+') as f:
#                 for chunk in file.chunks():
#                     f.write(chunk)
#
#         table = pd.read_excel(file_path, header=0, keep_default_na=False)
#
#         if is_del:
#             table.drop(columns=is_del, inplace=True)
#         if "_value_replace" in mapping:
#             replace = mapping["_value_replace"]
#             for k in replace:
#                 table[k] = table[k].replace(replace[k])
#         table.columns = list(
#             map(lambda x: mapping[x.replace("\xa0", "").replace(" ", "")], table.columns))
#         logging.debug(f"tmp_table{table}")
#         records = table.to_dict(orient="records")
#         return records
#
#     @staticmethod
#     def save_model(records, method, request):
#         save_flag = True
#         # 当前每条记录插入一次效率低，使用注意
#         for dic in records:
#             save_flag = method(dic, request)
#             if not save_flag:
#                 logging.warning(dic)
#         return save_flag
#
#     @staticmethod
#     def excel_2_model(request,  mapping, method, is_del=None):
#         records = ExcelParse2Model.excel_parse(request, mapping, is_del)
#         save_flag = ExcelParse2Model.save_model(records, method, request)
#         return save_flag
#
#
# class ExcelRow2column(object):
#     """
#     行列转换
#     """
#
#     def row2column(self, filename):
#         # file_path = f'{settings.FILE_CONFIG["base_path"]}{filename}'
#         file_path = filename
#         df = pd.DataFrame(pd.read_excel(file_path))
#         df_t = df.T
#         bio = BytesIO()
#         with pd.ExcelWriter(bio, engine='openpyxl') as writer:
#             df_t.to_excel(writer, header=False)
#         bio.seek(0)
#         return bio
#
#     def test(self, rows):
#         # matrix = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]])
#         matrix = np.array(rows)
#         column = matrix.T.tolist()
#         logging.debug(column)
#         return column
#
#     def openxlrow2column(self, filename=None, workbook=None):
#         from openpyxl.styles import Font
#         if not workbook:
#             workbook = openpyxl.load_workbook(filename)
#         # worksheet = workbook['sheetname']
#         worksheet = workbook.active
#         column_count = worksheet.max_column
#         row_count = worksheet.max_row
#         title = worksheet.title
#         new_worksheet = workbook.create_sheet(title + "1", 0)
#         alignment = Alignment(horizontal='left')
#         for column in range(1, column_count + 1):
#             new_row = []
#             for row in range(1, row_count + 1):
#                 cell_value = worksheet.cell(row=row, column=column).value
#                 new_row.append(cell_value)
#             new_worksheet.append(new_row)
#             for row in range(1, row_count + 1):
#                 hyperlink = worksheet.cell(row=row, column=column).hyperlink
#                 new_worksheet.cell(row=column, column=row).alignment = alignment
#                 if hyperlink:
#                     new_worksheet.cell(row=column, column=row).hyperlink = hyperlink
#                     new_worksheet.cell(row=column, column=row).font = Font(color="0000FF")
#         for col in new_worksheet.columns:
#             column = col[0].column_letter
#             new_worksheet.column_dimensions[column].width = 22
#         return workbook


# 计算EXCEL第几列
def get_col_name(col_idx: int):
    if col_idx <= 0:
        return ""
    else:
        next_cal = int(col_idx / 26)
        mod_val = col_idx % 26
        mod_val = chr(mod_val + 64)
        cal_val = get_col_name(next_cal)
        return f"{cal_val}{mod_val}"

class DistributedLockThread(Thread):
    def __init__(self, lock_name, name='DistributedLockThread', interval=10, switchName = "", timeout=60):
        super().__init__(name=name)
        # 分布式锁的名称，不能为空，用于多服务器之间的竞争
        self.lock_name = f"{name}:{lock_name}"
        # 每次运行完成之后休息的时间， 默认10s
        self.interval = interval
        # 是否启动线程的名称，不写默认执行
        self.switchName = switchName

        self.need_release = True
        self.timeout = timeout

    def run(self):
        # 如果没有指定key， 则说明必须要运行，如果指定了，那么需要判断下是否执行
        if self.switchName:
            if self.switchName not in settings.TASK_CONFIG or settings.TASK_CONFIG[self.switchName] == 0:
                logging.info(f"{self.switchName} switch not enable!")
                return
        self.before()
        while True:
            self.need_release = True
            try:
                time.sleep(self.interval)
                if not self.obtain_lock():
                    self.need_release = False
                    continue
                self.apply()
                self.release_lock()
            except:
                logging.error(f"jira listen {traceback.format_exc()}")
                self.release_lock()

    def before(self):
        pass

    def apply(self):
        pass

    def obtain_lock(self):
        for i in range(0, 3):
            if cache.set(self.lock_name, 1, nx=True, timeout=self.timeout):
                logging.info(f"obtain lock:{self.lock_name}")
                return True
            # 如果没有获取到，随机睡眠一会等待一下
            time.sleep(random.random())
        # 使用redis setnx 获取分布式锁 如果没有获取到尝试获取三次，否则就不再尝试获取
        return False

    def release_lock(self):
        if self.need_release is False:
            return
        try:
            result = cache.delete(self.lock_name)
            logging.info(f"release lock:{self.lock_name} {result}")
        except:
            logging.error(f"release lock:{self.lock_name} {traceback.format_exc()}")


class OssUtils:
    def __init__(self, bucket):
        ak = settings.AWS_CONFIG["ak"]
        sk = settings.AWS_CONFIG["sk"]
        endpoint = settings.AWS_CONFIG["endpoint_url"]
        self.client = boto3.client("s3", endpoint_url=endpoint, aws_access_key_id=ak, aws_secret_access_key=sk)
        self.bucket = bucket

    def ls(self, prefix, marker=''):
        result = []
        while True:
            resp = self.client.list_objects(Prefix=prefix, Bucket=self.bucket, MaxKeys=200, Marker=marker)
            for item in resp.get("Contents", []):
                result.append(item)
            if resp.get("IsTruncated"):
                marker = resp.get("NextMarker")
            else:
                break
        return result, marker

    def ls_dir(self, prefix: str, marker='', value_key="CommonPrefixes"):
        if not prefix.endswith("/"):
            prefix = f"{prefix}/"
        file_list = []
        dir_list = []
        while True:
            resp = self.client.list_objects(Prefix=prefix, Bucket=self.bucket, MaxKeys=100, Marker=marker,
                                            Delimiter="/")
            for item in resp.get("CommonPrefixes", []):
                dir_list.append(item)
            for item in resp.get("Contents", []):
                file_list.append(item)
            if resp.get("IsTruncated"):
                marker = resp.get("NextMarker")
            else:
                break
        return file_list, dir_list

    def download(self, source, dest):
        return self.client.download_file(self.bucket, source, dest)

    def upload(self, source, dest):
        self.client.upload_file(source, self.bucket, dest)

    def close(self):
        self.client.close()

    def get_object(self, object_key):
        object = self.client.get_object(Bucket=self.bucket, Key=object_key)
        file_content = object['Body'].read()
        return file_content
