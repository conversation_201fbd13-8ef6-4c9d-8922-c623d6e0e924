import datetime
import json
from importlib import import_module

from django.db.models import Q
from django.http import HttpResponse
from django.views.decorators.http import require_POST

import cron_task
from basic.services import get_global_kv
from basic.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Page<PERSON>tils, <PERSON><PERSON><PERSON>ncoder, <PERSON><PERSON><PERSON><PERSON><PERSON>
from cron_task.handlers.log_parsers.host_process_inner import model_model_map
from cron_task.models import OssFileList, E2eMcapInfo, CronJira2Mysql
from dataview.services import reform_acu_top_chart


# Create your views here.
@require_POST
def queryTopOverview(request):
    # 获取到VIN， 查询的起止时间， 还要获取指标。
    req = json.loads(request.body.decode("UTF-8"))

    columns = ""
    for item in req['indicator']:
        columns = f"{columns}, avg({item}) as {item}"

    sql = f"""
        select 
            record_time {columns}
        from dataview_top_overview
        where vin = %s
        and record_time >= %s
        and record_time <= %s
        group by record_time
        order by record_time
        """
    result = SqlUtil.query_all_dict(sql, (req['vin'], req['record_time_start'], req['record_time_end']))
    result = reform_acu_top_chart(result, req['indicator'])
    return HttpResponse(json.dumps(result), content_type='application/json')

@require_POST
def queryTopCpus(request):
    req = json.loads(request.body.decode("UTF-8"))

    columns = ""
    for item in req['indicator']:
        columns = f"{columns}, avg({item}) as {item}"
    # 如果是平均值，
    sql = f"""
           select 
               record_time {columns}
           from dataview_top_cpu
           where vin = %s
           and cpu_idx = %s
           and record_time >= %s
           and record_time <= %s
           group by record_time
           order by record_time
           """
    result = SqlUtil.query_all_dict(sql, (
        req['vin'], req['cpu'],  req['record_time_start'], req['record_time_end']))

    result = reform_acu_top_chart(result, req['indicator'])
    return HttpResponse(json.dumps(result), content_type='application/json')

@require_POST
def queryTopProcess(request):
    req = json.loads(request.body.decode("UTF-8"))
    columns = ""
    for item in req['indicator']:
        columns += f"avg({item}) as {item},"

    sql = f"""
        select 
            record_time, {columns[0:-1]}
        from dataview_top_progress
        where vin = %s
        and record_time >= %s
        and record_time <= %s
        and command like '{req['progress']}%%'
        group by record_time
        order by record_time
        """
    result = SqlUtil.query_all_dict(sql, (req['vin'],  req['record_time_start'], req['record_time_end']))
    result = reform_acu_top_chart(result, req['indicator'])
    return HttpResponse(json.dumps(result), content_type='application/json')


@require_POST
def queryCommonAnylizeApi(request):
    req = json.loads(request.body.decode("UTF-8"))
    chart_config = json.loads(get_global_kv(req.get("chartConfig")))
    # 获取表名：
    module = import_module(f".models", cron_task.__name__)
    table_name = getattr(module, chart_config.get("chart_config")[req.get("index")].get("name"))._meta.db_table
    columns = ""
    for item in req['indicator']:
        columns += f", round(avg({item}),4) as {item}"
    sql = f"""
            select 
                record_time {columns}
            from {table_name}
            where vin = %s
            and record_time >= %s
            and record_time <= %s
            group by record_time
            order by record_time
            """
    result = SqlUtil.query_all_dict(sql, (req['vin'], req['record_time_start'], req['record_time_end']))
    result = reform_acu_top_chart(result, req['indicator'])
    return HttpResponse(json.dumps(result), content_type='application/json')


@require_POST
def queryHostAllCpuMpstatUsage(request):
    req = json.loads(request.body.decode("UTF-8"))
    cloumns = ",".join(req['indicator'])
    # 如果是平均值，
    sql = f"""
           select 
               record_time, {cloumns}
           from dataview_host_all_cpu_mpstat
           where vin = %s
           and cpu_idx = %s
           and record_time >= %s
           and record_time <= %s
           order by record_time
           """
    result = SqlUtil.query_all_dict(sql, (
        req['vin'], req['cpu'],  req['record_time_start'], req['record_time_end']))

    result = reform_acu_top_chart(result, req['indicator'])
    return HttpResponse(json.dumps(result), content_type='application/json')



@require_POST
def getLogFileListPage(request):
    """
    分页查询 日志文件
    """
    req = json.loads(request.body.decode("UTF-8"))
    condition = OrmFilter.and_condition(OssFileList, req, 1)
    condition.children.append(('is_delete', 0))
    files = OssFileList.objects.filter(condition).order_by("-create_time")
    result = PageUtils.page(files, req)
    return HttpResponse(json.dumps(result, cls=JsonEncoder), content_type='application/json')

@require_POST
def insertOssFile(request):
    """
    插入文件信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    req_oss_path = req.get("oss_path", "")
    bucket = None
    oss_path = None
    path_arr = req_oss_path.split("/")
    if req_oss_path.startswith('s3://'):
        bucket = path_arr[2]
        oss_path = "/".join(path_arr[3:])
    elif req_oss_path.startswith('http'):
        bucket = path_arr[3]
        oss_path = "/".join(path_arr[4:])
    ossFileList = OssFileList.objects.filter(oss_path=oss_path, is_delete=0).first()
    if ossFileList is not None or bucket is None:
        return HttpResponse("{}", content_type='application/json')
    req["bucket_name"] = bucket
    req["oss_path"] = oss_path
    req['file_update_time'] = datetime.datetime.now()
    req['file_size'] = 111
    req['current_status'] = 0
    req['update_time'] = datetime.datetime.now()
    req['create_time'] = datetime.datetime.now()
    ossFileList = OssFileList(**req)
    ossFileList.save()
    return HttpResponse(json.dumps(JsonUtils.convert2Json(ossFileList)), content_type='application/json')


@require_POST
def rerunLogParse(request):
    """
    删除上传文件信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    OssFileList.objects.filter(id=req["id"]).update(current_status=0)
    return HttpResponse(json.dumps("{}"), content_type='application/json')


@require_POST
def queryHostProcessInnerApi(request):
    """
    查询进程分析的数据
    """
    req = json.loads(request.body.decode("UTF-8"))
    columns = ""
    for item in req['indicator']:
        columns += f"sum({item}) as {item},"
    table_name = model_model_map.get(req['module_name'])._meta.db_table
    # 先查询所有的指标， 指标是指的进程名称+pid， 然后横向展开
    sql = f""" select record_time, pid, command, avg({req["indicator"]}) ind_avg from {table_name} where vin = %s and record_time >= %s
        and record_time <= %s group by record_time, pid, command order by record_time asc"""
    result = SqlUtil.query_all_dict(sql, (req['vin'], req['record_time_start'], req['record_time_end']))

    # 重组数据
    indicator = list(set([f"{item['pid']}-{item['command']}" for item in result]))
    # 重组数据
    reform_data = {}
    chart_data = []
    for item in result:
        record_time = item["record_time"]
        current_entity = {}
        if record_time not in reform_data:
            current_entity["record_time"] = item["record_time"]
            reform_data[record_time] = current_entity
            chart_data.append(current_entity)
        else:
            current_entity = reform_data[record_time]
        current_entity[f"{item['pid']}-{item['command']}"] = item["ind_avg"]
    result = reform_acu_top_chart(chart_data, indicator)

    return HttpResponse(json.dumps(result), content_type='application/json')

