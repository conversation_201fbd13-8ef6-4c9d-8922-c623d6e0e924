import re

text_block_key = {
    1: "page",
    2: "text",
    3: "heading1",
    4: "heading2",
    5: "heading3",
    6: "heading4",
    7: "heading5",
    8: "heading6",
    9: "heading7",
    10: "heading8",
    11: "heading9",
    12: "bullet",
    13: "ordered",
    14: "code",
    15: "quote",
    16: "equation",
    17: "todo",
}

regexp_str = '\\{\\{[a-z0-9A-Z,_.()=/:%&#? \\[\\]\\-\u4e00-\u9fa5]+\\}\\}'
# regexp_str = '\\{\\{[a-z0-9A-Z,_.()=/: \\[\\]\\-\u4e00-\u9fa5]+\\}\\}'
args_pattern = re.compile(regexp_str)

class Parser:
    def __init__(self, block):
        self.block = block
        self.key = ""


    def apply(self):
        if self.block is None:
            return None
        if self.block.get('block_type', "") not in text_block_key:
            # 如果无法获取到
            return None
        self.key = text_block_key[self.block.get('block_type')]
        return self.exec()

    def exec(self):
        elements = self.block.get(self.key, {}).get('elements', [])
        match_info = ""
        for item in elements:
            text_content = item.get("text_run", {}).get('content', None)
            if text_content :
                # 如果文本中存在需要替换的变量，放到数组里面去
                match_info = f"{match_info}{text_content}"
        if not self.check_placeholder(match_info):
            match_info = ""
        if match_info.strip():
            return {
                "block_id": self.block.get("block_id", ""),
                "content": match_info.strip(),
                "parent_id": self.block.get("parent_id", ""),
            }
        return None

    def check_placeholder(self, content):
        result = args_pattern.search(content)
        return result is not None

