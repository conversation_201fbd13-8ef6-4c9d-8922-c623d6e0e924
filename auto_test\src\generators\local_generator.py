import os
from datetime import datetime
from typing import List, Optional, Dict
from src.generators.base_generator import BaseGenerator
from src.utils.logger import get_logger, log_success, log_error, log_warning, log_info
from src.utils.exceptions import FileOperationError

logger = get_logger(__name__)


class LocalGenerator(BaseGenerator):
    """本地报告生成器"""
    
    def __init__(self, config, jira_client):
        """初始化本地生成器"""
        super().__init__(config, jira_client)
    
    def generate(self, issues: List, route_info: Optional[Dict] = None, algorithm_stats: Optional[Dict] = None) -> Optional[str]:
        """生成本地报告"""
        if not issues:
            logger.warning("没有可用数据生成报告")
            return None
        
        try:
            report = self._generate_common_report_header(issues, route_info)
            report += self._format_issue_table_with_algorithm_labels(issues, route_info, algorithm_stats)
            report += self._generate_route_statistics(issues, route_info)
            report += self._generate_quality_metrics(issues, route_info)
            report += self._generate_issue_list(issues, algorithm_stats)  # 添加问题列表
            
            # 保存报告
            filename = self._save_report(report)
            if filename:
                logger.info(f"本地报告已保存到: {filename}")
                return filename
            else:
                logger.error("保存本地报告失败")
                return None
                
        except Exception as e:
            logger.error(f"生成本地报告失败: {str(e)}")
            raise FileOperationError(f"生成本地报告失败: {str(e)}")
    
    def _generate_common_report_header(self, issues: List, route_info: Optional[Dict] = None) -> str:
        """生成通用报告头部"""
        report = "# 自动测试报告\n\n"
        report += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 添加路线信息
        if route_info and route_info.get('total_distance', 0) > 0:
            report += "## 1. 测试路线信息\n\n"
            report += f"**总测试里程**: {route_info['total_distance']:.1f} 公里\n\n"
            report += "| 序号 | 路线名称 | 公里数 | 测试场景 |\n"
            report += "|------|----------|--------|----------|\n"
            for i, route in enumerate(route_info.get('routes', []), 1):
                report += f"| {i} | {route['name']} | {route['distance']:.1f} | {route['scenario']} |\n"
            report += "\n"
        
        report += f"**问题总数**: {len(issues)}\n\n"
        report += "---\n\n"
        
        return report
    
    def _format_issue_table_with_algorithm_labels(self, issues: List, route_info: Optional[Dict] = None, algorithm_stats: Optional[Dict] = None) -> str:
        """使用算法标签格式化问题表格"""
        if not issues:
            return ""
        
        total_distance = route_info.get('total_distance', 0) if route_info else 0
        
        report = "## 2. 算法问题分类统计\n\n"
        report += "| 序号 | 算法问题类型 | 数量 | 占比 | 问题密度(个/百公里) | 平均无问题里程(公里) | 问题链接 |\n"
        report += "|------|--------------|------|------|-------------------|-------------------|----------|\n"
        
        jira_url = self.get_config('Jira', 'url')
        
        if algorithm_stats and algorithm_stats.get('categories'):
            total_algorithm_issues = algorithm_stats.get('total_algorithm_issues', 0)
            multi_label_count = len(algorithm_stats.get('multi_label_issues', []))
            
            for i, (category, data) in enumerate(algorithm_stats['categories'].items(), 1):
                count = data['count']
                percentage = (count / len(issues)) * 100 if len(issues) > 0 else 0
                
                # 计算问题密度 (个/百公里)
                if total_distance > 0:
                    density_per_100km = (count / total_distance) * 100
                    density_str = f"{density_per_100km:.1f}"
                else:
                    density_str = "无数据"
                
                # 计算平均无问题里程
                if count > 0 and total_distance > 0:
                    avg_distance_between_issues = total_distance / count
                    avg_distance_str = f"{avg_distance_between_issues:.1f}"
                else:
                    avg_distance_str = "无问题"
                
                # 生成问题链接列表
                issue_links = []
                for issue in data['issues']:
                    issue_link = f"{jira_url}/browse/{issue['key']}"
                    issue_links.append(f"[{issue['key']}]({issue_link})")
                links_str = ", ".join(issue_links)
                
                report += f"| {i} | {category} | {count} | {percentage:.1f}% | {density_str} | {avg_distance_str} | {links_str} |\n"
            
            # 添加总计行
            if multi_label_count > 0:
                report += f"| **总计** | **多标签问题总计** | {multi_label_count} | {(multi_label_count / len(issues)) * 100:.1f}% | - | - | - |\n"
        else:
            # 如果没有算法标签，使用默认分类
            # 生成所有问题的链接
            issue_links = []
            for issue in issues:
                issue_link = f"{jira_url}/browse/{issue.key}"
                issue_links.append(f"[{issue.key}]({issue_link})")
            links_str = ", ".join(issue_links)
            
            report += f"| 1 | 未分类问题 | {len(issues)} | 100.0% | - | - | {links_str} |\n"
        
        report += "\n"
        
        # 添加多标签问题详情
        if algorithm_stats and algorithm_stats.get('multi_label_issues'):
            report += "### 2.1 多标签问题详情\n\n"
            report += "以下问题包含多个算法问题标签：\n\n"
            report += "| 序号 | 问题Key | 摘要 | 所有算法标签 | 链接 |\n"
            report += "|------|---------|------|-------------|------|\n"
            
            for i, issue in enumerate(algorithm_stats['multi_label_issues'], 1):
                summary = issue['summary'][:50] + "..." if len(issue['summary']) > 50 else issue['summary']
                labels_str = ", ".join(issue['all_algorithm_labels'])
                issue_link = f"{jira_url}/browse/{issue['key']}"
                report += f"| {i} | **{issue['key']}** | {summary} | {labels_str} | **[查看]({issue_link})** |\n"
            
            report += "\n"
        
        return report
    
    def _format_issue_table(self, issues: List, route_info: Optional[Dict] = None) -> str:
        """格式化问题表格（兼容旧方法）"""
        return self._format_issue_table_with_algorithm_labels(issues, route_info, None)
    
    def _extract_issue_type(self, issue) -> str:
        """提取问题类型（保留兼容性）"""
        # 这里保留原有的问题类型提取逻辑，作为备用
        summary = issue.fields.summary.lower()
        
        # 简单的关键词匹配
        if any(keyword in summary for keyword in ['算法', 'algorithm', 'algo']):
            return "算法问题"
        elif any(keyword in summary for keyword in ['性能', 'performance', 'perf']):
            return "性能问题"
        elif any(keyword in summary for keyword in ['界面', 'ui', 'ux', '界面']):
            return "界面问题"
        elif any(keyword in summary for keyword in ['数据', 'data', 'database']):
            return "数据问题"
        elif any(keyword in summary for keyword in ['网络', 'network', 'connection']):
            return "网络问题"
        else:
            return "其他问题"
    
    def _generate_route_statistics(self, issues: List, route_info: Optional[Dict] = None) -> str:
        """生成路线统计信息"""
        if not route_info or route_info.get('total_distance', 0) <= 0:
            return ""

        report = "## 3. 测试统计概览\n\n"
        report += f"**问题总数**: {len(issues)} 个\n"
        report += f"**测试路线数**: {len(route_info.get('routes', []))} 条\n"

        report += "\n"
        return report
    
    def _generate_quality_metrics(self, issues: List, route_info: Optional[Dict] = None) -> str:
        """生成质量评估指标"""
        if not issues:
            return ""
        
        total_distance = route_info.get('total_distance', 0) if route_info else 0
        total_issues = len(issues)
        
        report = "## 4. 质量评估指标\n\n"
        
        # 计算问题密度
        if total_distance > 0:
            issue_density = (total_issues / total_distance) * 100  # 个/百公里
            report += f"**问题密度**: {issue_density:.2f} 个/百公里\n"
        else:
            issue_density = 0
            report += "**问题密度**: 无数据\n"
        
        # 计算平均无问题里程
        if total_issues > 0 and total_distance > 0:
            avg_distance_between_issues = total_distance / total_issues
            report += f"**平均无问题里程**: {avg_distance_between_issues:.1f} 公里\n"
        else:
            report += "**平均无问题里程**: 无问题\n"
        
        # 计算质量评分 - 优化版本
        # 参考软件测试行业标准和自动驾驶安全要求
        # 基准：10个/百公里为良好水平，5个/百公里为优秀水平

        if issue_density == 0:
            # 零缺陷：满分
            quality_score = 100
        elif issue_density <= 5:
            # 0-5个/百公里：优秀区间，使用对数衰减保持高分
            quality_score = 100 - (issue_density / 5) * 15  # 85-100分
        elif issue_density <= 10:
            # 5-10个/百公里：良好区间，线性衰减
            quality_score = 85 - ((issue_density - 5) / 5) * 25  # 60-85分
        elif issue_density <= 20:
            # 10-20个/百公里：正常区间，加速衰减
            quality_score = 60 - ((issue_density - 10) / 10) * 30  # 30-60分
        else:
            # >20个/百公里：快速衰减到0分，考虑安全风险
            excess = issue_density - 20
            quality_score = max(0, 30 - excess * 1.5)
        
        report += f"**质量评分**: {quality_score:.1f}/100\n"
        

        
        # 统计置信度
        confidence = self._calculate_confidence(total_distance, total_issues)
        report += f"**统计置信度**: {confidence:.1f}%\n"
        
        # 测试覆盖率 - 基于路线复杂度和里程的综合评估
        if total_distance > 0:
            # 基础覆盖率：基于里程（200公里为完整覆盖基准）
            base_coverage = min(80, (total_distance / 200) * 80)
            # 场景复杂度加成：基于路线数量和场景多样性
            route_count = len(route_info.get('routes', [])) if route_info else 1
            scenario_bonus = min(20, route_count * 5)  # 每条路线加5%，最多20%
            coverage = min(100, base_coverage + scenario_bonus)
            report += f"**测试覆盖率**: {coverage:.1f}%\n"
        else:
            coverage = 0
            report += "**测试覆盖率**: 无数据\n"

        # 问题检出率 - 基于问题密度的检出效率评估
        if total_distance > 0:
            # 基于问题密度评估检出率：密度越高说明检出能力越强
            issue_density = (total_issues / total_distance) * 100
            if issue_density == 0:
                detection_rate = 20  # 零问题可能是检出不足
            elif issue_density <= 5:
                detection_rate = 60 + issue_density * 8  # 5个/百公里时达到100%
            elif issue_density <= 15:
                detection_rate = 100  # 最佳检出区间
            else:
                detection_rate = max(70, 100 - (issue_density - 15) * 2)  # 过多问题可能有遗漏
            report += f"**问题检出率**: {detection_rate:.1f}%\n"
        else:
            detection_rate = 0
            report += "**问题检出率**: 0%\n"
        
        report += "\n"
        
        # 添加计算公式说明
        report += "### 4.1 计算公式\n\n"
        report += "```\n"
        report += "问题密度 = (问题总数 / 总测试里程) × 100 (个/百公里)\n"
        report += "质量评分：0个=100分，≤5个=85-100分，5-10个=60-85分，10-20个=30-60分，>20个快速衰减\n"
        report += "统计置信度 = (里程因子×0.4 + 分布因子×0.4 + 样本因子×0.2) × 100%\n"
        report += "测试覆盖率 = min(100%, (里程/200km)×80% + 路线数×5%)\n"
        report += "问题检出率：基于问题密度评估，5-15个/百公里为最佳检出区间\n"
        report += "```\n\n"
        
        
        return report
    
    def _generate_issue_list(self, issues: List, algorithm_stats: Optional[Dict] = None) -> str:
        """生成问题列表"""
        if not issues:
            return ""
        
        report = "## 5. 问题详细列表\n\n"
        report += "| 序号 | 问题Key | 摘要 | 状态 | 链接 |\n"
        report += "|------|---------|------|------|------|\n"
        
        jira_url = self.get_config('Jira', 'url')
        
        # 创建问题key到多标签信息的映射
        multi_label_map = {}
        if algorithm_stats and algorithm_stats.get('multi_label_issues'):
            for issue in algorithm_stats['multi_label_issues']:
                multi_label_map[issue['key']] = issue['all_algorithm_labels']
        
        for i, issue in enumerate(issues, 1):
            issue_link = f"{jira_url}/browse/{issue.key}"
            summary = getattr(issue.fields, 'summary', 'N/A')
            status = getattr(issue.fields, 'status', 'N/A')
            
            # 截断过长的摘要
            if len(summary) > 60:
                summary = summary[:57] + "..."
            
            # 获取状态名称
            if hasattr(status, 'name'):
                status_name = status.name
            else:
                status_name = str(status)
            
            # 检查是否为多标签问题
            if issue.key in multi_label_map:
                labels_str = ", ".join(multi_label_map[issue.key])
                report += f"| {i} | **{issue.key}** | {summary} | {status_name} | **[查看]({issue_link})** (多标签: {labels_str}) |\n"
            else:
                report += f"| {i} | {issue.key} | {summary} | {status_name} | [查看]({issue_link}) |\n"
        
        report += "\n"

        # 添加参考标准
        report += "---\n\n"
        report += "## 参考标准\n\n"
        report += "**汽车行业标准**: ISO 26262 (功能安全), Automotive SPICE (过程改进)\n"
        report += "**软件测试标准**: IEEE 829 (测试文档), IEEE 1044 (异常分类)\n"
        report += "**质量管理标准**: ISO 9001 (质量管理), CMMI (能力成熟度)\n\n"

        return report
    

    def _calculate_confidence(self, distance: float, issues: int) -> float:
        """计算统计置信度 - 优化版本"""
        if distance <= 0:
            return 0.0

        # 里程因子：考虑测试覆盖度，使用对数函数避免过度依赖里程
        # 50公里达到80%置信度，100公里达到90%，200公里达到95%
        import math
        distance_factor = min(0.95, 0.6 + 0.35 * math.log10(distance / 10))

        # 问题分布因子：考虑问题密度的合理性
        issue_density = (issues / distance) * 100 if distance > 0 else 0
        if issue_density == 0:
            # 零问题：需要足够里程才有高置信度
            density_factor = 0.7 if distance >= 50 else 0.4
        elif 1 <= issue_density <= 15:
            # 合理问题密度：高置信度
            density_factor = 1.0
        elif issue_density <= 30:
            # 问题较多但可接受：中等置信度
            density_factor = 0.8
        else:
            # 问题过多：低置信度，可能存在系统性问题
            density_factor = 0.5

        # 样本量因子：考虑统计学意义
        sample_factor = min(1.0, (distance * issue_density) / 100)  # 至少需要一定的数据量

        # 综合置信度：加权平均
        confidence = (distance_factor * 0.4 + density_factor * 0.4 + sample_factor * 0.2) * 100
        return min(100.0, max(10.0, confidence))  # 置信度范围：10%-100%
    
    def _save_report(self, report_content: str) -> Optional[str]:
        """保存报告到文件"""
        try:
            output_dir = self.get_config('Report', 'output_dir', 'reports')
            file_format = self.get_config('Report', 'file_format', 'md')
            include_timestamp = self.get_config_boolean('Report', 'include_timestamp', True)
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if include_timestamp:
                filename = f"algorithm_analysis_report_{timestamp}.{file_format}"
            else:
                filename = f"algorithm_analysis_report.{file_format}"
            
            file_path = os.path.join(output_dir, filename)
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            return file_path
            
        except Exception as e:
            logger.error(f"保存报告失败: {str(e)}")
            return None
    
    def generate_simple_report(self, issues: List, route_info: Optional[Dict] = None, algorithm_stats: Optional[Dict] = None) -> Optional[str]:
        """生成简化报告"""
        if not issues:
            logger.warning("没有可用数据生成报告")
            return None
        
        try:
            report = self._generate_common_report_header(issues, route_info)
            report += self._generate_simple_statistics(issues)
            report += self._format_issue_table_with_algorithm_labels(issues, route_info, algorithm_stats)
            report += self._generate_route_statistics(issues, route_info)
            report += self._generate_quality_metrics(issues, route_info)
            report += self._generate_issue_list(issues, algorithm_stats)  # 添加问题列表
            
            # 保存报告
            filename = self._save_report(report)
            if filename:
                logger.info(f"简化报告已保存到: {filename}")
                return filename
            else:
                logger.error("保存简化报告失败")
                return None
                
        except Exception as e:
            logger.error(f"生成简化报告失败: {str(e)}")
            raise FileOperationError(f"生成简化报告失败: {str(e)}")
    
    def _generate_simple_statistics(self, issues: List) -> str:
        """生成简化统计信息"""
        if not issues:
            return ""
        
        report = "## 问题统计概览\n\n"
        report += f"**总问题数**: {len(issues)}\n"
        
        # 按状态统计
        status_stats = {}
        for issue in issues:
            status = issue.fields.status.name if issue.fields.status else 'Unknown'
            status_stats[status] = status_stats.get(status, 0) + 1
        
        report += "\n**按状态统计**:\n"
        for status, count in sorted(status_stats.items()):
            percentage = (count / len(issues)) * 100
            report += f"- {status}: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n"
        return report 