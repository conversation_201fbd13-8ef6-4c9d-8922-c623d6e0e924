import os
from datetime import datetime
from typing import List, Optional, Dict
from src.generators.base_generator import BaseGenerator
from src.utils.logger import get_logger, log_success, log_error, log_warning, log_info
from src.utils.exceptions import FileOperationError

logger = get_logger(__name__)


class LocalGenerator(BaseGenerator):
    """本地报告生成器"""
    
    def __init__(self, config, jira_client):
        """初始化本地生成器"""
        super().__init__(config, jira_client)
    
    def generate(self, issues: List, route_info: Optional[Dict] = None, algorithm_stats: Optional[Dict] = None) -> Optional[str]:
        """生成本地报告"""
        if not issues:
            logger.warning("没有可用数据生成报告")
            return None
        
        try:
            report = self._generate_common_report_header(issues, route_info)
            report += self._format_issue_table_with_algorithm_labels(issues, route_info, algorithm_stats)
            report += self._generate_route_statistics(issues, route_info)
            report += self._generate_quality_metrics(issues, route_info)
            report += self._generate_issue_list(issues, algorithm_stats)  # 添加问题列表
            
            # 保存报告
            filename = self._save_report(report)
            if filename:
                logger.info(f"本地报告已保存到: {filename}")
                return filename
            else:
                logger.error("保存本地报告失败")
                return None
                
        except Exception as e:
            logger.error(f"生成本地报告失败: {str(e)}")
            raise FileOperationError(f"生成本地报告失败: {str(e)}")
    
    def _generate_common_report_header(self, issues: List, route_info: Optional[Dict] = None) -> str:
        """生成通用报告头部"""
        report = "# 自动测试报告\n\n"
        report += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 添加路线信息
        if route_info and route_info.get('total_distance', 0) > 0:
            report += "## 1. 测试路线信息\n\n"
            report += f"**总测试里程**: {route_info['total_distance']:.1f} 公里\n\n"
            report += "| 序号 | 路线名称 | 公里数 | 测试场景 |\n"
            report += "|------|----------|--------|----------|\n"
            for i, route in enumerate(route_info.get('routes', []), 1):
                report += f"| {i} | {route['name']} | {route['distance']:.1f} | {route['scenario']} |\n"
            report += "\n"
        
        report += f"**问题总数**: {len(issues)}\n\n"
        report += "---\n\n"
        
        return report
    
    def _format_issue_table_with_algorithm_labels(self, issues: List, route_info: Optional[Dict] = None, algorithm_stats: Optional[Dict] = None) -> str:
        """使用算法标签格式化问题表格"""
        if not issues:
            return ""
        
        total_distance = route_info.get('total_distance', 0) if route_info else 0
        
        report = "## 2. 算法问题分类统计\n\n"
        report += "| 序号 | 算法问题类型 | 数量 | 占比 | 问题密度(个/百公里) | 平均无问题里程(公里) | 问题链接 |\n"
        report += "|------|--------------|------|------|-------------------|-------------------|----------|\n"
        
        jira_url = self.get_config('Jira', 'url')
        
        if algorithm_stats and algorithm_stats.get('categories'):
            total_algorithm_issues = algorithm_stats.get('total_algorithm_issues', 0)
            multi_label_count = len(algorithm_stats.get('multi_label_issues', []))
            
            for i, (category, data) in enumerate(algorithm_stats['categories'].items(), 1):
                count = data['count']
                percentage = (count / len(issues)) * 100 if len(issues) > 0 else 0
                
                # 计算问题密度 (个/百公里)
                if total_distance > 0:
                    density_per_100km = (count / total_distance) * 100
                    density_str = f"{density_per_100km:.1f}"
                else:
                    density_str = "无数据"
                
                # 计算平均无问题里程
                if count > 0 and total_distance > 0:
                    avg_distance_between_issues = total_distance / count
                    avg_distance_str = f"{avg_distance_between_issues:.1f}"
                else:
                    avg_distance_str = "无问题"
                
                # 生成问题链接列表
                issue_links = []
                for issue in data['issues']:
                    issue_link = f"{jira_url}/browse/{issue['key']}"
                    issue_links.append(f"[{issue['key']}]({issue_link})")
                links_str = ", ".join(issue_links)
                
                report += f"| {i} | {category} | {count} | {percentage:.1f}% | {density_str} | {avg_distance_str} | {links_str} |\n"
            
            # 添加总计行
            if multi_label_count > 0:
                report += f"| **总计** | **多标签问题总计** | {multi_label_count} | {(multi_label_count / len(issues)) * 100:.1f}% | - | - | - |\n"
        else:
            # 如果没有算法标签，使用默认分类
            # 生成所有问题的链接
            issue_links = []
            for issue in issues:
                issue_link = f"{jira_url}/browse/{issue.key}"
                issue_links.append(f"[{issue.key}]({issue_link})")
            links_str = ", ".join(issue_links)
            
            report += f"| 1 | 未分类问题 | {len(issues)} | 100.0% | - | - | {links_str} |\n"
        
        report += "\n"
        
        # 添加多标签问题详情
        if algorithm_stats and algorithm_stats.get('multi_label_issues'):
            report += "### 2.1 多标签问题详情\n\n"
            report += "以下问题包含多个算法问题标签：\n\n"
            report += "| 序号 | 问题Key | 摘要 | 所有算法标签 | 链接 |\n"
            report += "|------|---------|------|-------------|------|\n"
            
            for i, issue in enumerate(algorithm_stats['multi_label_issues'], 1):
                summary = issue['summary'][:50] + "..." if len(issue['summary']) > 50 else issue['summary']
                labels_str = ", ".join(issue['all_algorithm_labels'])
                issue_link = f"{jira_url}/browse/{issue['key']}"
                report += f"| {i} | **{issue['key']}** | {summary} | {labels_str} | **[查看]({issue_link})** |\n"
            
            report += "\n"
        
        return report
    
    def _format_issue_table(self, issues: List, route_info: Optional[Dict] = None) -> str:
        """格式化问题表格（兼容旧方法）"""
        return self._format_issue_table_with_algorithm_labels(issues, route_info, None)
    
    def _extract_issue_type(self, issue) -> str:
        """提取问题类型（保留兼容性）"""
        # 这里保留原有的问题类型提取逻辑，作为备用
        summary = issue.fields.summary.lower()
        
        # 简单的关键词匹配
        if any(keyword in summary for keyword in ['算法', 'algorithm', 'algo']):
            return "算法问题"
        elif any(keyword in summary for keyword in ['性能', 'performance', 'perf']):
            return "性能问题"
        elif any(keyword in summary for keyword in ['界面', 'ui', 'ux', '界面']):
            return "界面问题"
        elif any(keyword in summary for keyword in ['数据', 'data', 'database']):
            return "数据问题"
        elif any(keyword in summary for keyword in ['网络', 'network', 'connection']):
            return "网络问题"
        else:
            return "其他问题"
    
    def _generate_route_statistics(self, issues: List, route_info: Optional[Dict] = None) -> str:
        """生成路线统计信息"""
        if not route_info or route_info.get('total_distance', 0) <= 0:
            return ""
        
        report = "## 3. 测试路线统计\n\n"
        report += f"**总测试里程**: {route_info['total_distance']:.1f} 公里\n"
        report += f"**问题总数**: {len(issues)} 个\n"
        
        if len(issues) > 0:
            avg_issues_per_km = len(issues) / route_info['total_distance']
            report += f"**平均问题密度**: {avg_issues_per_km:.3f} 个/公里\n"
        
        report += f"**测试路线数**: {len(route_info.get('routes', []))} 条\n\n"
        
        # 路线详情
        report += "### 3.1 路线详情\n\n"
        report += "| 序号 | 路线名称 | 公里数 | 测试场景 |\n"
        report += "|------|----------|--------|----------|\n"
        
        for i, route in enumerate(route_info.get('routes', []), 1):
            report += f"| {i} | {route['name']} | {route['distance']:.1f} | {route['scenario']} |\n"
        
        report += "\n"
        return report
    
    def _generate_quality_metrics(self, issues: List, route_info: Optional[Dict] = None) -> str:
        """生成质量评估指标"""
        if not issues:
            return ""
        
        total_distance = route_info.get('total_distance', 0) if route_info else 0
        total_issues = len(issues)
        
        report = "## 4. 质量评估指标\n\n"
        
        # 计算问题密度
        if total_distance > 0:
            issue_density = (total_issues / total_distance) * 100  # 个/百公里
            report += f"**问题密度**: {issue_density:.2f} 个/百公里\n"
        else:
            issue_density = 0
            report += "**问题密度**: 无数据\n"
        
        # 计算平均无问题里程
        if total_issues > 0 and total_distance > 0:
            avg_distance_between_issues = total_distance / total_issues
            report += f"**平均无问题里程**: {avg_distance_between_issues:.1f} 公里\n"
        else:
            report += "**平均无问题里程**: 无问题\n"
        
        # 计算质量评分
        
        # 新的评分逻辑：20个/百公里为正常水平(50分)，0个/百公里为满分(100分)
        if issue_density <= 20:
            # 0-20个/百公里：线性映射到50-100分
            quality_score = 100 - (issue_density / 20) * 50
        else:
            # 超过20个/百公里：快速衰减到0分
            quality_score = max(0, 50 - (issue_density - 20) * 2.5)
        
        report += f"**质量评分**: {quality_score:.1f}/100\n"
        
        # 质量等级
        quality_level = self._get_quality_level(quality_score)
        report += f"**质量等级**: {quality_level}\n"
        
        # 统计置信度
        confidence = self._calculate_confidence(total_distance, total_issues)
        report += f"**统计置信度**: {confidence:.1f}%\n"
        
        # 测试覆盖率
        if total_distance > 0:
            coverage = min(100, (total_distance / 100) * 100)  # 假设100公里为完整覆盖
            report += f"**测试覆盖率**: {coverage:.1f}%\n"
        else:
            report += "**测试覆盖率**: 无数据\n"
        
        # 问题检出率
        if total_issues > 0:
            detection_rate = min(100, (total_issues / 10) * 100)  # 假设10个问题为高检出率
            report += f"**问题检出率**: {detection_rate:.1f}%\n"
        else:
            report += "**问题检出率**: 0%\n"
        
        report += "\n"
        
        # 添加质量评分计算公式说明
        report += "### 4.1 质量评分计算公式\n\n"
        report += "```\n"
        report += "当问题密度 ≤ 20个/百公里时：\n"
        report += "  质量评分 = 100 - (问题密度 / 20) × 50\n"
        report += "当问题密度 > 20个/百公里时：\n"
        report += "  质量评分 = max(0, 50 - (问题密度 - 20) × 2.5)\n"
        report += "其中：问题密度 = (问题总数 / 总测试里程) × 100 (个/百公里)\n"
        report += "```\n\n"
        report += "**评分说明**:\n"
        report += "- 问题密度为0时，质量评分为100分（优秀）\n"
        report += "- 问题密度为20个/百公里时，质量评分为50分（正常）\n"
        report += "- 问题密度为40个/百公里时，质量评分为0分（差）\n"
        report += "- 评分范围：0-100分\n\n"
        
        
        return report
    
    def _generate_issue_list(self, issues: List, algorithm_stats: Optional[Dict] = None) -> str:
        """生成问题列表"""
        if not issues:
            return ""
        
        report = "## 5. 问题详细列表\n\n"
        report += "| 序号 | 问题Key | 摘要 | 状态 | 链接 |\n"
        report += "|------|---------|------|------|------|\n"
        
        jira_url = self.get_config('Jira', 'url')
        
        # 创建问题key到多标签信息的映射
        multi_label_map = {}
        if algorithm_stats and algorithm_stats.get('multi_label_issues'):
            for issue in algorithm_stats['multi_label_issues']:
                multi_label_map[issue['key']] = issue['all_algorithm_labels']
        
        for i, issue in enumerate(issues, 1):
            issue_link = f"{jira_url}/browse/{issue.key}"
            summary = getattr(issue.fields, 'summary', 'N/A')
            status = getattr(issue.fields, 'status', 'N/A')
            
            # 截断过长的摘要
            if len(summary) > 60:
                summary = summary[:57] + "..."
            
            # 获取状态名称
            if hasattr(status, 'name'):
                status_name = status.name
            else:
                status_name = str(status)
            
            # 检查是否为多标签问题
            if issue.key in multi_label_map:
                labels_str = ", ".join(multi_label_map[issue.key])
                report += f"| {i} | **{issue.key}** | {summary} | {status_name} | **[查看]({issue_link})** (多标签: {labels_str}) |\n"
            else:
                report += f"| {i} | {issue.key} | {summary} | {status_name} | [查看]({issue_link}) |\n"
        
        report += "\n"
        return report
    
    def _get_quality_level(self, score: float) -> str:
        """获取质量等级"""
        if score >= 80:
            return "优秀"
        elif score >= 60:
            return "良好"
        elif score >= 40:
            return "正常"
        elif score >= 20:
            return "较差"
        else:
            return "差"
    
    def _calculate_confidence(self, distance: float, issues: int) -> float:
        """计算统计置信度"""
        # 基于测试里程和问题数量的简单置信度计算
        if distance <= 0:
            return 0.0
        
        # 里程因子：里程越多，置信度越高
        distance_factor = min(1.0, distance / 100.0)  # 100公里为满分
        
        # 问题因子：问题数量适中时置信度最高
        if issues == 0:
            issue_factor = 0.5  # 无问题时置信度中等
        elif issues <= 5:
            issue_factor = 1.0  # 问题数量适中
        else:
            issue_factor = max(0.3, 1.0 - (issues - 5) * 0.1)  # 问题过多时置信度降低
        
        confidence = (distance_factor * 0.6 + issue_factor * 0.4) * 100
        return min(100.0, max(0.0, confidence))
    
    def _save_report(self, report_content: str) -> Optional[str]:
        """保存报告到文件"""
        try:
            output_dir = self.get_config('Report', 'output_dir', 'reports')
            file_format = self.get_config('Report', 'file_format', 'md')
            include_timestamp = self.get_config_boolean('Report', 'include_timestamp', True)
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if include_timestamp:
                filename = f"algorithm_analysis_report_{timestamp}.{file_format}"
            else:
                filename = f"algorithm_analysis_report.{file_format}"
            
            file_path = os.path.join(output_dir, filename)
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            return file_path
            
        except Exception as e:
            logger.error(f"保存报告失败: {str(e)}")
            return None
    
    def generate_simple_report(self, issues: List, route_info: Optional[Dict] = None, algorithm_stats: Optional[Dict] = None) -> Optional[str]:
        """生成简化报告"""
        if not issues:
            logger.warning("没有可用数据生成报告")
            return None
        
        try:
            report = self._generate_common_report_header(issues, route_info)
            report += self._generate_simple_statistics(issues)
            report += self._format_issue_table_with_algorithm_labels(issues, route_info, algorithm_stats)
            report += self._generate_route_statistics(issues, route_info)
            report += self._generate_quality_metrics(issues, route_info)
            report += self._generate_issue_list(issues, algorithm_stats)  # 添加问题列表
            
            # 保存报告
            filename = self._save_report(report)
            if filename:
                logger.info(f"简化报告已保存到: {filename}")
                return filename
            else:
                logger.error("保存简化报告失败")
                return None
                
        except Exception as e:
            logger.error(f"生成简化报告失败: {str(e)}")
            raise FileOperationError(f"生成简化报告失败: {str(e)}")
    
    def _generate_simple_statistics(self, issues: List) -> str:
        """生成简化统计信息"""
        if not issues:
            return ""
        
        report = "## 问题统计概览\n\n"
        report += f"**总问题数**: {len(issues)}\n"
        
        # 按状态统计
        status_stats = {}
        for issue in issues:
            status = issue.fields.status.name if issue.fields.status else 'Unknown'
            status_stats[status] = status_stats.get(status, 0) + 1
        
        report += "\n**按状态统计**:\n"
        for status, count in sorted(status_stats.items()):
            percentage = (count / len(issues)) * 100
            report += f"- {status}: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n"
        return report 