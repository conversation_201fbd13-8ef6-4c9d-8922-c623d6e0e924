drop table if exists basic_key_value;

create table basic_key_value
(
    id        bigint unsigned auto_increment primary key comment '主键ID',
    name      varchar(128) not null comment 'Key名称',
    value     text not null comment 'Value消息',
    remark    varchar(256) not null comment '备注',
    create_by varchar(64)   not null DEFAULT '' comment '创建者',
    create_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    update_by varchar(64)   not null DEFAULT '' comment '更新者',
    update_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    is_delete  varchar(2)  not null DEFAULT '0' comment '是否已经删除 0-未删除 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci  comment 'KeyValue存储';

drop table if exists basic_user_info;
create table basic_user_info
(
    id        bigint unsigned auto_increment primary key comment '主键ID',
    username      varchar(32) not null comment '登录用户名',
    display_name      varchar(64) not null comment '中文姓名',
    email      varchar(64) not null comment '邮箱',
    phone      varchar(32) not null comment '电话号码',
    open_id      varchar(64) not null comment '飞书OpenId',
    dept_id      bigint not null comment '部门ID',
    dept_name      varchar(128) not null comment '部门名称',
    create_by varchar(64)   not null DEFAULT '' comment '创建者',
    create_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    update_by varchar(64)   not null DEFAULT '' comment '更新者',
    update_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    is_delete  varchar(2)  not null DEFAULT '0' comment '是否已经删除 0-未删除 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci  comment '用户信息表';


drop table if exists basic_alarm_group;
create table basic_alarm_group
(
    id        bigint unsigned auto_increment primary key comment '主键ID',
    group_name      varchar(64) not null comment '登录用户名',
    user_list    varchar(512) comment '分组中的用户列表',
    remark      varchar(256) not null comment '部门名称',
    create_by varchar(64)   not null DEFAULT '' comment '创建者',
    create_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    update_by varchar(64)   not null DEFAULT '' comment '更新者',
    update_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    is_delete  varchar(2)  not null DEFAULT '0' comment '是否已经删除 0-未删除 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci  comment '告警分组信息表';


drop table if exists basic_feishu_message_info;
create table basic_feishu_message_info
(
    id        bigint unsigned auto_increment primary key comment '主键ID',
    open_id      varchar(64) not null comment '发送给哪位',
    display_name    varchar(512) comment '展示名字',
    message      text not null comment '部门名称',
    remark      text not null comment '部门名称',
    create_by varchar(64)   not null DEFAULT '' comment '创建者',
    create_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    update_by varchar(64)   not null DEFAULT '' comment '更新者',
    update_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    is_delete  varchar(2)  not null DEFAULT '0' comment '是否已经删除 0-未删除 1-已删除'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci  comment '保存消息内容';
