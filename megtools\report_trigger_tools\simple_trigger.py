#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的飞书报告触发脚本
专门用于其他程序调用

使用方法:
1. 作为模块导入:
   from simple_trigger import trigger_report
   result = trigger_report(report_id=1, vin="TEST123")

2. 命令行调用:
   python simple_trigger.py --report-id 1 --vin TEST123

3. JSON参数调用:
   python simple_trigger.py --json '{"report_id": 1, "vin": "TEST123"}'
"""

import json
import sys
import argparse
import requests
from datetime import datetime
from typing import Dict, Any, Optional


class ReportTrigger:
    """简化的报告触发器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", 
                 username: str = "admin", password: str = "password"):
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.token = None
    
    def login(self) -> bool:
        """登录到MegTool Backend"""
        try:
            response = requests.post(
                f"{self.base_url}/api/basic/loginApi",
                json={"username": self.username, "password": self.password},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 200 and data.get('data', {}).get('token'):
                    self.token = data['data']['token']
                    return True
            
            return False
        except Exception:
            return False
    
    def trigger_report(self, report_id: int, vin: str = "", 
                      start_time: str = "", end_time: str = "", 
                      custom_params: str = "", status: str = "4") -> Dict[str, Any]:
        """触发报告生成"""
        if not self.token and not self.login():
            return {"success": False, "error": "登录失败"}
        
        # 构建参数
        params_dict = {}
        if vin:
            params_dict["vin"] = vin
        if start_time:
            params_dict["start_time"] = start_time
        if end_time:
            params_dict["end_time"] = end_time
        if custom_params:
            params_dict["custom"] = custom_params
        
        params_json = json.dumps(params_dict) if params_dict else ""
        
        try:
            response = requests.post(
                f"{self.base_url}/api/report/generateFeishuReportApi",
                json={
                    "id": report_id,
                    "params": params_json,
                    "status": status
                },
                headers={"Authorization": f"Bearer {self.token}"},
                timeout=60
            )
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        
        except Exception as e:
            return {"success": False, "error": str(e)}


def trigger_report(report_id: int, vin: str = "", start_time: str = "", 
                  end_time: str = "", custom_params: str = "", status: str = "4",
                  base_url: str = "http://localhost:8000", 
                  username: str = "admin", password: str = "password") -> Dict[str, Any]:
    """便捷函数：触发报告生成
    
    Args:
        report_id: 报告ID
        vin: 车辆识别码
        start_time: 开始时间 (格式: YYYY-MM-DD HH:MM:SS)
        end_time: 结束时间 (格式: YYYY-MM-DD HH:MM:SS)
        custom_params: 其他自定义参数
        status: 触发状态 (默认: 4-手动触发)
        base_url: MegTool Backend地址
        username: 用户名
        password: 密码
    
    Returns:
        Dict: {"success": bool, "data": dict} 或 {"success": bool, "error": str}
    """
    trigger = ReportTrigger(base_url, username, password)
    return trigger.trigger_report(report_id, vin, start_time, end_time, custom_params, status)


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description='飞书报告触发脚本')
    
    # JSON参数模式
    parser.add_argument('--json', type=str, help='JSON格式的参数')
    
    # 单独参数模式
    parser.add_argument('--report-id', type=int, help='报告ID')
    parser.add_argument('--vin', type=str, default='', help='车辆识别码')
    parser.add_argument('--start-time', type=str, default='', help='开始时间')
    parser.add_argument('--end-time', type=str, default='', help='结束时间')
    parser.add_argument('--custom-params', type=str, default='', help='其他自定义参数')
    parser.add_argument('--status', type=str, default='4', help='触发状态')
    
    # 连接参数
    parser.add_argument('--base-url', type=str, default='http://localhost:8000', help='MegTool Backend地址')
    parser.add_argument('--username', type=str, default='admin', help='用户名')
    parser.add_argument('--password', type=str, default='password', help='密码')
    
    # 输出选项
    parser.add_argument('--quiet', action='store_true', help='静默模式，只输出结果JSON')
    parser.add_argument('--pretty', action='store_true', help='格式化JSON输出')
    
    args = parser.parse_args()
    
    # 解析参数
    if args.json:
        try:
            params = json.loads(args.json)
            report_id = params.get('report_id')
            vin = params.get('vin', '')
            start_time = params.get('start_time', '')
            end_time = params.get('end_time', '')
            custom_params = params.get('custom_params', '')
            status = params.get('status', '4')
        except json.JSONDecodeError:
            print(json.dumps({"success": False, "error": "JSON参数格式错误"}))
            sys.exit(1)
    else:
        report_id = args.report_id
        vin = args.vin
        start_time = args.start_time
        end_time = args.end_time
        custom_params = args.custom_params
        status = args.status
    
    if not report_id:
        print(json.dumps({"success": False, "error": "必须指定报告ID"}))
        sys.exit(1)
    
    # 执行触发
    if not args.quiet:
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始触发报告，ID: {report_id}")
    
    result = trigger_report(
        report_id=report_id,
        vin=vin,
        start_time=start_time,
        end_time=end_time,
        custom_params=custom_params,
        status=status,
        base_url=args.base_url,
        username=args.username,
        password=args.password
    )
    
    # 输出结果
    if args.pretty:
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print(json.dumps(result, ensure_ascii=False))
    
    if not args.quiet:
        if result['success']:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 报告触发成功")
        else:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 报告触发失败: {result.get('error')}")
    
    # 设置退出码
    sys.exit(0 if result['success'] else 1)


if __name__ == '__main__':
    main()