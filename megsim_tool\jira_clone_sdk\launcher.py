#!/usr/bin/env python3
"""Jira Clone SDK 启动程序"""

import argparse
import json
import sys
from typing import Dict, Any
from jira_clone_sdk import JiraCloneSDK, CloneConfig, FieldConfig, FieldAction


def load_config_from_file(config_file: str) -> Dict[str, Any]:
    """从文件加载配置"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        sys.exit(1)


def create_clone_config(config_data: Dict[str, Any]) -> CloneConfig:
    """创建克隆配置"""
    clone_config = CloneConfig(
        clone_prefix=config_data.get('clone_prefix', 'CLONE - '),
        max_retries=config_data.get('max_retries', 3),
        copy_comments=config_data.get('copy_comments', False),
        copy_worklogs=config_data.get('copy_worklogs', False),
        create_clone_link=config_data.get('create_clone_link', True)
    )
    
    # 添加字段配置
    field_configs = config_data.get('field_configs', {})
    for field_name, field_config in field_configs.items():
        action = FieldAction(field_config.get('action', 'keep'))
        value = field_config.get('value')
        clone_config.add_field_config(field_name, FieldConfig(action=action, value=value))
    
    # 添加排除字段
    exclude_fields = config_data.get('exclude_fields', [])
    for field in exclude_fields:
        clone_config.exclude_field(field)
    
    return clone_config


def clone_single_issue(args):
    """克隆单个问题"""
    sdk = JiraCloneSDK(base_url=args.url, bearer_token=args.token)
    
    if not sdk.test_connection():
        print("连接失败")
        sys.exit(1)
    
    # 加载配置
    config_data = {}
    if args.config:
        config_data = load_config_from_file(args.config)
    
    config_data.update({
        'clone_prefix': args.prefix,
        'create_clone_link': args.link
    })
    
    clone_config = create_clone_config(config_data)
    
    try:
        cloned_key = sdk.clone_issue(args.issue, clone_config)
        print(f"克隆成功: {args.issue} -> {cloned_key}")
        
        # 如果有编辑数据
        if args.edit:
            edit_data = load_config_from_file(args.edit)
            if sdk.edit_cloned_data(cloned_key, edit_data):
                print(f"编辑成功: {cloned_key}")
            else:
                print(f"编辑失败: {cloned_key}")
                
    except Exception as e:
        print(f"克隆失败: {e}")
        sys.exit(1)


def clone_batch_issues(args):
    """批量克隆问题"""
    sdk = JiraCloneSDK(base_url=args.url, bearer_token=args.token)
    
    if not sdk.test_connection():
        print("连接失败")
        sys.exit(1)
    
    # 加载配置
    config_data = {}
    if args.config:
        config_data = load_config_from_file(args.config)
    
    config_data.update({
        'clone_prefix': args.prefix,
        'create_clone_link': args.link
    })
    
    clone_config = create_clone_config(config_data)
    
    try:
        if args.jql:
            # 通过JQL搜索克隆
            successful, failed = sdk.clone_issues_by_jql(args.jql, clone_config, args.max_results)
        else:
            # 批量克隆指定问题
            issue_keys = args.issues.split(',')
            successful, failed = sdk.clone_issues_batch(issue_keys, clone_config)
        
        print(f"\n克隆完成:")
        print(f"成功: {len(successful)} 个")
        print(f"失败: {len(failed)} 个")
        
        if successful:
            print(f"\n成功克隆的问题: {', '.join(successful)}")
        
        if failed:
            print(f"\n失败的问题: {', '.join(failed)}")
            
    except Exception as e:
        print(f"批量克隆失败: {e}")
        sys.exit(1)


def edit_issue_data(args):
    """编辑问题数据"""
    sdk = JiraCloneSDK(base_url=args.url, bearer_token=args.token)
    
    if not sdk.test_connection():
        print("连接失败")
        sys.exit(1)
    
    edit_data = load_config_from_file(args.data)
    
    try:
        if sdk.edit_cloned_data(args.issue, edit_data):
            print(f"编辑成功: {args.issue}")
        else:
            print(f"编辑失败: {args.issue}")
    except Exception as e:
        print(f"编辑失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Jira Clone SDK 启动程序')
    parser.add_argument('--url', required=True, help='Jira URL')
    parser.add_argument('--token', required=True, help='Bearer Token')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 克隆单个问题
    clone_parser = subparsers.add_parser('clone', help='克隆单个问题')
    clone_parser.add_argument('issue', help='问题Key')
    clone_parser.add_argument('--prefix', default='CLONE - ', help='克隆前缀')
    clone_parser.add_argument('--link', action='store_true', default=True, help='创建克隆链接')
    clone_parser.add_argument('--config', help='配置文件路径')
    clone_parser.add_argument('--edit', help='编辑数据文件路径')
    
    # 批量克隆
    batch_parser = subparsers.add_parser('batch', help='批量克隆问题')
    batch_parser.add_argument('--issues', help='问题Key列表，逗号分隔')
    batch_parser.add_argument('--jql', help='JQL查询语句')
    batch_parser.add_argument('--max-results', type=int, default=50, help='最大结果数')
    batch_parser.add_argument('--prefix', default='CLONE - ', help='克隆前缀')
    batch_parser.add_argument('--link', action='store_true', default=True, help='创建克隆链接')
    batch_parser.add_argument('--config', help='配置文件路径')
    
    # 编辑问题
    edit_parser = subparsers.add_parser('edit', help='编辑问题数据')
    edit_parser.add_argument('issue', help='问题Key')
    edit_parser.add_argument('--data', required=True, help='编辑数据文件路径')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    if args.command == 'clone':
        clone_single_issue(args)
    elif args.command == 'batch':
        if not args.issues and not args.jql:
            print("批量克隆需要指定 --issues 或 --jql")
            sys.exit(1)
        clone_batch_issues(args)
    elif args.command == 'edit':
        edit_issue_data(args)


if __name__ == '__main__':
    main()