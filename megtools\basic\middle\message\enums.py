# enums.py

#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Author: Hui
# @Desc: { 项目枚举类模块 }
# @Date: 2021/09/23 23:37

from enum import Enum


class StatusCodeEnum(Enum):
    """状态码枚举类"""

    OK = (0, '成功')
    ERROR = (-1, '错误')
    SERVER_ERR = (500, '服务器异常')
    TOKEN_ERR = (1000, '登录认证无效，请重新登录！')
    AUTH_ERR = (1001, '当前接口您无访问权限，请联系系统管理员！')

    FILE_EXISTS_ERR = (2001, '文件已存在')
    IP_EXISTS_ERR = (2001, '本上位机中已存在本IP地址，请检查！')
    DB_ERR = (3001, '数据库查询错误')

    @property
    def code(self):
        """获取状态码"""
        return self.value[0]

    @property
    def msg(self):
        """获取状态码信息"""
        return self.value[1]

