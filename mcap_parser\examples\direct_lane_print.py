#!/usr/bin/env python3
"""
直接打印车道线数据示例
使用SDK直接解析并打印车道线数据，不创建索引
"""

import sys
import os

# 添加SDK路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

# 导入日志系统
try:
    from src.utils.sdk_logger import log_info, log_error, log_success, log_warning, log_data_info
except ImportError:
    def log_info(msg): print(f"INFO - {msg}")
    def log_error(msg): print(f"ERROR - {msg}")
    def log_success(msg): print(f"SUCCESS - {msg}")
    def log_warning(msg): print(f"WARNING - {msg}")
    def log_data_info(msg): print(f"DATA - {msg}")

try:
    from src.main import McapAutoDriveSDK
except ImportError as e:
    log_error(f"无法导入SDK: {e}")
    log_info("请确保src目录中有mcap_autodrive_sdk.py")
    sys.exit(1)


def print_lane_data(mcap_file: str, max_messages: int = 5):
    """直接打印车道线数据，不创建索引"""
    log_info(f"使用SDK直接解析车道线: {mcap_file}")
    
    try:
        # 创建SDK实例，禁用快速模式和索引
        sdk = McapAutoDriveSDK(
            verbose=False,
            enable_fast_mode=False,  # 禁用快速模式
            enable_structured_data=False  # 禁用结构化数据转换
        )
        
        log_info("📡 开始直接流式读取车道线数据...")
        
        # 直接流式解析车道线数据
        count = 0
        lane_types = ["deva_perception_msgs/msg/LaneArrayv2", "LaneArrayv2"]
        for msg in sdk.stream_data(mcap_file, lane_types, max_messages=max_messages):
            count += 1
            log_info(f"{'='*60}")
            log_info(f"📍 车道线消息 {count}")
            log_info(f"{'='*60}")
            log_info(f"时间戳: {msg.timestamp:.6f}s")
            log_info(f"话题: {msg.topic}")
            log_info(f"消息类型: {msg.message_type}")
            
            # 直接打印车道线数据
            print_lane_array_details(msg.data)
        
        if count == 0:
            log_warning("  未找到车道线数据，请检查MCAP文件是否包含LaneArrayv2消息")
        else:
            log_success(f" 完成！共打印了 {count} 条车道线消息")
            
    except Exception as e:
        log_error(f" 解析失败: {e}")
        import traceback
        traceback.print_exc()


def print_lane_array_details(data):
    """打印LaneArrayv2的详细数据"""
    try:
        if hasattr(data, 'lane_array'):
            lanes = data.lane_array
            log_info(f"🛣️  车道线数组 - 共 {len(lanes)} 条车道线")
            
            for i, lane in enumerate(lanes):
                log_info(f"  📍 车道线 {i+1}:")
                log_info(f"     ID: {lane.lane_id}")
                log_info(f"     置信度: {lane.confidence:.4f}")
                
                if hasattr(lane, 'lane_property'):
                    log_info(f"     车道属性: {lane.lane_property}")
                
                if hasattr(lane, 'left_lane_id'):
                    log_info(f"     左车道ID: {lane.left_lane_id}")
                if hasattr(lane, 'right_lane_id'):
                    log_info(f"     右车道ID: {lane.right_lane_id}")
                
                # 打印路径点信息
                if hasattr(lane, 'waypoints') and lane.waypoints:
                    waypoints = lane.waypoints
                    log_info(f"     路径点数量: {len(waypoints)}")
                    
                    # 打印前3个路径点的详细坐标
                    log_info("     前3个路径点:")
                    for j, point in enumerate(waypoints[:3]):
                        log_info(f"       点{j+1}: X={point.x:.3f}, Y={point.y:.3f}, Z={point.z:.3f}")
                    
                    if len(waypoints) > 3:
                        log_info(f"       ... (还有 {len(waypoints)-3} 个点)")
                        
                        # 显示最后一个点
                        last_point = waypoints[-1]
                        log_info(f"       终点: X={last_point.x:.3f}, Y={last_point.y:.3f}, Z={last_point.z:.3f}")
                
                # 打印其他属性
                if hasattr(lane, 'fork_lane_id') and lane.fork_lane_id != 0:
                    log_info(f"     分叉车道ID: {lane.fork_lane_id}")
                
                if hasattr(lane, 'v_min') and hasattr(lane, 'v_max'):
                    log_info(f"     速度范围: {lane.v_min} - {lane.v_max}")
        
        # 打印人行横道信息
        if hasattr(data, 'crosswalk_array') and data.crosswalk_array:
            log_info(f"🚶 人行横道数组 - 共 {len(data.crosswalk_array)} 个")
            for i, crosswalk in enumerate(data.crosswalk_array):
                log_info(f"  人行横道 {i+1}: ID={crosswalk.id}, 置信度={crosswalk.confidence:.4f}")
        
        # 打印转向箭头信息
        if hasattr(data, 'steering_arrow_array') and data.steering_arrow_array:
            log_info(f"➡️  转向箭头数组 - 共 {len(data.steering_arrow_array)} 个")
            for i, arrow in enumerate(data.steering_arrow_array):
                log_info(f"  转向箭头 {i+1}: ID={arrow.id}, 置信度={arrow.confidence:.4f}")
        
        # 打印入口信息
        if hasattr(data, 'entrance_array') and data.entrance_array:
            log_info(f"🚪 入口数组 - 共 {len(data.entrance_array)} 个")
            for i, entrance in enumerate(data.entrance_array):
                log_info(f"  入口 {i+1}: ID={entrance.id}, 置信度={entrance.confidence:.4f}")
                
    except Exception as e:
        log_error(f" 解析车道线数据时出错: {e}")
        log_info(f"数据类型: {type(data)}")
        # 尝试打印数据的基本信息
        try:
            attrs = [attr for attr in dir(data) if not attr.startswith('_')]
            log_info(f"可用属性: {attrs[:10]}")  # 显示前10个属性
        except:
            pass


def main():
    """主函数"""
    if len(sys.argv) < 2:
        log_info("🚗 直接打印车道线数据示例")
        log_info(f"用法:")
        log_info("   python examples/direct_lane_print.py <mcap_file> [max_messages]")
        log_info(f"示例:")
        log_info("   python examples/direct_lane_print.py data.mcap 3")
        log_info(f"说明:")
        log_info("   - 使用您的SDK直接解析MCAP文件")
        log_info("   - 不创建索引，直接流式读取")
        log_info("   - 详细打印车道线的所有数据")
        return
    
    mcap_file = sys.argv[1]
    max_messages = int(sys.argv[2]) if len(sys.argv) > 2 else 5
    
    # 检查文件
    if not os.path.exists(mcap_file):
        log_error(f" 文件不存在: {mcap_file}")
        return
    
    # 直接打印车道线数据
    print_lane_data(mcap_file, max_messages)


if __name__ == "__main__":
    main()
