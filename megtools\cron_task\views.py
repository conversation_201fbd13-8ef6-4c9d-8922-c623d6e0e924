import datetime
import json

from django.http import HttpResponse
from django.views.decorators.http import require_POST

from basic.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PageUtils, parse_crontab, DateUtil
from cron_task.handlers.issue_daliy_group import DailyJiraIssueTask
from cron_task.models import CronTaskInfo
from cron_task.services import CloneIssue, BatchTagIssue, Jira2FeishuDoc


@require_POST
def genDailyExcel(request):
    req = json.loads(request.body.decode("UTF-8"))
    type = req["type"]
    if type == '无图':
        DailyJiraIssueTask().execute_nohd()
    if type == '有图':
        DailyJiraIssueTask().execute_hd()
    if type == 'All':
        DailyJiraIssueTask().execute_once()
    return HttpResponse(json.dumps({"content": "生成完成，请检查"}), content_type='application/json')


@require_POST
def cronTaskPage(request):
    """
    分页查询定时任务信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    condition = OrmFilter.and_condition(CronTaskInfo, req, 1)
    condition.children.append(('is_delete', 0))
    files = CronTaskInfo.objects.filter(condition)
    result = PageUtils.page(files, req)
    return HttpResponse(json.dumps(result, cls=JsonEncoder), content_type='application/json')


@require_POST
def modifyCronTask(request):
    """
    修改定时任务
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user["login_name"] if "user" in request.__dict__ else "remote"

    next_execute = parse_crontab(req.get("cron_exp", ""))
    req["next_exec_time"] = DateUtil.format(next_execute)
    req['update_by'] = who
    req['update_time'] = datetime.datetime.now()
    if req.get("id", ""):
        CronTaskInfo.objects.filter(id=req['id']).update(**req)
    else:
        if "id" in req:
            del req["id"]
        req['create_by'] = who
        req['create_time'] = datetime.datetime.now()
        taskInfo = CronTaskInfo(**req)
        taskInfo.save()
        req["id"] = taskInfo.id
    return HttpResponse(json.dumps(req, cls=JsonEncoder), content_type='application/json')


@require_POST
def delCronTask(request):
    """
    删除定时任务
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user['login_name'] if "user" in request.__dict__ else "remote"
    CronTaskInfo.objects.filter(id=req["id"]).update(is_delete=1, update_by=who)
    return HttpResponse(json.dumps("{}"), content_type='application/json')


@require_POST
def rerunTask(request):
    """
    重跑任务
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user['login_name'] if "user" in request.__dict__ else "remote"
    CronTaskInfo.objects.filter(id=req['id']).update(next_exec_time=datetime.datetime.now(), remark='', is_run='1',
                                                     update_by=who, update_time=datetime.datetime.now())
    return HttpResponse("{}", content_type='application/json')

@require_POST
def cloneJiraIssue(request):
    """
    执行Clone 的任务
    """
    req = json.loads(request.body.decode("UTF-8"))
    result = CloneIssue(req).apply()
    return HttpResponse(json.dumps(result), content_type='application/json')

@require_POST
def batchTagValues(request):
    """
    执行Clone 的任务
    """
    req = json.loads(request.body.decode("UTF-8"))
    result = BatchTagIssue(req).apply()
    return HttpResponse(json.dumps(result), content_type='application/json')

@require_POST
def jira2Feishu(request):
    """
    执行Clone 的任务
    """
    req = json.loads(request.body.decode("UTF-8"))
    result = Jira2FeishuDoc(req).apply()
    return HttpResponse(json.dumps(result), content_type='application/json')
