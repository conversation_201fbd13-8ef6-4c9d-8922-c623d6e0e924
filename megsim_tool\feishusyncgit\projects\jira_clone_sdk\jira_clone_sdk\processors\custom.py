"""自定义字段处理器"""

from typing import Any, Dict
from .base import FieldProcessor


class CustomFieldProcessor(FieldProcessor):
    """自定义字段处理器"""
    
    def __init__(self):
        super().__init__(priority=50)  # 低优先级
    
    def can_process(self, field_name: str, field_value: Any = None) -> bool:
        # 处理所有customfield_开头的字段
        return field_name.startswith('customfield_')
    
    def process(self, field_name: str, field_value: Any, context: Dict[str, Any]) -> Any:
        """处理自定义字段"""
        # 获取字段映射配置
        field_mapping = context.get('field_mapping', {})
        
        if field_name in field_mapping:
            # 如果有映射配置，使用映射后的值
            mapped_field = field_mapping[field_name]
            return context.get('source_fields', {}).get(mapped_field, field_value)
        
        # 特殊处理某些已知的自定义字段
        if self._is_user_field(field_value):
            # 用户类型字段保持原值
            return field_value
        elif self._is_option_field(field_value):
            # 选项类型字段保持原值
            return field_value
        elif self._is_multi_value_field(field_value):
            # 多值字段保持原值
            return field_value
        
        # 默认保持原值
        return field_value
    
    def _is_user_field(self, field_value: Any) -> bool:
        """判断是否为用户类型字段"""
        if isinstance(field_value, dict):
            return 'accountId' in field_value or 'name' in field_value
        return False
    
    def _is_option_field(self, field_value: Any) -> bool:
        """判断是否为选项类型字段"""
        if isinstance(field_value, dict):
            return 'value' in field_value or 'id' in field_value
        return False
    
    def _is_multi_value_field(self, field_value: Any) -> bool:
        """判断是否为多值字段"""
        return isinstance(field_value, list)
    
    def validate_field(self, field_name: str, field_value: Any) -> bool:
        """验证自定义字段"""
        # 自定义字段通常比较宽松，大部分情况下都是有效的
        return True