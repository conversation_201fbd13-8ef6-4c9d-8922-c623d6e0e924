import sys
from datetime import datetime
from typing import List, Optional
from src.core.config_manager import ConfigManager
from src.core.route_manager import RouteManager
from src.clients.jira_client import JiraClient
from src.clients.feishu_client import FeishuClient
from src.generators.local_generator import LocalGenerator
from src.utils.logger import get_logger, log_start, log_complete, log_success, log_error, log_connection, log_report, log_info
from src.utils.exceptions import AutoTestReportError

logger = get_logger(__name__)


class AutoTestReportGenerator:
    """主应用类"""
    
    def __init__(self, config_path: str = None):
        """初始化主应用"""
        try:
            self.config = ConfigManager(config_path)
            self.jira_client = JiraClient(self.config)
            self.feishu_client = FeishuClient(self.config)
            self.report_generator = LocalGenerator(self.config, self.jira_client)
            self.route_manager = RouteManager(self.config)
            
            # 验证配置
            if not self.config.validate_config():
                raise AutoTestReportError("配置验证失败")
                
        except Exception as e:
            log_error(f"初始化失败: {str(e)}")
            raise
    
    def run(self):
        """运行主流程"""
        print("\n" + "="*60)
        print(f"🚀 AutoTest Report Generator v3.0".center(60))
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".center(60))
        print("="*60 + "\n")
        
        try:
            # 测试连接
            self._test_connections()
            
            # 选择测试路线
            selected_routes = self._select_test_routes()
            if not selected_routes:
                log_error("未选择任何测试路线，程序退出")
                return
            
            # 获取Jira问题
            issues = self._get_issues()
            
            # 打印问题列表
            if issues:
                self.jira_client.print_issues(issues)
            
            # 提取MR标签
            mr_labels = self.jira_client.extract_mr_labels(issues)
            log_info(f"提取到MR标签: {mr_labels}")
            
            # 分析算法问题标签
            algorithm_stats = self._analyze_algorithm_issues(issues)
            
            # 生成报告（包含路线信息和算法标签分析）
            report_file = self._generate_report(issues, selected_routes, algorithm_stats)
            
            # 上传到飞书
            if report_file:
                doc_url = self._upload_to_feishu(report_file, mr_labels)
                if doc_url:
                    self._display_report_link(doc_url)
            
            log_complete("报告生成流程完成")
            
        except Exception as e:
            log_error(f"运行过程中出现错误: {str(e)}")
            raise
    
    def _test_connections(self):
        """测试各种连接"""
        log_start("正在测试连接...")
        
        # 测试Jira连接
        if self.jira_client.test_connection():
            log_success("Jira连接正常")
        else:
            log_error("Jira连接异常")
        
        # 测试飞书连接
        if self.feishu_client.test_connection():
            log_success("飞书连接正常")
        else:
            log_error("飞书连接异常")
    
    def _select_test_routes(self) -> List[str]:
        """选择测试路线"""
        log_start("正在选择测试路线...")
        selected_routes = self.route_manager.select_routes()
        
        if selected_routes:
            route_info = self.route_manager.get_route_info(selected_routes)
            total_distance = route_info.get('total_distance', 0)
            log_success(f"已选择 {len(selected_routes)} 条路线，总里程: {total_distance:.1f} 公里")
        else:
            log_warning("未选择任何路线")
        
        return selected_routes
    
    def _get_issues(self) -> List:
        """获取Jira问题"""
        log_start("正在获取Jira问题...")
        try:
            issues = self.jira_client.get_issues()
            log_success(f"成功获取 {len(issues)} 个问题")
            return issues
        except Exception as e:
            log_error(f"获取Jira问题失败: {str(e)}")
            raise
    
    def _analyze_algorithm_issues(self, issues: List) -> dict:
        """分析算法问题标签"""
        log_start("正在分析算法问题标签...")
        
        try:
            # 打印算法问题分析
            self.jira_client.print_algorithm_analysis(issues)
            
            # 获取算法问题统计
            algorithm_stats = self.jira_client.get_algorithm_issue_statistics(issues)
            
            log_success(f"算法问题分析完成，共 {algorithm_stats.get('total_algorithm_issues', 0)} 个算法问题")
            
            return algorithm_stats
            
        except Exception as e:
            log_error(f"分析算法问题标签失败: {str(e)}")
            return {}
    
    def _generate_report(self, issues: List, selected_routes: List[str], algorithm_stats: dict) -> str:
        """生成报告"""
        log_start("正在生成报告...")
        
        try:
            # 获取路线信息
            route_info = self.route_manager.get_route_info(selected_routes)
            
            # 生成报告
            report_file = self.report_generator.generate(issues, route_info, algorithm_stats)
            
            if report_file:
                log_success(f"报告生成成功: {report_file}")
                return report_file
            else:
                log_error("报告生成失败")
                return None
                
        except Exception as e:
            log_error(f"生成报告失败: {str(e)}")
            raise
    
    def _upload_to_feishu(self, report_file: str, mr_labels: List[str]) -> Optional[str]:
        """上传报告到飞书"""
        log_start("正在上传报告到飞书...")
        try:
            success, doc_url = self.feishu_client.upload_document(report_file, mr_labels)
            if success:
                log_success("报告上传成功")
                return doc_url
            else:
                log_error("报告上传失败")
                return None
        except Exception as e:
            log_error(f"上传报告时出现错误: {str(e)}")
            return None
    
    def _display_report_link(self, doc_url: str):
        """显示报告链接"""
        print("\n" + "="*60)
        print("📋 报告上传完成！".center(60))
        print("="*60)
        
        # 获取飞书app_id
        app_id = self.config.get('Feishu', 'app_id')
        if app_id:
            doc_link = f"https://{app_id}.feishu.cn/drive/folder/{doc_url}"
            print(f"\n🔗 报告链接: {doc_link}")
        else:
            print(f"\n🔗 报告链接: {doc_url}")
        
        print("\n💡 提示:")
        print("   - 点击链接可直接跳转到飞书文档")
        print("   - 文档已转换为飞书格式，支持在线查看和编辑")
        print("   - 可在飞书中分享给团队成员")
        print("="*60)
    
    def get_status(self) -> dict:
        """获取应用状态"""
        return {
            'jira_connected': self.jira_client.is_connected(),
            'feishu_connected': self.feishu_client.is_connected(),
            'config_loaded': self.config is not None,
            'routes_loaded': self.route_manager is not None
        }


def main():
    """主函数"""
    try:
        app = AutoTestReportGenerator()
        app.run()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 