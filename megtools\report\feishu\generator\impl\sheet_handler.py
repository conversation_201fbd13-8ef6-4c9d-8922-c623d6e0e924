import logging
import re
import time

from report.feishu.generator.handler import Handler

chr_regexp = r'[a-zA-Z1-9 ,\-_]'
chr_pattern = re.compile(chr_regexp)

class SheetHandler(Handler):

    def __init__(self, data, block, feishu_api):
        super().__init__(data, block, feishu_api)

    def apply(self):
        if len(self.data) == 0:
            return
        sheet_token, sheet_id = self.create_table()
        col_name = self.get_col_name(len(self.data[0]))
        body = {
            "valueRange": {
                "range": f"{sheet_id}!A1:{col_name}{len(self.data)}",
                "values": self.data
            }
        }
        self.feishu_api.sheet_write_cells(sheet_token, body)
        # 计算所有的单元格中数据的长度，根据长度设置列的大小
        self.cal_cols_len(sheet_token, sheet_id)


    def cal_cols_len(self, sheet_token, sheet_id):
        for col_idx in range(0, len(self.data[0])):
            sum = 0
            cnt = 0
            for row_idx in range(0, len(self.data)):
                if self.data[row_idx][col_idx]:
                    cnt += 1
                    if '\n' in str(self.data[row_idx][col_idx]):
                        value = str(max(self.data[row_idx][col_idx].split('\n'), key=len))
                    else:
                        value = str(self.data[row_idx][col_idx])
                    data = chr_pattern.findall(value)
                    sum += (len(value) - len(data)) * 20 + len(data) * 8
            col_width = int((sum / cnt))

            col_width = 40 if col_width <= 40 else col_width
            body = {
                "dimension":{
                    "sheetId": sheet_id,
                    "majorDimension": "COLUMNS",
                    "startIndex": col_idx + 1,
                    "endIndex": col_idx + 1
                },
                "dimensionProperties":{
                    "visible": True,
                    "fixedSize": col_width
                }
            }
            self.feishu_api.sheet_update_dimension(sheet_token, body)

    def get_col_name(self, col_idx: int):
        if col_idx <= 0:
            return ""
        else:
            next_cal = int(col_idx / 26)
            mod_val = col_idx % 26
            mod_val = chr(mod_val + 64)
            cal_val = self.get_col_name(next_cal)
            return f"{cal_val}{mod_val}"

    def create_table(self):
        """
        生成一个空的表格，准备数据填充！
        """
        rows = len(self.data)
        cols = len(self.data[0])
        gen_rows = rows if rows <= 9 else 9
        gen_cols = cols if cols <= 9 else 9
        block = [
            {
                "block_type": 30,
                "sheet": {
                    "row_size": gen_rows,
                    "column_size": gen_cols,
                }
            }
        ]

        parent_id = self.block.get("parent_id")
        data = self.feishu_api.create_document_block(parent_id, {
            'index': self.block.get("index", 0) + 1,
            'children': block
        })
        # 表格创建完成之后，判断是否需要进行行列扩展
        token = data.get("data", {}).get("children",[{}])[0].get("sheet", {}).get("token", "")
        if token == "":
            return
        sheet_token = token.split("_")[0]
        sheet_id = token.split("_")[1]
        # 为Sheet 增加行列
        self.append_row_col(sheet_token, sheet_id, "ROWS", rows - gen_rows)
        self.append_row_col(sheet_token, sheet_id, "COLUMNS", cols - gen_cols)
        return sheet_token, sheet_id

    def append_row_col(self, sheet_token, sheet_id, type, size):
        """
        生成行列，  行：row_index  列：col_index
        """
        if size <= 0:
            return
        body = {
            "dimension": {
                "sheetId": sheet_id,
                "majorDimension": type,
                "length": size
            }
        }
        data = self.feishu_api.sheet_add_cells(sheet_token, body)
        return data






