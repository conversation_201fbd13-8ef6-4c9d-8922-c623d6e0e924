

#!/usr/bin/env python3

# 版本信息
__version__ = "3.0.0"
__author__ = "MCAP AutoDrive SDK Team"

# 导入日志系统
try:
    from .utils.sdk_logger import get_logger, log_info, log_warning, log_error, log_success, log_progress, log_performance, log_file_operation, log_parsing_info, log_data_info, log_lane_info
except ImportError:
    try:
        from .utils.logger import get_logger, log_info, log_warning, log_error, log_success, log_progress, log_performance, log_file_operation, log_parsing_info, log_data_info, log_lane_info
    except ImportError:
        # 临时日志函数（如果日志系统不可用）
        def get_logger(name): return None
        def log_info(msg, **kwargs): print(f"INFO - {msg}")
        def log_warning(msg, **kwargs): print(f"WARNING - {msg}")
        def log_error(msg, **kwargs): print(f"ERROR - {msg}")
        def log_success(msg, **kwargs): print(f"SUCCESS - {msg}")
        def log_progress(msg, **kwargs): print(f"PROGRESS - {msg}")
        def log_performance(msg, **kwargs): print(f"PERFORMANCE - {msg}")
        def log_file_operation(msg, **kwargs): print(f"FILE - {msg}")
        def log_parsing_info(msg, **kwargs): print(f"PARSING - {msg}")
        def log_data_info(msg, **kwargs): print(f"DATA - {msg}")
        def log_lane_info(msg, **kwargs): print(f"LANE - {msg}")

# 导入所有公共接口，保持向后兼容
try:
    from .data_structures import (
        MessageData,
        TopicInfo,
        TimeRange,
        ProcessingStats,
        AnalysisResult,
        StreamConfig
    )
    from .parsers.message_registry import MessageTypeRegistry
    from .core.sdk import McapAutoDriveSDK
    from .tools.demo import McapDemo
except ImportError:
    # 支持直接运行的情况
    from .data_structures import (
        MessageData,
        TopicInfo,
        TimeRange,
        ProcessingStats,
        AnalysisResult,
        StreamConfig
    )
    from .parsers.message_registry import MessageTypeRegistry
    from .core.sdk import McapAutoDriveSDK
    from .tools.demo import McapDemo

# 为了向后兼容，保持原有的导入方式
# 用户可以继续使用: from mcap_autodrive_sdk import McapAutoDriveSDK


def get_version() -> str:
    """获取SDK版本"""
    return __version__


def get_supported_message_types() -> list:
    """获取支持的消息类型列表 (兼容性函数)"""
    return MessageTypeRegistry.get_all_types()


def is_message_type_supported(message_type: str) -> bool:
    """检查消息类型是否被支持 (兼容性函数)"""
    return MessageTypeRegistry.is_supported(message_type)


def create_sdk(enable_structured_data: bool = False, verbose: bool = False) -> McapAutoDriveSDK:
    """创建SDK实例 (便利函数)"""
    return McapAutoDriveSDK(
        enable_structured_data=enable_structured_data,
        verbose=verbose
    )


def quick_analyze(mcap_file: str, verbose: bool = True) -> AnalysisResult:
    """快速分析MCAP文件 (便利函数)"""
    sdk = create_sdk(verbose=verbose)
    return sdk.analyze_mcap_file(mcap_file)


def quick_stream(mcap_file: str,
                message_types: list = None,
                max_messages: int = None,
                verbose: bool = False):
    """快速流式处理 (便利函数)"""
    sdk = create_sdk(verbose=verbose)
    return sdk.stream_data(
        mcap_file=mcap_file,
        message_types=message_types,
        max_messages=max_messages
    )


def fast_stream(mcap_file: str,
               message_types: list = None,
               max_messages: int = None,
               verbose: bool = False):
    """高速流式处理 (便利函数)"""
    sdk = create_sdk(verbose=verbose)
    return sdk.fast_stream_data(
        mcap_file=mcap_file,
        message_types=message_types,
        max_messages=max_messages
    )


def random_access(mcap_file: str, timestamp: float, tolerance: float = 0.1, verbose: bool = False):
    """随机访问指定时间点的消息 (便利函数)"""
    sdk = create_sdk(verbose=verbose)
    return sdk.random_access(mcap_file, timestamp, tolerance)


# 导出所有公共接口
__all__ = [
    # 版本信息
    '__version__',
    '__author__',
    
    # 主要类
    'McapAutoDriveSDK',
    'MessageTypeRegistry',
    'McapDemo',
    
    # 数据类
    'MessageData',
    'TopicInfo',
    'TimeRange', 
    'ProcessingStats',
    'AnalysisResult',
    'StreamConfig',
    
    # 便利函数
    'get_version',
    'get_supported_message_types',
    'is_message_type_supported',
    'create_sdk',
    'quick_analyze',
    'quick_stream',
    'fast_stream',
    'random_access'
]


def print_module_info():
    """打印模块信息"""
    log_success(f"MCAP自动驾驶SDK v{__version__} (重构版)")
    log_data_info(f"支持的消息类型: {MessageTypeRegistry.get_supported_count()}")
    log_info("模块化架构: 4个专用模块")
    log_info("向后兼容: 保持原有API")
    log_info("")
    log_info("模块结构:")
    log_info("   mcap_data_classes.py     - 数据类定义")
    log_info("   mcap_message_registry.py - 消息类型注册表")
    log_info("   mcap_core_sdk.py         - 核心SDK功能")
    log_info("   mcap_demo_tools.py       - 演示和工具")
    log_info("")
    log_info("使用示例:")
    log_info("   from mcap_autodrive_sdk_new import McapAutoDriveSDK")
    log_info("   sdk = McapAutoDriveSDK()")
    log_info("   analysis = sdk.analyze_mcap_file('data.mcap')")


if __name__ == "__main__":
    print_module_info()
