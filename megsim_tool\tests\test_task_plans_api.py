import requests
import json
from datetime import datetime

def test_create_task_plan():
    """
    测试创建任务计划API
    """
    url = "https://megsim.mc.machdrive.cn/api/task_plans/"
    
    payload = {
        "type": 0,
        "task_ids": "[424160]",
        "pro_branch": "baohua/curb_zigzag_remove",
        "commit_id": "",
        "name": "test",
        "project": 4,
        "task_type": 20,
        "is_mdriver": 1,
        "replace_model": "",
        "module_branch": "",
        "module_commit": ""
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print("=== 测试创建任务计划API ===")
    print(f"URL: {url}")
    print(f"方法: POST")
    print("请求参数:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print("响应内容:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            return True, response_data
        except json.JSONDecodeError:
            print("响应内容 (文本):")
            print(response.text)
            return response.status_code in [200, 201], response.text
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return False, str(e)

def analyze_request_parameters(payload):
    """
    分析请求参数
    """
    print("\n=== 请求参数分析 ===")
    print(f"任务计划类型: {payload['type']}")
    print(f"关联任务ID: {payload['task_ids']}")
    print(f"项目分支: {payload['pro_branch']}")
    print(f"任务计划名称: {payload['name']}")
    print(f"项目ID: {payload['project']}")
    print(f"任务类型: {payload['task_type']}")
    print(f"是否为MDriver: {payload['is_mdriver']}")
    
    # 检查空字段
    empty_fields = [k for k, v in payload.items() if v == ""]
    if empty_fields:
        print(f"空字段: {', '.join(empty_fields)}")

def generate_curl_command(url, payload):
    """
    生成等效的cURL命令
    """
    print("\n=== 等效cURL命令 ===")
    curl_cmd = f"""curl -X POST '{url}' \\
  -H 'Content-Type: application/json' \\
  -H 'Accept: application/json' \\
  -d '{json.dumps(payload)}'"""
    print(curl_cmd)

def main():
    print("=== MegSim 任务计划API测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 请求参数
    payload = {
        "type": 0,
        "task_ids": "[424160]",
        "pro_branch": "baohua/curb_zigzag_remove",
        "commit_id": "",
        "name": "test",
        "project": 4,
        "task_type": 20,
        "is_mdriver": 1,
        "replace_model": "",
        "module_branch": "",
        "module_commit": ""
    }
    
    # 分析请求参数
    analyze_request_parameters(payload)
    
    # 测试API
    success, result = test_create_task_plan()
    
    # 生成cURL命令
    generate_curl_command("https://megsim.mc.machdrive.cn/api/task_plans/", payload)
    
    print("\n=== 测试结果汇总 ===")
    print(f"任务计划创建API: {'✅ 成功' if success else '❌ 失败'}")
    
    return success, result

if __name__ == "__main__":
    main()