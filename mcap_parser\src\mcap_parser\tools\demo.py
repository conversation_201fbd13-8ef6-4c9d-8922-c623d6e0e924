#!/usr/bin/env python3


import argparse
import sys
import time
from pathlib import Path
from typing import List, Optional

# 导入日志系统
try:
    from ..utils.sdk_logger import log_info, log_success, log_warning, log_error, log_data_info, log_progress
except ImportError:
    try:
        from ..utils.logger import log_info, log_success, log_warning, log_error, log_data_info, log_progress
    except ImportError:
        def log_info(msg, **kwargs): print(f"INFO - {msg}")
        def log_success(msg, **kwargs): print(f"SUCCESS - {msg}")
        def log_warning(msg, **kwargs): print(f"WARNING - {msg}")
        def log_error(msg, **kwargs): print(f"ERROR - {msg}")
        def log_data_info(msg, **kwargs): print(f"DATA - {msg}")
        def log_progress(msg, **kwargs): print(f"PROGRESS - {msg}")

# 支持相对导入和绝对导入
try:
    from ..core.sdk import McapAutoDriveSDK
    from ..data_structures import TimeRange
    from ..parsers.message_registry import MessageTypeRegistry
except ImportError:
    # 回退到绝对导入
    from ..core.sdk import McapAutoDriveSDK
from ..data_structures import TimeRange
from ..parsers.message_registry import MessageTypeRegistry


class McapDemo:
    """MCAP演示程序"""

    def __init__(self, mcap_file: str, verbose: bool = True):

        self.mcap_file = Path(mcap_file)
        self.verbose = verbose
        self.sdk = McapAutoDriveSDK(verbose=verbose)

        if not self.mcap_file.exists():
            raise FileNotFoundError(f"MCAP文件不存在: {self.mcap_file}")

    def run_full_demo(self):
        """运行完整演示"""
        log_success("MCAP自动驾驶SDK演示程序")
        log_info("=" * 60)
        log_info(f"文件: {self.mcap_file.name}")
        log_data_info(f"支持的消息类型: {MessageTypeRegistry.get_supported_count()}")
        log_info("")

        # 演示1: 文件分析
        self._demo_file_analysis()

        # 演示2: 消息类型展示
        self._demo_message_types()

        # 演示3: 流式数据处理
        self._demo_stream_processing()

        # 演示4: 过滤功能
        self._demo_filtering()

        # 演示5: 统计信息
        self._demo_statistics()

        log_success("演示完成!")

    def _demo_file_analysis(self):
        """演示文件分析功能"""
        log_data_info(" 1. 文件分析演示")
        log_info("-" * 30)

        analysis = self.sdk.analyze_mcap_file(self.mcap_file)

        log_data_info(" 文件信息:")
        log_info(f"   文件大小: {analysis.file_size / 1024 / 1024:.1f} MB")
        log_info(f"   总消息数: {analysis.total_messages:,}")
        log_info(f"   话题数量: {analysis.total_topics}")
        log_info(f"   消息类型数: {analysis.total_message_types}")
        log_info(f"   数据时长: {analysis.duration:.2f} 秒")
        log_info(f"   平均频率: {analysis.get_message_rate():.0f} 消息/秒")

        log_data_info("支持情况:")
        log_info(f"   支持的类型: {len(analysis.supported_types)}")
        log_info(f"   不支持的类型: {len(analysis.unsupported_types)}")
        log_info(f"   支持率: {analysis.get_support_ratio():.1%}")

        # 显示最活跃的话题
        most_active = analysis.get_most_active_topic()
        if most_active:
            topic_info = analysis.topics[most_active]
            log_info(f"📈 最活跃话题: {most_active}")
            log_info(f"   消息数量: {topic_info.message_count:,}")
            log_info(f"   消息类型: {topic_info.message_type}")

        log_info("")

    def _demo_message_types(self):
        """演示消息类型功能"""
        log_data_info(" 2. 消息类型演示")
        log_info("-" * 30)

        # 显示分类统计
        categories = MessageTypeRegistry.get_categories()
        log_data_info(" 消息类型分类:")
        for category, types in categories.items():
            log_info(f"   {category}: {len(types)} 种")

        # 显示部分支持的类型
        log_success(f" 部分支持的消息类型:")
        all_types = MessageTypeRegistry.get_all_types()
        for i, msg_type in enumerate(all_types[:10]):
            description = MessageTypeRegistry.get_description(msg_type)
            category = MessageTypeRegistry.get_category_for_type(msg_type)
            log_info(f"   {i+1:2d}. {msg_type} - {description} ({category})")

        if len(all_types) > 10:
            log_info(f"   ... 还有 {len(all_types) - 10} 种类型")

        log_info("")

    def _demo_stream_processing(self):
        """演示流式处理功能"""
        log_data_info(" 3. 流式处理演示")
        log_info("-" * 30)

        # 处理前5条消息
        log_progress(" 处理前5条消息:")
        count = 0
        for msg in self.sdk.stream_data(self.mcap_file, max_messages=5):
            count += 1
            simple_type = msg.message_type.split('.')[-1]
            supported = "✅" if MessageTypeRegistry.is_supported(msg.message_type) else "⚪"
            log_info(f"   {count}. {supported} {simple_type} @ {msg.timestamp:.2f}s (话题: {msg.topic})")

        log_info("")

    def _demo_filtering(self):
        """演示过滤功能"""
        log_data_info(" 4. 过滤功能演示")
        log_info("-" * 30)

        # 按消息类型过滤
        supported_types = ["LaneArrayv2", "RawImu", "PerceptionResult"]
        available_types = []

        # 检查哪些类型在文件中可用
        analysis = self.sdk.analyze_mcap_file(self.mcap_file)
        for msg_type in supported_types:
            for file_type in analysis.message_types.keys():
                if msg_type in file_type:
                    available_types.append(file_type)
                    break

        if available_types:
            log_data_info(f" 按消息类型过滤 (类型: {[t.split('.')[-1] for t in available_types]}):")
            count = 0
            for msg in self.sdk.stream_data(self.mcap_file, message_types=available_types, max_messages=3):
                count += 1
                simple_type = msg.message_type.split('.')[-1]
                log_info(f"   {count}. {simple_type} @ {msg.timestamp:.2f}s")
        else:
            log_warning("  文件中未找到常见的支持类型，显示前3条消息:")
            count = 0
            for msg in self.sdk.stream_data(self.mcap_file, max_messages=3):
                count += 1
                simple_type = msg.message_type.split('.')[-1]
                supported = "✅" if MessageTypeRegistry.is_supported(msg.message_type) else "⚪"
                log_info(f"   {count}. {supported} {simple_type} @ {msg.timestamp:.2f}s")

        # 时间范围过滤
        log_info(f"⏰ 时间范围过滤 (前10秒):")
        time_range = TimeRange(0, 10)
        count = 0
        for msg in self.sdk.stream_data(self.mcap_file, time_range=time_range, max_messages=3):
            count += 1
            simple_type = msg.message_type.split('.')[-1]
            log_info(f"   {count}. {simple_type} @ {msg.timestamp:.2f}s")

        if count == 0:
            log_info("   ⚠️  前10秒内无数据")

        log_info("")

    def _demo_statistics(self):
        """演示统计信息"""
        log_data_info(" 5. 统计信息演示")
        log_info("-" * 30)

        # 处理一些数据以生成统计
        count = 0
        for _ in self.sdk.stream_data(self.mcap_file, max_messages=100):
            count += 1

        stats = self.sdk.get_statistics()
        log_data_info(" 处理统计:")
        log_info(f"   扫描消息数: {stats.total_messages:,}")
        log_info(f"   处理消息数: {stats.processed_messages:,}")
        log_info(f"   过滤消息数: {stats.filtered_messages:,}")
        log_info(f"   处理时间: {stats.processing_time:.2f}s")
        log_info(f"   处理速率: {stats.get_processing_rate():.0f} 消息/秒")
        log_info(f"   过滤比率: {stats.get_filter_ratio():.1%}")

        log_info("")


def create_demo_parser() -> argparse.ArgumentParser:
    """创建演示程序的命令行解析器"""
    parser = argparse.ArgumentParser(
        description="MCAP自动驾驶SDK演示程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python -m src.mcap_demo_tools data/sample.mcap
  python -m src.mcap_demo_tools data/sample.mcap --quiet
  python -m src.mcap_demo_tools data/sample.mcap --analysis-only
        """
    )

    parser.add_argument(
        "mcap_file",
        help="MCAP文件路径"
    )

    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="静默模式，减少输出"
    )

    parser.add_argument(
        "--analysis-only",
        action="store_true",
        help="仅运行文件分析"
    )

    parser.add_argument(
        "--max-messages",
        type=int,
        default=100,
        help="最大处理消息数 (默认: 100)"
    )

    return parser


def main():
    """主函数"""
    parser = create_demo_parser()
    args = parser.parse_args()

    try:
        # 创建演示程序
        demo = McapDemo(args.mcap_file, verbose=not args.quiet)

        if args.analysis_only:
            # 仅运行分析
            demo._demo_file_analysis()
        else:
            # 运行完整演示
            demo.run_full_demo()

    except FileNotFoundError as e:
        log_error(f" 文件错误: {e}")
        sys.exit(1)
    except Exception as e:
        log_error(f" 程序错误: {e}")
        if not args.quiet:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
