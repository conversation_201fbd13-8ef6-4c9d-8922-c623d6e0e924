# exceptions.py

#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Desc: { 项目异常模块 }


class CommonException(Exception):
    """公共异常类"""

    def __init__(self, enum_cls):
        self.code = enum_cls.code
        self.msg = enum_cls.msg
        self.enum_cls = enum_cls	# 状态码枚举类
        super().__init__()


class BusinessException(CommonException):
    """业务异常类"""
    def __init__(self, enums_cls):
        super(BusinessException, self).__init__(enums_cls)


class APIException(CommonException):
    """接口异常类"""
    pass

