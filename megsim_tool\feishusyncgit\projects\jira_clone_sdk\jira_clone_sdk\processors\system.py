"""系统字段处理器"""

from typing import Any, Dict, Set
from datetime import datetime
from .base import FieldProcessor


class SystemFieldProcessor(FieldProcessor):
    """系统字段处理器"""
    
    SYSTEM_FIELDS: Set[str] = {'priority', 'labels', 'environment', 'duedate', 'components'}
    
    def __init__(self):
        super().__init__(priority=80)  # 中等优先级
    
    def can_process(self, field_name: str, field_value: Any = None) -> bool:
        return field_name in self.SYSTEM_FIELDS
    
    def process(self, field_name: str, field_value: Any, context: Dict[str, Any]) -> Any:
        """处理系统字段"""
        if field_name == 'labels':
            # 添加克隆标签
            labels = field_value or []
            if isinstance(labels, list):
                clone_label = f"克隆-{datetime.now().strftime('%m%d')}"
                if clone_label not in labels:
                    labels.append(clone_label)
            return labels
        elif field_name == 'components':
            # 保持原始组件
            return field_value
        elif field_name == 'priority':
            # 保持原始优先级
            return field_value
        elif field_name == 'environment':
            # 保持原始环境
            return field_value
        elif field_name == 'duedate':
            # 保持原始到期日期
            return field_value
        
        return field_value
    
    def validate_field(self, field_name: str, field_value: Any) -> bool:
        """验证系统字段"""
        if field_name == 'labels' and field_value is not None:
            return isinstance(field_value, list)
        if field_name == 'components' and field_value is not None:
            return isinstance(field_value, list)
        return True