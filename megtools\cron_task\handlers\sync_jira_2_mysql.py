import datetime
import json
import re

from basic.utils import Jira<PERSON>perate, DateUtil
from cron_task.models import CronJira2Mysql, E2eMcapInfo


def apply(task_config):
    Jira2Mysql(task_config).apply()

# 这里存储的是不变的数据， 变化的数据不存，因此存储的东西有限
class Jira2Mysql:
    def __init__(self, task_config):
        self.task_config = task_config
        self.jql = task_config.get("jql", "")
        self.trans_config = task_config.get("trans_config", {})
        # 对其去重
        self.jira_fields = []
        self.__init_jira_fields()
        self.jira_value_path = [item['jira_value_path'] for item in self.trans_config]

    def __init_jira_fields(self):
        for item in self.trans_config:
            field = item['jira_field']
            if isinstance(field, list):
                self.jira_fields.extend(field)
            else:
                self.jira_fields.append(field)
        self.jira_fields = list(dict.fromkeys(self.jira_fields))

    def apply(self):
        self.check()
        # 1. 查询MYSQL的最后更新时间，按照更新时间去查询上次更新之后的问题
        last_update_jira = CronJira2Mysql.objects.order_by("-updated").first()
        jira_data = self.query_jira(last_update_jira)
        self.update_lat_lon(list(jira_data.values()))
        self.write_2_mysql(jira_data)

    def update_lat_lon(self, jira_data):
        for item in jira_data:
            lat_lon = item.get('customfield_13400', None)
            if "customfield_13400" in item:
                del item['customfield_13400']
            if lat_lon and len(lat_lon.split(",")) == 2:
                item['lat'] = lat_lon.split(",")[0]
                item['lon'] = lat_lon.split(",")[1]

    def write_2_mysql(self, jira_data):
        current_key = [item for item in jira_data]
        # 先删除掉数据
        start = 0
        step = 500
        while True:
            CronJira2Mysql.objects.filter(key__in=current_key[start: start+step]).delete()
            start = start + step
            if start >= len(current_key):
                break
        insert_data = [CronJira2Mysql(**(jira_data.get(item))) for item in jira_data]
        # 然后批量将数据进行插入
        start = 0
        step = 500
        while True:
            CronJira2Mysql.objects.bulk_create(insert_data[start: start + step])
            start = start + step
            if start >= len(current_key):
                break


    def query_jira(self, last_update_jira):
        last_update_time = '2021-01-01 00:00'
        if last_update_jira:
            last_update_time = last_update_jira.updated.strftime("%Y-%m-%d %H:%M")
        # 添加SQL的片段
        self.jql = self.jql.replace("{{updated}}", last_update_time)
        jira_entities = JiraOperate().query_jira(self.jql, self.jira_fields)
        result = {}
        for item in jira_entities:
            result[item.get("key")] = self.trans_jira2mysql(item)
        return result

    def trans_jira2mysql(self, jira_entity):
        result = {}
        for item in self.trans_config:
            jira_field = item.get("jira_field", "")
            jira_path = item.get("jira_value_path", "")
            value = None
            if jira_path:
                # 如果jira_path 存在，通过jira_path 进行取值
                value = self.get_value_by_path(jira_path, jira_entity, item)
            result[jira_field] = value
            if jira_field == 'summary':
                result['point_time'] = self.extra_time(value)
        return result

    def extra_time(self, result):
        extra = None
        pattern = r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}'
        match = re.search(pattern, result)
        if match:
            extra = match.group()
            extra = extra.replace("T", " ")
        return extra

    # 通过配置的路径
    def get_value_by_path(self, jira_path, jira_entity, field_config):
        path_arr = jira_path.split(".")
        result = jira_entity
        for item in path_arr:
            if result is None or result == "":
                result = ""
                break
            if item == "[*]" and isinstance(result, list):
                temp = []
                for item in result:
                    temp.append(self.get_value_by_path(jira_path[jira_path.index("[*]")+4:], item, {}))
                result = temp
                break
            elif item.startswith("["):
                # 如果当前条目是数组下标则按照下标进行取值, 下标越界啥的都娶不到。
                if isinstance(result, list) and int(item[1:-1]) < len(result):
                    result = result[int(item[1:-1])]
                else:
                    result = ""
            else:
                result = result.get(item, "")
        if "format_type" in field_config:
            format_type = field_config.get("format_type", "%Y-%m-%d")
            if result:
                date_obj = datetime.datetime.strptime(result, format_type).strftime("%Y-%m-%d %H:%M:%S")
                return date_obj
        if isinstance(result, list):
            return json.dumps(result)
        return result

    def check(self):
        if not self.jql:
            raise Exception("请配置jql参数")


