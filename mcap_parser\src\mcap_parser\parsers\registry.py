"""
解析器注册表
管理所有解析器的注册和查找
"""

from typing import Dict, List, Optional, Callable, Any
from collections import defaultdict
import importlib
import inspect

from .base import BaseParser
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ParserRegistry:
    """
    解析器注册表
    管理所有解析器的注册、查找和生命周期
    """
    
    def __init__(self):
        self.parsers: Dict[str, Callable] = {}  # 消息类型 -> 解析器函数
        self.parser_objects: Dict[str, BaseParser] = {}  # 解析器名称 -> 解析器对象
        self.type_to_parser: Dict[str, str] = {}  # 消息类型 -> 解析器名称
        self.parser_metadata: Dict[str, Dict[str, Any]] = {}  # 解析器元数据
        
        # 自动加载内置解析器
        self._load_builtin_parsers()
        
        logger.info("解析器注册表初始化完成")
    
    def register(self, message_type: str, parser: Callable, metadata: Optional[Dict[str, Any]] = None):
        """
        注册解析器
        
        Args:
            message_type: 消息类型
            parser: 解析器函数或对象
            metadata: 解析器元数据
        """
        if message_type in self.parsers:
            logger.warning(f"消息类型 {message_type} 的解析器已存在，将被覆盖")
        
        self.parsers[message_type] = parser
        
        # 如果是BaseParser对象，记录对象引用
        if isinstance(parser, BaseParser):
            parser_name = parser.name
            self.parser_objects[parser_name] = parser
            self.type_to_parser[message_type] = parser_name
        elif hasattr(parser, '__name__'):
            parser_name = parser.__name__
            self.type_to_parser[message_type] = parser_name
        else:
            parser_name = f"parser_{id(parser)}"
            self.type_to_parser[message_type] = parser_name
        
        # 记录元数据
        if metadata:
            self.parser_metadata[parser_name] = metadata
        
        logger.info(f"注册解析器: {message_type} -> {parser_name}")
    
    def unregister(self, message_type: str):
        """
        注销解析器
        
        Args:
            message_type: 消息类型
        """
        if message_type not in self.parsers:
            logger.warning(f"消息类型 {message_type} 的解析器不存在")
            return
        
        parser_name = self.type_to_parser.get(message_type)
        
        # 清理注册表
        del self.parsers[message_type]
        if message_type in self.type_to_parser:
            del self.type_to_parser[message_type]
        
        # 如果没有其他消息类型使用此解析器，清理解析器对象
        if parser_name and parser_name not in self.type_to_parser.values():
            if parser_name in self.parser_objects:
                del self.parser_objects[parser_name]
            if parser_name in self.parser_metadata:
                del self.parser_metadata[parser_name]
        
        logger.info(f"注销解析器: {message_type}")
    
    def get_parser(self, message_type: str) -> Optional[Callable]:
        """
        获取解析器
        
        Args:
            message_type: 消息类型
            
        Returns:
            Optional[Callable]: 解析器函数，如果不存在返回None
        """
        return self.parsers.get(message_type)
    
    def get_parser_object(self, parser_name: str) -> Optional[BaseParser]:
        """
        获取解析器对象
        
        Args:
            parser_name: 解析器名称
            
        Returns:
            Optional[BaseParser]: 解析器对象，如果不存在返回None
        """
        return self.parser_objects.get(parser_name)
    
    def get_supported_types(self) -> List[str]:
        """获取所有支持的消息类型"""
        return list(self.parsers.keys())
    
    def get_parser_names(self) -> List[str]:
        """获取所有解析器名称"""
        return list(self.parser_objects.keys())
    
    def get_parser_info(self, message_type: str) -> Optional[Dict[str, Any]]:
        """
        获取解析器信息
        
        Args:
            message_type: 消息类型
            
        Returns:
            Optional[Dict]: 解析器信息，如果不存在返回None
        """
        if message_type not in self.parsers:
            return None
        
        parser = self.parsers[message_type]
        parser_name = self.type_to_parser.get(message_type, "unknown")
        
        info = {
            'message_type': message_type,
            'parser_name': parser_name,
            'parser_type': type(parser).__name__,
            'is_builtin': parser_name in self._get_builtin_parser_names(),
        }
        
        # 添加解析器对象的统计信息
        if parser_name in self.parser_objects:
            parser_obj = self.parser_objects[parser_name]
            info.update(parser_obj.get_statistics())
        
        # 添加元数据
        if parser_name in self.parser_metadata:
            info['metadata'] = self.parser_metadata[parser_name]
        
        return info
    
    def get_all_parser_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有解析器信息"""
        return {
            message_type: self.get_parser_info(message_type)
            for message_type in self.parsers.keys()
        }
    
    def register_from_module(self, module_name: str, prefix: str = "parse_"):
        """
        从模块中自动注册解析器
        
        Args:
            module_name: 模块名称
            prefix: 解析器函数前缀
        """
        try:
            module = importlib.import_module(module_name)
            
            for name, obj in inspect.getmembers(module):
                if name.startswith(prefix) and callable(obj):
                    # 从函数名推断消息类型
                    message_type = name[len(prefix):]  # 移除前缀
                    
                    # 转换命名格式 (如 parse_lane_array_v2 -> LaneArrayv2)
                    message_type = self._normalize_message_type(message_type)
                    
                    self.register(message_type, obj, {
                        'source_module': module_name,
                        'auto_registered': True
                    })
            
            logger.info(f"从模块 {module_name} 自动注册解析器完成")
        
        except ImportError as e:
            logger.error(f"无法导入模块 {module_name}: {e}")
        except Exception as e:
            logger.error(f"从模块 {module_name} 注册解析器失败: {e}")
    
    def _load_builtin_parsers(self):
        """加载内置解析器"""
        # 暂时跳过自动加载，避免导入问题
        logger.info("跳过内置解析器自动加载")
    
    def _get_builtin_parsers(self) -> Dict[str, Callable]:
        """获取内置解析器"""
        # 这里可以定义一些基本的内置解析器
        return {}
    
    def _get_builtin_parser_names(self) -> List[str]:
        """获取内置解析器名称列表"""
        return [
            'parse_perception_result',
            'parse_radar_object_array',
            'parse_lane_array_v2',
            'parse_raw_imu',
            'parse_point_cloud2',
            # 可以添加更多内置解析器名称
        ]
    
    def _normalize_message_type(self, type_name: str) -> str:
        """
        标准化消息类型名称
        
        Args:
            type_name: 原始类型名称
            
        Returns:
            str: 标准化后的类型名称
        """
        # 转换下划线命名为驼峰命名
        parts = type_name.split('_')
        return ''.join(word.capitalize() for word in parts)
    
    def validate_registry(self) -> Dict[str, List[str]]:
        """
        验证注册表的完整性
        
        Returns:
            Dict: 验证结果，包含错误和警告
        """
        errors = []
        warnings = []
        
        # 检查解析器函数是否可调用
        for message_type, parser in self.parsers.items():
            if not callable(parser):
                errors.append(f"解析器不可调用: {message_type}")
        
        # 检查解析器对象的一致性
        for parser_name, parser_obj in self.parser_objects.items():
            if not isinstance(parser_obj, BaseParser):
                errors.append(f"解析器对象类型错误: {parser_name}")
        
        # 检查孤立的元数据
        for parser_name in self.parser_metadata:
            if (parser_name not in self.parser_objects and 
                parser_name not in self.type_to_parser.values()):
                warnings.append(f"孤立的解析器元数据: {parser_name}")
        
        return {
            'errors': errors,
            'warnings': warnings,
            'total_parsers': len(self.parsers),
            'total_parser_objects': len(self.parser_objects)
        }
    
    def cleanup(self):
        """清理注册表"""
        # 清理解析器对象
        for parser_obj in self.parser_objects.values():
            if hasattr(parser_obj, 'cleanup'):
                try:
                    parser_obj.cleanup()
                except Exception as e:
                    logger.error(f"清理解析器对象失败: {e}")
        
        # 清空注册表
        self.parsers.clear()
        self.parser_objects.clear()
        self.type_to_parser.clear()
        self.parser_metadata.clear()
        
        logger.info("解析器注册表已清理")
    
    def __len__(self) -> int:
        """返回注册的解析器数量"""
        return len(self.parsers)
    
    def __contains__(self, message_type: str) -> bool:
        """检查是否包含指定消息类型的解析器"""
        return message_type in self.parsers
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ParserRegistry(解析器数量: {len(self.parsers)}, 解析器对象: {len(self.parser_objects)})"
