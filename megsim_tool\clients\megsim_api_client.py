import requests
import json
from urllib.parse import quote
from datetime import datetime

class MegSimAPIClient:
    def __init__(self, base_url="https://megsim.mc.machdrive.cn"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def search_event_records(self, vehicle_id=None, time_range=None, route=None, 
                           order="-id", limit=10, skip=0):
        """
        搜索事件记录
        
        Args:
            vehicle_id: 车辆ID列表
            time_range: 时间范围 [开始时间, 结束时间]
            route: 路线
            order: 排序方式，默认按ID倒序
            limit: 返回记录数量限制
            skip: 跳过记录数量
        
        Returns:
            dict: API响应数据
        """
        # 构建搜索参数
        search_params = {}
        if vehicle_id:
            search_params["vehicle_id"] = vehicle_id if isinstance(vehicle_id, list) else [vehicle_id]
        if time_range:
            search_params["time"] = time_range
        if route:
            search_params["route"] = route
        
        # 构建搜索参数JSON字符串
        search_json = json.dumps(search_params, separators=(',', ':'))
        
        # 构建完整URL
        url = f"{self.base_url}/api/event/records/multisearch/"
        params = {
            "order": order,
            "limit": limit,
            "skip": skip,
            "search": search_json
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None
    
    def get_record_groups(self, record_id):
        """
        获取指定记录的标签组
        
        Args:
            record_id: 记录ID
        
        Returns:
            dict: API响应数据
        """
        url = f"{self.base_url}/api/event/records/{record_id}/groups"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None

def main():
    # 创建API客户端
    client = MegSimAPIClient()
    
    print("=== MegSim API 测试 ===")
    print()
    
    # 测试搜索事件记录
    print("1. 搜索事件记录:")
    result = client.search_event_records(
        vehicle_id=[473],
        time_range=["2025-08-06", "2025-08-06"],
        route="LD3994",
        limit=10
    )
    
    if result:
        print(f"搜索结果:")
        print(f"  总记录数: {result.get('total', 0)}")
        print(f"  返回记录数: {len(result.get('records', []))}")
        print(f"  跳过记录数: {result.get('skip', 0)}")
        print(f"  限制记录数: {result.get('limit', 0)}")
        
        if result.get('records'):
            print("  记录详情:")
            for i, record in enumerate(result['records'][:3]):  # 只显示前3条
                print(f"    记录 {i+1}: {record}")
        else:
            print("  未找到匹配的记录")
    else:
        print("  请求失败")
    
    print()
    print("=== API调用示例 ===")
    print("原始请求URL:")
    print("https://megsim.mc.machdrive.cn/api/event/records/multisearch/?order=-id&limit=10&skip=0&search=%7B%22vehicle_id%22:[473],%22time%22:[%222025-08-06%22,%222025-08-06%22],%22route%22:%22LD3994%22%7D")
    print()
    print("解码后的搜索参数:")
    search_params = {
        "vehicle_id": [473],
        "time": ["2025-08-06", "2025-08-06"],
        "route": "LD3994"
    }
    print(json.dumps(search_params, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()