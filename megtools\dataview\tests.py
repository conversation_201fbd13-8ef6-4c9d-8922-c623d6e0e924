import traceback

import yaml
import pandas as pd
from datetime import datetime


def seconds_to_human_readable(seconds):
    """将秒数转换为人类可读的时间格式"""
    return datetime.fromtimestamp(seconds).strftime('%Y-%m-%d %H:%M:%S')


def process_text_segment(segment):
    """处理单个文本段，提取信息"""
    result_arr = []
    result = {}
    try:
        data = yaml.safe_load(segment)
        # 提取stamp.sec数值并转换
        result['stamp.sec (human readable)'] = seconds_to_human_readable(data.get("header",{}).get("stamp", {}).get("sec", 0))

        # 提取traffic_sign
        result['traffic_sign'] = data.get('traffic_sign', [])
        result['scence.time'] = data.get('scence', {}).get("time", "")
        result['scence.weather'] = data.get('scence', {}).get("weather", "")
        result['scence.scene'] = data.get('scence', {}).get("scene", "")
        result['scence.road'] = data.get('scence', {}).get("road", "")

        result['special_lane.left_lane'] = data.get('special_lane', {}).get("left_lane", "")
        result['special_lane.self_lane'] = data.get('special_lane', {}).get("self_lane", "")
        result['special_lane.right_lane'] = data.get('special_lane', {}).get("right_lane", "")
        result['opening_detect'] = handle_opening_detect(data.get('opening_detect', []))

    except yaml.YAMLError:
        print(f"{traceback.format_exc()}")
        pass

    # 根据 opening_detect 和 traffic_sign 找到最大的长度，复制数据
    traffic_sign = result['traffic_sign']
    opening_detect = result['opening_detect']
    if len(traffic_sign) <= 1 and len(opening_detect) <= 1:
        # 如果只有一个，那么直接返回，
        if result['traffic_sign'] and len(result['traffic_sign'])> 0:
            result['traffic_sign'] = "".join(result['traffic_sign'])
        if result['opening_detect'] and len(result['opening_detect'])> 0:
            result['opening_detect'] = "".join(opening_detect)
        return [result]
    for idx in range(max(len(traffic_sign), len(opening_detect))):
        # 如果是多个， 那么向里面填充数据
        keys = list(result.keys())
        values = ["" for _ in range(len(keys))]
        temp = dict(zip(keys, values))
        if idx == 0:
            temp = result
            temp['opening_detect'] = ""
            temp['traffic_sign'] = ""

        if idx < len(opening_detect):
            temp['opening_detect'] = opening_detect[idx]
        if idx < len(traffic_sign):
            temp['traffic_sign'] = traffic_sign[idx]
        result_arr.append(temp)
    return result_arr

def handle_opening_detect(opening_detect):
    temp = set()
    if isinstance(opening_detect, list):
        for item in opening_detect:
            for key in list(item.keys()):
                temp.add(key)
    elif isinstance(opening_detect, dict):
        for key in list(opening_detect.keys()):
            temp.add(key)
    return list(temp)

def process_file(file_path):
    """处理文件，分割文本段并提取信息"""
    all_results = []
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
        segments = content.split('---')
        for segment in segments:
            if segment.strip():
                segment_result = process_text_segment(segment)
                all_results.extend(segment_result)

    df = pd.DataFrame(all_results)
    df.to_excel('output.xlsx', index=False)


# 替换为你的文件路径
file_path = 'D:\\temp\\vlmpnc_cj1_20250307_2.txt'
process_file(file_path)