#!/usr/bin/env python3


from typing import Dict, List, Set


class MessageTypeRegistry:
    """消息类型注册表"""

    # 预定义的43种自动驾驶消息类型
    SUPPORTED_MESSAGE_TYPES = {
        # 感知数据类型
        "PerceptionResult": "感知融合结果",
        "RadarObjectArray": "雷达目标数组",
        "RadarObjectMessage": "雷达目标消息",
        "AEBObstacleArray": "AEB障碍物数组",
        "TrafficDetectData": "交通检测数据",

        # 车道线和地图数据类型
        "LaneArrayv2": "车道线数组v2",
        "EnvLaneArray": "环境车道线数组",
        "ReferenceLines": "参考线",
        "EntranceArray": "入口数组",
        "LocalMap": "局部地图",
        "AdasisMap": "ADASIS地图",
        "GlobalRouting": "全局路径规划",

        # 定位和导航数据类型
        "LocalizationEstimate": "定位估计",
        "GnssBestPose": "GNSS最佳位置",
        "Ins": "惯性导航系统",

        # 规划和控制数据类型
        "PlanningResult": "规划结果",
        "ControlResult": "控制结果",
        "ControlMonitorMsg": "控制监控消息",
        "MdriverResult": "驾驶员模型结果",

        # 传感器数据类型
        "RawImu": "原始IMU数据",
        "CorrectedImu": "校正IMU数据",
        "PointCloud2": "点云数据",
        "CompressedVideo": "压缩视频数据",

        # 车辆状态数据类型
        "VehicleReportCommon": "车辆报告通用",
        "FreespaceMatrix": "自由空间矩阵",
        "VirtualWallArray": "虚拟墙数组",

        # 可视化数据类型
        "MarkerArray": "标记数组",
        "AdsMarkerArrayVec": "ADS标记数组向量",

        # 其他专用数据类型
        "EnvInfo": "环境信息",
        "ExceptionMonitor": "异常监控",
        "FusionInfoForAEB": "AEB融合信息",
        "MdriverRefLines": "驾驶员模型参考线",
        "NaviAction": "导航动作",
        "NaviSocketStream": "导航Socket流",
        "OCCVisualization": "OCC可视化",
        "ObstacleTimestamp": "障碍物时间戳",
        "PlanningLog": "规划日志",
        "SFFusionTFLListNOA": "SF融合交通灯列表NOA",
        "SFFusionTSListNOA": "SF融合交通标志列表NOA",
        "Serialize": "序列化数据",
        "SerializeProto": "序列化Proto数据",
        "StateContext": "状态上下文",
        "String": "字符串消息"
    }

    @classmethod
    def is_supported(cls, message_type: str) -> bool:
        """检查消息类型是否被支持"""
        # 直接匹配
        if message_type in cls.SUPPORTED_MESSAGE_TYPES:
            return True
        
        # 尝试从完整类型名中提取简化名称
        simple_type = cls._extract_simple_type(message_type)
        return simple_type in cls.SUPPORTED_MESSAGE_TYPES

    @classmethod
    def get_description(cls, message_type: str) -> str:
        """获取消息类型的描述"""
        if message_type in cls.SUPPORTED_MESSAGE_TYPES:
            return cls.SUPPORTED_MESSAGE_TYPES[message_type]
        
        simple_type = cls._extract_simple_type(message_type)
        if simple_type in cls.SUPPORTED_MESSAGE_TYPES:
            return cls.SUPPORTED_MESSAGE_TYPES[simple_type]
        
        return "未知消息类型"

    @classmethod
    def get_all_types(cls) -> List[str]:
        """获取所有支持的消息类型"""
        return list(cls.SUPPORTED_MESSAGE_TYPES.keys())

    @classmethod
    def get_supported_count(cls) -> int:
        """获取支持的消息类型数量"""
        return len(cls.SUPPORTED_MESSAGE_TYPES)

    @classmethod
    def filter_supported_types(cls, message_types: List[str]) -> List[str]:
        """过滤出支持的消息类型"""
        return [msg_type for msg_type in message_types if cls.is_supported(msg_type)]

    @classmethod
    def filter_unsupported_types(cls, message_types: List[str]) -> List[str]:
        """过滤出不支持的消息类型"""
        return [msg_type for msg_type in message_types if not cls.is_supported(msg_type)]

    @classmethod
    def get_categories(cls) -> Dict[str, List[str]]:
        """获取消息类型分类"""
        categories = {
            "感知数据": [
                "PerceptionResult", "RadarObjectArray", "RadarObjectMessage", 
                "AEBObstacleArray", "TrafficDetectData"
            ],
            "车道线地图": [
                "LaneArrayv2", "EnvLaneArray", "ReferenceLines", "EntranceArray",
                "LocalMap", "AdasisMap", "GlobalRouting"
            ],
            "定位导航": [
                "LocalizationEstimate", "GnssBestPose", "Ins"
            ],
            "规划控制": [
                "PlanningResult", "ControlResult", "ControlMonitorMsg", "MdriverResult"
            ],
            "传感器": [
                "RawImu", "CorrectedImu", "PointCloud2", "CompressedVideo"
            ],
            "车辆状态": [
                "VehicleReportCommon", "FreespaceMatrix", "VirtualWallArray"
            ],
            "可视化": [
                "MarkerArray", "AdsMarkerArrayVec"
            ],
            "专用消息": [
                "EnvInfo", "ExceptionMonitor", "FusionInfoForAEB", "MdriverRefLines",
                "NaviAction", "NaviSocketStream", "OCCVisualization", "ObstacleTimestamp",
                "PlanningLog", "SFFusionTFLListNOA", "SFFusionTSListNOA", "Serialize",
                "SerializeProto", "StateContext", "String"
            ]
        }
        return categories

    @classmethod
    def get_category_for_type(cls, message_type: str) -> str:
        """获取消息类型所属的分类"""
        categories = cls.get_categories()
        simple_type = cls._extract_simple_type(message_type)
        
        for category, types in categories.items():
            if simple_type in types:
                return category
        
        return "未分类"

    @classmethod
    def get_types_by_category(cls, category: str) -> List[str]:
        """根据分类获取消息类型列表"""
        categories = cls.get_categories()
        return categories.get(category, [])

    @classmethod
    def search_types(cls, keyword: str) -> List[str]:
        """搜索包含关键词的消息类型"""
        keyword = keyword.lower()
        results = []
        
        for msg_type, description in cls.SUPPORTED_MESSAGE_TYPES.items():
            if (keyword in msg_type.lower() or 
                keyword in description.lower()):
                results.append(msg_type)
        
        return results

    @classmethod
    def get_statistics(cls) -> Dict[str, int]:
        """获取消息类型统计信息"""
        categories = cls.get_categories()
        stats = {
            "总消息类型数": len(cls.SUPPORTED_MESSAGE_TYPES),
            "分类数": len(categories)
        }
        
        for category, types in categories.items():
            stats[f"{category}类型数"] = len(types)
        
        return stats

    @classmethod
    def _extract_simple_type(cls, full_type: str) -> str:
        """从完整类型名中提取简化类型名"""
        # 处理类似 "mcap_ros2._dynamic.LaneArrayv2" 的情况
        if "." in full_type:
            return full_type.split(".")[-1]
        return full_type

    @classmethod
    def validate_types(cls, message_types: List[str]) -> Dict[str, List[str]]:
        """验证消息类型列表"""
        supported = []
        unsupported = []
        
        for msg_type in message_types:
            if cls.is_supported(msg_type):
                supported.append(msg_type)
            else:
                unsupported.append(msg_type)
        
        return {
            "supported": supported,
            "unsupported": unsupported,
            "support_ratio": len(supported) / len(message_types) if message_types else 0.0
        }

    @classmethod
    def get_type_mapping(cls) -> Dict[str, str]:
        """获取类型映射表 (完整名称 -> 简化名称)"""
        mapping = {}
        
        # 添加直接映射
        for msg_type in cls.SUPPORTED_MESSAGE_TYPES:
            mapping[msg_type] = msg_type
        
        # 添加常见的完整名称映射
        common_prefixes = [
            "mcap_ros2._dynamic.",
            "autoware_auto_msgs.msg.",
            "sensor_msgs.msg.",
            "geometry_msgs.msg.",
            "nav_msgs.msg."
        ]
        
        for prefix in common_prefixes:
            for msg_type in cls.SUPPORTED_MESSAGE_TYPES:
                full_name = f"{prefix}{msg_type}"
                mapping[full_name] = msg_type
        
        return mapping


class MessageRouter:
    """消息路由器，负责将消息路由到合适的解析器"""
    
    def __init__(self):
        self.registry = MessageTypeRegistry()
        self.routes = {}
    
    def register_route(self, message_type: str, parser_class):
        """注册消息类型到解析器的路由"""
        self.routes[message_type] = parser_class
    
    def route_message(self, message_type: str, message_data):
        """路由消息到对应的解析器"""
        if message_type in self.routes:
            parser = self.routes[message_type]()
            return parser.parse(message_data)
        else:
            # 使用通用解析器
            from .generic import GenericParser
            parser = GenericParser()
            return parser.parse(message_data)
    
    def get_supported_types(self) -> List[str]:
        """获取支持的消息类型列表"""
        return list(self.registry.SUPPORTED_MESSAGE_TYPES.keys())
    
    def is_supported(self, message_type: str) -> bool:
        """检查消息类型是否支持"""
        return self.registry.is_supported(message_type)
