from datetime import timedelta


def reform_acu_top_chart(data, indicator):
    # 1. 根据数据组织x轴，如果没有的数据，需要填充
    result = {
        "xAxis": [],
        "yAxis": {}
    }
    for item in indicator:
        result["yAxis"][item] = []

    if data is None or len(data) == 0:
        return result

    before = data[0]['record_time']
    for item in data:
        current = item['record_time']
        diff = (current - before).seconds
        if diff > 30:
            for idx in range(0, int(diff / 30) - 1):
                before = before + timedelta(seconds=30)
                result["xAxis"].append(before.strftime("%Y-%m-%d %H:%M:%S"))
                for indicate in indicator:
                    result["yAxis"][indicate].append(None)
        # 下面处理正常的
        result["xAxis"].append(current.strftime("%Y-%m-%d %H:%M:%S"))
        for indicate in indicator:
            result["yAxis"][indicate].append(item.get(indicate, None))
        before = current
    return result