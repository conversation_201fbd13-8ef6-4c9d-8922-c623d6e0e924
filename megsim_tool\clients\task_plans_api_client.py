import requests
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

class MegSimTaskPlansClient:
    """
    MegSim 任务计划API客户端
    提供任务计划创建和管理功能
    """
    
    def __init__(self, base_url: str = "https://megsim.mc.machdrive.cn"):
        self.base_url = base_url.rstrip('/')
        self.task_plans_endpoint = f"{self.base_url}/api/task_plans/"
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
    
    def create_task_plan(self, 
                        name: str,
                        project: int,
                        task_ids: List[int],
                        task_type: int = 20,
                        plan_type: int = 0,
                        is_mdriver: int = 1,
                        pro_branch: Optional[str] = None,
                        commit_id: Optional[str] = None,
                        replace_model: Optional[str] = None,
                        module_branch: Optional[str] = None,
                        module_commit: Optional[str] = None) -> Dict[str, Any]:
        """
        创建任务计划
        
        Args:
            name: 任务计划名称
            project: 项目ID
            task_ids: 关联任务ID列表
            task_type: 任务类型，默认20
            plan_type: 计划类型，默认0
            is_mdriver: 是否为MDriver，默认1
            pro_branch: 项目分支
            commit_id: 提交ID
            replace_model: 替换模型
            module_branch: 模块分支
            module_commit: 模块提交
        
        Returns:
            创建结果信息
        """
        payload = {
            "name": name,
            "project": project,
            "task_ids": json.dumps(task_ids),
            "task_type": task_type,
            "type": plan_type,
            "is_mdriver": is_mdriver,
            "pro_branch": pro_branch or "",
            "commit_id": commit_id or "",
            "replace_model": replace_model or "",
            "module_branch": module_branch or "",
            "module_commit": module_commit or ""
        }
        
        try:
            response = self.session.post(self.task_plans_endpoint, json=payload, timeout=30)
            
            # 处理响应
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                response_data = {"text": response.text}
            
            return {
                "success": response.status_code in [200, 201],
                "status_code": response.status_code,
                "data": response_data,
                "request_payload": payload
            }
            
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": str(e),
                "status_code": None,
                "request_payload": payload
            }
    
    def create_task_plan_with_unique_name(self, 
                                         base_name: str,
                                         project: int,
                                         task_ids: List[int],
                                         **kwargs) -> Dict[str, Any]:
        """
        创建任务计划，自动处理名称重复问题
        
        Args:
            base_name: 基础名称
            project: 项目ID
            task_ids: 关联任务ID列表
            **kwargs: 其他参数
        
        Returns:
            创建结果信息
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_name = f"{base_name}_{timestamp}"
        
        return self.create_task_plan(
            name=unique_name,
            project=project,
            task_ids=task_ids,
            **kwargs
        )
    
    def validate_task_plan_params(self, 
                                 name: str,
                                 project: int,
                                 task_ids: List[int]) -> Dict[str, Any]:
        """
        验证任务计划参数
        
        Args:
            name: 任务计划名称
            project: 项目ID
            task_ids: 关联任务ID列表
        
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        
        # 验证必填参数
        if not name or not name.strip():
            errors.append("任务计划名称不能为空")
        
        if not isinstance(project, int) or project <= 0:
            errors.append("项目ID必须是正整数")
        
        if not task_ids or not isinstance(task_ids, list):
            errors.append("任务ID列表不能为空")
        elif not all(isinstance(tid, int) and tid > 0 for tid in task_ids):
            errors.append("任务ID列表必须包含有效的正整数")
        
        # 检查警告
        if len(name) > 100:
            warnings.append("任务计划名称过长，建议控制在100字符以内")
        
        if len(task_ids) > 50:
            warnings.append("关联任务数量较多，可能影响性能")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def generate_curl_command(self, payload: Dict[str, Any]) -> str:
        """
        生成等效的cURL命令
        
        Args:
            payload: 请求数据
        
        Returns:
            cURL命令字符串
        """
        curl_cmd = f"curl -X POST '{self.task_plans_endpoint}' \\\n"
        
        for header, value in self.session.headers.items():
            curl_cmd += f"  -H '{header}: {value}' \\\n"
        
        curl_cmd += f"  -d '{json.dumps(payload)}'"
        
        return curl_cmd
    
    def analyze_error_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析错误响应
        
        Args:
            response_data: 响应数据
        
        Returns:
            错误分析结果
        """
        analysis = {
            "error_type": "unknown",
            "description": "未知错误",
            "suggestions": []
        }
        
        if "error_message" in response_data:
            error_msg = response_data["error_message"]
            
            if "重复" in error_msg:
                analysis["error_type"] = "duplicate_data"
                analysis["description"] = "数据重复错误"
                analysis["suggestions"] = [
                    "检查是否已存在相同的任务计划",
                    "修改任务计划名称",
                    "使用不同的参数组合",
                    "使用create_task_plan_with_unique_name方法自动生成唯一名称"
                ]
            elif "权限" in error_msg:
                analysis["error_type"] = "permission_denied"
                analysis["description"] = "权限不足"
                analysis["suggestions"] = [
                    "检查用户权限",
                    "联系管理员获取相应权限"
                ]
            elif "参数" in error_msg or "格式" in error_msg:
                analysis["error_type"] = "invalid_parameters"
                analysis["description"] = "参数格式错误"
                analysis["suggestions"] = [
                    "检查参数格式是否正确",
                    "验证必填参数是否完整",
                    "使用validate_task_plan_params方法验证参数"
                ]
        
        return analysis

def main():
    """
    测试任务计划API客户端
    """
    client = MegSimTaskPlansClient()
    
    print("=== MegSim 任务计划API客户端测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试参数
    test_params = {
        "name": "API客户端测试计划",
        "project": 4,
        "task_ids": [424160],
        "pro_branch": "test/api_client",
        "task_type": 20
    }
    
    # 验证参数
    print("1. 参数验证")
    validation = client.validate_task_plan_params(
        test_params["name"],
        test_params["project"],
        test_params["task_ids"]
    )
    
    if validation["valid"]:
        print("✅ 参数验证通过")
        if validation["warnings"]:
            for warning in validation["warnings"]:
                print(f"⚠️ 警告: {warning}")
    else:
        print("❌ 参数验证失败")
        for error in validation["errors"]:
            print(f"错误: {error}")
        return
    
    print()
    
    # 测试创建任务计划（使用唯一名称）
    print("2. 创建任务计划（自动生成唯一名称）")
    base_name = test_params.pop("name")
    result = client.create_task_plan_with_unique_name(
        base_name=base_name,
        **test_params
    )
    
    if result["success"]:
        print("✅ 任务计划创建成功")
        print(f"状态码: {result['status_code']}")
        if "data" in result:
            print("响应数据:")
            print(json.dumps(result["data"], indent=2, ensure_ascii=False))
    else:
        print("❌ 任务计划创建失败")
        print(f"状态码: {result.get('status_code', 'N/A')}")
        
        if "data" in result:
            # 分析错误
            error_analysis = client.analyze_error_response(result["data"])
            print(f"错误类型: {error_analysis['error_type']}")
            print(f"错误描述: {error_analysis['description']}")
            print("建议解决方案:")
            for suggestion in error_analysis["suggestions"]:
                print(f"  - {suggestion}")
    
    print()
    
    # 生成cURL命令
    print("3. 等效cURL命令")
    if "request_payload" in result:
        curl_cmd = client.generate_curl_command(result["request_payload"])
        print(curl_cmd)

if __name__ == "__main__":
    main()