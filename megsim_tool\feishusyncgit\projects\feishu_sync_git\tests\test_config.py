"""配置模块测试"""

import pytest
import os
import tempfile
from pathlib import Path
from src.config import Settings, FeishuConfig, GitLabConfig


class TestSettings:
    """设置类测试"""
    
    def test_from_env_with_values(self):
        """测试从环境变量创建配置"""
        # 设置环境变量
        env_vars = {
            "FEISHU_APP_ID": "test_app_id",
            "FEISHU_APP_SECRET": "test_secret",
            "FEISHU_APP_TOKEN": "test_token",
            "FEISHU_TABLE_ID": "test_table",
            "GITLAB_URL": "https://gitlab.test.com",
            "GITLAB_TOKEN": "gitlab_token",
            "POLL_INTERVAL": "30",
            "DEBUG": "true"
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
        
        try:
            settings = Settings.from_env()
            
            assert settings.feishu.app_id == "test_app_id"
            assert settings.feishu.app_secret == "test_secret"
            assert settings.gitlab.url == "https://gitlab.test.com"
            assert settings.polling.interval == 30
            assert settings.debug is True
            
        finally:
            # 清理环境变量
            for key in env_vars:
                os.environ.pop(key, None)
    
    def test_from_file_yaml(self):
        """测试从YAML文件创建配置"""
        config_content = """
feishu:
  app_id: "yaml_app_id"
  app_secret: "yaml_secret"
  app_token: "yaml_token"
  table_id: "yaml_table"

gitlab:
  url: "https://yaml-gitlab.com"
  token: "yaml_token"

polling:
  interval: 45

debug: true
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yml', delete=False) as f:
            f.write(config_content)
            config_path = Path(f.name)
        
        try:
            settings = Settings.from_file(config_path)
            
            assert settings.feishu.app_id == "yaml_app_id"
            assert settings.gitlab.url == "https://yaml-gitlab.com"
            assert settings.polling.interval == 45
            assert settings.debug is True
            
        finally:
            config_path.unlink()
    
    def test_validate_success(self, mock_settings):
        """测试配置验证成功"""
        # 不应该抛出异常
        mock_settings.validate()
    
    def test_validate_missing_feishu_config(self):
        """测试缺少飞书配置的验证"""
        settings = Settings(
            feishu=FeishuConfig(app_id="", app_secret="", app_token="", table_id=""),
            gitlab=GitLabConfig(url="https://test.com", token="token")
        )
        
        with pytest.raises(ValueError) as exc_info:
            settings.validate()
        
        assert "飞书 APP_ID 不能为空" in str(exc_info.value)
    
    def test_validate_invalid_polling_config(self, mock_settings):
        """测试无效的轮询配置"""
        mock_settings.polling.interval = -1
        
        with pytest.raises(ValueError) as exc_info:
            mock_settings.validate()
        
        assert "轮询间隔必须大于0" in str(exc_info.value)
