[tool:pytest]
# Pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-report=term-missing
    --cov-fail-under=80
    --durations=10
    --color=yes
    -m "not slow"

# 标记定义
markers =
    slow: 标记测试为慢速测试
    integration: 标记为集成测试
    unit: 标记为单元测试
    performance: 标记为性能测试
    smoke: 标记为冒烟测试
    regression: 标记为回归测试
    mcap: 标记为MCAP相关测试
    parser: 标记为解析器测试
    sdk: 标记为SDK测试

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*pkg_resources.*:UserWarning

# 最小版本要求
minversion = 7.0

# 测试超时（秒）
timeout = 300

# 并行测试
# 取消注释以启用并行测试
# addopts = -n auto

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 测试数据目录
testmon = true