# result.py

#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Desc: { 项目信息返回结果模块 }
from .enums import StatusCodeEnum


class R(object):
    """
    统一项目信息返回结果类
    """
    def __init__(self):
        self.code = None
        self.msg = None

    @staticmethod
    def ok():
        """
        组织成功响应信息
        :return:
        """
        r = R()
        r.code = StatusCodeEnum.OK.code
        r.msg = StatusCodeEnum.OK.msg
        return r

    @staticmethod
    def error():
        """
        组织错误响应信息
        :return:
        """
        r = R()
        r.code = StatusCodeEnum.ERROR.code
        r.msg = StatusCodeEnum.ERROR.msg
        return r

    @staticmethod
    def auth_fail():
        """
        组织错误响应信息
        :return:
        """
        r = R()
        r.code = StatusCodeEnum.TOKEN_ERR.code
        r.msg = StatusCodeEnum.TOKEN_ERR.msg
        return r

    @staticmethod
    def server_error():
        """
        组织服务器错误信息
        :return:
        """
        r = R()
        r.code = StatusCodeEnum.SERVER_ERR.code
        r.msg = StatusCodeEnum.SERVER_ERR.msg
        return r

    @staticmethod
    def set_result(enum):
        """
        组织对应枚举类的响应信息
        :param enum: 状态枚举类
        :return:
        """
        r = R()
        r.code = enum.code
        r.msg = enum.msg
        return r

    def data(self):
        """统一后端返回的数据"""
        context = {
            'code': self.code,
            'msg': self.msg
        }
        return context
