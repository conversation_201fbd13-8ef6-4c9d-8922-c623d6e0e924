import os
import re
import json
import time
import argparse
import logging
import requests
import configparser
from datetime import datetime
from jira import JIRA
from typing import List, Optional, Dict, Any, Tuple
import sys

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', handlers=[logging.StreamHandler()])
logger = logging.getLogger(__name__)

class ConfigManager:
    def __init__(self, config_path=None):
        self.config_path = self._resolve_config_path(config_path)
        self.parser = configparser.ConfigParser()
        self._load_config()
    
    def _resolve_config_path(self, config_path):
        if config_path:
            return config_path
        base_dir = os.path.dirname(sys.executable) if getattr(sys, 'frozen', False) else os.path.dirname(os.path.abspath(__file__))
        return os.path.join(base_dir, 'config.ini')
    
    def _load_config(self):
        if not os.path.exists(self.config_path):
            self.create_default_config()
            raise FileNotFoundError(f"配置文件 {self.config_path} 不存在，已创建默认配置")
        self.parser.read(self.config_path, encoding='utf-8')
    
    def create_default_config(self):
        config = configparser.ConfigParser()
        config['DeepSeek'] = {
            'api_key': 'your_deepseek_api_key_here',
            'api_url': 'https://api.deepseek.com/v1/chat/completions',
            'max_retries': '3',
            'retry_delay': '5',
            'max_comment_length': '2000'
        }
        config['Jira'] = {
            'url': 'https://your-jira-instance.com',
            'user': 'your_jira_username',
            'password': 'your_jira_password',
            'jql': 'project = YOURPROJECT AND comment IS NOT EMPTY',
        }
        config['LabelGeneration'] = {
            'system_prompt': """你是一个算法分类专家，负责根据技术讨论内容打上合适的算法标签。
请根据评论内容判断其涉及的核心算法问题，从以下标签列表中选择1-3个最相关的标签：

位置错误, 曲率错误, 多线重合, 实例断连, 实例多连, 实例弯折, 实例乱线, 漏检, 
类型错误, 系统问题, 可视化问题, 类型整体错误, 类型部分错误, 类型跳变错误, 
漏检道路线, 漏检路沿, 漏检停止线, 漏检driveline, 漏检地面箭头, 漏检禁停区, 
漏检斑马线, 检出距离不足, 漏检锥桶线, 系统性问题

规则：
1. 只能从上述列表中选择标签
2. 不要创建新标签
3. 不要解释或添加其他内容
4. 多个标签用逗号分隔
5. 如果没有匹配标签，返回"系统问题"

示例评论：高速场景下，路沿检测不连续，有断断续续的情况
示例回答：漏检路沿

当前评论：""",
            'max_labels': '3',
            'json_output_dir': './label_data',
            'comments_to_analyze': '3',
            'min_comment_length': '50'
        }
        with open(self.config_path, 'w', encoding='utf-8') as configfile:
            config.write(configfile)
        logger.info(f"配置文件 {self.config_path} 已创建，请修改其中的配置项")
    
    def get(self, section, option, default=''):
        try:
            return self.parser.get(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default
    
    def getint(self, section, option, default=0):
        try:
            return self.parser.getint(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default

class JiraClient:
    def __init__(self, config):
        self.config = config
        self.jira = self._connect_jira()
    
    def _connect_jira(self):
        try:
            jira_url = self.config.get('Jira', 'url')
            jira_user = self.config.get('Jira', 'user')
            jira_password = self.config.get('Jira', 'password')
            if not all([jira_url, jira_user, jira_password]):
                logger.error("Jira配置信息不完整")
                return None
            return JIRA(server=jira_url, basic_auth=(jira_user, jira_password), options={'verify': True}, timeout=30)
        except Exception as e:
            logger.error(f"连接Jira失败: {str(e)}")
            return None
    
    def get_issues(self, jql=None):
        if self.jira is None:
            logger.error("Jira连接未建立，无法获取问题")
            return []
        try:
            base_jql = jql or self.config.get('Jira', 'jql')
            logger.info(f"正在执行 JQL 查询: {base_jql}")
            all_issues = []
            start_at = 0
            max_results = 100
            total = None
            while total is None or start_at < total:
                issues = self.jira.search_issues(base_jql, startAt=start_at, maxResults=max_results, fields="summary,comment,labels")
                all_issues.extend(issues)
                if total is None:
                    total = issues.total
                start_at += len(issues)
                logger.info(f"已获取 {len(all_issues)}/{total} 个问题")
            logger.info(f"找到 {len(all_issues)} 个匹配问题")
            return all_issues
        except Exception as e:
            logger.error(f"获取问题失败: {str(e)}")
            return []
    
    def add_labels_to_issue(self, issue, new_labels):
        if not new_labels:
            return False
        try:
            current_labels = issue.fields.customfield_13000 or []
            labels_to_add = [label for label in new_labels if label not in current_labels]
            if not labels_to_add:
                logger.info(f"问题 {issue.key} 已包含所有算法标签，无需更新")
                return False
            updated_labels = current_labels + labels_to_add
            issue.update(fields={'customfield_13000': updated_labels})
            logger.info(f"已为问题 {issue.key} 添加标签: {', '.join(labels_to_add)}")
            return True
        except Exception as e:
            logger.error(f"更新问题 {issue.key} 标签失败: {str(e)}")
            return False
    
    def update_labels_from_json(self, json_data):
        if self.jira is None:
            logger.error("Jira连接未建立，无法更新标签")
            return {'total': 0, 'updated': 0, 'skipped': 0, 'failed': 0}
        stats = {'total': 0, 'updated': 0, 'skipped': 0, 'failed': 0}
        for issue_key, label_data in json_data.items():
            stats['total'] += 1
            try:
                issue = self.jira.issue(issue_key)
                generated_labels = label_data.get('generated_labels', [])
                if not generated_labels:
                    logger.info(f"问题 {issue_key} 没有生成的标签，跳过")
                    stats['skipped'] += 1
                    continue
                if self.add_labels_to_issue(issue, generated_labels):
                    stats['updated'] += 1
                else:
                    stats['skipped'] += 1
                time.sleep(0.5)
            except Exception as e:
                logger.error(f"处理问题 {issue_key} 失败: {str(e)}")
                stats['failed'] += 1
        return stats

class DeepSeekClient:
    def __init__(self, config):
        self.config = config
        self.valid_labels = [
            "位置错误", "曲率错误", "多线重合", "实例断连", "实例多连", 
            "实例弯折", "实例乱线", "漏检", "类型错误", "系统问题", 
            "可视化问题", "类型整体错误", "类型部分错误", "类型跳变错误", 
            "漏检道路线", "漏检路沿", "漏检停止线", "漏检driveline", 
            "漏检地面箭头", "漏检禁停区", "漏检斑马线", "检出距离不足", 
            "漏检锥桶线", "系统性问题"
        ]
        self.api_key = config.get('DeepSeek', 'api_key')
        self.api_url = config.get('DeepSeek', 'api_url', 'https://api.deepseek.com/v1/chat/completions')
        self.max_retries = config.getint('DeepSeek', 'max_retries', 3)
        self.retry_delay = config.getint('DeepSeek', 'retry_delay', 5)
        self.system_prompt = config.get('LabelGeneration', 'system_prompt', '')
        self.max_labels = config.getint('LabelGeneration', 'max_labels', 3)
        self.max_comment_length = config.getint('DeepSeek', 'max_comment_length', 2000)
    
    def generate_labels(self, comment_content):
        if not self.api_key or self.api_key == 'your_deepseek_api_key_here':
            logger.error("DeepSeek API key 未配置")
            return []
        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": comment_content}
            ],
            "temperature": 0.1,
            "max_tokens": 100,
            "stop": ["\n\n"]
        }
        for attempt in range(self.max_retries + 1):
            try:
                response = requests.post(self.api_url, headers=headers, json=payload, timeout=30)
                response.raise_for_status()
                result = response.json()
                content = result["choices"][0]["message"]["content"].strip()
                return self.parse_labels(content)
            except requests.exceptions.RequestException as e:
                if attempt < self.max_retries:
                    logger.warning(f"API请求失败（尝试 {attempt+1}/{self.max_retries}）: {str(e)}")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"DeepSeek API 请求失败: {str(e)}")
            except (KeyError, ValueError) as e:
                logger.error(f"DeepSeek API 响应解析失败: {str(e)}")
            except Exception as e:
                logger.error(f"DeepSeek API 调用失败: {str(e)}")
        return []
    
    def parse_labels(self, response):
        cleaned = re.sub(r'[^\w\u4e00-\u9fa5,，]', '', response)
        labels = []
        for part in re.split(r'[,，]', cleaned):
            part = part.strip()
            if part and part in self.valid_labels:
                labels.append(part)
        return labels[:self.max_labels] if labels else ["系统问题"]

class CommentLabeler:
    def __init__(self):
        self.config = ConfigManager()
        self.jira_client = JiraClient(self.config)
        self.deepseek_client = DeepSeekClient(self.config)
        self.json_output_dir = self.config.get('LabelGeneration', 'json_output_dir', './label_data')
        self.comments_to_analyze = self.config.getint('LabelGeneration', 'comments_to_analyze', 3)
        self.min_comment_length = self.config.getint('LabelGeneration', 'min_comment_length', 50)
    
    def generate_labels_json(self, jql=None, output_file=None):
        issues = self.jira_client.get_issues(jql)
        if not issues:
            logger.info("没有找到需要处理的问题")
            return ""
        os.makedirs(self.json_output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if not output_file:
            output_file = os.path.join(self.json_output_dir, f"jira_comment_labels_{timestamp}.json")
        label_data = {}
        stats = {'total_issues': 0, 'with_comments': 0, 'labeled': 0, 'no_comments': 0, 'short_comments': 0, 'skipped_existing': 0}
        for issue in issues:
            issue_key = issue.key
            stats['total_issues'] += 1
            logger.info(f"\n处理问题: {issue_key} - {issue.fields.summary}")
            current_labels = issue.fields.labels or []
            logger.info(f"当前标签: {', '.join(current_labels) if current_labels else '无'}")
            comment_content, comment_count = self._collect_issue_comments(issue)
            if not comment_content:
                logger.warning("问题没有评论或评论为空，跳过")
                stats['no_comments'] += 1
                continue
            stats['with_comments'] += 1
            if len(comment_content) < self.min_comment_length:
                logger.warning(f"评论内容过短 ({len(comment_content)} 字符)，跳过")
                stats['short_comments'] += 1
                continue
            logger.info(f"分析 {comment_count} 条评论 (共 {len(issue.fields.comment.comments)} 条)")
            labels = self.deepseek_client.generate_labels(comment_content)
            if not labels:
                logger.warning("未生成有效标签")
                continue
            new_labels = [label for label in labels if label not in current_labels]
            if not new_labels:
                logger.info("所有标签已存在，无需添加")
                stats['skipped_existing'] += 1
                continue
            label_data[issue_key] = {
                "summary": issue.fields.summary,
                "comment_count": comment_count,
                "comment_length": len(comment_content),
                "comment_content": comment_content,
                "generated_labels": new_labels,
                "existing_labels": current_labels,
                "generated_at": datetime.now().isoformat()
            }
            stats['labeled'] += 1
            logger.info(f"添加新算法标签: {', '.join(new_labels)}")
            time.sleep(1)
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(label_data, f, indent=2, ensure_ascii=False)
            logger.info("\n处理完成!")
            logger.info(f"总计问题: {stats['total_issues']}")
            logger.info(f"有评论的问题: {stats['with_comments']}")
            logger.info(f"成功添加标签: {stats['labeled']}")
            logger.info(f"无评论的问题: {stats['no_comments']}")
            logger.info(f"评论过短的问题: {stats['short_comments']}")
            logger.info(f"标签已存在的问题: {stats['skipped_existing']}")
            logger.info(f"\n标签数据已保存到: {output_file}")
            return output_file
        except Exception as e:
            logger.error(f"保存JSON文件失败: {str(e)}")
            return ""
    
    def _collect_issue_comments(self, issue):
        if not issue.fields.comment or not issue.fields.comment.comments:
            return None, 0
        comments = issue.fields.comment.comments
        logger.info(f"问题有 {len(comments)} 条评论")
        num_to_analyze = min(self.comments_to_analyze, len(comments))
        selected_comments = comments[-num_to_analyze:]
        content = ""
        max_length = self.deepseek_client.max_comment_length
        current_length = 0
        for i, comment in enumerate(reversed(selected_comments), 1):
            comment_text = f"评论 {i} (作者: {comment.author.displayName}, 时间: {comment.created}):\n{comment.body}\n\n"
            if current_length + len(comment_text) > max_length:
                remaining = max_length - current_length
                if remaining > 100:
                    content += comment_text[:remaining] + "\n[内容截断]"
                break
            content += comment_text
            current_length += len(comment_text)
        return content, num_to_analyze
    
    def update_labels_from_json(self, input_file):
        if not os.path.exists(input_file):
            logger.error(f"JSON文件不存在: {input_file}")
            return False
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                label_data = json.load(f)
            if not label_data:
                logger.warning("JSON文件中没有有效数据")
                return False
            stats = self.jira_client.update_labels_from_json(label_data)
            logger.info("\n标签更新完成!")
            logger.info(f"总计问题: {stats['total']}")
            logger.info(f"已更新问题: {stats['updated']}")
            logger.info(f"跳过问题: {stats['skipped']}")
            logger.info(f"失败问题: {stats['failed']}")
            return True
        except json.JSONDecodeError:
            logger.error("JSON文件格式错误")
        except Exception as e:
            logger.error(f"处理JSON文件失败: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Jira评论标签生成器')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    generate_parser = subparsers.add_parser('generate', help='根据评论生成算法标签并保存为JSON')
    generate_parser.add_argument('--jql', type=str, help='自定义JQL查询语句')
    generate_parser.add_argument('--output', type=str, help='指定输出JSON文件路径')
    
    update_parser = subparsers.add_parser('update', help='从JSON文件更新Jira标签')
    update_parser.add_argument('input', type=str, help='包含标签数据的JSON文件路径')
    
    full_parser = subparsers.add_parser('full', help='生成JSON并立即更新Jira标签')
    full_parser.add_argument('--jql', type=str, help='自定义JQL查询语句')
    full_parser.add_argument('--output', type=str, help='指定中间JSON文件路径')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    labeler = CommentLabeler()
    
    try:
        if args.command == 'generate':
            output_file = labeler.generate_labels_json(jql=args.jql, output_file=args.output)
            if output_file:
                logger.info(f"标签数据已成功生成: {output_file}")
        
        elif args.command == 'update':
            success = labeler.update_labels_from_json(args.input)
            if success:
                logger.info("标签更新成功")
            else:
                logger.error("标签更新失败")
        
        elif args.command == 'full':
            output_file = labeler.generate_labels_json(jql=args.jql, output_file=args.output)
            if output_file:
                logger.info(f"开始根据JSON文件更新标签: {output_file}")
                success = labeler.update_labels_from_json(output_file)
                if success:
                    logger.info("完整流程执行成功")
                else:
                    logger.error("标签更新阶段失败")
            else:
                logger.error("标签生成阶段失败")
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")

if __name__ == "__main__":
    main()