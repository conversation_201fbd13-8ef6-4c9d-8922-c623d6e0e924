drop table if exists cron_task_info;
create table cron_task_info
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    task_name  varchar(128) not null default  '' comment '给任务起个简单的名字',
    # 运行周期与时间
    cron_exp varchar(64) not null default '' comment '任务运行的时间通过这个来计算， */5 * * * *',
    next_exec_time datetime(6) not null comment '下次运行时间' default CURRENT_TIMESTAMP (6),
    is_run  boolean  not null DEFAULT true comment '配置任务是否运行',
    task_config text not null comment '任务的配置信息，这个表只记录什么时候运行， ',
    remark varchar(512) not null comment '任务描述 ',
    create_by varchar(64)   not null DEFAULT '' comment '创建者',
    create_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    update_by varchar(64)   not null DEFAULT '' comment '更新者',
    update_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    is_delete  varchar(2)  not null DEFAULT '0' comment '是否已经删除 0-未删除 1-已删除'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '定时任务的描述， 只记录什么时候运行，运行什么通过task_config 进行配置';


drop table if exists cron_oss_file_list;
create table cron_oss_file_list
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    bucket_name  varchar(64) not null default  '' comment '存储桶的名称',
    oss_path  varchar(256) not null default  '' comment 'oss 文件路径',
    file_update_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '文件的更新时间',
    file_size bigint not null DEFAULT 0 comment '文件大小',
    vin varchar(32) not null DEFAULT '' comment '车的型号',
    parse_type varchar(32) not null DEFAULT '' comment '解析类型',
    current_status varchar(2)   not null DEFAULT '' comment '解析状态 0-初始化成功 1-下载中 2-已下载 3-解析中 4-已解析 5-下载失败 6-解析失败 7-写数据库失败',
    host_name varchar(32) not null DEFAULT '' comment '解析服务器IP',
    remark varchar(256) not null DEFAULT '' comment '如果有异常信息，那么这里写入异常信息',
    create_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    update_time datetime(6)   not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间',
    is_delete  varchar(2)  not null DEFAULT '0' comment '是否已经删除 0-未删除 1-已删除'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '记录OSS的数据变化';

drop table if exists cron_gitlab_merge_requests;
create table cron_gitlab_merge_requests
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    merge_id  int not null default -1 comment 'gitlab merge_id',
    project_id  int not null default -1 comment 'gitlab project_id',
    title varchar(128) not null DEFAULT '' comment '合并标题',
    state varchar(16) not null DEFAULT '' comment '合并状态',
    merged_by varchar(64) not null DEFAULT '' comment 'merge 人',
    merged_at datetime(6)   not null  comment '合并时间',
    source_branch varchar(128) not null DEFAULT '' comment '来源分支',
    target_branch varchar(128) not null DEFAULT '' comment '目标分支',
    reviewers varchar(128) not null DEFAULT '' comment '代码review的人',
    author varchar(64) not null DEFAULT '' comment '提交人',
    sha varchar(64) not null DEFAULT '' comment 'sha',
    web_url varchar(128) not null DEFAULT '' comment 'web_url',
    test_author varchar(64) not null DEFAULT '' comment '测试者',
    pass_time datetime(6) comment '创建时间',
    created_at datetime(6)   not null  comment '创建时间',
    updated_at datetime(6)   not null  comment '更新时间',
    flush_batch varchar(64) not null DEFAULT '' comment '刷新到文档的时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '记录gitmerge的数据变化';



drop table if exists cron_jira_cnt_last_status;
create table cron_jira_cnt_last_status
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    type varchar(128) not null DEFAULT '' comment 'type',
    jira_key varchar(128) not null DEFAULT '' comment 'jira_key',
    summary varchar(128) not null DEFAULT '' comment 'summary'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '记录标签数据的变化';
