import json

from basic.utils import SqlUtil
from report.feishu.data_source.image.line_chart_image import LineChartImage
from report.feishu.generator.impl.text_handler import TextHand<PERSON>, BACK_COLOR


class OneFrameDelay(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.handle_conditions(variables, params)

    def apply(self):
        result = self.query_data()
        if len(result) > 0:
            result=result[0]

        # 下面再放一个描述
        url = self.get_resource_url({
            'vin': self.condition['vin'],
            'record_time_start': self.condition['start_time'],
            'record_time_end': self.condition['end_time'],
            'indicator': ['process_time'] * self.condition['chart_count'],
        }, f"/dataview/singleframe/{self.condition['chart_type']}")

        if "limit" in self.condition and result.get('max_value', ''):
            max_value = float(result.get('max_value', 0))
            if max_value > self.condition['limit']:
                TextHandler(f"{result.get('min_value', '')}~{result.get('max_value', '')}", self.block,
                            self.feishu_api).create_link(url, self.block.get("index", 0) + 1, back_color=BACK_COLOR.LightRed.code)
                return

        # 生成飞书链接
        TextHandler(f"{result.get('min_value', '')}~{result.get('max_value', '')}", self.block, self.feishu_api).create_link(url, self.block.get("index", 0)+1)

    def query_data(self):
        sql = f""" select ROUND(min(process_time),2) min_value, ROUND(max(process_time),2) max_value from {self.condition['table_name']} 
        where process_time < 10000 and vin = %s and record_time >= %s and record_time <= %s"""
        result = SqlUtil.query_all_dict(sql, (self.condition['vin'],
                                            self.condition['start_time'],
                                            self.condition['end_time'],))
        return result


    def handle_conditions(self, variables, params):
        params = params.split(",")
        # 如果没有指定VIN，直接生成图片
        self.condition["vin"] = variables.get('vin', '')
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.condition['table_name'] = params[0]
        self.condition['chart_type'] = params[1]
        self.condition['chart_count'] = int(params[2])
        if len(params) == 4:
            self.condition['limit'] = float(params[3])
