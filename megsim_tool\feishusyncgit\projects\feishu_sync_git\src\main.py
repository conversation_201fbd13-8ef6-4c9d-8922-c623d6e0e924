"""
飞书表格监控系统主入口
"""

import asyncio
import sys
from pathlib import Path

from .config import get_settings
from .utils import setup_logging
from .core import PollingDaemon


async def main() -> None:
    """主函数"""
    try:
        # 加载配置
        settings = get_settings()
        
        # 设置日志
        setup_logging(settings.logging)
        
        # 创建并启动守护进程
        daemon = PollingDaemon(settings)
        await daemon.start()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        if settings.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
