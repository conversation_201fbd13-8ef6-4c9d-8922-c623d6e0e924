import base64
import datetime
import hashlib
import hmac
import time
import urllib

import requests

def generate_signature(secret):
    """
    生成dinttalk 的签名
    """
    timestamp = str(round(time.time() * 1000))
    secret_enc = secret.encode('utf-8')
    string_to_sign = '{}\n{}'.format(timestamp, secret)
    string_to_sign_enc = string_to_sign.encode('utf-8')
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
    return f"&timestamp={timestamp}&sign={sign}"


def dinglink_message(url, message):
    resp = requests.post(url, json= message)

# if __name__ == '__main__':
#     secret = 'SEC0d76464ff403c814c915d011bb1a1b959bbdc401755d9a751183fafc634ceab5'
#     sign = generate_signature(secret)
#     url = f"https://oapi.dingtalk.com/robot/send?access_token=ac9b69ee4639f446efd1e6b3b7d54384fcb6f2915da6a13364ab89ab9681daf8{sign}"
#     message = {
#         "at": {
#             "atMobiles":[
#                 "17186782660"
#             ],
#             "isAtAll": False
#         },
#         "markdown": {
#              "title":"杭州天气",
#              "text": "#### 杭州天气 @150XXXXXXXX \n > 9度，西北风1级，空气良89，相对温度73%\n > ![screenshot](https://img.alicdn.com/tfs/TB1NwmBEL9TBuNjy1zbXXXpepXa-2400-1218.png)\n > ###### 10点20分发布 [天气](https://www.dingtalk.com) \n"
#          },
#         "msgtype":"markdown"
#     }
#     dinglink_message(url, message)