"""
性能监控工具
提供详细的性能监控和分析功能
"""

import time
import psutil
import threading
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
from dataclasses import dataclass
from contextlib import contextmanager

from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class OperationMetrics:
    """操作指标"""
    name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    memory_start: Optional[float] = None
    memory_end: Optional[float] = None
    memory_delta: Optional[float] = None
    cpu_percent: Optional[float] = None
    
    def finish(self, end_time: float, memory_end: float, cpu_percent: float):
        """完成操作记录"""
        self.end_time = end_time
        self.duration = end_time - self.start_time
        self.memory_end = memory_end
        if self.memory_start is not None:
            self.memory_delta = memory_end - self.memory_start
        self.cpu_percent = cpu_percent


class PerformanceMonitor:
    """
    性能监控器
    监控内存使用、CPU使用率、操作耗时等指标
    """
    
    def __init__(self, 
                 enable_memory_monitoring: bool = True,
                 enable_cpu_monitoring: bool = True,
                 history_size: int = 1000):
        """
        初始化性能监控器
        
        Args:
            enable_memory_monitoring: 是否启用内存监控
            enable_cpu_monitoring: 是否启用CPU监控
            history_size: 历史记录大小
        """
        self.enable_memory_monitoring = enable_memory_monitoring
        self.enable_cpu_monitoring = enable_cpu_monitoring
        self.history_size = history_size
        
        # 当前进程
        self.process = psutil.Process()
        
        # 操作记录
        self.active_operations: Dict[str, OperationMetrics] = {}
        self.completed_operations: deque = deque(maxlen=history_size)
        
        # 统计数据
        self.operation_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'count': 0,
            'total_duration': 0.0,
            'min_duration': float('inf'),
            'max_duration': 0.0,
            'total_memory_delta': 0.0,
            'max_memory_delta': 0.0
        })
        
        # 系统监控
        self.memory_history: deque = deque(maxlen=history_size)
        self.cpu_history: deque = deque(maxlen=history_size)
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 监控线程
        self.monitoring_thread = None
        self.monitoring_active = False
        
        # 初始化时间和内存使用
        self.start_time = time.time()
        self.memory_usage = self.process.memory_info().rss / 1024 / 1024  # MB
        
        logger.info("性能监控器初始化完成")
    
    def start_monitoring(self, interval: float = 1.0):
        """
        启动后台监控线程
        
        Args:
            interval: 监控间隔(秒)
        """
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            logger.warning("监控线程已在运行")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitoring_thread.start()
        logger.info(f"后台监控线程已启动 (间隔: {interval}s)")
    
    def stop_monitoring(self):
        """停止后台监控线程"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
        logger.info("后台监控线程已停止")
    
    def _monitoring_loop(self, interval: float):
        """监控循环"""
        while self.monitoring_active:
            try:
                with self.lock:
                    # 记录内存使用
                    if self.enable_memory_monitoring:
                        memory_mb = self.get_memory_usage_mb()
                        self.memory_history.append((time.time(), memory_mb))
                    
                    # 记录CPU使用
                    if self.enable_cpu_monitoring:
                        cpu_percent = self.get_cpu_percent()
                        self.cpu_history.append((time.time(), cpu_percent))
                
                time.sleep(interval)
            
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(interval)
    
    def start_operation(self, operation_name: str) -> str:
        """
        开始操作监控
        
        Args:
            operation_name: 操作名称
            
        Returns:
            str: 操作ID
        """
        operation_id = f"{operation_name}_{int(time.time() * 1000000)}"
        
        with self.lock:
            metrics = OperationMetrics(
                name=operation_name,
                start_time=time.time(),
                memory_start=self.get_memory_usage_mb() if self.enable_memory_monitoring else None
            )
            self.active_operations[operation_id] = metrics
        
        logger.debug(f"开始操作监控: {operation_name} ({operation_id})")
        return operation_id
    
    def end_operation(self, operation_name: str) -> Optional[OperationMetrics]:
        """
        结束操作监控
        
        Args:
            operation_name: 操作名称
            
        Returns:
            Optional[OperationMetrics]: 操作指标，如果操作不存在返回None
        """
        end_time = time.time()
        memory_end = self.get_memory_usage_mb() if self.enable_memory_monitoring else 0.0
        cpu_percent = self.get_cpu_percent() if self.enable_cpu_monitoring else 0.0
        
        with self.lock:
            # 查找匹配的操作
            operation_id = None
            for op_id, metrics in self.active_operations.items():
                if metrics.name == operation_name:
                    operation_id = op_id
                    break
            
            if operation_id is None:
                logger.warning(f"未找到活动操作: {operation_name}")
                return None
            
            # 完成操作记录
            metrics = self.active_operations.pop(operation_id)
            metrics.finish(end_time, memory_end, cpu_percent)
            
            # 添加到完成记录
            self.completed_operations.append(metrics)
            
            # 更新统计
            self._update_operation_stats(metrics)
        
        logger.debug(f"结束操作监控: {operation_name} (耗时: {metrics.duration:.3f}s)")
        return metrics
    
    @contextmanager
    def monitor_operation(self, operation_name: str):
        """
        操作监控上下文管理器
        
        Args:
            operation_name: 操作名称
        """
        operation_id = self.start_operation(operation_name)
        try:
            yield operation_id
        finally:
            self.end_operation(operation_name)
    
    def get_memory_usage_mb(self) -> float:
        """获取当前内存使用量(MB)"""
        try:
            return self.process.memory_info().rss / 1024 / 1024
        except Exception as e:
            logger.error(f"获取内存使用量失败: {e}")
            return 0.0
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量(MB) - 兼容性方法"""
        return self.get_memory_usage_mb()
    
    def get_cpu_percent(self) -> float:
        """获取CPU使用率"""
        try:
            return self.process.cpu_percent()
        except Exception as e:
            logger.error(f"获取CPU使用率失败: {e}")
            return 0.0
    
    def get_cpu_usage(self) -> float:
        """获取CPU使用率 - 兼容性方法"""
        return self.get_cpu_percent()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        current_memory = self.get_memory_usage()
        current_cpu = self.get_cpu_usage()
        
        stats = {
            'elapsed_time': getattr(self, 'elapsed_time', 0.0),
            'memory_usage': current_memory,
            'cpu_usage': current_cpu,
            'active_operations': len(self.active_operations),
            'completed_operations': len(self.completed_operations),
            'current_memory_mb': current_memory,
            'current_cpu_percent': current_cpu,
            'operation_stats': dict(self.operation_stats),
            'memory_stats': {
                'min': min(mem for _, mem in self.memory_history) if self.memory_history else current_memory,
                'max': max(mem for _, mem in self.memory_history) if self.memory_history else current_memory,
                'avg': sum(mem for _, mem in self.memory_history) / len(self.memory_history) if self.memory_history else current_memory,
                'current': current_memory
            },
            'cpu_stats': {
                'min': min(cpu for _, cpu in self.cpu_history) if self.cpu_history else current_cpu,
                'max': max(cpu for _, cpu in self.cpu_history) if self.cpu_history else current_cpu,
                'avg': sum(cpu for _, cpu in self.cpu_history) / len(self.cpu_history) if self.cpu_history else current_cpu,
                'current': current_cpu
            }
        }
            
        return stats
    
    def _update_operation_stats(self, metrics: OperationMetrics):
        """更新操作统计"""
        stats = self.operation_stats[metrics.name]
        
        stats['count'] += 1
        stats['total_duration'] += metrics.duration or 0.0
        
        if metrics.duration:
            stats['min_duration'] = min(stats['min_duration'], metrics.duration)
            stats['max_duration'] = max(stats['max_duration'], metrics.duration)
        
        if metrics.memory_delta:
            stats['total_memory_delta'] += abs(metrics.memory_delta)
            stats['max_memory_delta'] = max(stats['max_memory_delta'], abs(metrics.memory_delta))
    

    
    def get_recent_operations(self, count: int = 10) -> List[OperationMetrics]:
        """获取最近的操作记录"""
        with self.lock:
            return list(self.completed_operations)[-count:]
    
    def reset(self):
        """重置所有统计数据"""
        with self.lock:
            self.active_operations.clear()
            self.completed_operations.clear()
            self.operation_stats.clear()
            self.memory_history.clear()
            self.cpu_history.clear()
        
        logger.info("性能监控数据已重置")
    
    def cleanup(self):
        """清理资源"""
        self.stop_monitoring()
        # 保存elapsed_time
        elapsed_time = getattr(self, 'elapsed_time', None)
        self.reset()
        # 恢复elapsed_time
        if elapsed_time is not None:
            self.elapsed_time = elapsed_time
        logger.info("性能监控器已清理")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        # 计算并保存elapsed_time
        self.elapsed_time = time.time() - self.start_time
        # 停止监控但不重置数据
        self.stop_monitoring()
        logger.info("性能监控器已清理")
