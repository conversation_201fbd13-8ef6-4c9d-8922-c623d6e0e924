from basic.services import SendMessageApi
from basic.utils import SqlUtil
from report.feishu.data_source.image.line_chart_image import LineChartImage


class ReportMessage(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.handle_conditions(variables, params)

    def apply(self):
        value = self.condition['table_name'].split(",")
        if len(value) < 1:
            return
        feishu_url = f"[点击查看](https://yuanlijuhe.feishu.cn/docx/{self.feishu_api.get_target_file_token()})"
        message = f"报告已生成完成  {feishu_url}"
        if len(value) >= 1:
            message = message + "\n"
            for item in value[1:]:
                message = f'{message} <at id="{item}"></at>'
        SendMessageApi(message).send_message(value[0])
        return None


    def handle_conditions(self, variables, params):
        # 如果没有指定VIN，直接生成图片
        self.condition["vin"] = variables.get('vin', '')
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.condition['table_name'] = params
