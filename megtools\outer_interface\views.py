import datetime
import json
from urllib.parse import urlencode


from django.shortcuts import HttpResponse
from django.views.decorators.http import require_POST

from outer_interface.services import OssFileFilter


# Create your views here.

@require_POST
def parsePerformance(request):
    """
    查询进程分析的数据
    """
    req = json.loads(request.body.decode("UTF-8"))
    oss_path = req['oss_path']
    prefix = req.get("prefix")
    # 通过oss_path 获取数据， 然后再通过prefix 进行过滤。
    vin, time_group = OssFileFilter(oss_path, prefix).apply()
    meg_tool = "https://megtool-frontend-megtools.mc.machdrive.cn/#"
    result = {}
    for item in time_group:
        min_time = time_group[item]['min_time']
        max_time = time_group[item]['max_time']
        max_time = datetime.datetime.strptime(max_time, "%Y-%m-%d %H:%M:%S") + datetime.timedelta(hours=2)
        max_time = max_time.strftime("%Y-%m-%d %H:%M:%S")
        query_condition = {
            "vin": vin,
            "record_time_start": min_time,
            "record_time_end": max_time
        }
        uri = ""
        url_key = ""
        if item == 'comm_cpu_temperature':
            url_key = "cpu_temperature"
            uri = "/dataview/cpuTemperature"
        elif item == 'comm_gpu_nvidia_smi':
            url_key = "gpu_nvidia_smi"
            uri = "/dataview/gpuAnalyze"
        elif item == 'host_process':
            url_key = "host_process_usage"
            uri = "/dataview/onlinetop"
        if uri:
            result[url_key] = f"{meg_tool}{uri}?{urlencode(query_condition)}"
    # https://megtool-frontend-megtools.mc.machdrive.cn/#/dataview/onlinetop?vin=z19&record_time_start=2025-04-18%2016%3A35%3A43&record_time_end=2025-04-18%2016%3A48%3A43&indicator=%5B%7B%22indicator%22%3A%5B%22mem_used%22%5D%7D%2C%7B%22cpu%22%3A%22%25Cpu(s)%22%2C%22indicator%22%3A%5B%22cpu_us%22%5D%7D%2C%7B%7D%5D
    # result 按照url 进行处理后返回！
    return HttpResponse(json.dumps(result), content_type='application/json')
