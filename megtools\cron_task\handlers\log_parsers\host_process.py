"""
这里解析TOP相关的日志，
日志长这个样子：

Timestamp: 2025-02-17 11:13:48
%Cpu(s): 24.0 us,  6.1 sy,  0.0 ni, 69.8 id,  0.0 wa,  0.0 hi,  0.0 si,  0.0 st
MiB Mem :  64200.0 total,  50683.4 free,   1076.2 used,  12440.4 buff/cache
MiB Swap:   8192.0 total,   8192.0 free,      0.0 used.  62400.9 avail Mem

    PID USER      PR  NI    VIRT    RES    SHR S  %CPU  %MEM     TIME+ COMMAND
 242750 user      20   0  127548  34468  12216 S 100.0   0.1   0:00.33 python3 /mnt/data/zeta/output/bin/ros2 launch e2e_map_perceptor_node e2e_map_perceptor_node.launch.py
 242753 user      20   0  127808  34748  12428 S 100.0   0.1   0:00.33 python3 /mnt/data/zeta/output/bin/ros2 launch e2e_perceptor_node e2e_perceptor_node.launch.py
 242780 user      20   0   38548  25264   8568 R 100.0   0.0   0:00.25 python3 /mnt/data/zeta/output/bin/ros2 launch traffic_light_perceptor_node traffic_light_perceptor_node.launch.py
 242838 user      20   0   36036  26908  10128 R 100.0   0.0   0:00.15 python3 /mnt/data/zeta/output/bin/ros2 run e2e_perception_map_merger_node e2e_perception_map_merger_node_exe
 242582 user      20   0   16548   9212   4496 S  20.0   0.0   0:00.16 /bin/zsh
    942 root      20   0  243356  12340   6588 S   6.7   0.0   0:01.11 /usr/lib/accountsservice/accounts-daemon
      1 root      20   0  168656  11948   8388 S   0.0   0.0   0:16.14 /sbin/init maybe-ubiquity
      2 root      20   0       0      0      0 S   0.0   0.0   0:00.00 [kthreadd]
      3 root       0 -20       0      0      0 I   0.0   0.0   0:00.00 [rcu_gp]
      4 root       0 -20       0      0      0 I   0.0   0.0   0:00.00 [rcu_par_gp]
      6 root       0 -20       0      0      0 I   0.0   0.0   0:00.00 [kworker/0:0H-events_highpri]
      9 root       0 -20       0      0      0 I   0.0   0.0   0:00.00 [mm_percpu_wq]
     10 root      20   0       0      0      0 S   0.0   0.0   0:00.00 [rcu_tasks_rude_]
     11 root      20   0       0      0      0 S   0.0   0.0   0:00.00 [rcu_tasks_trace]
     12 root      20   0       0      0      0 S   0.0   0.0   0:00.07 [ksoftirqd/0]
     13 root      20   0       0      0      0 I   0.0   0.0   0:06.20 [rcu_sched]
     14 root      rt   0       0      0      0 S   0.0   0.0   0:00.03 [migration/0]
     15 root     -51   0       0      0      0 S   0.0   0.0   0:00.00 [idle_inject/0]
     16 root      20   0       0      0      0 S   0.0   0.0   0:00.00 [cpuhp/0]
     17 root      20   0       0      0      0 S   0.0   0.0   0:00.00 [cpuhp/1]
     18 root     -51   0       0      0      0 S   0.0   0.0   0:00.00 [idle_inject/1]
     19 root      rt   0       0      0      0 S   0.0   0.0   0:00.25 [migration/1]
     20 root      20   0       0      0      0 S   0.0   0.0   0:00.06 [ksoftirqd/1]
     22 root       0 -20       0      0      0 I   0.0   0.0   0:00.00 [kworker/1:0H]
     23 root      20   0       0      0      0 S   0.0   0.0   0:00.00 [cpuhp/2]

"""
import logging
import traceback

from cron_task.handlers.log_parsers.parser_super import Parser
from cron_task.models import TopCpuLog, TopProgressLog, TopOverviewLog


def apply(file_path, db_entity):
    TopLogParser().parse(db_entity.id, db_entity, file_path)



class TopLogParser(Parser):
    def __init__(self):
        super().__init__()
        # 记录当前Top分组中的数据
        self.one_top = {}
        # top 中 TOP,TASK, MEMORY, SWAP
        self.overall_cache = []
        # TOP上 %CPU开头的数据
        self.cpu_cache = []
        # 进程相关的数据
        self.progress_cache = []
        # 当前解析中TOP记录的时间 年月日时分秒
        self.current_time = ""
        self.db_entity = None

    def parse_file(self, id, db_entity, log_path):
        self.db_entity = db_entity
        with open(log_path, "r", encoding="utf-8") as log:
            is_new_line = True
            while True:
                try:
                    line = log.readline()
                    if line == "":
                        break
                    is_new_line = True
                    self.choose_parse(line)
                    self.check_flash_cache()
                except:
                    logging.error(f"top parse error: {line} {traceback.format_exc()}")
                    self.error_info = f"top parse error: {line} {traceback.format_exc()}"
                    if is_new_line is False:
                        ## 这时候说明，第二次进来的时候还是没有读取到新的行，从而破开这个死循环
                        break
                    is_new_line = False
        self.check_flash_cache(True)

    def parse_txt(self, base_info, text):
        self.db_entity = base_info
        self.origin = base_info.origin
        # 直接解析文本信息
        for item in text:
            lines = item.split('\n')
            for line in lines:
                self.choose_parse(line)
                self.check_flash_cache()
        self.check_flash_cache(True)

    # 将缓存数据 刷到数据库里面
    def check_flash_cache(self, is_force=False):
        # logging.info(f"flush cache: {len(self.cpu_cache)} \t {len(self.progress_cache)} \t {len(self.overall_cache)}")
        if len(self.cpu_cache) >= 1000 or is_force:
            self.flush_cache(TopCpuLog, self.cpu_cache)
        if len(self.progress_cache) >= 1000 or is_force:
            self.flush_cache(TopProgressLog, self.progress_cache)
        if len(self.overall_cache) >= 1000 or is_force:
            if len(self.one_top) > 2 and is_force:
                self.overall_cache.append(TopOverviewLog(**self.one_top))
                self.one_top = {}
            self.flush_cache(TopOverviewLog, self.overall_cache)


    def choose_parse(self, line):
        if line.strip() == '':
            return
        if line.startswith("Timestamp:"):
            if len(self.one_top) > 2:
                self.overall_cache.append(TopOverviewLog(**self.one_top))
            self.one_top = {}
            self.parse_top(line)
        elif line.startswith("%Cpu"):
            self.parse_cpu(line)
        elif line.startswith("MiB Mem"):
            self.parse_memory(line)
        elif line.startswith("MiB Swap"):
            self.parse_swap(line)
        elif "PID" in line and "USER" in line and "PR" in line:
            pass
        else:
            self.parse_progress(line)

    def parse_top(self, top_line):
        self.current_time = top_line[11:30]
        self.update_time(self.current_time)
        self.one_top.update({
            "vin": self.db_entity.vin,
            "record_time": self.current_time,
        })

    def parse_cpu(self, cpu_line):
        if self.current_time == "":
            return
        cpu_line_info = cpu_line[cpu_line.index(":") + 1:].split(",")
        cpu_info = {
            "vin": self.db_entity.vin,
            "record_time": self.current_time,
            "cpu_idx": cpu_line[0:cpu_line.index(":")].strip(),
            "cpu_us": float(cpu_line_info[0].split()[0]),
            "cpu_sy": float(cpu_line_info[1].split()[0]),
            "cpu_ni": float(cpu_line_info[2].split()[0]),
            "cpu_id": float(cpu_line_info[3].split()[0]),
            "cpu_wa": float(cpu_line_info[4].split()[0]),
            "cpu_hi": float(cpu_line_info[5].split()[0]),
            "cpu_si": float(cpu_line_info[6].split()[0]),
            "cpu_st": float(cpu_line_info[7].split()[0]),
        }
        self.cpu_cache.append(TopCpuLog(**cpu_info))

    def parse_memory(self, mem_line):
        mem_line_info = mem_line[mem_line.index(":") + 1:].split(",")
        value = []
        for item in mem_line_info:
            if "+" in item.strip():
                temp = item.strip().split("+")[0]
                if temp.endswith("."):
                    temp = temp[:-1]
                temp = float(temp)
                value.append(temp)
            else:
                temp = item.strip().split(" ")[0]
                value.append(float(temp))

        self.one_top.update({
            "mem_total": value[0],
            "mem_free": value[1],
            "mem_used": value[2],
            "mem_buff_cache": value[3],
        })

    def parse_swap(self, swap_line):
        swap_line_info = swap_line[swap_line.index(":") + 1:].split()
        self.one_top.update({
            "swap_total": float(swap_line_info[0]),
            "swap_free": float(swap_line_info[2]),
            "swap_used": float(swap_line_info[4]),
            "swap_avail_mem": float(swap_line_info[6]),
        })

    def parse_progress(self, line):
        line_elements = line.split()
        if len(line_elements) < 12:
            return
        if self.current_time == "":
            return
        progress_info = {
            "vin": self.db_entity.vin,
            "record_time": self.current_time,
            "pid": int(line_elements[0]),
            "user": line_elements[1],
            "priority": int(line_elements[2]) if line_elements[2] != "rt" else 1,
            "nice_value": int(line_elements[3]),
            "virtual_image": float(self.trans_unit(line_elements[4])),
            "resident_size": float(self.trans_unit(line_elements[5])),
            "shared_mem_size": float(self.trans_unit(line_elements[6])),
            "process_status": line_elements[7],
            "cpu_usage": float(line_elements[8]),
            "mem_usage": float(line_elements[9]),
            "cpu_time": int(line_elements[10].split(":")[0]) * 60 + float(line_elements[10].split(":")[1]),
            "command": " ".join(line_elements[11:]),
        }
        if (progress_info["command"].startswith("/home/<USER>/zeta-sdk")
                or progress_info["command"].startswith("/root/zeta-sdk")
                or progress_info["command"].startswith("./bin/machE2EExe")
                or progress_info["command"].startswith("mach_")
                or progress_info["command"].startswith("/geely_pilot/mach")):
            self.progress_cache.append(TopProgressLog(**progress_info))


    def trans_unit(self, value: str):
        if value.endswith("g"):
            value = value[0:-1]
            return float(value) * 1024 * 1024
        if value.endswith("m"):
            value = value[0:-1]
            return float(value) * 1024
        if value.endswith("t"):
            value = value[0:-1]
            return float(value) * 1024 * 1024 * 1024
        return value
