#!/usr/bin/env python3
"""Jira Clone SDK 测试"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from jira_clone_sdk import <PERSON>ra<PERSON>loneSDK, CloneConfig, FieldConfig, FieldAction
from jira_clone_sdk.exceptions import JiraA<PERSON>Exception, FieldProcessingException


class TestJiraCloneSDK(unittest.TestCase):
    """Jira Clone SDK 测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.sdk = JiraCloneSDK(
            base_url="https://test.atlassian.net",
            bearer_token="test-token"
        )
        self.config = CloneConfig(clone_prefix="TEST - ")
    
    @patch('jira_clone_sdk.client.requests.Session')
    def test_connection(self, mock_session):
        """测试连接"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"accountId": "test-user"}
        
        mock_session.return_value.get.return_value = mock_response
        
        result = self.sdk.test_connection()
        self.assertTrue(result)
    
    @patch('jira_clone_sdk.client.requests.Session')
    def test_clone_issue_success(self, mock_session):
        """测试成功克隆问题"""
        # Mock获取原问题
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {
            "key": "TEST-123",
            "fields": {
                "summary": "原始标题",
                "issuetype": {"id": "1"},
                "project": {"key": "TEST"},
                "description": "原始描述"
            }
        }
        
        # Mock创建新问题
        mock_post_response = Mock()
        mock_post_response.status_code = 201
        mock_post_response.json.return_value = {
            "key": "TEST-124"
        }
        
        # Mock获取当前用户
        mock_user_response = Mock()
        mock_user_response.status_code = 200
        mock_user_response.json.return_value = {"accountId": "test-user"}
        
        mock_session_instance = mock_session.return_value
        mock_session_instance.get.side_effect = [mock_user_response, mock_get_response]
        mock_session_instance.post.return_value = mock_post_response
        
        result = self.sdk.clone_issue("TEST-123", self.config)
        self.assertEqual(result, "TEST-124")
    
    @patch('jira_clone_sdk.client.requests.Session')
    def test_clone_issue_api_error(self, mock_session):
        """测试API错误"""
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.text = "Issue not found"
        
        mock_session.return_value.get.return_value = mock_response
        
        with self.assertRaises(JiraApiException):
            self.sdk.clone_issue("INVALID-123", self.config)
    
    def test_field_config(self):
        """测试字段配置"""
        config = CloneConfig()
        
        # 添加字段配置
        config.add_field_config("assignee", FieldConfig(action=FieldAction.KEEP))
        config.add_field_config("priority", FieldConfig(action=FieldAction.SET, value={"id": "1"}))
        
        # 检查配置
        self.assertIn("assignee", config.field_configs)
        self.assertEqual(config.field_configs["assignee"].action, FieldAction.KEEP)
        self.assertEqual(config.field_configs["priority"].value, {"id": "1"})
    
    def test_exclude_fields(self):
        """测试排除字段"""
        config = CloneConfig()
        config.exclude_field("created")
        config.exclude_field("updated")
        
        self.assertIn("created", config.exclude_fields)
        self.assertIn("updated", config.exclude_fields)
    
    @patch('jira_clone_sdk.client.requests.Session')
    def test_batch_clone(self, mock_session):
        """测试批量克隆"""
        # Mock成功响应
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.json.return_value = {
            "key": "TEST-123",
            "fields": {
                "summary": "测试标题",
                "issuetype": {"id": "1"},
                "project": {"key": "TEST"}
            }
        }
        
        mock_post_response = Mock()
        mock_post_response.status_code = 201
        mock_post_response.json.return_value = {"key": "TEST-124"}
        
        mock_user_response = Mock()
        mock_user_response.status_code = 200
        mock_user_response.json.return_value = {"accountId": "test-user"}
        
        mock_session_instance = mock_session.return_value
        mock_session_instance.get.side_effect = [mock_user_response, mock_get_response]
        mock_session_instance.post.return_value = mock_post_response
        
        successful, failed = self.sdk.clone_issues_batch(["TEST-123"], self.config)
        
        self.assertEqual(len(successful), 1)
        self.assertEqual(len(failed), 0)
        self.assertIn("TEST-124", successful)
    
    @patch('jira_clone_sdk.client.requests.Session')
    def test_edit_cloned_data(self, mock_session):
        """测试编辑克隆数据"""
        mock_response = Mock()
        mock_response.status_code = 204
        
        mock_session.return_value.put.return_value = mock_response
        
        edit_data = {
            "fields": {
                "summary": "新标题",
                "description": "新描述"
            }
        }
        
        result = self.sdk.edit_cloned_data("TEST-123", edit_data)
        self.assertTrue(result)


class TestFieldProcessors(unittest.TestCase):
    """字段处理器测试类"""
    
    def test_basic_field_processor(self):
        """测试基础字段处理器"""
        from jira_clone_sdk.processors import BasicFieldProcessor
        
        processor = BasicFieldProcessor()
        
        # 测试可处理的字段
        self.assertTrue(processor.can_handle("summary"))
        self.assertTrue(processor.can_handle("description"))
        self.assertTrue(processor.can_handle("assignee"))
        self.assertFalse(processor.can_handle("customfield_10001"))
        
        # 测试字段处理
        context = {"clone_prefix": "TEST - "}
        result = processor.process("summary", "原始标题", context)
        self.assertEqual(result, "TEST - 原始标题")
    
    def test_system_field_processor(self):
        """测试系统字段处理器"""
        from jira_clone_sdk.processors import SystemFieldProcessor
        
        processor = SystemFieldProcessor()
        
        # 测试可处理的字段
        self.assertTrue(processor.can_handle("priority"))
        self.assertTrue(processor.can_handle("labels"))
        self.assertFalse(processor.can_handle("summary"))
        
        # 测试标签处理
        context = {}
        labels = ["标签1", "标签2"]
        result = processor.process("labels", labels, context)
        self.assertIsInstance(result, list)
        self.assertIn("标签1", result)
        self.assertIn("标签2", result)
    
    def test_custom_field_processor(self):
        """测试自定义字段处理器"""
        from jira_clone_sdk.processors import CustomFieldProcessor
        
        processor = CustomFieldProcessor()
        
        # 测试可处理的字段
        self.assertTrue(processor.can_handle("customfield_10001"))
        self.assertTrue(processor.can_handle("customfield_99999"))
        self.assertFalse(processor.can_handle("summary"))
        
        # 测试字段处理
        context = {}
        result = processor.process("customfield_10001", "自定义值", context)
        self.assertEqual(result, "自定义值")


if __name__ == '__main__':
    unittest.main()