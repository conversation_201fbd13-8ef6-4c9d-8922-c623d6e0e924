# build.spec
block_cipher = None

a = Analysis(
    ['AutoTestReportGenerator.py'],  # 你的主脚本文件
    pathex=[],
    binaries=[],
    datas=[('config.ini', '.')],  # 包含配置文件
    hiddenimports=[
        'lark_oapi',
        'lark_oapi.api.drive.v1',
        'jira',
        'requests',
        'configparser'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ReAutoTestReportGenerator',  # 生成的exe名称
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 设置为False可隐藏控制台窗口
)