#!/usr/bin/env python3
"""
验证MCAP Parser包修复情况
"""

import sys
from pathlib import Path

def test_imports():
    """测试导入功能"""
    print("🔍 测试包导入...")
    try:
        import mcap_parser
        from mcap_parser.core.sdk import McapAutoDriveSDK
        from mcap_parser.core.parser_manager import ParserManager
        from mcap_parser.cli.main import main, create_parser, MESSAGE_CATEGORIES
        print("✅ 所有核心模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_cli_parser():
    """测试CLI解析器"""
    print("\n🔍 测试CLI解析器...")
    try:
        from mcap_parser.cli.main import create_parser
        parser = create_parser()
        
        # 测试子命令
        subparsers_actions = [
            action for action in parser._actions 
            if hasattr(action, 'choices') and action.choices is not None
        ]
        
        if subparsers_actions and len(subparsers_actions) >= 1:
            choices = subparsers_actions[0].choices
            if all(cmd in choices for cmd in ['parse', 'analyze', 'list']):
                print("✅ CLI解析器结构正确")
                return True
        
        print("❌ CLI解析器结构不正确")
        return False
    except Exception as e:
        print(f"❌ CLI解析器测试失败: {e}")
        return False

def test_message_categories():
    """测试消息分类"""
    print("\n🔍 测试消息分类...")
    try:
        from mcap_parser.cli.main import MESSAGE_CATEGORIES
        
        expected_categories = ['perception', 'lane_map', 'localization', 'planning_control', 'sensors', 'vehicle', 'visualization', 'specialized']
        
        if all(cat in MESSAGE_CATEGORIES for cat in expected_categories):
            total_types = sum(len(cat['types']) for cat in MESSAGE_CATEGORIES.values())
            print(f"✅ 消息分类正确 ({len(MESSAGE_CATEGORIES)} 个分类, {total_types} 种类型)")
            return True
        else:
            print("❌ 消息分类不完整")
            return False
    except Exception as e:
        print(f"❌ 消息分类测试失败: {e}")
        return False

def test_core_components():
    """测试核心组件"""
    print("\n🔍 测试核心组件初始化...")
    try:
        from mcap_parser.core.sdk import McapAutoDriveSDK
        from mcap_parser.core.parser_manager import ParserManager
        
        # 测试SDK初始化
        sdk = McapAutoDriveSDK()
        sdk_methods = [method for method in dir(sdk) if not method.startswith('_')]
        expected_methods = ['analyze_mcap_file', 'fast_stream_data', 'get_statistics', 'random_access', 'stream_data']
        
        if all(method in sdk_methods for method in expected_methods):
            print(f"✅ SDK组件正常 ({len([m for m in sdk_methods if not m.startswith('_')])} 个方法)")
        else:
            print("⚠️ SDK组件部分方法缺失")
        
        # 测试ParserManager类存在性
        try:
            pm_methods = [method for method in dir(ParserManager) if not method.startswith('_')]
            print(f"✅ ParserManager类可用 ({len(pm_methods)} 个方法)")
        except Exception as pm_e:
            print(f"⚠️ ParserManager类测试失败: {pm_e}")
        
        return True
    except Exception as e:
        print(f"❌ 核心组件测试失败: {e}")
        return False

def main_test():
    """主测试函数"""
    print("🚀 MCAP Parser 包修复验证")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_cli_parser,
        test_message_categories,
        test_core_components
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCAP Parser包已完全修复")
        return 0
    else:
        print(f"⚠️ {total - passed} 个测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main_test())