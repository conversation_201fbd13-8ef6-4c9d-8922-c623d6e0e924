# MegTool Backend API 文档

## 概述

MegTool Backend 是一个基于 Django 的后端服务系统，提供数据分析、任务管理、报告生成等功能的 REST API 接口。

## 基础信息

- **基础URL**: `http://your-domain.com`
- **API版本**: v1
- **认证方式**: Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 0,
  "data": {},
  "message": "success",
  "error": null
}
```

## 认证说明

大部分API需要在请求头中包含认证信息：

```
Authorization: Bearer <your-token>
Content-Type: application/json
```

---

## 1. 基础服务模块 (/api/basic/)

### 1.1 用户认证

#### 登录
- **接口**: `POST /api/basic/auth/login`
- **描述**: 用户登录认证
- **请求参数**:
```json
{
  "username": "用户名",
  "password": "密码"
}
```
- **响应示例**:
```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user_info": {
      "login_name": "admin",
      "display_name": "管理员"
    }
  },
  "message": "登录成功"
}
```

#### 获取用户信息
- **接口**: `GET /api/basic/user/info`
- **描述**: 获取当前登录用户信息
- **请求头**: 需要 Authorization
- **响应示例**:
```json
{
  "login_name": "admin",
  "display_name": "管理员",
  "email": "<EMAIL>"
}
```

#### 获取权限代码
- **接口**: `GET /api/basic/auth/codes`
- **描述**: 获取用户权限代码列表
- **响应示例**:
```json
{
  "code": 0,
  "data": {
    "codes": ["AC_100100", "AC_100110", "AC_100120", "AC_100010"],
    "username": "admin"
  },
  "message": "ok"
}
```

### 1.2 配置管理

#### 分页查询配置
- **接口**: `POST /api/basic/kvPage`
- **描述**: 分页查询键值对配置
- **请求参数**:
```json
{
  "page": 1,
  "size": 10,
  "name": "配置名称(可选)"
}
```

#### 修改配置
- **接口**: `POST /api/basic/modifyKey`
- **描述**: 新增或修改配置项
- **请求参数**:
```json
{
  "id": "配置ID(修改时必填)",
  "name": "配置名称",
  "value": "配置值",
  "description": "配置描述"
}
```

#### 删除配置
- **接口**: `POST /api/basic/delByKey`
- **描述**: 删除指定配置项
- **请求参数**:
```json
{
  "key": "配置名称"
}
```

#### 查询配置值
- **接口**: `POST /api/basic/queryKey`
- **描述**: 根据Key查询配置值
- **请求参数**:
```json
{
  "key": "配置名称",
  "type": "json|text"
}
```

### 1.3 用户管理

#### 分页查询用户
- **接口**: `POST /api/basic/userinfoPageApi`
- **描述**: 分页查询用户信息
- **请求参数**:
```json
{
  "page": 1,
  "size": 10,
  "login_name": "用户名(可选)"
}
```

#### 修改用户
- **接口**: `POST /api/basic/modifyUserApi`
- **描述**: 新增或修改用户信息
- **请求参数**:
```json
{
  "id": "用户ID(修改时必填)",
  "login_name": "登录名",
  "display_name": "显示名称",
  "email": "邮箱"
}
```

#### 删除用户
- **接口**: `POST /api/basic/delUserinfoApi`
- **描述**: 删除用户信息
- **请求参数**:
```json
{
  "id": "用户ID"
}
```

#### 获取用户名列表
- **接口**: `POST /api/basic/getUserNameListApi`
- **描述**: 获取所有用户名列表

### 1.4 告警分组管理

#### 分页查询告警分组
- **接口**: `POST /api/basic/alarmGroupPageApi`
- **请求参数**:
```json
{
  "page": 1,
  "size": 10
}
```

#### 修改告警分组
- **接口**: `POST /api/basic/modifyAlarmGroupApi`
- **请求参数**:
```json
{
  "id": "分组ID(修改时必填)",
  "group_name": "分组名称",
  "members": "成员列表"
}
```

#### 删除告警分组
- **接口**: `POST /api/basic/delAlarmGroupApi`
- **请求参数**:
```json
{
  "id": "分组ID"
}
```

### 1.5 其他功能

#### 查询Jira数据
- **接口**: `POST /api/basic/queryJira`
- **描述**: 查询Jira问题数据
- **请求参数**:
```json
{
  "jql": "Jira查询语句",
  "fields": ["字段列表"]
}
```

#### OSS数据转换
- **接口**: `GET /api/basic/transOss/<path>`
- **描述**: 转换OSS中的数据文件
- **路径参数**: `path` - OSS文件路径

#### 发送飞书消息
- **接口**: `POST /api/basic/send_feishu_message`
- **描述**: 发送飞书通知消息
- **请求参数**:
```json
{
  "message": "消息内容",
  "webhook_url": "飞书机器人webhook地址"
}
```

---

## 2. 数据视图模块 (/api/dataview/)

### 2.1 性能监控

#### 查询系统概览
- **接口**: `POST /api/dataview/queryTopOverview`
- **描述**: 查询系统性能概览数据
- **请求参数**:
```json
{
  "vin": "车辆识别码",
  "record_time_start": "2024-01-01 00:00:00",
  "record_time_end": "2024-01-01 23:59:59",
  "indicator": ["cpu_usage", "memory_usage"]
}
```

#### 查询CPU数据
- **接口**: `POST /api/dataview/queryTopCpus`
- **描述**: 查询CPU使用率数据
- **请求参数**:
```json
{
  "vin": "车辆识别码",
  "cpu": "CPU索引",
  "record_time_start": "2024-01-01 00:00:00",
  "record_time_end": "2024-01-01 23:59:59",
  "indicator": ["cpu_us", "cpu_sy"]
}
```

#### 查询进程数据
- **接口**: `POST /api/dataview/queryTopProcess`
- **描述**: 查询进程性能数据
- **请求参数**:
```json
{
  "vin": "车辆识别码",
  "progress": "进程名称",
  "record_time_start": "2024-01-01 00:00:00",
  "record_time_end": "2024-01-01 23:59:59",
  "indicator": ["cpu_percent", "mem_percent"]
}
```

#### 通用分析查询
- **接口**: `POST /api/dataview/queryCommonAnylizeApi`
- **描述**: 通用数据分析查询
- **请求参数**:
```json
{
  "chartConfig": "图表配置Key",
  "index": 0,
  "vin": "车辆识别码",
  "record_time_start": "2024-01-01 00:00:00",
  "record_time_end": "2024-01-01 23:59:59",
  "indicator": ["指标列表"]
}
```

#### 查询主机CPU统计
- **接口**: `POST /api/dataview/queryHostAllCpuMpstatUsageApi`
- **描述**: 查询主机所有CPU的mpstat统计数据
- **请求参数**:
```json
{
  "vin": "车辆识别码",
  "record_time_start": "2024-01-01 00:00:00",
  "record_time_end": "2024-01-01 23:59:59"
}
```

#### 查询主机进程内部数据
- **接口**: `POST /api/dataview/queryHostProcessInnerApi`
- **描述**: 查询主机进程内部详细数据
- **请求参数**:
```json
{
  "vin": "车辆识别码",
  "process_name": "进程名称",
  "record_time_start": "2024-01-01 00:00:00",
  "record_time_end": "2024-01-01 23:59:59"
}
```

### 2.2 文件管理

#### 查询日志文件列表
- **接口**: `POST /api/dataview/getLogFileListPageApi`
- **描述**: 分页查询日志文件列表
- **请求参数**:
```json
{
  "page": 1,
  "size": 10,
  "file_name": "文件名(可选)"
}
```

#### 插入OSS文件记录
- **接口**: `POST /api/dataview/insertOssFile`
- **描述**: 插入OSS文件记录到数据库
- **请求参数**:
```json
{
  "file_path": "文件路径",
  "file_size": "文件大小",
  "upload_time": "上传时间"
}
```

#### 重新解析日志
- **接口**: `POST /api/dataview/rerunLogParseApi`
- **描述**: 重新解析指定的日志文件
- **请求参数**:
```json
{
  "file_id": "文件ID"
}
```

---

## 3. 定时任务模块 (/api/cron/)

### 3.1 任务管理

#### 分页查询定时任务
- **接口**: `POST /api/cron/cronTaskPage`
- **描述**: 分页查询定时任务列表
- **请求参数**:
```json
{
  "page": 1,
  "size": 10,
  "task_name": "任务名称(可选)"
}
```

#### 修改定时任务
- **接口**: `POST /api/cron/modifyCronTask`
- **描述**: 新增或修改定时任务
- **请求参数**:
```json
{
  "id": "任务ID(修改时必填)",
  "task_name": "任务名称",
  "cron_exp": "0 0 * * *",
  "handler_class": "处理器类名",
  "is_active": true,
  "description": "任务描述"
}
```

#### 删除定时任务
- **接口**: `POST /api/cron/delCronTask`
- **描述**: 删除指定定时任务
- **请求参数**:
```json
{
  "id": "任务ID"
}
```

#### 重新运行任务
- **接口**: `POST /api/cron/rerunTask`
- **描述**: 立即重新运行指定任务
- **请求参数**:
```json
{
  "id": "任务ID"
}
```

### 3.2 专项功能

#### 生成日报Excel
- **接口**: `POST /api/cron/genDailyExcel`
- **描述**: 生成日报Excel文件
- **请求参数**:
```json
{
  "type": "无图|有图|All"
}
```

#### 克隆Jira问题
- **接口**: `POST /api/cron/cloneJiraIssue`
- **描述**: 克隆Jira问题到新问题
- **请求参数**:
```json
{
  "source_key": "源问题Key",
  "target_project": "目标项目",
  "clone_options": {
    "include_attachments": true,
    "include_comments": false
  }
}
```

#### 批量标记值
- **接口**: `POST /api/cron/batchTagValues`
- **描述**: 批量为Jira问题添加标签
- **请求参数**:
```json
{
  "jira_keys": ["PROJ-123", "PROJ-124"],
  "tags": ["标签1", "标签2"]
}
```

#### Jira同步到飞书
- **接口**: `POST /api/cron/jira2Feishu`
- **描述**: 将Jira数据同步到飞书文档
- **请求参数**:
```json
{
  "jql": "Jira查询语句",
  "feishu_doc_id": "飞书文档ID"
}
```

---

## 4. 报告模块 (/api/report/)

### 4.1 飞书报告管理

#### 分页查询飞书报告
- **接口**: `POST /api/report/feishuReportPage`
- **描述**: 分页查询飞书报告配置
- **请求参数**:
```json
{
  "page": 1,
  "size": 10,
  "name": "报告名称(可选)"
}
```

#### 修改飞书报告
- **接口**: `POST /api/report/modifyFeishuReport`
- **描述**: 新增或修改飞书报告配置
- **请求参数**:
```json
{
  "id": "报告ID(修改时必填)",
  "name": "报告名称",
  "template_id": "模板ID",
  "target_token": "目标文档Token",
  "cron_exp": "0 9 * * 1",
  "is_active": true
}
```

#### 删除飞书报告
- **接口**: `POST /api/report/delFeishuReport`
- **描述**: 删除飞书报告配置
- **请求参数**:
```json
{
  "id": "报告ID"
}
```

#### 生成飞书报告
- **接口**: `POST /api/report/generateFeishuReportApi`
- **描述**: 手动生成飞书报告
- **请求参数**:
```json
{
  "id": "报告ID",
  "params": "额外参数(可选)",
  "status": "4|5"
}
```

#### 查询报告日志
- **接口**: `POST /api/report/getFeishuReportLogPageApi`
- **描述**: 分页查询报告生成日志
- **请求参数**:
```json
{
  "page": 1,
  "size": 10,
  "report_id": "报告ID(可选)"
}
```

#### 重新生成报告
- **接口**: `POST /api/report/rerunFeishuReportApi`
- **描述**: 重新生成指定报告
- **请求参数**:
```json
{
  "log_id": "日志ID"
}
```

#### 删除报告日志
- **接口**: `POST /api/report/delFeishuReportLogApi`
- **描述**: 删除报告生成日志
- **请求参数**:
```json
{
  "id": "日志ID"
}
```

#### 修改报告备注
- **接口**: `POST /api/report/modifyReportRemark`
- **描述**: 修改报告备注信息
- **请求参数**:
```json
{
  "id": "报告ID",
  "remark": "备注内容"
}
```

---

## 5. 地图服务模块 (/api/amap/)

### 5.1 车辆和问题查询

#### 查询车辆位置
- **接口**: `POST /api/amap/queryCarSite`
- **描述**: 查询车辆位置轨迹信息
- **请求参数**:
```json
{
  "vin": "车辆识别码",
  "record_time_start": "2024-01-01 00:00:00",
  "record_time_end": "2024-01-01 23:59:59"
}
```

#### 查询Jira问题
- **接口**: `POST /api/amap/queryJiraIssues`
- **描述**: 查询与车辆相关的Jira问题
- **请求参数**:
```json
{
  "vin": "车辆识别码",
  "record_time_start": "2024-01-01 00:00:00",
  "record_time_end": "2024-01-01 23:59:59"
}
```

#### 查询Jira问题位置
- **接口**: `POST /api/amap/queryJiraSite`
- **描述**: 查询Jira问题的地理位置信息
- **请求参数**:
```json
{
  "jira_list": ["PROJ-123", "PROJ-124"],
  "record_time_start": "2024-01-01 00:00:00",
  "record_time_end": "2024-01-01 23:59:59"
}
```

#### 查询Jira问题轨迹
- **接口**: `POST /api/amap/queryJiraLine`
- **描述**: 查询Jira问题发生时的车辆轨迹
- **请求参数**:
```json
{
  "point_time": "2024-01-01 12:00:00",
  "vin": "车辆识别码"
}
```

#### 通过JQL查询Jira Key
- **接口**: `POST /api/amap/queryJiraKeysByJql`
- **描述**: 使用JQL语句查询Jira问题Key列表
- **请求参数**:
```json
{
  "jql": "project = PROJ AND status = Open"
}
```

#### 根据Key查询Jira详情
- **接口**: `POST /api/amap/queryJiraByKey`
- **描述**: 根据Jira Key查询问题详细信息
- **请求参数**:
```json
{
  "jira_key": "PROJ-123"
}
```

---

## 6. 外部接口模块 (/api/outer/)

### 6.1 性能分析

#### 解析性能数据
- **接口**: `POST /api/outer/parsePerformance`
- **描述**: 解析OSS中的性能数据文件
- **请求参数**:
```json
{
  "oss_path": "OSS文件路径",
  "prefix": "文件前缀(可选)"
}
```
- **响应示例**:
```json
{
  "cpu_temperature": "https://megtool-frontend.com/#/dataview/cpuTemperature?vin=z19&record_time_start=2024-01-01%2000:00:00&record_time_end=2024-01-01%2023:59:59",
  "gpu_nvidia_smi": "https://megtool-frontend.com/#/dataview/gpuAnalyze?vin=z19&record_time_start=2024-01-01%2000:00:00&record_time_end=2024-01-01%2023:59:59",
  "host_process_usage": "https://megtool-frontend.com/#/dataview/onlinetop?vin=z19&record_time_start=2024-01-01%2000:00:00&record_time_end=2024-01-01%2023:59:59"
}
```

---

## 7. 系统监控接口

### 7.1 健康检查

#### 系统Ping检查
- **接口**: `GET /misc/ping`
- **描述**: 检查系统是否正常运行
- **响应示例**:
```json
{
  "status": "success"
}
```

#### 健康状态检查
- **接口**: `GET /actuator/health`
- **描述**: 检查系统健康状态
- **响应示例**:
```json
{
  "health": "up"
}
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### Python 示例

```python
import requests
import json

# 基础配置
base_url = "http://your-domain.com"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer your-token-here"
}

# 登录示例
def login(username, password):
    url = f"{base_url}/api/basic/auth/login"
    data = {
        "username": username,
        "password": password
    }
    response = requests.post(url, json=data)
    return response.json()

# 查询配置示例
def query_config(key):
    url = f"{base_url}/api/basic/queryKey"
    data = {
        "key": key,
        "type": "json"
    }
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# 查询车辆位置示例
def query_car_location(vin, start_time, end_time):
    url = f"{base_url}/api/amap/queryCarSite"
    data = {
        "vin": vin,
        "record_time_start": start_time,
        "record_time_end": end_time
    }
    response = requests.post(url, json=data, headers=headers)
    return response.json()
```

### JavaScript 示例

```javascript
// 基础配置
const baseUrl = 'http://your-domain.com';
const token = 'your-token-here';

// 通用请求函数
async function apiRequest(endpoint, data, method = 'POST') {
    const response = await fetch(`${baseUrl}${endpoint}`, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: method !== 'GET' ? JSON.stringify(data) : undefined
    });
    return await response.json();
}

// 登录示例
async function login(username, password) {
    return await apiRequest('/api/basic/auth/login', {
        username: username,
        password: password
    });
}

// 查询定时任务示例
async function getCronTasks(page = 1, size = 10) {
    return await apiRequest('/api/cron/cronTaskPage', {
        page: page,
        size: size
    });
}

// 生成报告示例
async function generateReport(reportId, params = '') {
    return await apiRequest('/api/report/generateFeishuReportApi', {
        id: reportId,
        params: params
    });
}
```

### cURL 示例

```bash
# 登录
curl -X POST "http://your-domain.com/api/basic/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password123"
  }'

# 查询系统概览数据
curl -X POST "http://your-domain.com/api/dataview/queryTopOverview" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token-here" \
  -d '{
    "vin": "TESTVIN123",
    "record_time_start": "2024-01-01 00:00:00",
    "record_time_end": "2024-01-01 23:59:59",
    "indicator": ["cpu_usage", "memory_usage"]
  }'

# 健康检查
curl -X GET "http://your-domain.com/misc/ping"
```

## 注意事项

1. **认证**: 除了登录和健康检查接口外，其他接口都需要在请求头中包含有效的 Bearer Token
2. **时间格式**: 所有时间参数都使用 `YYYY-MM-DD HH:MM:SS` 格式
3. **分页**: 分页查询接口的 page 参数从 1 开始
4. **错误处理**: 请根据返回的 code 字段判断请求是否成功
5. **频率限制**: 建议控制API调用频率，避免对服务器造成过大压力
6. **数据安全**: 请妥善保管认证Token，避免泄露

## 联系方式

如有问题或建议，请联系开发团队。