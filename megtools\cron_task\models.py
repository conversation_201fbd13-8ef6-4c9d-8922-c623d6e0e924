from django.db import models

# Create your models here.
class CronTaskInfo(models.Model):
    id = models.BigAutoField(name='id', primary_key=True, help_text='编号')
    task_name = models.CharField(name='task_name', max_length=128, verbose_name="登录用户名", default='', null=False)
    cron_exp = models.CharField(name='cron_exp', max_length=64, verbose_name="Cron table 表达式， 分时日月周", null=False)
    next_exec_time = models.DateTimeField(name='next_exec_time', verbose_name="下次运行事件", auto_now=True)
    task_config = models.TextField(name='task_config', verbose_name="任务配置信息", default='', null=False)
    is_run = models.BooleanField(name='is_run', verbose_name="是否运行 0-未运行 1-运行", default=True, null=False)
    speed_time = models.FloatField(name='speed_time', verbose_name="运行时间", default=True, null=False)
    remark = models.CharField(name='remark', max_length=512, verbose_name="任务描述", default='', null=False)
    create_by = models.CharField(name='create_by', max_length=64, verbose_name="创建者", null=False)
    create_time = models.DateTimeField(name='create_time', verbose_name="创建时间", auto_now_add=True)
    update_by = models.CharField(name='update_by', max_length=64, verbose_name="更新者", null=False)
    update_time = models.DateTimeField(name='update_time', verbose_name="更新时间", auto_now=True)
    is_delete = models.CharField(name='is_delete', verbose_name="是否已经删除 0-未删除 1-已删除", default='0',
                                 max_length=2, null=False)

    class Meta:
        ordering = ['-create_time']
        db_table = "cron_task_info"
        verbose_name = "保存用户的基本信息"

class OssFileList(models.Model):
    id = models.BigAutoField(name='id', primary_key=True, help_text='编号')
    bucket_name = models.CharField(name='bucket_name', max_length=64, verbose_name="对应文件的bucket_name", default='', null=False)
    oss_path = models.CharField(name='oss_path', max_length=256, verbose_name="oss object 路径", default='', null=False)
    file_update_time = models.DateTimeField(name='file_update_time', verbose_name="更新时间", auto_now=True)
    file_size = models.BigIntegerField(name='file_size', default=0, verbose_name="更新时间")

    vin = models.CharField(name='vin', max_length=32, verbose_name="车的编号", default='', null=False)
    parse_type = models.CharField(name='parse_type', max_length=32, verbose_name="解析类型", default='', null=False)
    current_status = models.CharField(name='current_status', max_length=2, verbose_name="解析状态 0-初始化成功 1-下载中 2-已下载 3-解析中 4-已解析 5-下载失败 6-解析失败 7-写数据库失败", default='0', null=False)
    host_name = models.CharField(name='host_name', max_length=32, verbose_name="文件在哪进行解析", default='', null=False)
    remark = models.CharField(name='remark', max_length=256, verbose_name="失败的信息等", default='', null=False)

    create_time = models.DateTimeField(name='create_time', verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(name='update_time', verbose_name="更新时间", auto_now=True)
    is_delete = models.CharField(name='is_delete', verbose_name="是否已经删除 0-未删除 1-已删除", default='0',
                                 max_length=2, null=False)

    class Meta:
        ordering = ['id']
        db_table = "cron_oss_file_list"
        verbose_name = "保存监控的文件列表"

class TopOverviewLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    mem_total = models.FloatField(name="mem_total", verbose_name="总内存", max_length=50, default=0, null=False)
    mem_free = models.FloatField(name="mem_free", verbose_name="空闲内存", max_length=50, default=0, null=False)
    mem_used = models.FloatField(name="mem_used", verbose_name="使用内存", max_length=50, default=0, null=False)
    mem_buff_cache = models.FloatField(name="mem_buff_cache", verbose_name="用作内核缓存的内存", default=0, max_length=50,
                                       null=False)
    swap_total = models.FloatField(name="swap_total", verbose_name="交换区总量", max_length=50, default=0, null=False)
    swap_free = models.FloatField(name="swap_free", verbose_name="空闲交换区总量", max_length=50, default=0, null=False)
    swap_used = models.FloatField(name="swap_used", verbose_name="使用交换区总量", max_length=50, default=0, null=False)
    swap_avail_mem = models.FloatField(name="swap_avail_mem", verbose_name="缓冲的交换区总量", default=0, max_length=50,
                                       null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_top_overview"
        verbose_name = "Top日志， 顶部内存任务等信息"

class TopCpuLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    cpu_idx = models.CharField(name='cpu_idx', verbose_name="CPU编号", max_length=16, null=False)
    cpu_us = models.FloatField(name="cpu_us", verbose_name="用户空间占用CPU百分比", max_length=20, null=False)
    cpu_sy = models.FloatField(name="cpu_sy", verbose_name="内核空间占用CPU百分比", max_length=20, null=False)
    cpu_ni = models.FloatField(name="cpu_ni", verbose_name="用户进程空间内改变过优先级的进程占用CPU百分比",
                               max_length=20, null=False)
    cpu_id = models.FloatField(name="cpu_id", verbose_name="空闲CPU百分比", max_length=20, null=False)  # Line 4
    cpu_wa = models.FloatField(name="cpu_wa", verbose_name="等待输入输出的CPU时间百分比", max_length=20, null=False)
    cpu_hi = models.FloatField(name="cpu_hi", verbose_name="硬件中断的CPU时间百分比", max_length=20, null=False)
    cpu_si = models.FloatField(name="cpu_si", verbose_name="软件中断的CPU时间百分比", max_length=20, null=False)
    cpu_st = models.FloatField(name="cpu_st", verbose_name="虚拟机进程在物理CPU上等待其CPU时间的时间百分比",
                               help_text="ST为0表示流畅，CPU资源足够完全不需要等待，当数值增加时，表示服务器资源不够，母机可能超售。"
                                         "你的虚拟VPS需要等待分配物理CPU的等待时间的百分比，你排队等候分配资源的百分比。",
                               max_length=20, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_top_cpu"
        verbose_name = "Top日志， CPU 的信息"


class TopProgressLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    pid = models.IntegerField(name="pid", verbose_name="进程ID", null=False)
    user = models.CharField(name="user", verbose_name="用户名", max_length=50, null=False)
    priority = models.IntegerField(name="priority", verbose_name="优先级", null=False)
    nice_value = models.IntegerField(name="nice_value", verbose_name="负值表示高优先级，正值表示低优先级", null=False)
    virtual_image = models.IntegerField(name="virtual_image", verbose_name="进程使用的虚拟内存总量(kb)", null=False)
    resident_size = models.IntegerField(name="resident_size", verbose_name="进程使用的、未被换出的物理内存大小(kb)", null=False)
    shared_mem_size = models.IntegerField(name="shared_mem_size", verbose_name="共享内存大小(kb)", null=False)
    process_status = models.CharField(name="process_status", verbose_name="进程状态。", max_length=20,
                                      help_text="D=不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程", null=False)
    cpu_usage = models.FloatField(name="cpu_usage", verbose_name="上次更新到现在的CPU时间占用百分比", max_length=50, null=False)
    mem_usage = models.FloatField(name="mem_usage", verbose_name="进程使用的物理内存百分比", max_length=50, null=False)
    cpu_time = models.FloatField(name="cpu_time", verbose_name="进程使用的CPU时间总计", help_text="单位1/100秒", null=False)
    command = models.CharField(name="command", verbose_name="命令名/命令行", max_length=256, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_top_progress"
        verbose_name = "Top日志， 顶部内存任务等信息"


class GpuNvidiaSmiLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    memory_total_1 = models.FloatField(name="memory_total_1", verbose_name="", max_length=50, null=False)
    memory_used_1 = models.FloatField(name="memory_used_1", verbose_name="", max_length=50, null=False)
    memory_free_1 = models.FloatField(name="memory_free_1", verbose_name="", max_length=50, null=False)
    utilization_gpu_1 = models.FloatField(name="utilization_gpu_1", verbose_name="", max_length=50, null=False)
    utilization_memory_1 = models.FloatField(name="utilization_memory_1", verbose_name="", max_length=50, null=False)
    temperature_gpu_1 = models.FloatField(name="temperature_gpu_1", verbose_name="", max_length=50, null=False)
    utilization_enc_1 = models.FloatField(name="utilization_enc_1", verbose_name="", default=0,  null=False)
    utilization_dec_1 = models.FloatField(name="utilization_dec_1", verbose_name="", default=0, null=False)

    memory_total_2 = models.FloatField(name="memory_total_2", verbose_name="", max_length=50, null=False)
    memory_used_2 = models.FloatField(name="memory_used_2", verbose_name="", max_length=50, null=False)
    memory_free_2 = models.FloatField(name="memory_free_2", verbose_name="", max_length=50, null=False)
    utilization_gpu_2 = models.FloatField(name="utilization_gpu_2", verbose_name="", max_length=50, null=False)
    utilization_memory_2 = models.FloatField(name="utilization_memory_2", verbose_name="", max_length=50, null=False)
    temperature_gpu_2 = models.FloatField(name="temperature_gpu_2", verbose_name="", max_length=50, null=False)
    utilization_enc_2 = models.FloatField(name="utilization_enc_2", verbose_name="", default=0,  null=False)
    utilization_dec_2 = models.FloatField(name="utilization_dec_2", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_gpu_nvidia_smi"
        verbose_name = "GPU 日志解析"


class GpuTegrastatsMonitor(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    memory_total_1 = models.FloatField(name="memory_total_1", verbose_name="", max_length=50, null=False)
    memory_used_1 = models.FloatField(name="memory_used_1", verbose_name="", max_length=50, null=False)
    utilization_gpu_1 = models.FloatField(name="utilization_gpu_1", verbose_name="", max_length=50, null=False)
    temperature_gpu_1 = models.FloatField(name="temperature_gpu_1", verbose_name="", max_length=50, null=False)

    cpu_0 = models.FloatField(name="cpu_0", verbose_name="", max_length=50, null=False)
    cpu_1 = models.FloatField(name="cpu_1", verbose_name="", max_length=50, null=False)
    cpu_2 = models.FloatField(name="cpu_2", verbose_name="", max_length=50, null=False)
    cpu_3 = models.FloatField(name="cpu_3", verbose_name="", max_length=50, null=False)
    cpu_4 = models.FloatField(name="cpu_4", verbose_name="", max_length=50, null=False)
    cpu_5 = models.FloatField(name="cpu_5", verbose_name="", max_length=50, null=False)
    cpu_6 = models.FloatField(name="cpu_6", verbose_name="", max_length=50, null=False)
    cpu_7 = models.FloatField(name="cpu_7", verbose_name="", max_length=50, null=False)
    cpu_8 = models.FloatField(name="cpu_8", verbose_name="", max_length=50, null=False)
    cpu_9 = models.FloatField(name="cpu_9", verbose_name="", max_length=50, null=False)
    cpu_10 = models.FloatField(name="cpu_10", verbose_name="", max_length=50, null=False)
    cpu_11 = models.FloatField(name="cpu_11", verbose_name="", max_length=50, null=False)


    class Meta:
        ordering = ['-id']
        db_table = "dataview_gpu_tegrastats_monitor"
        verbose_name = "Thor GPU 日志解析"


class FpsMonitorTrafficLights(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_traffic_light"
        verbose_name = "e2e_perceptor traffic light fps"

class FpsMonitorObstacleTimestamp(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_obstacle_timestamp"
        verbose_name = "e2e_perceptor obstacle timestamp fps"

class FpsMonitorObstacleRviz(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_obstacle_rviz"
        verbose_name = "e2e_perceptor obstacle rviz fps"


class FpsMonitorLaneArray(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_lane_array"
        verbose_name = "e2e_perceptor lane_array fps"


class FpsMonitorRadar0(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_radar0"
        verbose_name = "e2e_perceptor radar0 fps"


class FpsMonitorRadar1(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_radar1"
        verbose_name = "e2e_perceptor radar1 fps"


class FpsMonitorRadar2(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_radar2"
        verbose_name = "e2e_perceptor radar2 fps"


class FpsMonitorRadar3(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_radar3"
        verbose_name = "e2e_perceptor radar3 fps"


class FpsMonitorRadar4(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_radar4"
        verbose_name = "e2e_perceptor radar4 fps"


class FpsMonitorObstacleArray(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_obstacle_array"
        verbose_name = "e2e_perceptor obstacle_array fps"


class FpsMonitorCamFront120(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_cam_front_120"
        verbose_name = "e2e_perceptor cam_front_120 fps"


class FpsMonitorCamFrontLeft(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_cam_front_left_100"
        verbose_name = "e2e_perceptor cam_front_left_100 fps"


class FpsMonitorCamFront30(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_cam_front_30"
        verbose_name = "e2e_perceptor cam_front_30 fps"


class FpsMonitorCamBack70(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_cam_back_70"
        verbose_name = "e2e_perceptor cam_back_70 fps"


class FpsMonitorCamRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_cam_front_right_100"
        verbose_name = "e2e_perceptor cam_front_right_100 fps"


class FpsMonitorFrontRslidar(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_front_rslidar"
        verbose_name = "e2e_perceptor front_rslidar fps"


class FpsMonitorCamBackLeft100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_cam_back_left"
        verbose_name = "e2e_perceptor cam_back_left fps"


class FpsMonitorCamBackRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_cam_back_right"
        verbose_name = "e2e_perceptor cam_back_right fps"


class FpsMonitorStaticObstacle(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_static_obstacle"
        verbose_name = "e2e_perceptor static_obstacle fps"


class FpsMonitorLocalizationEstimate(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_localization_estimate"
        verbose_name = "e2e_perceptor localization_estimate fps"


class FpsMdriverMworldArray(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_mdriver_fps_mworld_array"
        verbose_name = "dataview_mdriver_fps_mworld_array"

class FpsMdriverMworldArrayFromCam(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_mdriver_fps_mworld_array_from_cam"
        verbose_name = "dataview_mdriver_fps_mworld_array_from_cam"


class FpsMdriverPlanningEnvInfo(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_mdriver_fps_planning"
        verbose_name = "dataview_mdriver_fps_planning"


class FpsMdriverObstacleArray(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_mdriver_fps_obtacle_array"
        verbose_name = "dataview_mdriver_fps_obtacle_array"


class FpsAllCamBackLeft100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_back_left_100"
        verbose_name = "dataview_all_fps_cam_back_left_100"


class FpsAllCamFrontRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_front_right_100"
        verbose_name = "dataview_all_fps_cam_front_right_100"


class FpsAllCamFrontLeft100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_front_left_100"
        verbose_name = "dataview_all_fps_cam_front_left_100"


class FpsAllCamBack70(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_back_70"
        verbose_name = "dataview_all_fps_cam_back_70"


class FpsAllCamFront30(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_front_30"
        verbose_name = "dataview_all_fps_cam_front_30"


class FpsAllCamBackRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_back_right_100"
        verbose_name = "dataview_all_fps_cam_back_right_100"


class FpsAllCamLeft200(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_left_200"
        verbose_name = "dataview_all_fps_cam_left_200"


class FpsAllCamRight200(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_right_200"
        verbose_name = "dataview_all_fps_cam_right_200"


class FpsAllCamFront200(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_front_200"
        verbose_name = "dataview_all_fps_cam_front_200"


class FpsAllCamFront120(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_front_120"
        verbose_name = "dataview_all_fps_cam_front_120"


class FpsAllCamBack200(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_all_fps_cam_back_200"
        verbose_name = "dataview_all_fps_cam_back_200"


class FpsControlVehicleReport(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_control_fps_vehicle_report"
        verbose_name = "dataview_control_fps_vehicle_report"


class FpsGnssInsPose(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_gnss_fps_ins_pose"
        verbose_name = "dataview_gnss_fps_ins_pose"


class FpsGnssRawImu(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_gnss_fps_raw_imu"
        verbose_name = "dataview_gnss_fps_raw_imu"


class FpsGnssCorrImu(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_gnss_fps_corr_imu"
        verbose_name = "dataview_gnss_fps_corr_imu"


class FpsGnssGps(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_gnss_fps_gps"
        verbose_name = "dataview_gnss_fps_gps"


class FpsInnovusionLidar(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_innovusion_fps_front_lidar"
        verbose_name = "dataview_innovusion_fps_front_lidar"


class FpsProxyRadar0(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_proxy_radar_fps_0"
        verbose_name = "dataview_proxy_radar_fps_0"


class FpsProxyRadar1(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_proxy_radar_fps_1"
        verbose_name = "dataview_proxy_radar_fps_1"


class FpsProxyRadar2(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_proxy_radar_fps_2"
        verbose_name = "dataview_proxy_radar_fps_2"


class FpsProxyRadar3(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_proxy_radar_fps_3"
        verbose_name = "dataview_proxy_radar_fps_3"


class FpsProxyRadar4(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_proxy_radar_fps_4"
        verbose_name = "dataview_proxy_radar_fps_4"


class FpsInnovusionFrontLidar(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_innovusion_lidar_front_lidar_points'
        verbose_name = 'innovusion_lidar /sensor/front_lidar_points fps 日志分析'


class FpsLocalizationEstimate(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_localization_localization_estimate'
        verbose_name = 'localization /localization/localization_estimate fps 日志分析'


class FpsLocalizationCorrImu(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_localization_corr_imu'
        verbose_name = 'localization /sensor/corr_imu fps 日志分析'


class FpsLocalizationGps(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_localization_gps'
        verbose_name = 'localization /sensor/gps fps 日志分析'


class FpsLocalizationInsPose(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_localization_ins_pose'
        verbose_name = 'localization /sensor/ins_pose fps 日志分析'


class FpsLocalizationRawImu(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_localization_raw_imu'
        verbose_name = 'localization /sensor/raw_imu fps 日志分析'


class FpsLocalizationVehicleReport(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_localization_vehicle_report'
        verbose_name = 'localization /sensor/vehicle_report_common fps 日志分析'



class HostAllCpuMpStatLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    cpu_idx = models.CharField(name='cpu_idx', verbose_name="CPU编号", max_length=16, null=False)
    cpu_usr = models.FloatField(name="cpu_usr", verbose_name="", max_length=20, null=False)
    cpu_nice = models.FloatField(name="cpu_nice", verbose_name="", max_length=20, null=False)
    cpu_sys = models.FloatField(name="cpu_sys", verbose_name="", max_length=20, null=False)
    cpu_iowait = models.FloatField(name="cpu_iowait", verbose_name="", max_length=20, null=False)  # Line 4
    cpu_irq = models.FloatField(name="cpu_irq", verbose_name="", max_length=20, null=False)
    cpu_soft = models.FloatField(name="cpu_soft", verbose_name="", max_length=20, null=False)
    cpu_steal = models.FloatField(name="cpu_steal", verbose_name="", max_length=20, null=False)
    cpu_guest = models.FloatField(name="cpu_guest",  max_length=20, null=False)
    cpu_gnice = models.FloatField(name="cpu_gnice",  max_length=20, null=False)
    cpu_idle = models.FloatField(name="cpu_idle",  max_length=20, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = "dataview_host_all_cpu_mpstat"
        verbose_name = "host_all_cpu_mpstat_usage_monitor_***.log， CPU 的信息"


class GitlabMergeRequests(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    merge_id = models.IntegerField(name='merge_id', verbose_name="gitlab merge_id id", default=-1)
    project_id = models.IntegerField(name='project_id', verbose_name="gitlab project id", default=-1)
    title = models.CharField(name='title', verbose_name="gitlab merge title", max_length=128, default='', null=False)
    state = models.CharField(name='state', verbose_name="gitlab merge 状态", max_length=16, default='', null=False)
    merged_by = models.CharField(name='merged_by', verbose_name="gitlab merge 人", max_length=64, default='', null=False)
    merged_at = models.DateTimeField(name='merged_at', verbose_name="合并时间")
    source_branch = models.CharField(name='source_branch', verbose_name="源分支", max_length=128, default='', null=False)
    target_branch = models.CharField(name='target_branch', verbose_name="目标分支", max_length=128, default='', null=False)
    # 这个会有多个人
    reviewers = models.CharField(name='reviewers', verbose_name="代码Reviewer", max_length=128, default='', null=False)
    author = models.CharField(name='author', verbose_name="发起合并人", max_length=64, default='', null=False)
    sha = models.CharField(name='sha', verbose_name="sha", max_length=64, default='', null=False)
    web_url = models.CharField(name='web_url', verbose_name="sha", max_length=128, default='', null=False)
    test_author = models.CharField(name='test_author', verbose_name="测试者", max_length=64, default='', null=False)
    pass_time = models.DateTimeField(name='pass_time', verbose_name="测试通过时间")
    created_at = models.DateTimeField(name='created_at', verbose_name="创建时间")
    updated_at = models.DateTimeField(name='updated_at', verbose_name="更新时间")
    flush_batch = models.CharField(name='flush_batch', verbose_name="刷新到文档的时间", max_length=64, default='', null=False)

    class Meta:
        ordering = ['-id']
        db_table = "cron_gitlab_merge_requests"
        verbose_name = "cron_gitlab_merge_requests merge request 信息"



class HostProcessInnerE2eLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    module_name = models.CharField(name='module_name', max_length=64, verbose_name="模块名称", default='', null=False)
    pid = models.IntegerField(name="pid", verbose_name="进程ID", null=False)
    user = models.CharField(name="user", verbose_name="用户名", max_length=50, null=False)
    priority = models.IntegerField(name="priority", verbose_name="优先级", null=False)
    nice_value = models.IntegerField(name="nice_value", verbose_name="负值表示高优先级，正值表示低优先级", null=False)
    virtual_image = models.IntegerField(name="virtual_image", verbose_name="进程使用的虚拟内存总量(kb)", null=False)
    resident_size = models.IntegerField(name="resident_size", verbose_name="进程使用的、未被换出的物理内存大小(kb)",
                                        null=False)
    shared_mem_size = models.IntegerField(name="shared_mem_size", verbose_name="共享内存大小(kb)", null=False)
    process_status = models.CharField(name="process_status", verbose_name="进程状态。", max_length=20,
                                      help_text="D=不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程", null=False)
    cpu_usage = models.FloatField(name="cpu_usage", verbose_name="上次更新到现在的CPU时间占用百分比", max_length=50,
                                  null=False)
    mem_usage = models.FloatField(name="mem_usage", verbose_name="进程使用的物理内存百分比", max_length=50, null=False)
    cpu_time = models.FloatField(name="cpu_time", verbose_name="进程使用的CPU时间总计", help_text="单位1/100秒",
                                 null=False)
    command = models.CharField(name="command", verbose_name="命令名/命令行", max_length=256, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = "dataview_host_process_inner_e2e"
        verbose_name = "dataview_process_inner***.log， 线程 的信息"


class HostProcessInnerMdriverLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    module_name = models.CharField(name='module_name', max_length=64, verbose_name="模块名称", default='', null=False)
    pid = models.IntegerField(name="pid", verbose_name="进程ID", null=False)
    user = models.CharField(name="user", verbose_name="用户名", max_length=50, null=False)
    priority = models.IntegerField(name="priority", verbose_name="优先级", null=False)
    nice_value = models.IntegerField(name="nice_value", verbose_name="负值表示高优先级，正值表示低优先级", null=False)
    virtual_image = models.IntegerField(name="virtual_image", verbose_name="进程使用的虚拟内存总量(kb)", null=False)
    resident_size = models.IntegerField(name="resident_size", verbose_name="进程使用的、未被换出的物理内存大小(kb)",
                                        null=False)
    shared_mem_size = models.IntegerField(name="shared_mem_size", verbose_name="共享内存大小(kb)", null=False)
    process_status = models.CharField(name="process_status", verbose_name="进程状态。", max_length=20,
                                      help_text="D=不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程", null=False)
    cpu_usage = models.FloatField(name="cpu_usage", verbose_name="上次更新到现在的CPU时间占用百分比", max_length=50,
                                  null=False)
    mem_usage = models.FloatField(name="mem_usage", verbose_name="进程使用的物理内存百分比", max_length=50, null=False)
    cpu_time = models.FloatField(name="cpu_time", verbose_name="进程使用的CPU时间总计", help_text="单位1/100秒",
                                 null=False)
    command = models.CharField(name="command", verbose_name="命令名/命令行", max_length=256, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = "dataview_host_process_inner_mdriver"
        verbose_name = "dataview_process_inner***.log， 线程 的信息"


class HostProcessInnerEnvLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    module_name = models.CharField(name='module_name', max_length=64, verbose_name="模块名称", default='', null=False)
    pid = models.IntegerField(name="pid", verbose_name="进程ID", null=False)
    user = models.CharField(name="user", verbose_name="用户名", max_length=50, null=False)
    priority = models.IntegerField(name="priority", verbose_name="优先级", null=False)
    nice_value = models.IntegerField(name="nice_value", verbose_name="负值表示高优先级，正值表示低优先级", null=False)
    virtual_image = models.IntegerField(name="virtual_image", verbose_name="进程使用的虚拟内存总量(kb)", null=False)
    resident_size = models.IntegerField(name="resident_size", verbose_name="进程使用的、未被换出的物理内存大小(kb)",
                                        null=False)
    shared_mem_size = models.IntegerField(name="shared_mem_size", verbose_name="共享内存大小(kb)", null=False)
    process_status = models.CharField(name="process_status", verbose_name="进程状态。", max_length=20,
                                      help_text="D=不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程", null=False)
    cpu_usage = models.FloatField(name="cpu_usage", verbose_name="上次更新到现在的CPU时间占用百分比", max_length=50,
                                  null=False)
    mem_usage = models.FloatField(name="mem_usage", verbose_name="进程使用的物理内存百分比", max_length=50, null=False)
    cpu_time = models.FloatField(name="cpu_time", verbose_name="进程使用的CPU时间总计", help_text="单位1/100秒",
                                 null=False)
    command = models.CharField(name="command", verbose_name="命令名/命令行", max_length=256, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = "dataview_host_process_inner_env"
        verbose_name = "dataview_process_inner***.log， 线程 的信息"


class HostProcessInnerPilotLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    module_name = models.CharField(name='module_name', max_length=64, verbose_name="模块名称", default='', null=False)
    pid = models.IntegerField(name="pid", verbose_name="进程ID", null=False)
    user = models.CharField(name="user", verbose_name="用户名", max_length=50, null=False)
    priority = models.IntegerField(name="priority", verbose_name="优先级", null=False)
    nice_value = models.IntegerField(name="nice_value", verbose_name="负值表示高优先级，正值表示低优先级", null=False)
    virtual_image = models.IntegerField(name="virtual_image", verbose_name="进程使用的虚拟内存总量(kb)", null=False)
    resident_size = models.IntegerField(name="resident_size", verbose_name="进程使用的、未被换出的物理内存大小(kb)",
                                        null=False)
    shared_mem_size = models.IntegerField(name="shared_mem_size", verbose_name="共享内存大小(kb)", null=False)
    process_status = models.CharField(name="process_status", verbose_name="进程状态。", max_length=20,
                                      help_text="D=不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程", null=False)
    cpu_usage = models.FloatField(name="cpu_usage", verbose_name="上次更新到现在的CPU时间占用百分比", max_length=50,
                                  null=False)
    mem_usage = models.FloatField(name="mem_usage", verbose_name="进程使用的物理内存百分比", max_length=50, null=False)
    cpu_time = models.FloatField(name="cpu_time", verbose_name="进程使用的CPU时间总计", help_text="单位1/100秒",
                                 null=False)
    command = models.CharField(name="command", verbose_name="命令名/命令行", max_length=256, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = "dataview_host_process_inner_pilot"
        verbose_name = "dataview_process_inner***.log， 线程 的信息"


class HostProcessInnerControlLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    module_name = models.CharField(name='module_name', max_length=64, verbose_name="模块名称", default='', null=False)
    pid = models.IntegerField(name="pid", verbose_name="进程ID", null=False)
    user = models.CharField(name="user", verbose_name="用户名", max_length=50, null=False)
    priority = models.IntegerField(name="priority", verbose_name="优先级", null=False)
    nice_value = models.IntegerField(name="nice_value", verbose_name="负值表示高优先级，正值表示低优先级", null=False)
    virtual_image = models.IntegerField(name="virtual_image", verbose_name="进程使用的虚拟内存总量(kb)", null=False)
    resident_size = models.IntegerField(name="resident_size", verbose_name="进程使用的、未被换出的物理内存大小(kb)",
                                        null=False)
    shared_mem_size = models.IntegerField(name="shared_mem_size", verbose_name="共享内存大小(kb)", null=False)
    process_status = models.CharField(name="process_status", verbose_name="进程状态。", max_length=20,
                                      help_text="D=不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程", null=False)
    cpu_usage = models.FloatField(name="cpu_usage", verbose_name="上次更新到现在的CPU时间占用百分比", max_length=50,
                                  null=False)
    mem_usage = models.FloatField(name="mem_usage", verbose_name="进程使用的物理内存百分比", max_length=50, null=False)
    cpu_time = models.FloatField(name="cpu_time", verbose_name="进程使用的CPU时间总计", help_text="单位1/100秒",
                                 null=False)
    command = models.CharField(name="command", verbose_name="命令名/命令行", max_length=256, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = "dataview_host_process_inner_control"
        verbose_name = "dataview_process_inner***.log， 线程 的信息"

class HostProcessInnerPerceptionLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    module_name = models.CharField(name='module_name', max_length=64, verbose_name="模块名称", default='', null=False)
    pid = models.IntegerField(name="pid", verbose_name="进程ID", null=False)
    user = models.CharField(name="user", verbose_name="用户名", max_length=50, null=False)
    priority = models.IntegerField(name="priority", verbose_name="优先级", null=False)
    nice_value = models.IntegerField(name="nice_value", verbose_name="负值表示高优先级，正值表示低优先级", null=False)
    virtual_image = models.IntegerField(name="virtual_image", verbose_name="进程使用的虚拟内存总量(kb)", null=False)
    resident_size = models.IntegerField(name="resident_size", verbose_name="进程使用的、未被换出的物理内存大小(kb)",
                                        null=False)
    shared_mem_size = models.IntegerField(name="shared_mem_size", verbose_name="共享内存大小(kb)", null=False)
    process_status = models.CharField(name="process_status", verbose_name="进程状态。", max_length=20,
                                      help_text="D=不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程", null=False)
    cpu_usage = models.FloatField(name="cpu_usage", verbose_name="上次更新到现在的CPU时间占用百分比", max_length=50,
                                  null=False)
    mem_usage = models.FloatField(name="mem_usage", verbose_name="进程使用的物理内存百分比", max_length=50, null=False)
    cpu_time = models.FloatField(name="cpu_time", verbose_name="进程使用的CPU时间总计", help_text="单位1/100秒",
                                 null=False)
    command = models.CharField(name="command", verbose_name="命令名/命令行", max_length=256, null=False)

    class Meta:
            ordering = ['record_time']
            db_table = "dataview_host_process_inner_perception"
            verbose_name = "dataview_process_inner***.log， 线程 的信息"

class HostProcessInnerLocLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    module_name = models.CharField(name='module_name', max_length=64, verbose_name="模块名称", default='', null=False)
    pid = models.IntegerField(name="pid", verbose_name="进程ID", null=False)
    user = models.CharField(name="user", verbose_name="用户名", max_length=50, null=False)
    priority = models.IntegerField(name="priority", verbose_name="优先级", null=False)
    nice_value = models.IntegerField(name="nice_value", verbose_name="负值表示高优先级，正值表示低优先级", null=False)
    virtual_image = models.IntegerField(name="virtual_image", verbose_name="进程使用的虚拟内存总量(kb)", null=False)
    resident_size = models.IntegerField(name="resident_size", verbose_name="进程使用的、未被换出的物理内存大小(kb)",
                                        null=False)
    shared_mem_size = models.IntegerField(name="shared_mem_size", verbose_name="共享内存大小(kb)", null=False)
    process_status = models.CharField(name="process_status", verbose_name="进程状态。", max_length=20,
                                      help_text="D=不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程", null=False)
    cpu_usage = models.FloatField(name="cpu_usage", verbose_name="上次更新到现在的CPU时间占用百分比", max_length=50,
                                  null=False)
    mem_usage = models.FloatField(name="mem_usage", verbose_name="进程使用的物理内存百分比", max_length=50, null=False)
    cpu_time = models.FloatField(name="cpu_time", verbose_name="进程使用的CPU时间总计", help_text="单位1/100秒",
                                 null=False)
    command = models.CharField(name="command", verbose_name="命令名/命令行", max_length=256, null=False)

    class Meta:
            ordering = ['record_time']
            db_table = "dataview_host_process_inner_loc"
            verbose_name = "dataview_process_inner***.log， 线程 的信息"

class OneFrameE2EObstacle(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    process_time = models.FloatField(name="process_time", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "one_frame_e2e_obstacle"
        verbose_name = "obstacle_process_time"


class OneFrameE2ETrafficLight(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    process_time = models.FloatField(name="process_time", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "one_frame_e2e_traffic_light"
        verbose_name = "one_frame_e2e_traffic_light"


class OneFrameMdriverProcess(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    process_time = models.FloatField(name="process_time", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "one_frame_mdriver_process_time"
        verbose_name = "one_frame_mdriver_process_time"


class OneFrameControlProcess(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    process_time = models.FloatField(name="process_time", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "one_frame_control_process_time"
        verbose_name = "one_frame_control_process_time"


class OneFrameEnvironmentModelProcess(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    process_time = models.FloatField(name="process_time", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "one_frame_environment_process_time"
        verbose_name = "one_frame_environment_process_time"



class OneFramePilotPlanningProcessTime(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    process_time = models.FloatField(name="process_time", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "one_frame_pilot_planning_process_time"
        verbose_name = "one_frame_pilot_planning_process_time"



class OneFramePilotPlanningSensor2Planning(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    process_time = models.FloatField(name="process_time", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "one_frame_pilot_planning_sensor2planning"
        verbose_name = "one_frame_pilot_planning_sensor2planning"



class OneFramePilotPlanningPrediction(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    process_time = models.FloatField(name="process_time", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "one_frame_pilot_planning_prediction"
        verbose_name = "one_frame_pilot_planning_prediction"


class DataviewCpuTemperature(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    package = models.FloatField(name="package", verbose_name="", default=0, null=False)
    core_0 = models.FloatField(name="core_0", verbose_name="", default=0, null=False)
    core_4 = models.FloatField(name="core_4", verbose_name="", default=0, null=False)
    core_8 = models.FloatField(name="core_8", verbose_name="", default=0, null=False)
    core_12 = models.FloatField(name="core_12", verbose_name="", default=0, null=False)
    core_16 = models.FloatField(name="core_16", verbose_name="", default=0, null=False)
    core_20 = models.FloatField(name="core_20", verbose_name="", default=0, null=False)
    core_24 = models.FloatField(name="core_24", verbose_name="", default=0, null=False)
    core_28 = models.FloatField(name="core_28", verbose_name="", default=0, null=False)
    core_32 = models.FloatField(name="core_32", verbose_name="", default=0, null=False)
    core_33 = models.FloatField(name="core_33", verbose_name="", default=0, null=False)
    core_34 = models.FloatField(name="core_34", verbose_name="", default=0, null=False)
    core_35 = models.FloatField(name="core_35", verbose_name="", default=0, null=False)
    core_36 = models.FloatField(name="core_36", verbose_name="", default=0, null=False)
    core_37 = models.FloatField(name="core_37", verbose_name="", default=0, null=False)
    core_38 = models.FloatField(name="core_38", verbose_name="", default=0, null=False)
    core_39 = models.FloatField(name="core_39", verbose_name="", default=0, null=False)
    core_40 = models.FloatField(name="core_40", verbose_name="", default=0, null=False)
    core_41 = models.FloatField(name="core_41", verbose_name="", default=0, null=False)
    core_42 = models.FloatField(name="core_42", verbose_name="", default=0, null=False)
    core_43 = models.FloatField(name="core_43", verbose_name="", default=0, null=False)
    core_44 = models.FloatField(name="core_44", verbose_name="", default=0, null=False)
    core_45 = models.FloatField(name="core_45", verbose_name="", default=0, null=False)
    core_46 = models.FloatField(name="core_46", verbose_name="", default=0, null=False)
    core_47 = models.FloatField(name="core_47", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_cpu_temperature"
        verbose_name = "dataview_cpu_temperature"


class FpsFusionRadarRearLeft(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fusion_radar_rear_left'
        verbose_name = 'perception_fusion_node /sensor/radar_rear_left fps 日志分析'


class FpsFusionRadarRearRight(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fusion_radar_rear_right'
        verbose_name = 'perception_fusion_node /sensor/radar_rear_right fps 日志分析'


class FpsFusionRadarFrontLeft(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fusion_radar_front_left'
        verbose_name = 'perception_fusion_node /sensor/radar_front_right fps 日志分析'


class FpsFusionRadarFrontRight(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fusion_radar_front_right'
        verbose_name = 'perception_fusion_node /sensor/radar_front_left fps 日志分析'


class FpsFusionRadarLocalization(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fusion_radar_localization_estimate'
        verbose_name = 'perception_fusion_node /localization/localization_estimate fps 日志分析'


class FpsFusionFusionObstacleArray(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fusion_fusion_obstacle_array'
        verbose_name = 'perception_fusion_node /perception/fusion/obstacle_array_result fps 日志分析'


class FpsFusionRadarFrontMiddle(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fusion_radar_front_middle'
        verbose_name = 'perception_fusion_node /sensor/radar_front_middle fps 日志分析'


class FpsFusionVehicleReportCommon(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fusion_vehicle_report_common'
        verbose_name = 'perception_fusion_node /sensor/vehicle_report_common fps 日志分析'


class FpsFusionDetectionObstacleArray(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fusion_detection_obstacle_array'
        verbose_name = 'perception_fusion_node /perception/detection/obstacle_array_result fps 日志分析'


class FpsOccStaticObstacleResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_static_obstacle_result'
        verbose_name = 'e2e_occ /perception/detection/static_obstacle_result fps 日志分析'

class FpsOccStaticObstacleResultSecond(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_static_obstacle_result_second'
        verbose_name = 'e2e_occ /perception/detection/static_obstacle_result_second fps 日志分析'

class FpsOccFrontRslidar(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_front_rslidar'
        verbose_name = 'e2e_occ /sensor/front_rslidar_points fps 日志分析'

class FpsOccLocalization(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_localization'
        verbose_name = 'e2e_occ /localization/localization_estimate fps 日志分析'

class FpsOccCamBackRight(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_cam_back_right'
        verbose_name = 'e2e_occ /sensor/cam_back_right_100/h264 fps 日志分析'

class FpsOccCamBackLeft(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_cam_back_left'
        verbose_name = 'e2e_occ /sensor/cam_back_left_100/h264 fps 日志分析'

class FpsOccCamFrontRight(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_cam_front_right'
        verbose_name = 'e2e_occ /sensor/cam_front_right_100/h264 fps 日志分析'

class FpsOccCamFrontLeft(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_cam_front_left'
        verbose_name = 'e2e_occ /sensor/cam_front_left_100/h264 fps 日志分析'

class FpsOccCamBack70(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_cam_back_70'
        verbose_name = 'e2e_occ /sensor/cam_back_70/h264 fps 日志分析'

class FpsOccVisualization(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_visualization'
        verbose_name = 'e2e_occ /perception/occ/visualization fps 日志分析'

class FpsOccVisualization3D(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_visualization_3d'
        verbose_name = 'e2e_occ /perception/occ/visualization_3d fps 日志分析'

class FpsOccCamFront30(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_cam_front_30'
        verbose_name = 'e2e_occ /sensor/cam_front_30/h264 fps 日志分析'

class FpsOccCamFront120(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_cam_front_120'
        verbose_name = 'e2e_occ /sensor/cam_front_120/h264 fps 日志分析'


class FpsPlanningResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_pilot_planngin_result'
        verbose_name = 'pilot_planning /planning/planning_result fps 日志分析'

class FpsEnvTrafficLight(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_env_fps_traffic_light'
        verbose_name = 'environment_model /perception/traffic_lights_3in1_result fps 日志分析'

class FpsEnvPlanningEnvInfo(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_env_fps_env_info'
        verbose_name = 'environment_model /planning/env_info fps 日志分析'


class PilotPlanningThreadTimeSpeed(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    curr = models.FloatField(name='curr', verbose_name='curr', default=0, max_length=50, null=False)
    average = models.FloatField(name='average', verbose_name='average', default=0, max_length=50, null=False)
    max = models.FloatField(name='max', verbose_name='max', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_planning_thread_speed'
        verbose_name = 'PilotPlanning PlanningThread time spend'


class VehicleSoftwareVersionInfo(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    version_info = models.TextField(name='version_info', verbose_name='版本号', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_vehicle_software_info'
        verbose_name = '整车版本号'

class FpsE2ePerceptorMapInputSync(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_fps_map_input_sync'
        verbose_name = 'e2e_perceptor /debug/profile/map_input_sync fps 日志分析'

class FpsOccInputTimestamps(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_input_timestamps'
        verbose_name = 'e2e_occ /perception/occ/input_timestamps fps 日志分析'

class FpsPilotPlanningLaneArray(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_pilot_planning_lane_array'
        verbose_name = 'pilot_planning /debug/env_info/lane_array fps 日志分析'

class FpsPerceptorDecodeBack70(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_decode_back70'
        verbose_name = 'e2e_perceptor decode/sensor/cam_back_70/h264 fps 日志分析'

class FpsPerceptorDecodeBackLeft100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_decode_back_left_100'
        verbose_name = 'e2e_perceptor decode/sensor/cam_back_left_100/h264 fps 日志分析'

class FpsPerceptorDecodeBackRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_decode_back_right_100'
        verbose_name = 'e2e_perceptor decode/sensor/cam_back_right_100/h264 fps 日志分析'

class FpsPerceptorDecodeFront120(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_decode_front_120'
        verbose_name = 'e2e_perceptor decode/sensor/cam_front_120/h264 fps 日志分析'

class FpsPerceptorDecodeFront30(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_decode_front_30'
        verbose_name = 'e2e_perceptor decode/sensor/cam_front_30/h264 fps 日志分析'

class FpsPerceptorDecodeFrontLeft100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_decode_front_left_100'
        verbose_name = 'e2e_perceptor decode/sensor/cam_front_left_100/h264 fps 日志分析'

class FpsPerceptorDecodeFrontRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_decode_front_right_100'
        verbose_name = 'e2e_perceptor decode/sensor/cam_front_right_100/h264 fps 日志分析'


class FpsE2eFailsafeCamBack70(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_cam_back_70'
        verbose_name = 'e2e_failsafe /failsafe/cam_back_70 fps 日志分析'

class FpsE2eFailsafeCamBackLeft100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_cam_back_left_100'
        verbose_name = 'e2e_failsafe /failsafe/cam_back_left_100 fps 日志分析'

class FpsE2eFailsafeCamBackRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_cam_back_right_100'
        verbose_name = 'e2e_failsafe /failsafe/cam_back_right_100 fps 日志分析'

class FpsE2eFailsafeCamFront120(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_cam_front_120'
        verbose_name = 'e2e_failsafe /failsafe/cam_front_120 fps 日志分析'

class FpsE2eFailsafeCamFront30(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_cam_front_30'
        verbose_name = 'e2e_failsafe /failsafe/cam_front_30 fps 日志分析'

class FpsE2eFailsafeCamFrontLeft100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_cam_front_left_100'
        verbose_name = 'e2e_failsafe /failsafe/cam_front_left_100 fps 日志分析'

class FpsE2eFailsafeCamFrontRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_cam_front_right_100'
        verbose_name = 'e2e_failsafe /failsafe/cam_front_right_100 fps 日志分析'

class FpsE2eFailsafeSensorBack70(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_sensor_back_70'
        verbose_name = 'e2e_failsafe /sensor/cam_back_70/h264 fps 日志分析'

class FpsE2eFailsafeSensorBackLeft100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_sensor_back_left_100'
        verbose_name = 'e2e_failsafe /sensor/cam_back_left_100/h264 fps 日志分析'

class FpsE2eFailsafeSensorBackRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_sensor_back_right_100'
        verbose_name = 'e2e_failsafe /sensor/cam_back_right_100/h264 fps 日志分析'

class FpsE2eFailsafeSensorFront120(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_sensor_front_120'
        verbose_name = 'e2e_failsafe /sensor/cam_front_120/h264 fps 日志分析'

class FpsE2eFailsafeSensorFront30(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_sensor_front_30'
        verbose_name = 'e2e_failsafe /sensor/cam_front_30/h264 fps 日志分析'

class FpsE2eFailsafeSensorFrontLeft100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_sensor_front_left_100'
        verbose_name = 'e2e_failsafe /sensor/cam_front_left_100/h264 fps 日志分析'

class FpsE2eFailsafeSensorFrontRight100(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_failsafe_sensor_front_right_100'
        verbose_name = 'e2e_failsafe /sensor/cam_front_right_100/h264 fps 日志分析'





class FpsOccDynamicObstacleResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_dynamic_obstacle_result'
        verbose_name = 'e2e_occ /perception/detection/dynamic_obstacle_result fps 日志分析'


class FpsOccDynamicObstacleResultSecond(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_occ_dynamic_obstacle_result_second'
        verbose_name = 'e2e_occ /perception/detection/dynamic_obstacle_result_second fps 日志分析'


class E2eMcapInfo(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    lat = models.FloatField(name='lat', verbose_name='纬度', null=True)
    lon = models.FloatField(name='lon', verbose_name='经度', null=True)
    behavior = models.CharField(name='behavior', max_length=17, verbose_name='behavior', default='', null=True)
    speed = models.FloatField(name='speed', verbose_name='车速', null=True)
    has_map = models.CharField(name='has_map',max_length=16, verbose_name='有图无图', null=True)
    state = models.CharField(name='state',max_length=8, verbose_name='驾驶状态 AUTO/MAN', null=True)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_mcap_info'
        verbose_name = '保存Mcap中的数据，获得'

class OneFrameVlmLlmnode (models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    llmnode = models.FloatField(name='llmnode', verbose_name='llmnode', max_length=50)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_one_frame_vlm_llmnode'
        verbose_name = 'vlm_ Llmnode OneFrame 日志分析'

class OneFrameVlmPrecess (models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    precess = models.FloatField(name='precess', verbose_name='precess', max_length=50)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_one_frame_vlm_precess'
        verbose_name = 'vlm_ precess OneFrame 日志分析'

class OneFrameVlmGenerateInfer  (models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    generate_infer = models.FloatField(name='generate_infer', verbose_name='generate_infer', max_length=50)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_one_frame_vlm_generate_infer'
        verbose_name = 'vlm_ generate_infer OneFrame 日志分析'


class FpsVlmNodeFs  (models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', max_length=50)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_fps_vlm_node_fps'
        verbose_name = 'dataview_fps_vlm_node_fps fps分析'


class CronJira2Mysql(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    key = models.CharField(name='key', max_length=16, verbose_name='jira_key', default='', null=False)
    summary = models.CharField(name='summary', max_length=256, verbose_name='概述', default='', null=False)
    issuetype = models.CharField(name='issuetype', max_length=16, verbose_name='类型', default='', null=False)
    status = models.CharField(name='status', max_length=16, verbose_name='状态', default='', null=False)
    priority = models.CharField(name='priority', max_length=16, verbose_name='优先级', default='', null=False)
    components = models.CharField(name='components', max_length=128, verbose_name='模块', default='', null=False)
    labels = models.CharField(name='labels', max_length=512, verbose_name='标签', default='', null=False)
    customfield_13000 = models.CharField(name='customfield_13000', max_length=256, verbose_name='算法问题标签', default='', null=False)
    customfield_11706 = models.CharField(name='customfield_11706', max_length=64, verbose_name='MegSim报告人', default='', null=False)
    customfield_12306 = models.CharField(name='customfield_12306', max_length=16, verbose_name='车型', default='', null=False)
    customfield_11701 = models.CharField(name='customfield_11701', max_length=32, verbose_name='测试车号', default='', null=False)
    assignee = models.CharField(name='assignee', max_length=32, verbose_name='经办人', default='', null=False)
    reporter = models.CharField(name='reporter', max_length=32, verbose_name='报告人', default='', null=False)
    created = models.DateTimeField(name='created', verbose_name="创建时间")
    updated = models.DateTimeField(name='updated', verbose_name="更新时间")
    customfield_12001 = models.DateTimeField(name='customfield_12001', verbose_name="预计完成时间")
    customfield_11502 = models.DateTimeField(name='customfield_11502', verbose_name="实际修复时间")
    point_time = models.DateTimeField(name='point_time', verbose_name='summary 中提取的时间')
    customfield_10211 = models.CharField(name='customfield_10211', max_length=256, verbose_name='mcap链接', default='', null=False)
    lat = models.FloatField(name='lat', verbose_name='纬度', null=True)
    lon = models.FloatField(name='lon', verbose_name='经度', null=True)

    class Meta:
        ordering = ['updated']
        db_table = 'cron_jira_e2e_2_mysql'
        verbose_name = 'Jira 数据同步'


class FpsAebLidarObstacle(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_lidar_obstacle'
        verbose_name = 'aeb_perceptor /perception/aeb_lidar_obstacle_array fps 日志分析'

class FpsAebRvObstacle(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_rv_obstacle'
        verbose_name = 'aeb_perceptor /perception/aeb_rv_obstacle_array fps 日志分析'

class FpsAebFrontRslidar(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_front_rslidar_points'
        verbose_name = 'aeb_perceptor /sensor/front_rslidar_points fps 日志分析'

class FpsAebCamFront120(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_cam_front_120'
        verbose_name = 'aeb_perceptor /sensor/cam_front_120/h264 fps 日志分析'


class FpsAebLidarObstacleArray(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_lidar_obstacle_array'
        verbose_name = 'aeb_perceptor /perception/aeb_lidar_obstacle_array fps 日志分析'


class OneFrameEnvPublishAdmap(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    process_time = models.FloatField(name="process_time", verbose_name="", default=0, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "one_frame_env_public_admap"
        verbose_name = "one_frame_env_public_admap"

class FpsMonitorEnvMapAdmapL0(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")

    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_env_map_admap_l0"
        verbose_name = "environment /map/admap_l0"


class CronJiraCntLastStatus(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    type = models.CharField(name='type', max_length=128, verbose_name="type", default='', null=False)
    jira_key = models.CharField(name='jira_key', max_length=32, verbose_name="jira_key", default='', null=False)
    summary = models.CharField(name='summary', max_length=512, verbose_name="summary", default='', null=False)

    class Meta:
        ordering = ['-id']
        db_table = "cron_jira_cnt_last_status"
        verbose_name = "Jira 数量的最后一次状态"


class FpsE2ePerceptorDebugInputSync(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name="vin", default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name="记录时间")
    fps = models.FloatField(name="fps", verbose_name="", default=0, max_length=50, null=False)
    delay = models.FloatField(name="delay", verbose_name="", default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name="maxdelay", verbose_name="", default=0, max_length=50, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "dataview_e2e_perceptor_fps_debug_input_sync"
        verbose_name = "e2e_perceptor /debug/mc_algo_context/input_sync_7v fps"

class FpsE2ePerceptorTrafficSigns(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_fps_traffic_signs'
        verbose_name = 'aeb_perceptor /perception/traffic_signs_result fps 日志分析'

class FpsE2ePerceptorTrafficRaw(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_e2e_perceptor_fps_traffic_raw'
        verbose_name = 'aeb_perceptor /perception/traffic_raw_result fps 日志分析'

class FpsAebRvDynResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_rv_dyn_result'
        verbose_name = 'aeb_perceptor /perception/aeb_rv_dyn_result fps 日志分析'

class FpsAebRvResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_rv_result'
        verbose_name = 'aeb_perceptor /perception/aeb_rv_result fps 日志分析'

class FpsAebRvObstacleResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_rv_obstacle_result'
        verbose_name = 'aeb_perceptor /perception/aeb_rv_obstacle_array fps 日志分析'


class FpsRvStaticNodeRvStaticResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_rv_static_node_fps_rvstatic_result'
        verbose_name = 'rv_static_node /perception/rvstatic_result fps 日志分析'

class FpsRvStaticNodeLaneArrayResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_rv_static_node_fps_lane_array_result'
        verbose_name = 'rv_static_node /perception/lane_array_result fps 日志分析'

class FpsRvStaticNodeLocalization(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_rv_static_node_fps_localization'
        verbose_name = 'rv_static_node /localization/localization_estimate fps 日志分析'

class FpsRvStaticNodeAebRvResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_rv_static_node_fps_aeb_rv_result'
        verbose_name = 'rv_static_node /perception/aeb_rv_result fps 日志分析'

class FpsRvStaticNodeRvStaticWordResult(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_rv_static_node_fps_rvstatic_word_result'
        verbose_name = 'rv_static_node /perception/rvstatic_word_result fps 日志分析'


class FpsAebLights(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_lights'
        verbose_name = 'aeb_perceptor /perception/aeb_lights fps 日志分析'

class FpsAebAdb(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_adb'
        verbose_name = 'aeb_perceptor /perception/aeb_adb fps 日志分析'


class FpsLidarAbsFrontRsLidarPoints(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_lidarabs_sensor_front_rslidar_points'
        verbose_name = 'lidarabs_ /sensor/front_rslidar_points fps 日志分析'

class FpsAebRvDynSubclass(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    vin = models.CharField(name='vin', max_length=17, verbose_name='vin', default='', null=False)
    record_time = models.DateTimeField(name='record_time', verbose_name='记录时间')
    fps = models.FloatField(name='fps', verbose_name='fps', default=0, max_length=50, null=False)
    delay = models.FloatField(name='delay', verbose_name='delay', default=0, max_length=50, null=False)
    maxdelay = models.FloatField(name='maxdelay', verbose_name='maxdelay', default=0, max_length=50, null=False)

    class Meta:
        ordering = ['record_time']
        db_table = 'dataview_aeb_rv_dyn_subclass'
        verbose_name = 'aeb_perceptor /perception/aeb_rv_dyn_subclass fps 日志分析'
