import logging
import sys
from typing import Optional
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    # 图标映射
    ICONS = {
        'DEBUG': '🔍',
        'INFO': 'ℹ️',
        'WARNING': '⚠️',
        'ERROR': '❌',
        'CRITICAL': '🚨'
    }
    
    def format(self, record):
        # 获取原始格式
        log_message = super().format(record)
        
        # 添加颜色和图标
        level_name = record.levelname
        color = self.COLORS.get(level_name, self.COLORS['RESET'])
        icon = self.ICONS.get(level_name, '')
        
        # 格式化时间
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        # 构建美化后的消息
        if level_name == 'INFO':
            # 特殊处理INFO消息，添加更多视觉元素
            if '✅' in log_message or '成功' in log_message:
                icon = '✅'
                color = '\033[92m'  # 亮绿色
            elif '❌' in log_message or '失败' in log_message:
                icon = '❌'
                color = '\033[91m'  # 亮红色
            elif '🚀' in log_message or '开始' in log_message:
                icon = '🚀'
                color = '\033[94m'  # 蓝色
            elif '🎉' in log_message or '完成' in log_message or '结束' in log_message:
                icon = '🎉'
                color = '\033[95m'  # 紫色
            elif '🧪' in log_message or '测试' in log_message:
                icon = '🧪'
                color = '\033[93m'  # 黄色
            elif '🔗' in log_message or '连接' in log_message:
                icon = '🔗'
                color = '\033[96m'  # 青色
            elif '⚙️' in log_message or '配置' in log_message:
                icon = '⚙️'
                color = '\033[97m'  # 白色
            elif '📊' in log_message or '报告' in log_message:
                icon = '📊'
                color = '\033[94m'  # 蓝色
            elif '🤖' in log_message or 'AI' in log_message:
                icon = '🤖'
                color = '\033[95m'  # 紫色
        
        # 构建最终格式
        formatted_message = f"{color}{icon} [{timestamp}] {log_message}{self.COLORS['RESET']}"
        return formatted_message


class LoggerManager:
    """日志管理器"""
    
    _instance = None
    _logger = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_logger()
            self._initialized = True
    
    def _setup_logger(self):
        """设置日志配置"""
        # 清除现有的处理器
        logging.getLogger().handlers.clear()
        
        # 创建格式化器
        formatter = ColoredFormatter(
            fmt='%(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # 配置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        root_logger.handlers.clear()
        root_logger.addHandler(console_handler)
        
        self._logger = logging.getLogger('AutoTestReport')
        self._logger.setLevel(logging.INFO)
        self._logger.propagate = False  # 防止重复输出
        self._logger.addHandler(console_handler)
    
    @property
    def logger(self):
        """获取日志实例"""
        return self._logger
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志实例"""
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        logger.propagate = False  # 防止重复输出
        
        # 确保只有一个处理器
        if not logger.handlers:
            formatter = ColoredFormatter(
                fmt='%(message)s',
                datefmt='%H:%M:%S'
            )
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            console_handler.setLevel(logging.INFO)
            logger.addHandler(console_handler)
        
        return logger


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """获取日志实例的便捷函数"""
    if name is None:
        return LoggerManager().logger
    return LoggerManager().get_logger(name)


# 便捷的日志函数
def log_success(message: str):
    """记录成功信息"""
    logger = get_logger()
    logger.info(f"✅ {message}")


def log_error(message: str):
    """记录错误信息"""
    logger = get_logger()
    logger.error(f"❌ {message}")


def log_warning(message: str):
    """记录警告信息"""
    logger = get_logger()
    logger.warning(f"⚠️ {message}")


def log_info(message: str):
    """记录一般信息"""
    logger = get_logger()
    logger.info(f"ℹ️ {message}")


def log_start(message: str):
    """记录开始信息"""
    logger = get_logger()
    logger.info(f"🚀 {message}")


def log_complete(message: str):
    """记录完成信息"""
    logger = get_logger()
    logger.info(f"🎉 {message}")


def log_test(message: str):
    """记录测试信息"""
    logger = get_logger()
    logger.info(f"🧪 {message}")


def log_connection(message: str):
    """记录连接信息"""
    logger = get_logger()
    logger.info(f"🔗 {message}")


def log_config(message: str):
    """记录配置信息"""
    logger = get_logger()
    logger.info(f"⚙️ {message}")


def log_report(message: str):
    """记录报告信息"""
    logger = get_logger()
    logger.info(f"📊 {message}")


 