import logging
import re
import traceback

from common.util import get_column_name
from test_doc.feishu.api.feishu_api import FeishuApi
from test_doc.feishu.data_source.copy_sheet import CopySheet
from test_doc.feishu.data_source.jira_cnt import JiraCnt

regexp_str = '\\{\\{[a-z0-9A-Z,_.()=/: \\-]+\\}\\}'
args_pattern = re.compile(regexp_str)

class FeishuExcelParser:
    def __init__(self, excel_token, variables):
        self.feishu_api = FeishuApi(excel_token)
        self.variables = variables
        self.excel_token = excel_token
        self.copy_data = []

    def execute(self):
        # 获取工作表
        sheets = self.get_sheets()
        for sheet in sheets:
            # 读取sheet内容
            sheet_data = self.get_sheet_data(sheet['sheet_id'], sheet['grid_properties']['row_count'], sheet['grid_properties']['column_count'])
            # 解析并替换变量
            self.parse_placeholder(sheet_data, sheet['sheet_id'])
            # 复制功能
            for item in self.copy_data:
                CopySheet(item['key'], self.feishu_api, self.excel_token, sheet['sheet_id'], item['address']).apply()


    def get_sheets(self):
        data = self.feishu_api.get_sheets()
        sheets = data.get('data', {}).get('sheets', [])
        return sheets

    def get_sheet_data(self, sheet_id, row_count, column_count):
        column_name = get_column_name(column_count)
        sheet_data = self.feishu_api.sheet_multi_area_data(self.excel_token, [f"{sheet_id}!A1:{column_name}{row_count}"])
        valueRanges = sheet_data.get("data", {}).get("valueRanges", [])
        result = {}
        for row_index, arr in enumerate(valueRanges[0]['values']):
            for column_index, value in enumerate(arr):
                if value is None:
                    continue
                result[f"{get_column_name(column_index + 1)}{row_index+1}"] = value
        return result

    # 26 的时候处理不对！
    # def get_column_name(self, col_idx: int):
    #     if col_idx <= 0:
    #         return ""
    #     else:
    #         next_cal = int(col_idx / 26)
    #         mod_val = col_idx % 26
    #         mod_val = chr(mod_val + 64)
    #         cal_val = self.get_column_name(next_cal)
    #         return f"{cal_val}{mod_val}"

    def parse_placeholder(self, sheet_data, sheet_id):
        """
        解析变量
        """
        all_data = []
        for cell_address, content in sheet_data.items():
            # 查找value中有{{}}的
            result = args_pattern.findall(str(content))
            if len(result) == 0:
                continue
            for key in result:
                try:
                    value = self.handle_method(key, sheet_id, cell_address, sheet_data)
                except:
                    value = f"{traceback.format_exc()}"
                    logging.error(f"excel parser error: {traceback.format_exc()}")
                if value and isinstance(value, list):
                    all_data.extend(value)
                    content = content.replace(key, '')
                else:
                    content = content.replace(key, value)
            if content.strip():
                self.feishu_api.sheet_write_cells(self.excel_token,{
                    "valueRange": {
                        "range": f"{sheet_id}!{cell_address}:{cell_address}",
                        "values": [[content.strip()]]
                    }
                })
        # 批量更新
        if len(all_data) > 0:
            self.feishu_api.sheet_batch_write_cells(self.excel_token, {"valueRanges": all_data})



    def handle_method(self, key, sheet_id, cell_address, sheet_data):
        unpack_key = key[2:-2].strip()
        if unpack_key.endswith(")"):
            # 这里都是创建一个新的block， 然后进行存储即可
            method = self.choose_method(unpack_key, sheet_id, cell_address, sheet_data)
            if method:
                key = method.apply()
        elif unpack_key in self.variables:
            key = self.variables[unpack_key]
        return key if key is not None else ""

    def choose_method(self, key: str, sheet_id, cell_address, sheet_data):
        # -------------------- 一个Report里面有多个Suite 的时候，使用这个方法
        if key.startswith("jira_cnt("):
            return JiraCnt(key[9:-1], self.variables, self.feishu_api, self.excel_token, sheet_id, cell_address, sheet_data)
        if key.startswith('copy_sheet('):
            self.copy_data.append({"key": key[11:-1], "address": cell_address})
            return

