"""内容处理服务"""

import re
from typing import Optional, Tuple
from .base import BaseService
from ..models.feishu import <PERSON><PERSON>uRecord, UpdateFields
from ..models.gitlab import GitLabMR
from ..config import Settings


class ContentProcessor(BaseService):
    """内容处理器"""
    
    def __init__(self, settings: Settings):
        super().__init__(settings)
        
        # 预编译正则表达式
        self.content_regex = re.compile(
            r'(?:##\s*)?更新内容【\\?\*?必填】\s*\n?(.*?)(?=##|###|所属模块)',
            re.DOTALL | re.IGNORECASE
        )
        
        self.method_regex = re.compile(
            r'(?:##\s*)?测试方法【\\?\*必填】\s*\n(.*?)(?=##|###|自测结果)',
            re.DOTALL | re.IGNORECASE
        )
    
    async def initialize(self) -> None:
        """初始化处理器"""
        self._log_info("内容处理器初始化成功")
    
    async def cleanup(self) -> None:
        """清理资源"""
        self._log_info("内容处理器资源已清理")
    
    def extract_section(self, text: str, pattern: re.Pattern) -> Optional[str]:
        """从描述文本中提取特定部分的内容"""
        if not text:
            return None
        
        match = pattern.search(text)
        if match:
            content = match.group(1).strip()
            # 清理多余空白字符
            return re.sub(r'\s+', ' ', content)
        return None
    
    def extract_update_content(self, description: str) -> Optional[str]:
        """提取更新内容"""
        return self.extract_section(description, self.content_regex)
    
    def extract_test_method(self, description: str) -> Optional[str]:
        """提取测试方法"""
        return self.extract_section(description, self.method_regex)
    
    def process_mr_content(self, description: str) -> Tuple[Optional[str], Optional[str]]:
        """处理合并请求内容，提取更新内容和测试方法"""
        if not description:
            return None, None

        # 首先尝试使用插件系统
        if hasattr(self, 'plugin_manager'):
            from ..plugins.processors import ContentExtractorPlugin
            extractors = self.plugin_manager.get_plugins_by_type(ContentExtractorPlugin)
            for extractor in extractors:
                try:
                    result = extractor.extract_content(description)
                    if result and (result[0] or result[1]):
                        update_content, test_method = result
                        if update_content:
                            self._log_debug(f"插件 {extractor.name} 提取到更新内容: {update_content[:50]}...")
                        if test_method:
                            self._log_debug(f"插件 {extractor.name} 提取到测试方法: {test_method[:50]}...")
                        return result
                except Exception as e:
                    self._log_error(f"插件 {extractor.name} 处理失败", e)

        # 回退到内置处理器
        update_content = self.extract_update_content(description)
        test_method = self.extract_test_method(description)

        if update_content:
            self._log_debug(f"提取到更新内容: {update_content[:50]}...")
        if test_method:
            self._log_debug(f"提取到测试方法: {test_method[:50]}...")

        return update_content, test_method
    
    def should_update_field(self, record: FeishuRecord, field_name: str) -> bool:
        """判断字段是否需要更新"""
        return record.is_field_empty(field_name)
    
    def create_update_fields(
        self, 
        record: FeishuRecord, 
        commit_id: Optional[str] = None,
        update_content: Optional[str] = None,
        test_method: Optional[str] = None
    ) -> UpdateFields:
        """创建更新字段对象"""
        fields = UpdateFields()
        
        # 检查并设置需要更新的字段
        if commit_id and self.should_update_field(record, "Commit ID"):
            fields.commit_id = commit_id
            self._log_debug(f"准备更新Commit ID: {record.id} -> {commit_id}")
        
        if update_content and self.should_update_field(record, "更新内容"):
            fields.update_content = update_content
            self._log_debug(f"准备更新更新内容: {record.id}")
        
        if test_method and self.should_update_field(record, "验证要求"):
            fields.test_method = test_method
            self._log_debug(f"准备更新验证要求: {record.id}")
        
        return fields
