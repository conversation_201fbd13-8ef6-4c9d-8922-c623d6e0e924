"""基础字段处理器"""

from typing import Any, Dict, Set
from .base import FieldProcessor


class BasicFieldProcessor(FieldProcessor):
    """基础字段处理器"""
    
    BASIC_FIELDS: Set[str] = {'project', 'summary', 'issuetype', 'description', 'assignee'}
    
    def __init__(self):
        super().__init__(priority=100)  # 高优先级
    
    def can_process(self, field_name: str, field_value: Any = None) -> bool:
        return field_name in self.BASIC_FIELDS
    
    def process(self, field_name: str, field_value: Any, context: Dict[str, Any]) -> Any:
        """处理基础字段"""
        if field_name == 'assignee':
            # 保持原始assignee
            return field_value
        elif field_name == 'issuetype':
            # 保持原始问题类型
            return field_value
        elif field_name == 'summary':
            # 保持原始标题，前缀在服务层添加
            return field_value
        elif field_name == 'description' and not field_value:
            return '克隆的问题，原问题无描述'
        elif field_name == 'project':
            # 保持原项目
            return field_value
        
        return field_value
    
    def validate_field(self, field_name: str, field_value: Any) -> bool:
        """验证基础字段"""
        if field_name == 'project' and not field_value:
            return False
        if field_name == 'issuetype' and not field_value:
            return False
        if field_name == 'summary' and not field_value:
            return False
        return True