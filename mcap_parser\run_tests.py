#!/usr/bin/env python3
"""
测试运行脚本
提供不同的测试运行选项以优化测试速度
"""

import subprocess
import sys
import argparse

def run_fast_tests():
    """运行快速测试（跳过slow、performance、integration标记的测试）"""
    print("运行快速测试...")
    cmd = ["pytest", "tests/", "-v", "-m", "not slow and not performance and not integration", "--tb=short"]
    return subprocess.run(cmd)

def run_all_tests():
    """运行所有测试"""
    print("运行所有测试...")
    cmd = ["pytest", "tests/", "-v", "--tb=short"]
    return subprocess.run(cmd)

def run_slow_tests():
    """只运行慢速测试"""
    print("运行慢速测试...")
    cmd = ["pytest", "tests/", "-v", "-m", "slow", "--tb=short"]
    return subprocess.run(cmd)

def run_unit_tests():
    """只运行单元测试"""
    print("运行单元测试...")
    cmd = ["pytest", "tests/", "-v", "-m", "unit", "--tb=short"]
    return subprocess.run(cmd)

def run_performance_tests():
    """只运行性能测试"""
    print("运行性能测试...")
    cmd = ["pytest", "tests/test_performance.py", "-v", "--tb=short"]
    return subprocess.run(cmd)

def main():
    parser = argparse.ArgumentParser(description="测试运行脚本")
    parser.add_argument("--fast", action="store_true", help="运行快速测试（默认）")
    parser.add_argument("--all", action="store_true", help="运行所有测试")
    parser.add_argument("--slow", action="store_true", help="只运行慢速测试")
    parser.add_argument("--unit", action="store_true", help="只运行单元测试")
    parser.add_argument("--performance", action="store_true", help="只运行性能测试")
    
    args = parser.parse_args()
    
    if args.all:
        result = run_all_tests()
    elif args.slow:
        result = run_slow_tests()
    elif args.unit:
        result = run_unit_tests()
    elif args.performance:
        result = run_performance_tests()
    else:
        # 默认运行快速测试
        result = run_fast_tests()
    
    sys.exit(result.returncode)

if __name__ == "__main__":
    main()