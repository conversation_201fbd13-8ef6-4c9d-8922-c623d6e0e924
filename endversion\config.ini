[DeepSeek]
api_key = ***********************************
api_url = https://api.deepseek.com/v1/chat/completions

[Jira]
url = https://jira.mach-drive-inc.com
user = t-majingmiao
password = mach.1234
jql = labels = MR_3808  and issueFunction  in hasComments()
[Feishu]
app_id = cli_a8d028a01ed8900b
app_secret = fA8FpGvqdoZvxBe9tTqiDbp6KkRGWmEF
folder_token = Kf6xfRFZfld96AdEOYrcgJscnEb
[Analysis]
system_prompt = 
    请基于以下要求进行深度分析：
    1. 问题分类统计：统计各分类下的问题数量，计算占比（数量 / 总问题数） ，用数据呈现问题分布规律，对比不同分类的规模差异。并贴上对应的问题链接，请贴全，不要贴示例。
    2. top问题总结：识别最常见（数量最多） 、最关键（影响最大 / 关联核心流程） 的问题
    需基于具体问题集合展开，确保分类合理、统计准确、总结聚焦，输出清晰可落地的分析结论 ,不需要提供建议，只需要给出详细的问题分析和分类就行，有回归通过的问题请也表现在报告中。
    这是我们常用的测试分类，请依据这个分类进行问题分析，表格不需要列出所有分类，只需要将存在的问题分类列出即可，并且不要重复统计,接管问题统计到接管中，闪烁问题归类到漏检。
    位置错误、曲率错误、功能问题接管、多线重合、实例断连、实例多连、实例弯折、实例乱线、漏检道路线、漏检路沿、漏检停止线、漏检driveline(driveline指可驶入的路口)、漏检地面箭头、漏检禁停区、
    漏检斑马线、检出距离不足、漏检锥桶线、类型整体错误、类型部分错误、类型跳变错误、可视化问题、系统性问题