[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mcap-parser"
version = "3.1.0"
description = "现代化的MCAP自动驾驶数据解析工具包"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "MCAP SDK Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "MCAP SDK Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: System :: Hardware :: Hardware Drivers"
]
keywords = ["mcap", "ros2", "autonomous-driving", "data-parser", "robotics"]
requires-python = ">=3.8"
dependencies = [
    "mcap-ros2-support>=0.5.0",
    "psutil>=5.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
]
analysis = [
    "numpy>=1.19.0",
    "pandas>=1.3.0",
    "matplotlib>=3.5.0",
]
visualization = [
    "plotly>=5.0.0",
    "dash>=2.0.0",
]
all = [
    "mcap-parser[dev,analysis,visualization]"
]

[project.urls]
Homepage = "https://github.com/mcap-sdk/mcap-parser"
Documentation = "https://mcap-parser.readthedocs.io"
Repository = "https://github.com/mcap-sdk/mcap-parser.git"
"Bug Tracker" = "https://github.com/mcap-sdk/mcap-parser/issues"
Changelog = "https://github.com/mcap-sdk/mcap-parser/blob/main/CHANGELOG.md"

[project.scripts]
mcap-parser = "src.mcap_parser.cli.main:main"
mcap-analyze = "src.mcap_parser.cli.main:analyze_command"
mcap-tools = "src.mcap_parser.tools.demo:main"

[tool.setuptools]
package-dir = {"" = "src"}
include-package-data = true

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
mcap_parser = ["py.typed"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
)
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config -c config/pytest.ini"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "performance: marks tests as performance tests",
    "benchmark: marks tests as benchmark tests"
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "mcap_ros2.*",
    "psutil.*",
]
ignore_missing_imports = true