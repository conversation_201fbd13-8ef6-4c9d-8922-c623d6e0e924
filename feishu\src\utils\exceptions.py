"""自定义异常类"""


class FeishuMonitorError(Exception):
    """飞书监控系统基础异常"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class ConfigurationError(FeishuMonitorError):
    """配置错误"""
    
    def __init__(self, message: str, config_key: str = None):
        super().__init__(message, "CONFIG_ERROR", {"config_key": config_key})
        self.config_key = config_key


class ServiceError(FeishuMonitorError):
    """服务错误"""
    
    def __init__(self, message: str, service_name: str = None, status_code: int = None):
        super().__init__(
            message, 
            "SERVICE_ERROR", 
            {"service_name": service_name, "status_code": status_code}
        )
        self.service_name = service_name
        self.status_code = status_code


class FeishuAPIError(ServiceError):
    """飞书API错误"""
    
    def __init__(self, message: str, api_code: str = None, api_msg: str = None):
        super().__init__(message, "FeishuService")
        self.api_code = api_code
        self.api_msg = api_msg
        self.details.update({"api_code": api_code, "api_msg": api_msg})


class GitLabAPIError(ServiceError):
    """GitLab API错误"""
    
    def __init__(self, message: str, status_code: int = None, project_path: str = None, mr_id: int = None):
        super().__init__(message, "GitLabService", status_code)
        self.project_path = project_path
        self.mr_id = mr_id
        self.details.update({"project_path": project_path, "mr_id": mr_id})


class ProcessingError(FeishuMonitorError):
    """处理错误"""
    
    def __init__(self, message: str, record_id: str = None, stage: str = None):
        super().__init__(message, "PROCESSING_ERROR", {"record_id": record_id, "stage": stage})
        self.record_id = record_id
        self.stage = stage
