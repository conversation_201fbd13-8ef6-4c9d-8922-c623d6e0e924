@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ============================================================
echo 飞书报告触发脚本
echo ============================================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

REM 检查脚本文件
if not exist "trigger_report_script.py" (
    echo [错误] 未找到 trigger_report_script.py 文件
    pause
    exit /b 1
)

REM 显示使用说明
echo 使用方法:
echo   1. 直接运行 - 使用默认参数
echo   2. 命令行参数 - 指定具体参数
echo   3. 配置文件 - 使用JSON配置文件
echo   4. 创建配置 - 生成示例配置文件
echo.

set /p choice="请选择操作方式 (1-4): "

if "%choice%"=="1" (
    echo.
    echo [提示] 使用默认参数触发报告...
    set /p report_id="请输入报告ID: "
    set /p vin="请输入车辆识别码 (可选): "
    set /p start_time="请输入开始时间 (可选, 格式: 2024-01-01 00:00:00): "
    set /p end_time="请输入结束时间 (可选, 格式: 2024-01-01 23:59:59): "
    
    set cmd=python trigger_report_script.py --report-id !report_id!
    if not "!vin!"=="" set cmd=!cmd! --vin "!vin!"
    if not "!start_time!"=="" set cmd=!cmd! --start-time "!start_time!"
    if not "!end_time!"=="" set cmd=!cmd! --end-time "!end_time!"
    
    echo.
    echo [执行] !cmd!
    echo.
    !cmd!
    
) else if "%choice%"=="2" (
    echo.
    echo [提示] 请手动运行命令，示例:
    echo python trigger_report_script.py --report-id 1 --vin TEST123 --start-time "2024-01-01 00:00:00" --end-time "2024-01-01 23:59:59"
    echo.
    echo 更多参数说明:
    python trigger_report_script.py --help
    
) else if "%choice%"=="3" (
    echo.
    set /p config_file="请输入配置文件路径 (默认: report_config.json): "
    if "!config_file!"=="" set config_file=report_config.json
    
    if not exist "!config_file!" (
        echo [错误] 配置文件不存在: !config_file!
        echo [提示] 请先创建配置文件或选择选项4生成示例配置
    ) else (
        set /p batch_mode="是否批量模式? (y/n, 默认: n): "
        if /i "!batch_mode!"=="y" (
            echo [执行] python trigger_report_script.py --config "!config_file!" --batch
            python trigger_report_script.py --config "!config_file!" --batch
        ) else (
            echo [执行] python trigger_report_script.py --config "!config_file!"
            python trigger_report_script.py --config "!config_file!"
        )
    )
    
) else if "%choice%"=="4" (
    echo.
    echo [执行] 创建示例配置文件...
    python trigger_report_script.py --create-config
    echo.
    echo [完成] 示例配置文件已创建，请根据需要修改配置
    
) else (
    echo [错误] 无效的选择
)

echo.
echo ============================================================
echo 操作完成
echo ============================================================
pause