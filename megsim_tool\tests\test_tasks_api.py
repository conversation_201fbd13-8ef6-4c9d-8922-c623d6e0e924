import requests
import json
from datetime import datetime

def test_create_task():
    """
    测试创建任务API
    """
    url = "https://megsim.mc.machdrive.cn/api/tasks/"
    
    payload = {
        "priority": 4,
        "run_metric": True,
        "run_test": True,
        "bp_delay_enable": False,
        "end_early_enable": False,
        "name": "test",
        "project": 4,
        "car_type": "Z10",
        "reinjection_scene_type": None,
        "reinjection_task_type": 150291,
        "tag_ids": "[7736]",
        "sync_jira_status": True,
        "type": 20,
        "case_status": "",
        "case_time": "",
        "scene_set_enable": False,
        "ego_cruise_speed": None,
        "evaluation_checkers": None
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print("=== 测试创建任务API ===")
    print(f"URL: {url}")
    print(f"方法: POST")
    print("请求参数:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print("响应内容:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            return True, response_data
        except json.JSONDecodeError:
            print("响应内容 (文本):")
            print(response.text)
            return response.status_code == 200 or response.status_code == 201, response.text
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return False, str(e)

def test_get_tasks():
    """
    测试获取任务列表API
    """
    url = "https://megsim.mc.machdrive.cn/api/tasks/"
    
    params = {
        "order": "-create_at",
        "limit": 10,
        "skip": 0,
        "search": "{}"
    }
    
    print("\n=== 测试获取任务列表API ===")
    print(f"URL: {url}")
    print(f"方法: GET")
    print("查询参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    print()
    
    try:
        response = requests.get(url, params=params, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print("响应内容:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            return True, response_data
        except json.JSONDecodeError:
            print("响应内容 (文本):")
            print(response.text)
            return response.status_code == 200, response.text
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return False, str(e)

def main():
    print("=== MegSim 任务API测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试创建任务
    create_success, create_result = test_create_task()
    
    # 测试获取任务列表
    get_success, get_result = test_get_tasks()
    
    print("\n=== 测试结果汇总 ===")
    print(f"创建任务API: {'✅ 成功' if create_success else '❌ 失败'}")
    print(f"获取任务API: {'✅ 成功' if get_success else '❌ 失败'}")
    
    return create_success, get_success, create_result, get_result

if __name__ == "__main__":
    main()