import os
import sys
import configparser
from typing import Optional, Dict, Any
from src.utils.logger import get_logger
from src.utils.exceptions import ConfigError

logger = get_logger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """配置管理器初始化"""
        self.config_path = self._resolve_config_path(config_path)
        self.parser = configparser.ConfigParser()
        self._load_config()
    
    def _resolve_config_path(self, config_path: Optional[str]) -> str:
        """解析配置文件路径"""
        if config_path:
            return config_path
        
        # 判断是否是打包后的可执行文件
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        return os.path.join(base_dir, 'config.ini')
    
    def _load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            self.create_default_config()
            raise ConfigError(f"配置文件 {self.config_path} 不存在，已创建默认配置")
        
        self.parser.read(self.config_path, encoding='utf-8')
        logger.info(f"配置文件加载成功: {self.config_path}")
    
    def create_default_config(self):
        """创建默认配置文件"""
        config = configparser.ConfigParser()
        

        
        # Jira 配置
        config['Jira'] = {
            'url': 'https://your-jira-instance.com',
            'user': 'your_jira_username',
            'password': 'your_jira_password',
            'jql': 'project = YOURPROJECT AND status = "To Do"',
            'timeout': '20',
            'max_results': '100'
        }
        
        # Feishu 配置
        config['Feishu'] = {
            'app_id': 'your_feishu_app_id',
            'app_secret': 'your_feishu_app_secret',
            'folder_token': 'your_feishu_folder_token',
        }
        
        # Analysis 配置
        config['Analysis'] = {
            'system_prompt': '测试报告生成配置',
            'enable_ai_analysis': 'false',
            'report_template': 'default'
        }
        
        # Report 配置
        config['Report'] = {
            'output_dir': './reports',
            'file_format': 'md',
            'include_timestamp': 'true',
            'auto_cleanup': 'false'
        }
        
        with open(self.config_path, 'w', encoding='utf-8') as configfile:
            config.write(configfile)
        logger.info(f"配置文件 {self.config_path} 已创建，请修改其中的配置项")
    
    def get(self, section: str, option: str, default: str = '') -> str:
        """获取配置值"""
        try:
            return self.parser.get(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default
    
    def get_int(self, section: str, option: str, default: int = 0) -> int:
        """获取整数配置值"""
        try:
            return self.parser.getint(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default
    
    def get_float(self, section: str, option: str, default: float = 0.0) -> float:
        """获取浮点数配置值"""
        try:
            return self.parser.getfloat(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default
    
    def get_boolean(self, section: str, option: str, default: bool = False) -> bool:
        """获取布尔配置值"""
        try:
            return self.parser.getboolean(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default
    
    def get_section(self, section: str) -> Dict[str, str]:
        """获取整个配置段"""
        try:
            return dict(self.parser.items(section))
        except configparser.NoSectionError:
            return {}
    
    def set(self, section: str, option: str, value: str):
        """设置配置值"""
        if not self.parser.has_section(section):
            self.parser.add_section(section)
        self.parser.set(section, option, value)
    
    def save(self):
        """保存配置到文件"""
        with open(self.config_path, 'w', encoding='utf-8') as configfile:
            self.parser.write(configfile)
        logger.info("配置文件已保存")
    
    def validate_config(self) -> bool:
        """验证配置完整性"""
        required_sections = ['Jira', 'Feishu', 'Analysis']
        
        for section in required_sections:
            if not self.parser.has_section(section):
                logger.error(f"缺少必需的配置段: {section}")
                return False
        
        return True 