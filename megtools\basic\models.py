from django.db import models

# Create your models here.
class BasicKeyValue(models.Model):
    id = models.BigAutoField(name='id', primary_key=True, help_text='编号')
    name = models.CharField(name='name', max_length=128, verbose_name="名称", default='', null=False)
    value = models.TextField(name='value', verbose_name="数值", null=False)
    remark = models.CharField(name='remark', max_length=256, verbose_name="描述", default='', null=False)
    create_by = models.Char<PERSON>ield(name='create_by', max_length=64, verbose_name="创建者", null=False)
    create_time = models.DateTimeField(name='create_time', verbose_name="创建时间", auto_now_add=True)
    update_by = models.CharField(name='update_by', max_length=64, verbose_name="更新者", null=False)
    update_time = models.DateTimeField(name='update_time', verbose_name="更新时间", auto_now=True)
    is_delete = models.Char<PERSON>ield(name='is_delete', verbose_name="是否已经删除 0-未删除 1-已删除", default='0', max_length=2, null=False)

    class Meta:
        ordering = ['-update_time']
        db_table = "basic_key_value"
        verbose_name = "保存Key Value"

class UserInfo(models.Model):
    id = models.BigAutoField(name='id', primary_key=True, help_text='编号')
    username = models.CharField(name='username', max_length=32, verbose_name="登录用户名", default='', null=False)
    display_name = models.CharField(name='display_name', max_length=64, verbose_name="中文姓名", null=False)
    email = models.CharField(name='email', max_length=64, verbose_name="邮箱", default='', null=False)
    phone = models.CharField(name='phone', max_length=32, verbose_name="电话号码", default='', null=False)
    open_id = models.CharField(name='open_id', max_length=64, verbose_name="飞书OpenId", default='', null=False)
    dept_id = models.BigIntegerField(name="dept_id", verbose_name="部门ID", default=-1, null=False,)
    dept_name = models.CharField(name="dept_name", verbose_name="部门名称", default="", max_length=128, null=False)
    create_by = models.CharField(name='create_by', max_length=64, verbose_name="创建者", null=False)
    create_time = models.DateTimeField(name='create_time', verbose_name="创建时间", auto_now_add=True)
    update_by = models.CharField(name='update_by', max_length=64, verbose_name="更新者", null=False)
    update_time = models.DateTimeField(name='update_time', verbose_name="更新时间", auto_now=True)
    is_delete = models.CharField(name='is_delete', verbose_name="是否已经删除 0-未删除 1-已删除", default='0',
                                 max_length=2, null=False)

    class Meta:
        ordering = ['-create_time']
        db_table = "basic_user_info"
        verbose_name = "保存用户的基本信息, 也可以保存一些chat_id 等信息"


class BasicAlarmGroup(models.Model):
    id = models.BigAutoField(name='id', primary_key=True, help_text='编号')
    group_name = models.CharField(name='group_name', max_length=64, verbose_name="分组名称", default='', null=False)
    user_list = models.CharField(name='user_list', max_length=512, verbose_name="保存分组下用户的id", default='', null=False)
    remark = models.CharField(name='remark', max_length=256, verbose_name="描述", default='', null=False)
    create_by = models.CharField(name='create_by', max_length=64, verbose_name="创建者", null=False)
    create_time = models.DateTimeField(name='create_time', verbose_name="创建时间", auto_now_add=True)
    update_by = models.CharField(name='update_by', max_length=64, verbose_name="更新者", null=False)
    update_time = models.DateTimeField(name='update_time', verbose_name="更新时间", auto_now=True)
    is_delete = models.CharField(name='is_delete', verbose_name="是否已经删除 0-未删除 1-已删除", default='0',
                                 max_length=2, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "basic_alarm_group"
        verbose_name = "保存用户的基本信息"

class BasicFeishuMessageInfo(models.Model):
    id = models.BigAutoField(name='id', primary_key=True, help_text='编号')
    open_id = models.CharField(name='open_id', max_length=128, verbose_name="发送给哪位", default='', null=False)
    display_name = models.CharField(name='display_name', max_length=128, verbose_name="发送给哪位", default='', null=False)
    message = models.TextField(name='message', verbose_name="发送的消息内容", null=False)
    remark = models.TextField(name='remark', verbose_name="消息发送失败的原因", null=False)
    create_by = models.CharField(name='create_by', max_length=64, verbose_name="创建者", null=False)
    create_time = models.DateTimeField(name='create_time', verbose_name="创建时间", auto_now_add=True)
    update_by = models.CharField(name='update_by', max_length=64, verbose_name="更新者", null=False)
    update_time = models.DateTimeField(name='update_time', verbose_name="更新时间", auto_now=True)
    is_delete = models.CharField(name='is_delete', verbose_name="是否已经删除 0-未删除 1-已删除", default='0',
                                 max_length=2, null=False)

    class Meta:
        ordering = ['-id']
        db_table = "basic_feishu_message_info"
        verbose_name = "保存发送消息的数据"