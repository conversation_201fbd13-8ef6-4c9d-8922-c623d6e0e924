import datetime
import json

from django.db.models import Q
from django.http import HttpResponseRedirect
from django.shortcuts import HttpResponse
from django.views.decorators.http import require_POST, require_GET

from basic.models import BasicKeyValue, BasicAlarmGroup, UserInfo
from basic.services import cas_check, get_global_kv, set_global_kv, TransAdsFile, FileDownloadThread, SendMessageApi
from basic.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, JsonEncoder, PageUtils, JsonUtils, generate_md5, <PERSON>raOperate
from django.core.cache import cache


# Create your views here.
def login(request):
    req = json.loads(request.body.decode("UTF-8"))
    result = cas_check(req)
    return HttpResponse(json.dumps(result), content_type='application/json')


def userInfo(request):
    token = request.headers['Authorization'][7:]
    token_key = f'valid_token_origin:{generate_md5(token)}'
    redis_value = cache.get(token_key)
    return HttpResponse(json.dumps(redis_value), content_type='application/json')


def auth_codes(request):
    return HttpResponse(json.dumps({
        "code": 0,
        "data": {
            "codes": ['AC_100100', 'AC_100110', 'AC_100120', 'AC_100010'],
            "username": 'vben',
        },
        "error": None,
        "message": "ok"
    }), content_type='application/json')


@require_POST
def kvPage(request):
    """
    分页查询 KValue 信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    condition = OrmFilter.and_condition(BasicKeyValue, req, 1)
    condition.children.append(('is_delete', 0))
    files = BasicKeyValue.objects.filter(condition)
    result = PageUtils.page(files, req)
    return HttpResponse(json.dumps(result, cls=JsonEncoder), content_type='application/json')


@require_POST
def modifyKey(request):
    """
    分页查询文件上传信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user["login_name"] if "user" in request.__dict__ else "remote"
    keyInfo = BasicKeyValue.objects.filter(name=req["name"], is_delete=0).first()
    req['update_by'] = who
    req['update_time'] = datetime.datetime.now()
    if keyInfo:
        BasicKeyValue.objects.filter(id=req['id']).update(**req)
    else:
        req['create_by'] = who
        req['create_time'] = datetime.datetime.now()
        keyInfo = BasicKeyValue(**req)
        keyInfo.save()
    set_global_kv(req["name"], req['value'])
    return HttpResponse(json.dumps(JsonUtils.convert2Json(keyInfo)), content_type='application/json')


@require_POST
def delByKey(request):
    """
    删除上传文件信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user['login_name'] if "user" in request.__dict__ else "remote"
    BasicKeyValue.objects.filter(name=req["key"]).update(is_delete=1, update_by = who)
    return HttpResponse(json.dumps("{}"), content_type='application/json')


@require_POST
def queryKey(request):
    """
    根据Key 进行查询
    """
    req = json.loads(request.body.decode("UTF-8"))
    value = get_global_kv(req.get("key", ""))
    if req.get("type", "json") == "json":
        if not value:
            value = "{}"
        return HttpResponse(value, content_type='application/json')
    else:
        return HttpResponse(value, content_type='text/plain')


# 健康检查
def ping(request):
    return HttpResponse(json.dumps({"status": "success"}), content_type='application/json')


def health(request):
    return HttpResponse(json.dumps({"health": "up"}), content_type='application/json')



@require_POST
def userinfoPage(request):
    """
    分页查询
    """
    req = json.loads(request.body.decode("UTF-8"))
    condition = OrmFilter.and_condition(UserInfo, req, 1)
    condition.children.append(('is_delete', 0))
    files = UserInfo.objects.filter(condition)
    result = PageUtils.page(files, req)
    return HttpResponse(json.dumps(result, cls=JsonEncoder), content_type='application/json')


@require_POST
def modifyUser(request):
    """
    修改信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user["login_name"] if "user" in request.__dict__ else "remote"
    userInfo = UserInfo.objects.filter(id=req.get("id", -1), is_delete=0).first()
    req['update_by'] = who
    req['update_time'] = datetime.datetime.now()
    if userInfo:
        UserInfo.objects.filter(id=req['id']).update(**req)
    else:
        req['create_by'] = who
        req['create_time'] = datetime.datetime.now()
        userInfo = UserInfo(**req)
        userInfo.save()
    return HttpResponse(json.dumps(JsonUtils.convert2Json(userInfo)), content_type='application/json')


@require_POST
def delUserinfo(request):
    """
    删除
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user['login_name'] if "user" in request.__dict__ else "remote"
    UserInfo.objects.filter(id=req["id"]).update(is_delete=1, update_by = who)
    return HttpResponse(json.dumps("{}"), content_type='application/json')


@require_POST
def alarmGroupPage(request):
    """
    分页查询
    """
    req = json.loads(request.body.decode("UTF-8"))
    condition = OrmFilter.and_condition(BasicAlarmGroup, req, 1)
    condition.children.append(('is_delete', 0))
    files = BasicAlarmGroup.objects.filter(condition)
    result = PageUtils.page(files, req)
    return HttpResponse(json.dumps(result, cls=JsonEncoder), content_type='application/json')


@require_POST
def modifyAlarmGroup(request):
    """
    修改信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user["login_name"] if "user" in request.__dict__ else "remote"
    alarmGroup = BasicAlarmGroup.objects.filter(id=req.get("id", -1), is_delete=0).first()
    req['update_by'] = who
    req['update_time'] = datetime.datetime.now()
    if "user_list" in req:
        # 如果用户分组在请求对象里面，那么，进行处理
        req["user_list"] = ",".join(req["user_list"])
    if alarmGroup:
        BasicAlarmGroup.objects.filter(id=req['id']).update(**req)
    else:
        req['create_by'] = who
        req['create_time'] = datetime.datetime.now()
        alarmGroup = BasicAlarmGroup(**req)
        alarmGroup.save()
    return HttpResponse(json.dumps(JsonUtils.convert2Json(alarmGroup)), content_type='application/json')


@require_POST
def delAlarmGroup(request):
    """
    删除
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user['login_name'] if "user" in request.__dict__ else "remote"
    BasicAlarmGroup.objects.filter(id=req["id"]).update(is_delete=1, update_by = who)
    return HttpResponse(json.dumps("{}"), content_type='application/json')

@require_POST
def getUserNameList(request):
    """
    根据Key 进行查询
    """
    req = json.loads(request.body.decode("UTF-8"))
    user_name = UserInfo.objects.filter(Q(username__contains=req.get("pattern", '')) |
                                        Q(display_name__contains=req.get("pattern", '')) |
                                        Q(email__contains=req.get("pattern", '')) |
                                        Q(phone__contains=req.get("pattern", ''))
                                        ).values('username', 'display_name', 'id')[0:10]
    return HttpResponse(json.dumps(list(user_name)), content_type='application/json')


@require_POST
def queryJira(request):
    req = json.loads(request.body.decode("UTF-8"))
    jql = req.get("jql", "")
    if jql == "":
        return HttpResponse("[]", content_type='application/json')
    fields = ["key", "summary", "issuetype", "status", "priority"]
    result = JiraOperate().page_jira(0, jql, fields,count=100)
    resp_body = []
    for item in result:
        fields = item.get("fields")
        resp_body.append({
            "jira_key": item["key"],
            "summary": fields.get("summary", ""),
            "issuetype": fields.get("issuetype", {}).get("name",""),
            "status": fields.get("status", {}).get("name",""),
            "priority": fields.get("priority", {}).get("name",""),
        })
    return HttpResponse(json.dumps(resp_body), content_type='application/json')


@require_GET
def transOss(request, dynamic_path):
    """
    对 oss 的数据进行加速
    加速思路：camera 数据有很多，
    当前页面是需要查看哪个摄像头的数据？
    如果知道是哪个，仅仅返回指定摄像头的数据和其他数据，从而减少数据量
    """
    url = dynamic_path
    url_arr = url.split('/')
    bucket = url_arr[0]
    file_name = url_arr[-1]
    path = url[len(bucket)+1:-len(file_name)]
    if not url.endswith("frame.json"):
        # 如果不是frame.json 结尾， 那么需要跳转到oss 直接下载
        return HttpResponseRedirect(f'https://oss-gateway.mc.machdrive.cn/{url}')

    # 循环取值， 等待三秒取不到，那么单独取一个文件！
    # gzipped_content = GetOssFileInfo(bucket, path, file_name, cam_name).get_value()
    if file_name == "0-frame.json" and cache.set(f"oss:ads:{generate_md5(path)}", 1, nx=True, timeout=60*60):
        FileDownloadThread(bucket, path, "1/cam_front_120").start()
    gzipped_content = TransAdsFile(bucket, f"{path}{file_name}", "1/cam_front_120").apply()
    return HttpResponse(gzipped_content, content_type='application/json')

@require_POST
def send_feishu_message(request):
    req = json.loads(request.body.decode("UTF-8"))

    if "to" not in req or "body" not in req:
        alarm_message = '{"to": "somebody", "body": {"topic_info": [], "node_info": [], "core_dump": false }} '
        return HttpResponse(f"check your parameters! eg: {alarm_message}", content_type='application/json')
    req["body"]["run_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # 校验是否可以发送：
    if "template_id" in req:
        template_id = req["template_id"]
        body = {"template_id": template_id, "template_variable": req["body"]}
    else:
        body = {"template_id": "AAqIon1cGgYUM", "template_variable": req["body"]}
    resp = SendMessageApi(body).send_message(req.get("to"))
    if not resp:
        resp = "success"
    return HttpResponse(json.dumps({"status": resp}), content_type='application/json')
