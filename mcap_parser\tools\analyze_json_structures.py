#!/usr/bin/env python3


import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Set
from collections import defaultdict, Counter

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))


class JSONStructureAnalyzer:
    """JSON数据结构分析器"""
    
    def __init__(self, json_dir: str = "./"):
        """初始化分析器"""
        self.json_dir = Path(json_dir)
        self.json_files = list(self.json_dir.glob("mcap_ros2._dynamic.*.json"))
        self.analysis_results = {}
    
    def analyze_all_files(self):
        """分析所有JSON文件"""
        
        print(f"🔍 开始分析 {len(self.json_files)} 个JSON文件")
        print("=" * 60)
        
        for json_file in self.json_files:
            message_type = self._extract_message_type(json_file.name)
            print(f"\n📋 分析 {message_type}...")
            
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                analysis = self._analyze_message_structure(data, message_type)
                self.analysis_results[message_type] = analysis
                
                print(f"   ✅ 分析完成")
                
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")
    
    def _extract_message_type(self, filename: str) -> str:
        """从文件名提取消息类型"""
        # 从 "mcap_ros2._dynamic.LaneArrayv2.json" 提取 "LaneArrayv2"
        parts = filename.replace('.json', '').split('.')
        return parts[-1] if len(parts) > 0 else filename
    
    def _analyze_message_structure(self, data: Dict, message_type: str) -> Dict:
        """分析消息结构"""
        
        analysis = {
            'message_type': message_type,
            'total_count': data.get('total_count', 0),
            'topics': data.get('topics_using_this_type', []),
            'sample_data': data.get('sample_data', {}),
            'field_analysis': {},
            'data_patterns': {},
            'complexity_metrics': {}
        }
        
        # 分析样本消息结构
        sample_message = data.get('sample_data', {}).get('message')
        if sample_message:
            analysis['field_analysis'] = self._analyze_fields(sample_message, message_type)
            analysis['data_patterns'] = self._extract_data_patterns(sample_message, message_type)
            analysis['complexity_metrics'] = self._calculate_complexity(sample_message)
        
        return analysis
    
    def _analyze_fields(self, message: Any, message_type: str) -> Dict:
        """分析消息字段"""
        
        field_info = {}
        
        if hasattr(message, '__dict__'):
            # 处理对象类型
            for field_name, field_value in message.__dict__.items():
                field_info[field_name] = self._analyze_field_value(field_value, field_name, message_type)
        elif isinstance(message, dict):
            # 处理字典类型
            for field_name, field_value in message.items():
                field_info[field_name] = self._analyze_field_value(field_value, field_name, message_type)
        else:
            # 处理其他类型
            field_info['_root_value'] = {
                'type': type(message).__name__,
                'value': str(message)[:100] + '...' if len(str(message)) > 100 else str(message)
            }
        
        return field_info
    
    def _analyze_field_value(self, value: Any, field_name: str, message_type: str) -> Dict:
        """分析字段值"""
        
        field_analysis = {
            'type': type(value).__name__,
            'description': self._infer_field_meaning(field_name, value, message_type)
        }
        
        if isinstance(value, (list, tuple)):
            field_analysis['length'] = len(value)
            if len(value) > 0:
                field_analysis['element_type'] = type(value[0]).__name__
                field_analysis['sample_elements'] = [str(v)[:50] for v in value[:3]]
        elif isinstance(value, (int, float)):
            field_analysis['value'] = value
            field_analysis['range_info'] = self._analyze_numeric_range(field_name, value)
        elif isinstance(value, str):
            field_analysis['length'] = len(value)
            field_analysis['sample'] = value[:100] + '...' if len(value) > 100 else value
        elif hasattr(value, '__dict__'):
            field_analysis['nested_fields'] = list(value.__dict__.keys())
            field_analysis['nested_count'] = len(value.__dict__)
        
        return field_analysis
    
    def _infer_field_meaning(self, field_name: str, value: Any, message_type: str) -> str:
        """推断字段含义"""
        
        field_name_lower = field_name.lower()
        
        # 基于字段名推断含义
        if 'header' in field_name_lower:
            return "ROS消息头，包含时间戳和坐标系信息"
        elif 'id' in field_name_lower:
            return "唯一标识符"
        elif 'confidence' in field_name_lower:
            return "置信度，表示数据的可信程度"
        elif 'timestamp' in field_name_lower or 'time' in field_name_lower:
            return "时间戳信息"
        elif 'position' in field_name_lower or 'pose' in field_name_lower:
            return "位置或位姿信息"
        elif 'velocity' in field_name_lower or 'speed' in field_name_lower:
            return "速度信息"
        elif 'acceleration' in field_name_lower or 'accel' in field_name_lower:
            return "加速度信息"
        elif 'orientation' in field_name_lower or 'rotation' in field_name_lower:
            return "方向或旋转信息"
        elif 'lane' in field_name_lower:
            return "车道相关信息"
        elif 'point' in field_name_lower or 'waypoint' in field_name_lower:
            return "点坐标信息"
        elif 'array' in field_name_lower or 'list' in field_name_lower:
            return "数组或列表数据"
        elif 'covariance' in field_name_lower:
            return "协方差矩阵，表示不确定性"
        elif 'distance' in field_name_lower:
            return "距离信息"
        elif 'angle' in field_name_lower:
            return "角度信息"
        elif 'width' in field_name_lower or 'length' in field_name_lower or 'height' in field_name_lower:
            return "尺寸信息"
        elif 'type' in field_name_lower or 'class' in field_name_lower:
            return "分类或类型信息"
        elif 'property' in field_name_lower:
            return "属性信息"
        elif 'boundary' in field_name_lower:
            return "边界信息"
        elif 'curve' in field_name_lower:
            return "曲线拟合参数"
        elif any(coord in field_name_lower for coord in ['x', 'y', 'z']):
            return "坐标分量"
        elif message_type == 'RadarObjectArray' and 'rcs' in field_name_lower:
            return "雷达截面积(Radar Cross Section)"
        elif message_type == 'RadarObjectArray' and 'dynamic' in field_name_lower:
            return "动态属性，表示目标运动状态"
        elif message_type == 'LaneArrayv2' and 'fork' in field_name_lower:
            return "车道分叉相关信息"
        elif message_type in ['RawImu', 'CorrectedImu'] and 'angular' in field_name_lower:
            return "角速度信息"
        elif message_type in ['RawImu', 'CorrectedImu'] and 'linear' in field_name_lower:
            return "线性加速度信息"
        else:
            return f"待分析字段 ({type(value).__name__})"
    
    def _analyze_numeric_range(self, field_name: str, value: float) -> str:
        """分析数值范围"""
        
        if 'confidence' in field_name.lower():
            return "通常范围: 0.0-1.0"
        elif 'angle' in field_name.lower():
            return "通常范围: -π到π或0-2π"
        elif 'timestamp' in field_name.lower():
            return "Unix时间戳"
        elif abs(value) < 1e-6:
            return "接近零值"
        elif abs(value) > 1e6:
            return "大数值"
        else:
            return f"当前值: {value}"
    
    def _extract_data_patterns(self, message: Any, message_type: str) -> Dict:
        """提取数据模式"""
        
        patterns = {}
        
        # 分析特定消息类型的模式
        if message_type == 'LaneArrayv2':
            patterns.update(self._analyze_lane_patterns(message))
        elif message_type == 'RadarObjectArray':
            patterns.update(self._analyze_radar_patterns(message))
        elif message_type == 'PerceptionResult':
            patterns.update(self._analyze_perception_patterns(message))
        elif message_type in ['RawImu', 'CorrectedImu']:
            patterns.update(self._analyze_imu_patterns(message))
        
        return patterns
    
    def _analyze_lane_patterns(self, message: Any) -> Dict:
        """分析车道线数据模式"""
        
        patterns = {
            'data_type': '车道线检测结果',
            'key_components': [
                '车道线数组 - 包含多条车道线信息',
                '人行横道数组 - 检测到的人行横道',
                '转向箭头数组 - 道路标识',
                '入口数组 - 道路入口信息'
            ],
            'coordinate_systems': [
                '图像坐标系 - imagepoints',
                '车辆坐标系 - waypoints',
                'BEV坐标系 - bev_fork_point'
            ],
            'fitting_methods': [
                'XY平面拟合 - lane_xy',
                'XZ平面拟合 - lane_xz'
            ]
        }
        
        return patterns
    
    def _analyze_radar_patterns(self, message: Any) -> Dict:
        """分析雷达数据模式"""
        
        patterns = {
            'data_type': '雷达目标检测结果',
            'key_components': [
                '目标数组 - 检测到的雷达目标',
                '位置信息 - 相对位置和协方差',
                '运动信息 - 相对速度和加速度',
                '物理属性 - 尺寸、RCS等'
            ],
            'measurement_types': [
                '距离测量 - position',
                '速度测量 - relative_velocity',
                '角度测量 - orientation_angle',
                'RCS测量 - 雷达截面积'
            ],
            'classification': [
                '目标分类 - class_type',
                '动态属性 - dynamic_property',
                '存在概率 - prob_of_exist'
            ]
        }
        
        return patterns
    
    def _analyze_perception_patterns(self, message: Any) -> Dict:
        """分析感知数据模式"""
        
        patterns = {
            'data_type': '感知融合结果',
            'key_components': [
                'ST边界 - 时空边界信息',
                '坐标变换 - translation和orientation',
                '车道类型 - lane_type信息'
            ],
            'boundary_types': [
                '方程边界 - equation_curves',
                '离散边界 - disperse_curve',
                '扩展边界 - extend_*'
            ]
        }
        
        return patterns
    
    def _analyze_imu_patterns(self, message: Any) -> Dict:
        """分析IMU数据模式"""
        
        patterns = {
            'data_type': 'IMU传感器数据',
            'key_components': [
                '方向信息 - orientation四元数',
                '角速度 - angular_velocity',
                '线性加速度 - linear_acceleration',
                '协方差矩阵 - 各种covariance'
            ],
            'measurement_units': [
                '角速度: rad/s',
                '线性加速度: m/s²',
                '方向: 四元数表示'
            ]
        }
        
        return patterns
    
    def _calculate_complexity(self, message: Any) -> Dict:
        """计算数据复杂度"""
        
        complexity = {
            'field_count': 0,
            'nested_levels': 0,
            'array_count': 0,
            'total_elements': 0
        }
        
        def count_fields(obj, level=0):
            if level > complexity['nested_levels']:
                complexity['nested_levels'] = level
            
            if hasattr(obj, '__dict__'):
                complexity['field_count'] += len(obj.__dict__)
                for value in obj.__dict__.values():
                    if isinstance(value, (list, tuple)):
                        complexity['array_count'] += 1
                        complexity['total_elements'] += len(value)
                        for item in value:
                            count_fields(item, level + 1)
                    elif hasattr(value, '__dict__'):
                        count_fields(value, level + 1)
        
        count_fields(message)
        
        return complexity
    
    def generate_summary_report(self):
        """生成分析总结报告"""
        
        print(f"\n📊 JSON数据结构分析总结报告")
        print("=" * 60)
        
        if not self.analysis_results:
            print("❌ 没有分析结果")
            return
        
        # 统计信息
        total_types = len(self.analysis_results)
        total_messages = sum(result['total_count'] for result in self.analysis_results.values())
        
        print(f"📈 总体统计:")
        print(f"   分析的消息类型: {total_types}")
        print(f"   总消息数量: {total_messages:,}")
        
        # 按消息数量排序
        sorted_results = sorted(
            self.analysis_results.items(),
            key=lambda x: x[1]['total_count'],
            reverse=True
        )
        
        print(f"\n🏆 Top 10 消息类型 (按数量):")
        for i, (msg_type, result) in enumerate(sorted_results[:10], 1):
            print(f"   {i:2d}. {msg_type}: {result['total_count']:,} 条消息")
        
        # 复杂度分析
        print(f"\n🔬 复杂度分析:")
        complexity_data = []
        for msg_type, result in self.analysis_results.items():
            if 'complexity_metrics' in result:
                metrics = result['complexity_metrics']
                complexity_data.append((msg_type, metrics.get('field_count', 0), metrics.get('nested_levels', 0)))
        
        complexity_data.sort(key=lambda x: x[1], reverse=True)
        
        print(f"   最复杂的消息类型 (按字段数):")
        for msg_type, field_count, nested_levels in complexity_data[:5]:
            print(f"     {msg_type}: {field_count} 字段, {nested_levels} 层嵌套")
        
        # 数据模式总结
        print(f"\n📋 数据模式总结:")
        
        key_types = ['LaneArrayv2', 'RadarObjectArray', 'PerceptionResult', 'RawImu']
        for msg_type in key_types:
            if msg_type in self.analysis_results:
                result = self.analysis_results[msg_type]
                patterns = result.get('data_patterns', {})
                
                print(f"\n   {msg_type}:")
                print(f"     数据类型: {patterns.get('data_type', '未知')}")
                
                key_components = patterns.get('key_components', [])
                if key_components:
                    print(f"     关键组件:")
                    for component in key_components[:3]:
                        print(f"       - {component}")
    
    def export_analysis_results(self, output_file: str = "json_structure_analysis.json"):
        """导出分析结果"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 分析结果已导出到: {output_file}")


def main():
    """主函数"""
    
    print("🔍 JSON数据结构深度分析")
    print("=" * 60)
    
    # 初始化分析器
    analyzer = JSONStructureAnalyzer("./")
    
    # 执行分析
    analyzer.analyze_all_files()
    
    # 生成报告
    analyzer.generate_summary_report()
    
    # 导出结果
    analyzer.export_analysis_results()
    
    print(f"\n🎉 分析完成！")


if __name__ == "__main__":
    main()
