﻿# Project: Lane Detection Annotation System
# Developer: majingmiao
# Version: 2.0.0
# Release Date: July 14, 2025
# Copyright © 2025 Mach-Driver Co.Ltd. All Rights Reserved.

import json
import urllib.request
import datetime
import os
import sys
from pathlib import Path
import argparse
import shutil

# Windows兼容性处理
try:
    import readline
except ImportError:
    # Windows上readline不可用，使用替代方案
    pass

# 快捷命令配置
SHORTCUTS = {
    # 检测问题类
    'm': "漏检车道线",
    'f': "误检车道线", 
    'd': "车道线检测不稳定",
    'n': "噪声干扰",
    'b': "边界检测错误",
    
    # 线型识别类
    's': "实线识别错误",
    'x': "虚线识别错误",
    'c': "双线识别错误",
    'z': "导流线识别错误",
    
    # 颜色识别类
    'y': "黄色线识别错误",
    'w': "白色线识别错误",
    'r': "红色线识别错误",
    
    # 位置和连续性
    'p': "车道线位置偏移",
    'l': "车道线中断",
    't': "车道线类型切换错误",
    'j': "车道线连接错误",
    
    # 环境因素
    'g': "光照影响",
    'a': "阴影干扰",
    'e': "天气影响",
    
    # 系统命令
    '?': "查看所有快捷命令",
    'h': "显示帮助信息",
    'v': "查看打点记录",
    'clear': "清空记录",
    'export': "导出记录",
    'stats': "统计信息"
}

# 配置参数
CONFIG = {
    'log_file': 'event_log.txt',
    'server_url': 'http://127.0.0.1:5000/graphql',
    'max_log_entries': 1000,
    'auto_backup': True,
    'backup_interval': 100
}

# 颜色输出支持
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'
    
    @staticmethod
    def colored(text, color):
        return f"{color}{text}{Colors.END}"

def show_shortcuts():
    """显示所有可用的快捷命令及其描述"""
    print(Colors.colored("\n📋 可用快捷命令:", Colors.BOLD + Colors.CYAN))
    
    categories = {
        "检测问题类": ['m', 'f', 'd', 'n', 'b'],
        "线型识别类": ['s', 'x', 'c', 'z'],
        "颜色识别类": ['y', 'w', 'r'],
        "位置连续性": ['p', 'l', 't', 'j'],
        "环境因素": ['g', 'a', 'e'],
        "系统命令": ['?', 'h', 'v', 'clear', 'export', 'stats']
    }
    
    for category, keys in categories.items():
        print(Colors.colored(f"\n  {category}:", Colors.YELLOW))
        for key in keys:
            if key in SHORTCUTS:
                print(f"    {Colors.colored(key, Colors.GREEN)}: {SHORTCUTS[key]}")
    print()

def send_event(desc, tags=None):
    """发送事件到服务器并记录到本地文件"""
    if not desc or desc.strip() == '':
        print(Colors.colored("⚠️  请输入有效描述", Colors.YELLOW))
        return False
    
    desc = desc.strip()
    
    body = {
        "query": """
            mutation NewEvent($tags: [String!], $desc: String) {
                newEvent(tags: $tags, desc: $desc)
            }
        """,
        "operationName": "NewEvent",
        "variables": {
            "desc": desc,
            "tags": tags or []
        },
    }

    req = urllib.request.Request(
        CONFIG['server_url'],
        data=json.dumps(body).encode("utf-8"),
        headers={"Content-Type": "application/json"},
    )

    server_success = False
    try:
        resp = urllib.request.urlopen(req, timeout=5)
        if resp.getcode() == 200:
            print(Colors.colored(f"✅ 事件提交成功: {desc}", Colors.GREEN))
            server_success = True
        else:
            print(Colors.colored(f"❌ 服务器返回错误状态码: {resp.getcode()}", Colors.RED))
    except Exception as e:
        print(Colors.colored(f"⚠️  服务器连接失败: {str(e)}", Colors.YELLOW))
        print(Colors.colored("📝 事件将仅保存到本地", Colors.CYAN))

    local_success = log_event(desc, tags, server_success)
    return local_success

def log_event(desc, tags=None, server_synced=False):
    """将事件记录到本地文件"""
    desc = desc.strip()
    if not desc:
        return False
    
    # 替换中文标点为英文标点
    desc = desc.translate(str.maketrans({''': "'", ''': "'", '"': '\"', '"': '\"'}))
    
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    log_entry = f"[{timestamp}] {desc}"
    if tags:
        log_entry += f" [标签: {', '.join(tags)}]"
    if server_synced:
        log_entry += " ✓"
    log_entry += "\n"

    try:
        # 检查文件大小，如果太大则备份
        if os.path.exists(CONFIG['log_file']):
            file_size = os.path.getsize(CONFIG['log_file'])
            if file_size > 1024 * 1024:  # 1MB
                backup_logs()
        
        with open(CONFIG['log_file'], "a", encoding="utf-8") as f:
            f.write(log_entry)
        
        # 自动备份检查
        if CONFIG['auto_backup']:
            check_auto_backup()
            
        return True
    except Exception as e:
        print(Colors.colored(f"❌ 本地记录失败: {str(e)}", Colors.RED))
        return False

def backup_logs():
    """备份日志文件"""
    if not os.path.exists(CONFIG['log_file']):
        return
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"event_log_backup_{timestamp}.txt"
    
    try:
        shutil.copy2(CONFIG['log_file'], backup_name)
        print(Colors.colored(f"📦 日志已备份到: {backup_name}", Colors.CYAN))
        
        # 清空原文件
        open(CONFIG['log_file'], 'w').close()
    except Exception as e:
        print(Colors.colored(f"❌ 备份失败: {str(e)}", Colors.RED))

def check_auto_backup():
    """检查是否需要自动备份"""
    if not os.path.exists(CONFIG['log_file']):
        return
    
    try:
        with open(CONFIG['log_file'], 'r', encoding='utf-8') as f:
            line_count = sum(1 for _ in f)
        
        if line_count >= CONFIG['backup_interval']:
            backup_logs()
    except Exception:
        pass

def view_log(lines=20):
    """查看打点记录"""
    if not os.path.exists(CONFIG['log_file']):
        print(Colors.colored("\n📝 还没有任何打点记录", Colors.YELLOW))
        return

    try:
        with open(CONFIG['log_file'], "r", encoding="utf-8") as f:
            all_lines = f.readlines()

        if not all_lines:
            print(Colors.colored("\n📝 打点记录为空", Colors.YELLOW))
            return

        print(Colors.colored("\n📋 最近的打点记录:", Colors.BOLD + Colors.CYAN))
        print(Colors.colored("-" * 60, Colors.BLUE))
        
        # 显示最后N行
        recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        for line in recent_lines:
            line = line.strip()
            if "✓" in line:
                print(Colors.colored(line, Colors.GREEN))
            else:
                print(line)
        
        print(Colors.colored("-" * 60, Colors.BLUE))
        print(f"总计: {len(all_lines)} 条记录 | 文件: {os.path.abspath(CONFIG['log_file'])}")
        
        if len(all_lines) > lines:
            print(Colors.colored(f"(仅显示最近 {lines} 条，输入 'v all' 查看全部)", Colors.YELLOW))
            
    except Exception as e:
        print(Colors.colored(f"❌ 读取记录失败: {str(e)}", Colors.RED))

def clear_log():
    """清空打点记录"""
    if not os.path.exists(CONFIG['log_file']):
        print(Colors.colored("📝 没有记录需要清空", Colors.YELLOW))
        return
    
    try:
        # 先备份
        backup_logs()
        print(Colors.colored("✅ 记录已清空", Colors.GREEN))
    except Exception as e:
        print(Colors.colored(f"❌ 清空失败: {str(e)}", Colors.RED))

def export_log():
    """导出打点记录"""
    if not os.path.exists(CONFIG['log_file']):
        print(Colors.colored("📝 没有记录可导出", Colors.YELLOW))
        return
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    export_name = f"lane_detection_events_{timestamp}.txt"
    
    try:
        shutil.copy2(CONFIG['log_file'], export_name)
        print(Colors.colored(f"📤 记录已导出到: {export_name}", Colors.GREEN))
    except Exception as e:
        print(Colors.colored(f"❌ 导出失败: {str(e)}", Colors.RED))

def show_stats():
    """显示统计信息"""
    if not os.path.exists(CONFIG['log_file']):
        print(Colors.colored("📝 没有记录可统计", Colors.YELLOW))
        return
    
    try:
        with open(CONFIG['log_file'], 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            print(Colors.colored("�� 记录为空", Colors.YELLOW))
            return
        
        total_count = len(lines)
        synced_count = sum(1 for line in lines if "✓" in line)
        local_only_count = total_count - synced_count
        
        # 统计各类问题
        problem_stats = {}
        for shortcut, desc in SHORTCUTS.items():
            if shortcut not in ['?', 'h', 'v', 'clear', 'export', 'stats']:
                count = sum(1 for line in lines if desc in line)
                if count > 0:
                    problem_stats[desc] = count
        
        print(Colors.colored("\n📊 统计信息:", Colors.BOLD + Colors.CYAN))
        print(Colors.colored("-" * 40, Colors.BLUE))
        print(f"总记录数: {Colors.colored(str(total_count), Colors.BOLD)}")
        print(f"已同步: {Colors.colored(str(synced_count), Colors.GREEN)}")
        print(f"仅本地: {Colors.colored(str(local_only_count), Colors.YELLOW)}")
        
        if problem_stats:
            print(Colors.colored("\n问题类型分布:", Colors.YELLOW))
            sorted_problems = sorted(problem_stats.items(), key=lambda x: x[1], reverse=True)
            for problem, count in sorted_problems[:10]:  # 显示前10个
                print(f"  {problem}: {Colors.colored(str(count), Colors.BOLD)}")
        
        print(Colors.colored("-" * 40, Colors.BLUE))
        
    except Exception as e:
        print(Colors.colored(f"❌ 统计失败: {str(e)}", Colors.RED))

def show_help():
    """显示帮助信息"""
    help_text = f"""
{Colors.colored('🚗 车道线检测打点工具 v2.0', Colors.BOLD + Colors.CYAN)}

{Colors.colored('基本操作:', Colors.YELLOW)}
  • 直接输入问题描述进行记录
  • 使用快捷命令快速记录常见问题
  • 所有记录会自动保存到本地并尝试同步到服务器

{Colors.colored('系统命令:', Colors.YELLOW)}
  {Colors.colored('?', Colors.GREEN)}     - 查看所有快捷命令
  {Colors.colored('h', Colors.GREEN)}     - 显示此帮助信息
  {Colors.colored('v', Colors.GREEN)}     - 查看最近20条记录
  {Colors.colored('v all', Colors.GREEN)} - 查看所有记录
  {Colors.colored('clear', Colors.GREEN)} - 清空记录(会先备份)
  {Colors.colored('export', Colors.GREEN)}- 导出记录到文件
  {Colors.colored('stats', Colors.GREEN)} - 显示统计信息

{Colors.colored('特色功能:', Colors.YELLOW)}
  • 彩色输出，直观易读
  • 自动备份，数据安全
  • 离线工作，网络断开时仅保存本地
  • 智能分类，快捷命令按问题类型分组
  • 统计分析，了解问题分布

{Colors.colored('退出程序:', Colors.YELLOW)} Ctrl+C
"""
    print(help_text)

def main():
    """主程序入口"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='车道线检测打点工具')
    parser.add_argument('--server', default=CONFIG['server_url'], help='服务器地址')
    parser.add_argument('--log-file', default=CONFIG['log_file'], help='日志文件路径')
    args = parser.parse_args()
    
    # 更新配置
    CONFIG['server_url'] = args.server
    CONFIG['log_file'] = args.log_file
    
    # 显示欢迎信息
    print(Colors.colored("\n🚗 车道线检测打点工具 v2.0", Colors.BOLD + Colors.CYAN))
    print(Colors.colored("=" * 40, Colors.BLUE))
    print("💡 输入 'h' 查看帮助，'?' 查看快捷命令")
    print("�� 直接输入问题描述或使用快捷命令")
    print("💡 按 Ctrl+C 退出程序\n")

    while True:
        try:
            user_input = input(Colors.colored("📝 输入描述或命令: ", Colors.BOLD)).strip()
        except (EOFError, KeyboardInterrupt):
            print(Colors.colored("\n👋 程序已退出，感谢使用！", Colors.CYAN))
            break

        if not user_input:
            continue

        # 转换为小写进行命令匹配
        cmd = user_input.lower()

        # 系统命令处理
        if cmd == '?':
            show_shortcuts()
        elif cmd == 'h':
            show_help()
        elif cmd == 'v':
            view_log()
        elif cmd == 'v all':
            view_log(lines=999999)
        elif cmd == 'clear':
            confirm = input(Colors.colored("⚠️  确认清空所有记录？(y/N): ", Colors.YELLOW))
            if confirm.lower() == 'y':
                clear_log()
        elif cmd == 'export':
            export_log()
        elif cmd == 'stats':
            show_stats()
        elif cmd in SHORTCUTS:
            # 快捷命令处理
            desc = SHORTCUTS[cmd]
            send_event(desc)
        else:
            # 自定义描述处理
            send_event(user_input)

if __name__ == "__main__":
    main()
