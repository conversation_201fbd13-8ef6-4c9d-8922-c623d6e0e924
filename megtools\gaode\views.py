import datetime
import json

from django.db.models import Q

from django.shortcuts import HttpResponse
from django.views.decorators.http import require_POST

from basic.utils import JsonUtil<PERSON>, <PERSON><PERSON><PERSON>perate

from cron_task.models import E2eMcapInfo, CronJira2Mysql


# Create your views here.

@require_POST
def queryCarSite(request):
    """
    查询进程分析的数据
    """
    req = json.loads(request.body.decode("UTF-8"))
    result = E2eMcapInfo.objects.filter(Q(vin=req.get('vin')) &
                                        Q(record_time__gte=req.get('record_time_start')) &
                                        Q(record_time__lte=req.get('record_time_end'))).order_by("record_time").all()
    return HttpResponse(json.dumps(JsonUtils.convertList2Json(result)), content_type='application/json')


@require_POST
def queryJiraIssues(request):
    """
    查询进程分析的数据
    """
    req = json.loads(request.body.decode("UTF-8"))
    result = CronJira2Mysql.objects.filter(Q(customfield_11701=f"car_{req.get('vin')}") &
                                           Q(issuetype="故障") &
                                        Q(point_time__gte=req.get('record_time_start')) &
                                        Q(point_time__lte=req.get('record_time_end'))).order_by("point_time").all()
    return HttpResponse(json.dumps(JsonUtils.convertList2Json(result)), content_type='application/json')


@require_POST
def queryJiraSite(request):
    """
    查询进程分析的数据
    """
    req = json.loads(request.body.decode("UTF-8"))

    q_obj = Q()
    q_obj.connector = 'or'
    jira_list = req.get("jira_list", [])
    if jira_list :
        if isinstance(jira_list, list):
            q_obj.children.append(Q(key__in=jira_list))
        if isinstance(jira_list, str):
            q_obj.children.append(Q(key=jira_list))

    # 如果输入jirakey 了， 那么就不用时间进行限制， 如果没有jira_key ，则用时间进行限制。
    if req.get('record_time_start') and req.get('record_time_end'):
        q_date_obj = Q()
        q_date_obj.connector = "and"
        q_date_obj.children.append(Q(point_time__gte=req.get('record_time_start')))
        q_date_obj.children.append(Q(point_time__lte=req.get('record_time_end')))
        q_obj.children.append(q_date_obj)

    query_obj = Q()
    query_obj.connector = 'and'
    query_obj.children.append(Q(issuetype='故障'))
    query_obj.children.append(q_obj)
    result = CronJira2Mysql.objects.filter(query_obj).order_by("point_time").all()[0:500]
    return HttpResponse(json.dumps(JsonUtils.convertList2Json(result)), content_type='application/json')


@require_POST
def queryJiraLine(request):
    """
    查询进程分析的数据
    """
    req = json.loads(request.body.decode("UTF-8"))
    point_time = req.get("point_time", "")
    vin = req.get("vin", "")
    if not point_time or not vin:
        return HttpResponse("{}", content_type='application/json')
    point_time = datetime.datetime.fromisoformat(point_time)
    record_time_start = point_time - datetime.timedelta(seconds=35)
    record_time_end = point_time + datetime.timedelta(seconds=30)
    result = E2eMcapInfo.objects.filter(Q(record_time__gte=record_time_start) &
                               Q(record_time__lte=record_time_end) &
                               Q(vin=vin[4:])
                                        ).order_by("record_time").all()
    return HttpResponse(json.dumps(JsonUtils.convertList2Json(result)), content_type='application/json')


@require_POST
def queryJiraKeysByJql(request):
    """
    查询进程分析的数据
    """
    req = json.loads(request.body.decode("UTF-8"))
    jql = req.get("jql", "")
    result = JiraOperate().query_jira(jql, ["key", "created"])
    result = [item.get("key") for item in result]
    return HttpResponse(json.dumps(result), content_type='application/json')

@require_POST
def queryJiraByKey(request):
    """
    查询进程分析的数据
    """
    req = json.loads(request.body.decode("UTF-8"))
    jql = req.get("jql", "")
    fields = req.get("fields", "")
    result = JiraOperate().query_jira(jql, fields)
    if len(result) == 1:
        return HttpResponse(json.dumps(result[0]), content_type='application/json')
    return HttpResponse("{}", content_type='application/json')
