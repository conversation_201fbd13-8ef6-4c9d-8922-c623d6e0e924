from django.db import models

# Create your models here.
class ReportFeishuInfo(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    name = models.CharField(name='name', max_length=128, verbose_name="报告名称", null=False)
    template = models.Char<PERSON>ield(name='template', max_length=64, verbose_name="模板ID", null=False)
    target = models.CharField(name='target', max_length=64, verbose_name="目标位置", null=False)
    params = models.TextField(name='params', verbose_name="报告参数", default='', null=False)
    remark = models.CharField(name='remark', max_length=256, verbose_name="备注描述", null=False)
    create_by = models.Char<PERSON>ield(name='create_by', max_length=64, verbose_name="创建者", null=False)
    create_time = models.DateTimeField(name='create_time', verbose_name="创建时间", auto_now_add=True)
    update_by = models.Char<PERSON><PERSON>(name='update_by', max_length=64, verbose_name="更新者", null=False)
    update_time = models.DateTimeField(name='update_time', verbose_name="更新时间", auto_now=True)
    is_delete = models.CharField(name='is_delete', verbose_name="是否已经删除 0-未删除 1-已删除", default='0',
                                 max_length=2, null=False)

    class Meta:
        ordering = ['-create_time']
        db_table = "report_feishu_info"
        verbose_name = "report_feishu_info"


class ReportFeishuInfoLog(models.Model):
    id = models.BigIntegerField(name='id', primary_key=True, help_text='编号')
    feishu_info_id = models.BigIntegerField(name='feishu_info_id',  verbose_name="飞书配置ID", null=False)
    name = models.CharField(name='name', max_length=128, verbose_name="报告名称", null=False)
    report_url = models.CharField(name='report_url', max_length=64, verbose_name="目标位置", null=False)
    params = models.TextField(name='params', verbose_name="报告参数", default='', null=False)
    remark = models.CharField(name='remark', max_length=256, verbose_name="备注描述", null=False)
    status = models.CharField(name='status', max_length=2, default=0, verbose_name="生成状态 0-初始状态 1-生成中 2-生成成功 3-生成失败 4-手动触发", null=False)
    create_by = models.CharField(name='create_by', max_length=64, verbose_name="创建者", null=False)
    create_time = models.DateTimeField(name='create_time', verbose_name="创建时间", auto_now_add=True)
    update_by = models.CharField(name='update_by', max_length=64, verbose_name="更新者", null=False)
    update_time = models.DateTimeField(name='update_time', verbose_name="更新时间", auto_now=True)
    is_delete = models.CharField(name='is_delete', verbose_name="是否已经删除 0-未删除 1-已删除", default='0',
                                 max_length=2, null=False)

    class Meta:
        ordering = ['-create_time']
        db_table = "report_feishu_info_log"
        verbose_name = "report_feishu_info_log"