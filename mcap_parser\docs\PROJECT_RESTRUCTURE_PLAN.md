# MCAP Parser 项目重构计划

## 当前问题分析

### 1. 配置文件重复
- `pyproject.toml` 和 `setup.py` 功能重复
- 版本号不一致（3.1.0 vs 2.0.0）
- 依赖管理分散

### 2. 代码组织混乱
- `tools/` 目录与 `src/tools/` 重复
- `improvements/` 目录应整合到主代码
- CLI入口点分散（`mcap_parser_cli.py`, `src/cli/main.py`）

### 3. 测试结构不清晰
- 测试文件缺乏分类
- 缺少单元测试和集成测试的明确分离

### 4. 文档和示例分散
- 多个README文件
- 示例代码组织不够清晰

## 现代化重构方案

### 新的目录结构

```
mcap_parser/
├── pyproject.toml              # 唯一的配置文件
├── README.md                   # 主文档
├── CHANGELOG.md               # 变更日志
├── LICENSE                    # 许可证
├── .gitignore                # Git忽略文件
├── .github/                  # GitHub配置
│   └── workflows/
│       └── ci.yml
├── docs/                     # 文档目录
│   ├── api/                 # API文档
│   ├── guides/              # 使用指南
│   └── examples/            # 示例文档
├── src/
│   └── mcap_parser/         # 主包
│       ├── __init__.py
│       ├── cli/             # 命令行接口
│       │   ├── __init__.py
│       │   ├── main.py
│       │   └── commands/
│       ├── core/            # 核心功能
│       │   ├── __init__.py
│       │   ├── sdk.py
│       │   ├── parser.py
│       │   └── message_router.py
│       ├── parsers/         # 解析器
│       │   ├── __init__.py
│       │   ├── base.py
│       │   ├── fast_parser.py
│       │   └── registry.py
│       ├── utils/           # 工具函数
│       │   ├── __init__.py
│       │   ├── logger.py
│       │   ├── performance.py
│       │   └── validation.py
│       ├── data/            # 数据结构
│       │   ├── __init__.py
│       │   ├── structures.py
│       │   └── types.py
│       └── tools/           # 内置工具
│           ├── __init__.py
│           ├── analyzer.py
│           └── converter.py
├── tests/                   # 测试目录
│   ├── __init__.py
│   ├── unit/               # 单元测试
│   │   ├── test_core.py
│   │   ├── test_parsers.py
│   │   └── test_utils.py
│   ├── integration/        # 集成测试
│   │   ├── test_cli.py
│   │   └── test_workflow.py
│   ├── performance/        # 性能测试
│   │   └── test_benchmarks.py
│   └── fixtures/           # 测试数据
│       └── sample_data/
├── examples/               # 示例代码
│   ├── basic/             # 基础示例
│   ├── advanced/          # 高级示例
│   └── tutorials/         # 教程
├── scripts/               # 构建和部署脚本
│   ├── build.py
│   ├── release.py
│   └── setup_dev.py
└── tools/                 # 开发工具
    ├── lint.py
    ├── format.py
    └── benchmark.py
```

### 重构步骤

#### 第一阶段：配置文件整理
1. 删除 `setup.py`，统一使用 `pyproject.toml`
2. 统一版本号和依赖管理
3. 更新构建配置

#### 第二阶段：代码重组
1. 将 `improvements/` 目录的功能整合到主代码
2. 合并重复的工具目录
3. 重新组织 `src/` 目录结构

#### 第三阶段：测试重构
1. 按类型重新组织测试文件
2. 添加测试配置和fixtures
3. 完善测试覆盖率

#### 第四阶段：文档整理
1. 整合分散的文档
2. 重新组织示例代码
3. 添加API文档

### 重构收益

1. **更清晰的模块边界**：每个模块职责明确
2. **更好的可维护性**：代码组织更合理
3. **更标准的Python项目结构**：符合现代Python项目规范
4. **更好的开发体验**：工具和配置统一
5. **更容易的CI/CD**：标准化的构建流程

### 兼容性考虑

1. 保持公共API不变
2. 提供迁移指南
3. 渐进式重构，确保每个阶段都可用
4. 保留重要的向后兼容性

### 实施时间表

- **第一阶段**：1-2天（配置文件整理）
- **第二阶段**：3-4天（代码重组）
- **第三阶段**：2-3天（测试重构）
- **第四阶段**：2-3天（文档整理）

总计：8-12天完成重构