"""插件系统测试"""

import pytest
from src.plugins import PluginManager, GitLabURLParserPlugin, DefaultContentExtractorPlugin
from src.models.gitlab import GitLabMR


class TestGitLabURLParserPlugin:
    """GitLab URL解析插件测试"""
    
    def test_can_parse_gitlab_url(self, mock_settings):
        """测试能否解析GitLab URL"""
        plugin = GitLabURLParserPlugin(mock_settings)
        
        # 有效的GitLab URL
        valid_urls = [
            "https://gitlab.com/group/project/-/merge_requests/123",
            "https://gitlab.com/group/project/merge_requests/456",
            "https://gitlab.example.com/my-group/my-project/-/merge_requests/789/diffs"
        ]
        
        for url in valid_urls:
            assert plugin.can_parse(url) is True
        
        # 无效的URL
        invalid_urls = [
            "https://github.com/user/repo/pull/123",
            "https://gitlab.com/group/project/issues/123",
            "not-a-url"
        ]
        
        for url in invalid_urls:
            assert plugin.can_parse(url) is False
    
    def test_parse_url(self, mock_settings):
        """测试URL解析"""
        plugin = GitLabURLParserPlugin(mock_settings)
        
        url = "https://gitlab.com/group/project/-/merge_requests/123"
        result = plugin.parse_url(url)
        
        assert result is not None
        assert result.project_path == "group/project"
        assert result.mr_id == 123
    
    def test_parse_invalid_url(self, mock_settings):
        """测试解析无效URL"""
        plugin = GitLabURLParserPlugin(mock_settings)
        
        result = plugin.parse_url("invalid-url")
        assert result is None


class TestDefaultContentExtractorPlugin:
    """默认内容提取插件测试"""
    
    def test_extract_content(self, mock_settings):
        """测试内容提取"""
        plugin = DefaultContentExtractorPlugin(mock_settings)
        
        description = """
## 更新内容【必填】
这是更新的内容描述

## 测试方法【必填】
这是测试方法描述

## 其他内容
其他信息
"""
        
        update_content, test_method = plugin.extract_content(description)
        
        assert update_content == "这是更新的内容描述"
        assert test_method == "这是测试方法描述"
    
    def test_extract_content_empty(self, mock_settings):
        """测试提取空内容"""
        plugin = DefaultContentExtractorPlugin(mock_settings)
        
        result = plugin.extract_content("")
        assert result == (None, None)
        
        result = plugin.extract_content(None)
        assert result == (None, None)


class TestPluginManager:
    """插件管理器测试"""
    
    @pytest.mark.asyncio
    async def test_register_and_get_plugin(self, mock_settings):
        """测试注册和获取插件"""
        manager = PluginManager(mock_settings)
        plugin = GitLabURLParserPlugin(mock_settings)
        
        manager.register_plugin(plugin)
        
        retrieved = manager.get_plugin(plugin.name)
        assert retrieved is plugin
    
    @pytest.mark.asyncio
    async def test_get_plugins_by_type(self, mock_settings):
        """测试按类型获取插件"""
        manager = PluginManager(mock_settings)
        
        url_plugin = GitLabURLParserPlugin(mock_settings)
        content_plugin = DefaultContentExtractorPlugin(mock_settings)
        
        manager.register_plugin(url_plugin)
        manager.register_plugin(content_plugin)
        
        from src.plugins.processors import URLParserPlugin, ContentExtractorPlugin
        
        url_plugins = manager.get_plugins_by_type(URLParserPlugin)
        content_plugins = manager.get_plugins_by_type(ContentExtractorPlugin)
        
        assert len(url_plugins) == 1
        assert len(content_plugins) == 1
        assert url_plugins[0] is url_plugin
        assert content_plugins[0] is content_plugin
    
    @pytest.mark.asyncio
    async def test_enable_disable_plugin(self, mock_settings):
        """测试启用和禁用插件"""
        manager = PluginManager(mock_settings)
        plugin = GitLabURLParserPlugin(mock_settings)
        
        manager.register_plugin(plugin)
        
        # 默认启用
        assert plugin.enabled is True
        
        # 禁用
        manager.disable_plugin(plugin.name)
        assert plugin.enabled is False
        
        # 启用
        manager.enable_plugin(plugin.name)
        assert plugin.enabled is True
