import json
from typing import List, Dict, Optional
from dataclasses import dataclass
from src.utils.logger import get_logger, log_info, log_warning, log_error
from src.core.config_manager import ConfigManager

logger = get_logger(__name__)


@dataclass
class Route:
    """路线信息"""

    name: str        # 路线名称
    distance: float  # 公里数
    scenario: str    # 场景描述


class RouteManager:
    """路线管理器"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.routes = self._load_routes()
    
    def _load_routes(self) -> List[Route]:
        """从配置加载路线信息"""
        routes = []
        
        try:
            # 从配置文件读取路线信息
            route_config = self.config.get('Routes', 'route_list', '[]')
            log_info(f"读取到的路线配置: {route_config[:100]}...")  # 只显示前100个字符
            route_data = json.loads(route_config)
            
            for route_info in route_data:
                if isinstance(route_info, dict) and 'name' in route_info and 'distance' in route_info and 'scenario' in route_info:
                    route = Route(
                        name=route_info['name'],
                        distance=float(route_info['distance']),
                        scenario=route_info['scenario']
                    )
                    routes.append(route)
                else:
                    log_warning(f"跳过无效的路线配置: {route_info}")
            
            if routes:
                log_info(f"成功从配置文件加载 {len(routes)} 条路线")
            else:
                log_warning("未能从配置文件加载任何有效路线，将使用默认路线")
                routes = self._get_default_routes()

        except Exception as e:
            log_error(f"加载路线配置失败: {str(e)}")
            # 使用默认路线
            routes = self._get_default_routes()
        
        return routes
    
    def _get_default_routes(self) -> List[Route]:
        """获取默认路线（当配置文件读取失败时使用）"""
        default_routes = [
            Route("城市道路测试", 25.5, "城市主干道、次干道、支路混合场景"),
            Route("高速公路测试", 120.0, "高速公路、服务区、收费站场景"),
            Route("乡村道路测试", 45.8, "乡村道路、县道、乡道混合场景")
        ]
        log_warning("配置文件读取失败或配置为空，使用默认路线配置")
        return default_routes
    
    def get_all_routes(self) -> List[Route]:
        """获取所有路线"""
        return self.routes
    
    def get_route_by_name(self, name: str) -> Optional[Route]:
        """根据名称获取路线"""
        for route in self.routes:
            if route.name == name:
                return route
        return None
    
    def get_total_distance(self, selected_routes: List[str]) -> float:
        """计算选中路线的总公里数"""
        total_distance = 0.0
        for route_name in selected_routes:
            route = self.get_route_by_name(route_name)
            if route:
                total_distance += route.distance
        return total_distance
    
    def get_route_info(self, selected_routes: List[str]) -> Dict:
        """获取选中路线的详细信息"""
        route_info = {
            'total_distance': 0.0,
            'routes': [],
            'scenarios': []
        }
        
        for route_name in selected_routes:
            route = self.get_route_by_name(route_name)
            if route:
                route_info['total_distance'] += route.distance
                route_info['routes'].append({
                    'name': route.name,
                    'distance': route.distance,
                    'scenario': route.scenario
                })
                route_info['scenarios'].append(route.scenario)
        
        return route_info
    
    def display_routes(self):
        """显示所有可用路线"""
        print("\n📋 可用测试路线:")
        print("=" * 80)
        print(f"{'序号':<4} {'路线名称':<20} {'公里数':<10} {'场景描述':<40}")
        print("-" * 80)
        
        for i, route in enumerate(self.routes, 1):
            print(f"{i:<4} {route.name:<20} {route.distance:<10.1f} {route.scenario:<40}")
        
        print("=" * 80)
    
    def select_routes(self) -> List[str]:
        """选择路线"""
        self.display_routes()
        
        print("\n请选择本次测试包含的路线 (输入序号，多个用逗号分隔，如: 1,2,3):")
        print("输入 'all' 选择所有路线")
        print("输入 'q' 退出")
        
        while True:
            try:
                user_input = input("请输入选择: ").strip()
                
                if user_input.lower() == 'q':
                    return []
                
                if user_input.lower() == 'all':
                    return [route.name for route in self.routes]
                
                # 解析用户输入
                selected_indices = []
                for item in user_input.split(','):
                    item = item.strip()
                    if item.isdigit():
                        index = int(item) - 1  # 转换为0基索引
                        if 0 <= index < len(self.routes):
                            selected_indices.append(index)
                        else:
                            print(f"❌ 无效的序号: {item}")
                            continue
                    else:
                        print(f"❌ 无效的输入: {item}")
                        continue
                
                if not selected_indices:
                    print("❌ 请至少选择一个路线")
                    continue
                
                # 获取选中的路线名称
                selected_routes = [self.routes[i].name for i in selected_indices]
                
                # 去重
                selected_routes = sorted(list(set(selected_routes)))
                
                # 显示选择结果
                print(f"\n✅ 已选择 {len(selected_routes)} 条路线:")
                total_distance = 0.0
                for route_name in selected_routes:
                    route = self.get_route_by_name(route_name)
                    if route:
                        print(f"  - {route.name}: {route.distance}公里 ({route.scenario})")
                        total_distance += route.distance
                
                print(f"📊 总测试里程: {total_distance:.1f} 公里")
                
                return selected_routes
                
            except KeyboardInterrupt:
                print("\n❌ 操作被取消")
                return []
            except Exception as e:
                print(f"❌ 输入错误: {str(e)}")
                continue 