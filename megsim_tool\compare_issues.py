#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较原问题和克隆问题的字段差异
"""

import requests
import json
from typing import Dict, Any, Set
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class JiraIssueComparator:
    def __init__(self, base_url: str, bearer_token: str):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {bearer_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    
    def get_issue(self, issue_key: str) -> Dict[str, Any]:
        """获取问题详情"""
        url = f"{self.base_url}/rest/api/2/issue/{issue_key}"
        response = requests.get(url, headers=self.headers)
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"获取问题失败 {issue_key}: {response.status_code} - {response.text}")
            return None
    
    def compare_issues(self, original_key: str, clone_key: str):
        """比较两个问题的字段差异"""
        logger.info(f"开始比较问题: {original_key} vs {clone_key}")
        
        # 获取两个问题的详情
        original = self.get_issue(original_key)
        clone = self.get_issue(clone_key)
        
        if not original or not clone:
            logger.error("无法获取问题详情")
            return
        
        original_fields = original.get('fields', {})
        clone_fields = clone.get('fields', {})
        
        # 获取所有字段名
        all_fields = set(original_fields.keys()) | set(clone_fields.keys())
        
        # 分类字段
        same_fields = []
        different_fields = []
        missing_in_clone = []
        extra_in_clone = []
        
        for field in all_fields:
            original_value = original_fields.get(field)
            clone_value = clone_fields.get(field)
            
            if field not in original_fields:
                extra_in_clone.append(field)
            elif field not in clone_fields:
                missing_in_clone.append(field)
            elif self._values_equal(original_value, clone_value):
                same_fields.append(field)
            else:
                different_fields.append((field, original_value, clone_value))
        
        # 输出比较结果
        self._print_comparison_results(
            original_key, clone_key,
            same_fields, different_fields, missing_in_clone, extra_in_clone
        )
    
    def _values_equal(self, val1: Any, val2: Any) -> bool:
        """比较两个值是否相等"""
        # 处理None值
        if val1 is None and val2 is None:
            return True
        if val1 is None or val2 is None:
            return False
        
        # 转换为字符串比较（简化处理）
        return str(val1) == str(val2)
    
    def _print_comparison_results(self, original_key: str, clone_key: str,
                                same_fields: list, different_fields: list,
                                missing_in_clone: list, extra_in_clone: list):
        """打印比较结果"""
        print(f"\n{'='*80}")
        print(f"问题比较结果: {original_key} vs {clone_key}")
        print(f"{'='*80}")
        
        print(f"\n📊 统计信息:")
        print(f"  相同字段: {len(same_fields)}")
        print(f"  不同字段: {len(different_fields)}")
        print(f"  克隆中缺失: {len(missing_in_clone)}")
        print(f"  克隆中多出: {len(extra_in_clone)}")
        
        if different_fields:
            print(f"\n❌ 不同的字段 ({len(different_fields)}个):")
            for field, orig_val, clone_val in different_fields:
                print(f"  🔸 {field}:")
                print(f"    原问题: {self._format_value(orig_val)}")
                print(f"    克隆问题: {self._format_value(clone_val)}")
                print()
        
        if missing_in_clone:
            print(f"\n⚠️  克隆中缺失的字段 ({len(missing_in_clone)}个):")
            for field in missing_in_clone:
                print(f"  🔸 {field}")
        
        if extra_in_clone:
            print(f"\n➕ 克隆中多出的字段 ({len(extra_in_clone)}个):")
            for field in extra_in_clone:
                print(f"  🔸 {field}")
        
        if same_fields:
            print(f"\n✅ 相同的字段 ({len(same_fields)}个):")
            # 只显示前10个，避免输出过长
            display_fields = same_fields[:10]
            for field in display_fields:
                print(f"  🔸 {field}")
            if len(same_fields) > 10:
                print(f"  ... 还有 {len(same_fields) - 10} 个相同字段")
    
    def _format_value(self, value: Any) -> str:
        """格式化值用于显示"""
        if value is None:
            return "None"
        elif isinstance(value, (dict, list)):
            return json.dumps(value, ensure_ascii=False, indent=2)[:200] + "..."
        else:
            return str(value)[:200]

def main():
    # Jira配置
    BASE_URL = "https://jira.mach-drive-inc.com"
    BEARER_TOKEN = "OTAxMzc2MDUxNDM4OuLU8ywhVqtKIJW2qlZUeQxOS1bv"
    
    # 要比较的问题
    original_issue = "E2E-69424"
    clone_issue = "E2E-100931"
    
    try:
        comparator = JiraIssueComparator(BASE_URL, BEARER_TOKEN)
        comparator.compare_issues(original_issue, clone_issue)
        
    except Exception as e:
        logger.error(f"比较过程中发生错误: {e}")

if __name__ == "__main__":
    main()