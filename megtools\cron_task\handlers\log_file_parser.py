import logging
import os
import time
import traceback
from datetime import datetime
from importlib import import_module

from basic.utils import get_ip, DistributedLockThread, generate_md5
from cron_task.handlers.log_parsers.common_parser import CommonParser
from cron_task.models import OssFileList
from cron_task.handlers import log_parsers


class LogFileParserThread(DistributedLockThread):
    def __init__(self):
        self.host_name = get_ip()
        lock_name = f"log_file_parser.lock:{self.host_name}"
        super().__init__(lock_name, name="LogFileParserThread", switchName="file_parser_switch")

    def apply(self):
        self.need_release = True
        self.interval = 10
        result = OssFileList.objects.filter(current_status=2, host_name=self.host_name).order_by('id')[0:10]
        # 没有查出数据来就不干活了
        if len(result) == 0:
            return
        path = []
        db_entities = []
        ids = []
        parse_type = []
        for item in result:
            self.interval = 0.01
            db_entities.append(item)
            ids.append(item.id)
            parse_type.append(item.parse_type)
            log_path = os.path.join('oss_logs', item.vin, item.oss_path[0:8], f"{generate_md5(item.oss_path)}_{item.oss_path.split('/')[-1]}")
            path.append(f"{log_path}.log")
        OssFileList.objects.filter(id__in=ids).update(current_status=3, host_name=self.host_name)
        self.release_lock()
        self.need_release = False
        for idx in range(0, len(db_entities)):
            try:
                if parse_type[idx].startswith("comm_"):
                    CommonParser().parse(ids[idx], db_entities[idx], path[idx])
                else:
                    handler = import_module(f".{parse_type[idx]}", log_parsers.__name__)
                    handler.apply(path[idx], db_entities[idx])
                OssFileList.objects.filter(id=ids[idx], current_status='3').update(current_status=4)
            except:
                logging.error(f"log parser error! {traceback.format_exc()}")
                OssFileList.objects.filter(id=ids[idx], current_status='3').update(current_status=6,
                                                                                       host_name=self.host_name,
                                                                                       remark=f"parse error: {traceback.format_exc()}"[0:200])