INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('select.onlinetop.carlist', '[{"label":"Z15","value":"z15"},{"label":"Z17","value":"z17"},{"label":"Z18","value":"z18"},{"label":"Z18_EV","value":"z18_ev"},{"label":"Z19","value":"z19"}]', '', 'guosongduo', '2025-03-06 22:18:29.918501', 'guosongduo', '2025-03-23 20:55:06.650004', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('select.onlinetop.progresslist', '["/home/<USER>/zeta-sdk/output/lib/e2e_perceptor_node/e2e_perceptor_node_exe","/home/<USER>/zeta-sdk/output/lib/rslidar_node/rslidar_node_exe","/home/<USER>/zeta-sdk/output/lib/control_node/control_node_exe","/home/<USER>/zeta-sdk/output/lib/planning_obstacle_visualize_node/planning_obstacle_visualize_node_exe","/home/<USER>/zeta-sdk/output/lib/pilot_planning_node/pilot_planning_multithread","/home/<USER>/zeta-sdk/output/lib/radar_node_for_z10/radar_node_for_z10_exe","/home/<USER>/zeta-sdk/output/lib/perception_fusion_node/perception_fusion_node_exe","/home/<USER>/zeta-sdk/output/lib/freq_monitor_node/freq_monitor_node_exe","/home/<USER>/zeta-sdk/output/lib/vehicle_data_recorder/vehicle_data_recorder_exe","/home/<USER>/zeta-sdk/output/lib/routing_node/routing_node_exe","/home/<USER>/zeta-sdk/output/lib/mdriver_nodes/mdriver_nodes_exe","/home/<USER>/zeta-sdk/output/lib/environment_model_node/environment_model_node_exe","/home/<USER>/zeta-sdk/output/lib/pilot_perception_map_e171_node/pilot_perception_map_e171_node","/home/<USER>/zeta-sdk/output/lib/localization_nodes/localization_nodes_exe","/home/<USER>/zeta-sdk/output/lib/local_map_node/local_map_node_exe","/home/<USER>/zeta-sdk/output/lib/hmi_proxy/pilot_proxy_node","/home/<USER>/zeta-sdk/output/lib/gnss_nodes/gnss_node_exe","/home/<USER>/zeta-sdk/output/lib/planning_visualize_pub_nodes/planning_visualize_pub_nodes_exe","/home/<USER>/zeta-sdk/output/lib/map_visualize/map_visualize_exe","/home/<USER>/zeta-sdk/output/lib/innovusion_sdk/innovusion_sdk_pointcloud_exe","/home/<USER>/zeta-sdk/output/lib/bywire_node/bywire_node","/home/<USER>/zeta-sdk/output/lib/perception_obstacle_visualize/perception_obstacle_visualize_exe","/home/<USER>/zeta-sdk/output/lib/routing_gaode_node/routing_gaode_node_exe","/home/<USER>/zeta-sdk/output/lib/hesai_ros_driver/hesai_ros_driver_node","/home/<USER>/zeta-sdk/output/lib/e2e_occ_node/e2e_occ_node_exe","/home/<USER>/zeta-sdk/output/lib/environment_model_node/environment_model_multithread","/home/<USER>/zeta-sdk/output/lib/e2e_failsafe_node/e2e_failsafe_node_exe"]', '', 'guosongduo', '2025-03-07 10:07:34.006951', 'liuhongyan', '2025-04-02 09:02:31.650725', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_gpu_nvidia_smi', '{"name":"Gpu日志解析","group_sign":"timestamp, memory.total","chart_config":[{"name":"GpuNvidiaSmiLog","filter":{"type":"contains","pattern":"MiB,"},"xAxis":{"group_idx":1,"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"/","newValue":"-"}]},"yAxis":[{"name":"memory_total_1","comment":"memory.total [MiB] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":2}]},{"name":"memory_used_1","comment":"memory.used [MiB] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":4}]},{"name":"memory_free_1","comment":"memory.free [MiB] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":6}]},{"name":"utilization_gpu_1","comment":"utilization.gpu [%] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"utilization_memory_1","comment":"utilization.memory [%] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":10}]},{"name":"temperature_gpu_1","comment":"temperature.gpu 1","group_idx":1,"operate":[{"type":"split","value":" ","index":12}]},{"name":"memory_total_2","comment":"memory.total [MiB] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":2}]},{"name":"memory_used_2","comment":"memory.used [MiB] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":4}]},{"name":"memory_free_2","comment":"memory.free [MiB] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":6}]},{"name":"utilization_gpu_2","comment":"utilization.gpu [%] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":8}]},{"name":"utilization_memory_2","comment":"utilization.memory [%] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":10}]},{"name":"temperature_gpu_2","comment":"temperature.gpu 2","group_idx":2,"operate":[{"type":"split","value":" ","index":12}]}]}]}', 'GPU日志解析配置', 'guosongduo', '2025-03-07 10:07:34.006951', 'guosongduo', '2025-03-07 10:07:34.006951', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_innovusion_fps_monitor', '{"name":"innovusion fps monitor","chart_config":[
{"name":"FpsInnovusionLidar","alias":"Topic: /sensor/front_lidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_lidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}', '', 'guosongduo', '2025-03-11 18:00:39.240741', 'guosongduo', '2025-03-11 18:00:39.240794', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_proxy_radar_fps_monitor', '{"name":"proxy radar fps monitor","chart_config":[
{"name":"FpsProxyRadar0","alias":"Topic: /sensor/radar0_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar0_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsProxyRadar1","alias":"Topic: /sensor/radar1_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar1_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsProxyRadar2","alias":"Topic: /sensor/radar2_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar2_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsProxyRadar3","alias":"Topic: /sensor/radar3_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar3_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsProxyRadar4","alias":"Topic: /sensor/radar4_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar4_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}', '', 'guosongduo', '2025-03-11 18:08:20.174320', 'guosongduo', '2025-03-11 18:08:20.174390', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:pilot_planning', '{"name":"pilot_planning single_process view","chart_config":[{"name":"OneFramePilotPlanningProcessTime","alias":"pilot planning Processing time","yAxis":[{"name":"process_time","comment":"pilot planning single_frame_process_time  (ms)"}]},{"name":"OneFramePilotPlanningSensor2Planning","alias":"pilot planning sesor to planning time","yAxis":[{"name":"process_time","comment":"pilot planning sensor_to_planning_output_time  (ms)"}]},{"name":"OneFramePilotPlanningPrediction","alias":"pilot planning prediction","yAxis":[{"name":"process_time","comment":"pilot planning pred delay  (ms)"}]},{"name":"PilotPlanningThreadTimeSpeed","alias":"PlanningThread time spend: curr: 0.261000 ms, PlanningThread average: 0.251413 ms max: 0.693000 ms","yAxis":[{"name":"curr","comment":"Current Speed(ms)"},{"name":"average","comment":"Average(ms)"},{"name":"max","comment":"Max(ms)"}]}]}', '', 'guosongduo', '2025-03-19 19:00:29.467906', 'guosongduo', '2025-03-29 10:27:46.992707', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_environment_model', '{"name":"environment_monitor monitor","chart_config":[{"name":"OneFrameEnvironmentModelProcess","alias":"Control Processing time:","filter":{"type":"contains","pattern":"NavigatorThread time spend: curr"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"environment model single_frame_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"FpsEnvTrafficLight","alias":"Topic: /perception/traffic_lights_3in1_result","filter":{"type":"contains","pattern":"Topic: /perception/traffic_lights_3in1_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsEnvPlanningEnvInfo","alias":"Topic: /planning/env_info","filter":{"type":"contains","pattern":"Topic: /planning/env_info"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '', 'guosongduo', '2025-03-19 19:05:51.016922', 'guosongduo', '2025-03-28 21:57:07.243506', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:comm_control_fps_monitor', '{"name":"control sigle frame monitor","chart_config":[{"name":"OneFrameControlProcess","alias":"control_single_frame_processing_time","yAxis":[{"name":"process_time","comment":"control_single_frame_processing_time"}]}]}
', '', 'guosongduo', '2025-03-19 19:29:17.093867', 'guosongduo', '2025-03-19 19:29:17.093936', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:all', '{"name": "all file_fps viewer", "chart_config": [{"name": "FpsAllCamBack200", "alias": "Topic: /sensor/cam_back_200/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamBack70", "alias": "Topic: /sensor/cam_back_70/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamBackLeft100", "alias": "Topic: /sensor/cam_back_left_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamBackRight100", "alias": "Topic: /sensor/cam_back_right_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFront120", "alias": "Topic: /sensor/cam_front_120/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFront200", "alias": "Topic: /sensor/cam_front_200/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFront30", "alias": "Topic: /sensor/cam_front_30/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFrontLeft100", "alias": "Topic: /sensor/cam_front_left_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFrontRight100", "alias": "Topic: /sensor/cam_front_right_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamLeft200", "alias": "Topic: /sensor/cam_left_200/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamRight200", "alias": "Topic: /sensor/cam_right_200/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:48.000000', 'sys', '2025-03-24 08:06:48.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:control', '{"name": "control file_fps viewer", "chart_config": [{"name": "FpsControlVehicleReport", "alias": "Topic: /sensor/vehicle_report_common", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:48.000000', 'sys', '2025-03-24 08:06:48.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:e2e_perceptor', '{"name":"e2e_perceptor file_fps viewer","chart_config":[{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorObstacleTimestamp","alias":"Topic: /perception/detection/obstacle_timestamp_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsE2ePerceptorMapInputSync","alias":"Topic: /debug/profile/map_input_sync","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'guosongduo', '2025-04-09 20:11:12.306617', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:gnss', '{"name": "gnss file_fps viewer", "chart_config": [{"name": "FpsGnssCorrImu", "alias": "Topic: /sensor/corr_imu", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsGnssGps", "alias": "Topic: /sensor/gps", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsGnssInsPose", "alias": "Topic: /sensor/ins_pose", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsGnssRawImu", "alias": "Topic: /sensor/raw_imu", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'sys', '2025-03-24 08:06:49.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:innovusion_lidar', '{"name":"innovusion_lidar file_fps viewer","chart_config":[{"name":"FpsInnovusionFrontLidar","alias":"Topic: /sensor/front_lidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'guosongduo', '2025-03-28 21:31:16.888617', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:localization', '{"name": "localization file_fps viewer", "chart_config": [{"name": "FpsLocalizationEstimate", "alias": "Topic: /localization/localization_estimate", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationCorrImu", "alias": "Topic: /sensor/corr_imu", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationGps", "alias": "Topic: /sensor/gps", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationInsPose", "alias": "Topic: /sensor/ins_pose", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationRawImu", "alias": "Topic: /sensor/raw_imu", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationVehicleReport", "alias": "Topic: /sensor/vehicle_report_common", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'sys', '2025-03-24 08:06:49.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:mdriver', '{"name": "mdriver file_fps viewer", "chart_config": [{"name": "FpsMdriverMworldArray", "alias": "Topic: /mdriver/mworld_array_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsMdriverObstacleArray", "alias": "Topic: /perception/tracking/obstacle_array_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsMdriverPlanningEnvInfo", "alias": "Topic: /planning/env_info", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'sys', '2025-03-24 08:06:49.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:pilot_perception_map_e171', '{"name": "pilot_perception_map_e171 file_fps viewer", "chart_config": [{"name": "FpsPilotPerceptionMapMerger", "alias": "Topic: /map/map_merger", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsPilotPerceptionMap", "alias": "Topic: /perception/perception_map", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'guosongduo', '2025-03-24 08:06:49.000000', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_all', '{"name": "all log parser", "chart_config": [{"name": "FpsAllCamBack200", "alias": "Topic: /sensor/cam_back_200/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_200/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamBack70", "alias": "Topic: /sensor/cam_back_70/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_70/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamBackLeft100", "alias": "Topic: /sensor/cam_back_left_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_left_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamBackRight100", "alias": "Topic: /sensor/cam_back_right_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_right_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFront120", "alias": "Topic: /sensor/cam_front_120/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_120/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFront200", "alias": "Topic: /sensor/cam_front_200/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_200/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFront30", "alias": "Topic: /sensor/cam_front_30/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_30/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFrontLeft100", "alias": "Topic: /sensor/cam_front_left_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_left_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFrontRight100", "alias": "Topic: /sensor/cam_front_right_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_right_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamLeft200", "alias": "Topic: /sensor/cam_left_200/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_left_200/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamRight200", "alias": "Topic: /sensor/cam_right_200/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_right_200/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'sys', '2025-03-24 09:00:01.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_gnss', '{"name": "gnss log parser", "chart_config": [{"name": "FpsGnssCorrImu", "alias": "Topic: /sensor/corr_imu", "filter": {"type": "contains", "pattern": "Topic: /sensor/corr_imu"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsGnssGps", "alias": "Topic: /sensor/gps", "filter": {"type": "contains", "pattern": "Topic: /sensor/gps"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsGnssInsPose", "alias": "Topic: /sensor/ins_pose", "filter": {"type": "contains", "pattern": "Topic: /sensor/ins_pose"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsGnssRawImu", "alias": "Topic: /sensor/raw_imu", "filter": {"type": "contains", "pattern": "Topic: /sensor/raw_imu"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'sys', '2025-03-24 09:00:01.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_innovusion_lidar', '{"name":"innovusion_lidar log parser","chart_config":[{"name":"FpsInnovusionFrontLidar","alias":"Topic: /sensor/front_lidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_lidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'guosongduo', '2025-03-28 21:32:21.326603', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_localization', '{"name": "localization log parser", "chart_config": [{"name": "FpsLocalizationEstimate", "alias": "Topic: /localization/localization_estimate", "filter": {"type": "contains", "pattern": "Topic: /localization/localization_estimate"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationCorrImu", "alias": "Topic: /sensor/corr_imu", "filter": {"type": "contains", "pattern": "Topic: /sensor/corr_imu"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationGps", "alias": "Topic: /sensor/gps", "filter": {"type": "contains", "pattern": "Topic: /sensor/gps"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationInsPose", "alias": "Topic: /sensor/ins_pose", "filter": {"type": "contains", "pattern": "Topic: /sensor/ins_pose"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationRawImu", "alias": "Topic: /sensor/raw_imu", "filter": {"type": "contains", "pattern": "Topic: /sensor/raw_imu"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationVehicleReport", "alias": "Topic: /sensor/vehicle_report_common", "filter": {"type": "contains", "pattern": "Topic: /sensor/vehicle_report_common"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'sys', '2025-03-24 09:00:01.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_pilot_perception_map_e171', '{"name": "pilot_perception_map_e171 log parser", "chart_config": [{"name": "FpsPilotPerceptionMapMerger", "alias": "Topic: /map/map_merger", "filter": {"type": "contains", "pattern": "Topic: /map/map_merger"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsPilotPerceptionMap", "alias": "Topic: /perception/perception_map", "filter": {"type": "contains", "pattern": "Topic: /perception/perception_map"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'guosongduo', '2025-03-24 09:00:01.000000', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_proxy_radar', '{"name":"proxy radar fps monitor","chart_config":[{"name":"FpsProxyRadar0","alias":"Topic: /sensor/radar0_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar0_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsProxyRadar1","alias":"Topic: /sensor/radar1_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar1_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsProxyRadar2","alias":"Topic: /sensor/radar2_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar2_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsProxyRadar3","alias":"Topic: /sensor/radar3_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar3_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsProxyRadar4","alias":"Topic: /sensor/radar4_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar4_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'sys', '2025-03-24 09:00:01.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_control', '{"name": "control log parser", "chart_config": [{"name":"OneFrameControlProcess","alias":"Control Processing time:","filter":{"type":"contains","pattern":"Processing time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"control single_frame_process_time","operate":[{"type":"split","value":" ","index":7}]}]},{"name": "FpsControlVehicleReport", "alias": "Topic: /sensor/vehicle_report_common", "filter": {"type": "contains", "pattern": "Topic: /sensor/vehicle_report_common"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 10:10:22.000000', 'sys', '2025-03-24 10:10:22.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_cpu_temperature', '{
	"name": "CPU温度解析",
	"group_sign": "CPU Temperatures:",
	"chart_config": [{
		"name": "DataviewCpuTemperature",
		"xAxis": { "group_idx": 0, "operate": [{ "type": "split", "value": "]", "index": 0 }, { "type": "replace", "oldValue": "[", "newValue": "" }] },
		"yAxis": [
		{"name": "package", "comment": "Package", "group_idx": 1, "operate": [{ "type": "split", "value": " ", "index": 1 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_0", "comment": "Core 0(°C)", "group_idx": 2, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_4", "comment": "Core 4(°C)", "group_idx": 3, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_8", "comment": "Core 8(°C)", "group_idx": 4, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_12", "comment": "Core 12(°C)", "group_idx": 5, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_16", "comment": "Core 16(°C)", "group_idx": 6, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_20", "comment": "Core 20(°C)", "group_idx": 7, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_24", "comment": "Core 24(°C)", "group_idx": 8, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_28", "comment": "Core 28(°C)", "group_idx": 9, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_32", "comment": "Core 32(°C)", "group_idx": 10, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_33", "comment": "Core 33(°C)", "group_idx": 11, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_34", "comment": "Core 34(°C)", "group_idx": 12, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_35", "comment": "Core 35(°C)", "group_idx": 13, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_36", "comment": "Core 36(°C)", "group_idx": 14, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_37", "comment": "Core 37(°C)", "group_idx": 15, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_38", "comment": "Core 38(°C)", "group_idx": 16, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_39", "comment": "Core 39(°C)", "group_idx": 17, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_40", "comment": "Core 40(°C)", "group_idx": 18, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_41", "comment": "Core 41(°C)", "group_idx": 19, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_42", "comment": "Core 42(°C)", "group_idx": 20, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_43", "comment": "Core 43(°C)", "group_idx": 21, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_44", "comment": "Core 44(°C)", "group_idx": 22, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_45", "comment": "Core 45(°C)", "group_idx": 23, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_46", "comment": "Core 46(°C)", "group_idx": 24, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_47", "comment": "Core 47(°C)", "group_idx": 25, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		]
	}]
}', '', 'guosongduo', '2025-03-25 16:49:53.665771', 'guosongduo', '2025-03-25 16:49:53.665935', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:perception_fusion_node', '{"name": "perception_fusion_node file_fps viewer", "chart_config": [{"name": "FpsFusionRadarRearLeft", "alias": "Topic: /sensor/radar_rear_left", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarRearRight", "alias": "Topic: /sensor/radar_rear_right", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarFrontLeft", "alias": "Topic: /sensor/radar_front_right", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarFrontRight", "alias": "Topic: /sensor/radar_front_left", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarLocalization", "alias": "Topic: /localization/localization_estimate", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionFusionObstacleArray", "alias": "Topic: /perception/fusion/obstacle_array_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarFrontMiddle", "alias": "Topic: /sensor/radar_front_middle", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionVehicleReportCommon", "alias": "Topic: /sensor/vehicle_report_common", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionDetectionObstacleArray", "alias": "Topic: /perception/detection/obstacle_array_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-27 04:44:59.000000', 'sys', '2025-03-27 04:44:59.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_perception_fusion_node', '{"name": "perception_fusion_node log parser", "chart_config": [{"name": "FpsFusionRadarRearLeft", "alias": "Topic: /sensor/radar_rear_left", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_rear_left"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarRearRight", "alias": "Topic: /sensor/radar_rear_right", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_rear_right"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarFrontLeft", "alias": "Topic: /sensor/radar_front_right", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_front_right"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarFrontRight", "alias": "Topic: /sensor/radar_front_left", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_front_left"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarLocalization", "alias": "Topic: /localization/localization_estimate", "filter": {"type": "contains", "pattern": "Topic: /localization/localization_estimate"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionFusionObstacleArray", "alias": "Topic: /perception/fusion/obstacle_array_result", "filter": {"type": "contains", "pattern": "Topic: /perception/fusion/obstacle_array_result"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarFrontMiddle", "alias": "Topic: /sensor/radar_front_middle", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_front_middle"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionVehicleReportCommon", "alias": "Topic: /sensor/vehicle_report_common", "filter": {"type": "contains", "pattern": "Topic: /sensor/vehicle_report_common"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionDetectionObstacleArray", "alias": "Topic: /perception/detection/obstacle_array_result", "filter": {"type": "contains", "pattern": "Topic: /perception/detection/obstacle_array_result"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-27 04:44:59.000000', 'sys', '2025-03-27 04:44:59.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_static_obstacle_result', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccStaticObstacleResult","alias":"Topic: /perception/detection/static_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_front_rslidar', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_localization', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccLocalization","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_back_right', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamBackRight","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_back_left', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamBackLeft","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_front_right', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamFrontRight","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_front_left', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'guosongduo', '2025-03-27 17:40:14.153186', 'guosongduo', '2025-03-27 17:40:14.153455', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_back_70', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:25.000000', 'sys', '2025-03-27 09:25:25.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_visualization', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccVisualization","alias":"Topic: /perception/occ/visualization","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:25.000000', 'sys', '2025-03-27 09:25:25.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_front_30', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:25.000000', 'sys', '2025-03-27 09:25:25.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_front_120', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:25.000000', 'sys', '2025-03-27 09:25:25.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:e2e_occ', '{"name":"e2e_occ file_fps viewer","chart_config":[{"name":"FpsOccStaticObstacleResult","alias":"Topic: /perception/detection/static_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccDynamicObstacleResult","alias":"Topic: /perception/detection/dynamic_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccInputTimestamps","alias":"Topic: /perception/occ/input_timestamps","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccLocalization","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamBackRight","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamBackLeft","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamFrontRight","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccVisualization","alias":"Topic: /perception/occ/visualization","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-27 09:28:29.000000', 'guosongduo', '2025-04-08 16:10:19.451083', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_e2e_occ', '{"name":"e2e_occ log parser","chart_config":[{"name":"FpsOccStaticObstacleResult","alias":"Topic: /perception/detection/static_obstacle_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/static_obstacle_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccInputTimestamps","alias":"Topic: /perception/occ/input_timestamps","filter":{"type":"contains","pattern":"Topic: /perception/occ/input_timestamps"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_rslidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccLocalization","alias":"Topic: /localization/localization_estimate","filter":{"type":"contains","pattern":"Topic: /localization/localization_estimate"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamBackRight","alias":"Topic: /sensor/cam_back_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamBackLeft","alias":"Topic: /sensor/cam_back_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamFrontRight","alias":"Topic: /sensor/cam_front_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamBack70","alias":"Topic: /sensor/cam_back_70/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_70/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccVisualization","alias":"Topic: /perception/occ/visualization","filter":{"type":"contains","pattern":"Topic: /perception/occ/visualization"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamFront30","alias":"Topic: /sensor/cam_front_30/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_30/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamFront120","alias":"Topic: /sensor/cam_front_120/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_120/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccDynamicObstacleResult","alias":"Topic: /perception/detection/dynamic_obstacle_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/dynamic_obstacle_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '文件日志解析', 'sys', '2025-03-27 09:28:29.000000', 'guosongduo', '2025-04-08 16:10:19.549078', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:pilot_planngin_result', '{"name":"pilot_planning fps monitor","chart_config":[{"name":"FpsPlanningResult","alias":"Topic: /planning/planning_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-28 13:41:17.000000', 'sys', '2025-03-28 13:41:17.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:env_fps_traffic_light', '{"name":"environment_model fps monitor","chart_config":[{"name":"FpsEnvTrafficLight","alias":"Topic: /perception/traffic_lights_3in1_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-28 13:41:17.000000', 'sys', '2025-03-28 13:41:17.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:env_fps_env_info', '{"name":"environment_model fps monitor","chart_config":[{"name":"FpsEnvPlanningEnvInfo","alias":"Topic: /planning/env_info","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-28 13:41:17.000000', 'sys', '2025-03-28 13:41:17.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_gnss_fps_monitor', '{"name":"gnss fps monitor","chart_config":[
{"name":"FpsGnssInsPose","alias":"Topic: /sensor/ins_pose","filter":{"type":"contains","pattern":"Topic: /sensor/ins_pose"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsGnssRawImu","alias":"Topic: /sensor/raw_imu","filter":{"type":"contains","pattern":"Topic: /sensor/raw_imu"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsGnssCorrImu","alias":"Topic: /sensor/corr_imu","filter":{"type":"contains","pattern":"Topic: /sensor/corr_imu"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsGnssGps","alias":"Topic: /sensor/gps","filter":{"type":"contains","pattern":"Topic: /sensor/gps"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}


', '', 'guosongduo', '2025-03-11 17:46:27.737867', 'guosongduo', '2025-03-11 17:46:27.737909', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:control', '{"name":"control single_process view","chart_config":[{"name":"OneFrameControlProcess","alias":"Control Processing time","yAxis":[{"name":"process_time","comment":"control single_frame_process_time (ms)"}]}]}', '', 'guosongduo', '2025-03-19 19:00:43.790592', 'guosongduo', '2025-03-22 16:19:19.194831', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:environment_model', '{"name":"environment single_process view","chart_config":[{"name":"OneFrameEnvironmentModelProcess","alias":"Control Processing time ","yAxis":[{"name":"process_time","comment":"environment model single_frame_process_time （ms）"}]}]}', '', 'guosongduo', '2025-03-19 19:01:01.137658', 'guosongduo', '2025-03-22 16:19:19.103203', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_pilot_planning', '{"name":"pilot planning log monitor","chart_config":[{"name":"OneFramePilotPlanningProcessTime","alias":"pilot planning Processing time:","filter":{"type":"contains","pattern":"Processing time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"pilot planning single_frame_process_time","operate":[{"type":"split","value":" ","index":7}]}]},{"name":"OneFramePilotPlanningSensor2Planning","alias":"pilot planning sesor to planning time:","filter":{"type":"contains","pattern":"Time difference between predicition sensor and planning output"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"pilot planning sensor_to_planning_output_time","operate":[{"type":"split","value":" ","index":13}]}]},{"name":"OneFramePilotPlanningPrediction","alias":"pilot planning prediction ","filter":{"type":"contains","pattern":"pred delay :"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"pilot planning pred delay","operate":[{"type":"split","value":" ","index":8}]}]},{"name":"FpsPlanningResult","alias":"Topic: /planning/planning_result","filter":{"type":"contains","pattern":"Topic: /planning/planning_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsPilotPlanningLaneArray","alias":"Topic: /debug/env_info/lane_array","filter":{"type":"contains","pattern":"Topic: /debug/env_info/lane_array"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"PilotPlanningThreadTimeSpeed","alias":"PlanningThread time spend: curr: 0.261000 ms, PlanningThread average: 0.251413 ms max: 0.693000 ms","filter":{"type":"contains","pattern":"ms, PlanningThread average:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"curr","comment":"Current Speed(ms)","operate":[{"type":"split","value":" ","index":9}]},{"name":"average","comment":"Average(ms)","operate":[{"type":"split","value":" ","index":13}]},{"name":"max","comment":"Max(ms)","operate":[{"type":"split","value":" ","index":16}]}]}]}', '', 'guosongduo', '2025-03-19 19:08:32.752432', 'guosongduo', '2025-04-02 11:46:29.217174', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_back_200', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamBack200","alias":"Topic: /sensor/cam_back_200/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_back_70', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_back_left_100', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_back_right_100', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_120', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_200', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFront200","alias":"Topic: /sensor/cam_front_200/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_30', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_left_100', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFrontLeft100","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_right_100', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFrontRight100","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_left_200', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamLeft200","alias":"Topic: /sensor/cam_left_200/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_right_200', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamRight200","alias":"Topic: /sensor/cam_right_200/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:control_fps_vehicle_report', '{"name":"control fps monitor","chart_config":[{"name":"FpsControlVehicleReport","alias":"Topic: /sensor/vehicle_report_common","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_localization_estimate', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_obstacle_array', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'guosongduo', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_obstacle_rviz', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_obstacle_timestamp', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorObstacleTimestamp","alias":"Topic: /perception/detection/obstacle_timestamp_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_lane_array', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_traffic_light', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_back_70', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_back_left', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_back_right', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_front_120', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_front_30', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_front_left_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_front_right_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_front_rslidar', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar0', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar1', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar2', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar3', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar4', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:gnss_fps_corr_imu', '{"name":"gnss fps monitor","chart_config":[{"name":"FpsGnssCorrImu","alias":"Topic: /sensor/corr_imu","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:gnss_fps_gps', '{"name":"gnss fps monitor","chart_config":[{"name":"FpsGnssGps","alias":"Topic: /sensor/gps","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:gnss_fps_ins_pose', '{"name":"gnss fps monitor","chart_config":[{"name":"FpsGnssInsPose","alias":"Topic: /sensor/ins_pose","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:gnss_fps_raw_imu', '{"name":"gnss fps monitor","chart_config":[{"name":"FpsGnssRawImu","alias":"Topic: /sensor/raw_imu","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:innovusion_lidar_se', '{"name":"innovusion_lidar fps monitor","chart_config":[{"name":"FpsInnovusionSe","alias":"Topic: /se","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'guosongduo', '2025-03-24 05:19:40.000000', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:innovusion_lidar_front_lidar_points', '{"name":"innovusion_lidar fps monitor","chart_config":[{"name":"FpsInnovusionFrontLidar","alias":"Topic: /sensor/front_lidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_localization_estimate', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationEstimate","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_corr_imu', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationCorrImu","alias":"Topic: /sensor/corr_imu","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_gps', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationGps","alias":"Topic: /sensor/gps","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_ins_pose', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationInsPose","alias":"Topic: /sensor/ins_pose","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_raw_imu', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationRawImu","alias":"Topic: /sensor/raw_imu","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_vehicle_report', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationVehicleReport","alias":"Topic: /sensor/vehicle_report_common","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:mdriver_fps_mworld_array', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverMworldArray","alias":"Topic: /mdriver/mworld_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:mdriver_fps_obtacle_array', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverObstacleArray","alias":"Topic: /perception/tracking/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:mdriver_fps_planning', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverPlanningEnvInfo","alias":"Topic: /planning/env_info","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:pilot_perception_map_fps_merger', '{"name":"pilot_perception_map_e171 fps monitor","chart_config":[{"name":"FpsPilotPerceptionMapMerger","alias":"Topic: /map/map_merger","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:pilot_perception_map_fps_perception_map', '{"name":"pilot_perception_map_e171 fps monitor","chart_config":[{"name":"FpsPilotPerceptionMap","alias":"Topic: /perception/perception_map","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_0', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar0","alias":"Topic: /sensor/radar0_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_1', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar1","alias":"Topic: /sensor/radar1_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_2', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar2","alias":"Topic: /sensor/radar2_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_3', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar3","alias":"Topic: /sensor/radar3_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_4', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar4","alias":"Topic: /sensor/radar4_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_mdriver', '{"name": "mdriver log parser", "chart_config": [{"name":"OneFrameMdriverProcess","alias":"mdriver callback time cost:","filter":{"type":"contains","pattern":"callback time cost:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"mdriver single_frame_process_time","operate":[{"type":"split","value":" ","index":8}]}]},{"name": "FpsMdriverMworldArray", "alias": "Topic: /mdriver/mworld_array_result", "filter": {"type": "contains", "pattern": "Topic: /mdriver/mworld_array_result"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsMdriverObstacleArray", "alias": "Topic: /perception/tracking/obstacle_array_result", "filter": {"type": "contains", "pattern": "Topic: /perception/tracking/obstacle_array_result"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsMdriverPlanningEnvInfo", "alias": "Topic: /planning/env_info", "filter": {"type": "contains", "pattern": "Topic: /planning/env_info"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 10:03:53.000000', 'sys', '2025-03-24 10:03:53.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:pilot_planning', '{"name": "pilot_planning file_fps viewer", "chart_config": [{"name": "FpsPilotPlanningLaneArray", "alias": "Topic: /debug/env_info/lane_array", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]},{"name": "FpsPlanningResult", "alias": "Topic: /planning/planning_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-28 13:44:04.000000', 'guosongduo', '2025-04-02 11:46:46.158223', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:environment_model', '{"name": "environment_model file_fps viewer", "chart_config": [{"name": "FpsEnvTrafficLight", "alias": "Topic: /perception/traffic_lights_3in1_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsEnvPlanningEnvInfo", "alias": "Topic: /planning/env_info", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-28 13:44:04.000000', 'sys', '2025-03-28 13:44:04.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:pilot_planning_lane_array', '{"name":"pilot_planning fps monitor","chart_config":[{"name":"FpsPilotPlanningLaneArray","alias":"Topic: /debug/env_info/lane_array","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-02 03:22:51.000000', 'sys', '2025-04-02 03:22:51.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_input_timestamps', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccInputTimestamps","alias":"Topic: /perception/occ/input_timestamps","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-02 03:22:51.000000', 'sys', '2025-04-02 03:22:51.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_map_input_sync', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsE2ePerceptorMapInputSync","alias":"Topic: /debug/profile/map_input_sync","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-02 03:22:51.000000', 'sys', '2025-04-02 03:22:51.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_dynamic_obstacle_result', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccDynamicObstacleResult","alias":"Topic: /perception/detection/dynamic_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-08 08:03:51.000000', 'sys', '2025-04-08 08:03:51.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_vlm', '{"name":"vlm_ log parser","chart_config":[{"name":"OneFrameVlmLlmnode","alias":"llmnode all time:","filter":{"type":"contains","pattern":"llmnode all time:"},"xAxis":{"operate":[{"type":"split","value":",","index":0}]},"yAxis":[{"name":"llmnode","comment":"llmnode all time","operate":[{"type":"split","value":" ","index":8}]}]},{"name":"FpsVlmNodeFs","alias":"node fps","filter":{"type":"contains","pattern":"node fps:"},"xAxis":{"operate":[{"type":"split","value":",","index":0}]},"yAxis":[{"name":"fps","comment":"node fps:","operate":[{"type":"split","value":" ","index":7}]}]},{"name":"OneFrameVlmPrecess","alias":"precess time111:","filter":{"type":"contains","pattern":"precess time111:"},"xAxis":{"operate":[{"type":"split","value":",","index":0}]},"yAxis":[{"name":"precess","comment":"precess time111:","operate":[{"type":"split","value":" ","index":7}]}]},{"name":"OneFrameVlmGenerateInfer","alias":"generate_infer time:","filter":{"type":"contains","pattern":"generate_infer time:"},"xAxis":{"operate":[{"type":"split","value":",","index":0}]},"yAxis":[{"name":"generate_infer","comment":"generate_infer time:","operate":[{"type":"split","value":" ","index":7}]}]}]}', '', 'guosongduo', '2025-04-09 11:22:57.703979', 'guosongduo', '2025-04-10 14:07:22.424959', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:aeb_lidar_obstacle', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebLidarObstacle","alias":"Topic: /perception/aeb_lidar_obstacle_array","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-10 12:03:30.000000', 'sys', '2025-04-10 12:03:30.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:aeb_rv_obstacle', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebRvObstacle","alias":"Topic: /perception/aeb_rv_obstacle_array","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-10 12:03:30.000000', 'sys', '2025-04-10 12:03:30.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:aeb_front_rslidar_points', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-10 12:03:30.000000', 'sys', '2025-04-10 12:03:30.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:aeb_cam_front_120', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-10 12:03:30.000000', 'sys', '2025-04-10 12:03:30.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('select.gaode.planRoutePath', '[{
	"label": "线路9：0430 有无图一体：90%无图+隧道+主辅切换",
	"value": "route9",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=B0H31AY4WC-from&from[name]=绿地创智中心&from[lnglat]=121.215596,30.341717&from[modxy]=121.215852,30.341863&from[poitype]=120201&from[adcode]=330282&to[id]=B0H31AY4WC-to&to[name]=绿地创智中心&to[lnglat]=121.215596,30.341717&to[modxy]=121.215852,30.341863&to[poitype]=120201&to[adcode]=330282&via[0][id]=regeo_1744264440846&via[0][name]=连江路&via[0][lnglat]=121.223682,30.328393&via[0][adcode]=330282&via[0][modxy]=&via[1][id]=regeo_1744272659397&via[1][name]=中兴一路&via[1][lnglat]=121.209161,30.323129&via[1][adcode]=330282&via[1][modxy]=&via[2][name]=宁波市博华路&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.199399,30.310146&via[2][modxy]=&via[3][name]=宁波市滨海六路&via[3][id]=&via[3][adcode]=330282&via[3][poitype]=&via[3][lnglat]=121.193196,30.335946&via[3][modxy]=&via[4][name]=宁波市越耕路&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.191696,30.348548&via[4][modxy]=&via[5][name]=宁波市水云边路&via[5][id]=&via[5][adcode]=330282&via[5][poitype]=&via[5][lnglat]=121.211218,30.350784&via[5][modxy]="
}, {
	"label": "线路8：0430 有无图一体：小路+VRU+隧道+主辅切换+有图S弯",
	"value": "route8",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=regeo_1744084694587-from&from[name]=海浦路&from[lnglat]=121.215854,30.341298&from[adcode]=330282&from[modxy]=&to[id]=regeo_1744084718613-to&to[name]=海浦路&to[lnglat]=121.215906,30.340819&to[adcode]=330282&to[modxy]=&via[0][id]=regeo_1744085243702&via[0][name]=水云边路&via[0][lnglat]=121.205992,30.350572&via[0][adcode]=330282&via[0][modxy]=&via[1][name]=宁波市滨海六路辅路&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.2218,30.339032&via[1][modxy]=&via[2][id]=regeo_1744336786255&via[2][name]=滨海四路&via[2][lnglat]=121.243254,30.334976&via[2][adcode]=330282&via[2][modxy]=&via[3][name]=宁波市金合路&via[3][id]=&via[3][adcode]=330282&via[3][poitype]=&via[3][lnglat]=121.231594,30.325084&via[3][modxy]=&via[4][name]=宁波市海沧路&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.226001,30.318053&via[4][modxy]=&via[5][id]=regeo_1744088777903&via[5][name]=连江路&via[5][lnglat]=121.224293,30.32846&via[5][adcode]=330282&via[5][modxy]="
}, {
	"label": "线路7：0430 有无图一体：小路+VRU+隧道+主辅切换",
	"value": "route7",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=regeo_1744084694587-from&from[name]=海浦路&from[lnglat]=121.215854,30.341298&from[adcode]=330282&from[modxy]=&to[id]=regeo_1744084718613-to&to[name]=海浦路&to[lnglat]=121.215906,30.340819&to[adcode]=330282&to[modxy]=&via[0][id]=regeo_1744085243702&via[0][name]=水云边路&via[0][lnglat]=121.205992,30.350572&via[0][adcode]=330282&via[0][modxy]=&via[1][name]=宁波市滨海六路辅路&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.2218,30.339032&via[1][modxy]=&via[2][name]=宁波市贵安路&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.232366,30.330706&via[2][modxy]=&via[3][name]=宁波市金合路&via[3][id]=&via[3][adcode]=330282&via[3][poitype]=&via[3][lnglat]=121.231594,30.325084&via[3][modxy]=&via[4][name]=宁波市海沧路&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.226001,30.318053&via[4][modxy]=&via[5][id]=regeo_1744088777903&via[5][name]=连江路&via[5][lnglat]=121.224293,30.32846&via[5][adcode]=330282&via[5][modxy]="
}, {
	"label": "线路6：0430 有无图一体：小路+VRU",
	"value": "route6",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[name]=浙江省宁波市慈溪市博湾路159号&from[lnglat]=121.21583819389346,30.341332605258&from[id]=1744015349000-from&to[name]=浙江省宁波市慈溪市博湾路159号&to[lnglat]=121.21550694108011,30.341150890894564&to[id]=1744015349000-to&via[0][name]=宁波市滨海六路辅路&via[0][id]=&via[0][adcode]=330282&via[0][poitype]=&via[0][lnglat]=121.221875,30.339058&via[0][modxy]=&via[1][name]=宁波市贵安路&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.233091,30.330809&via[1][modxy]=&via[2][name]=宁波市滨海二路&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.231594,30.32494&via[2][modxy]=&via[3][id]=regeo_1744015546927&via[3][name]=滨海二路&via[3][lnglat]=121.222188,30.322636&via[3][adcode]=330282&via[3][modxy]=&via[4][name]=宁波市连江路&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.22376,30.328371&via[4][modxy]="
}, {
	"label": "路线5：0430 无图：隧道+桥，纯无图方案",
	"value": "route5",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=regeo_1743998763443&from[name]=海浦路&from[lnglat]=121.215831,30.341305&from[adcode]=330282&from[modxy]=&to[id]=regeo_1743996160160-to&to[name]=海浦路&to[lnglat]=121.21584,30.341409&to[adcode]=330282&to[modxy]=&via[0][id]=regeo_1743996302441&via[0][name]=滨海七路&via[0][lnglat]=121.212685,30.344847&via[0][adcode]=330282&via[0][modxy]=&via[1][id]=regeo_1743996289955&via[1][name]=水云边路&via[1][lnglat]=121.215021,30.350249&via[1][adcode]=330282&via[1][modxy]="
}, {
	"label": "路线4：0330demo 演示路线：绿地B座-吉利研究院",
	"value": "route4",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[name]=宁波市海浦路&from[id]=menufrom-from&from[adcode]=330282&from[poitype]=&from[lnglat]=121.21579,30.34098&from[modxy]=&to[name]=宁波市滨海四路&to[id]=menuto-to&to[adcode]=330282&to[poitype]=&to[lnglat]=121.247121,30.334918&to[modxy]=&via[0][id]=regeo_1743416238455&via[0][name]=滨海四路&via[0][lnglat]=121.222533,30.333064&via[0][adcode]=330282&via[0][modxy]=&via[1][id]=regeo_1743416381360&via[1][name]=滨海二路&via[1][lnglat]=121.228901,30.323347&via[1][adcode]=330282&via[1][modxy]=&via[2][id]=regeo_1743416309896&via[2][name]=芦苇路&via[2][lnglat]=121.234717,30.330372&via[2][adcode]=330282&via[2][modxy]="
}, {
	"label": "路线3：0330demo 演示路线：吉利研究院-吉利研究院",
	"value": "route3",
	"gaodeUrl": "https://www.amap.com/dir?from[id]=regeo_1743415851589-from&from[name]=滨海四路&from[lnglat]=121.248547,30.335151&from[adcode]=330282&from[modxy]=&to[id]=regeo_1743415855888-to&to[name]=滨海四路&to[lnglat]=121.248455,30.334947&to[adcode]=330282&to[modxy]=&via[0][name]=宁波市金源大道&via[0][id]=&via[0][adcode]=330282&via[0][poitype]=&via[0][lnglat]=121.228556,30.334419&via[0][modxy]=&via[1][name]=宁波市滨海六路&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.221671,30.339299&via[1][modxy]=&via[2][name]=宁波市滨海大道&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.219192,30.333135&via[2][modxy]=&via[3][id]=regeo_1743415833641&via[3][name]=芦汀路&via[3][lnglat]=121.224008,30.330894&via[3][adcode]=330282&via[3][modxy]=&via[4][id]=regeo_1743415919334&via[4][name]=滨海二路&via[4][lnglat]=121.232655,30.323811&via[4][adcode]=330282&via[4][modxy]=&type=car&policy=1"
}, {
	"label": "路线2：0315demo演示路线",
	"value": "route2",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=regeo_1741599585793&from[name]=海浦路&from[lnglat]=121.215836,30.341018&from[adcode]=330282&from[modxy]=&to[id]=regeo_1741599563759-to&to[name]=海浦路&to[lnglat]=121.216224,30.341103&to[adcode]=330282&to[modxy]=&via[0][name]=宁波市连江路&via[0][id]=&via[0][adcode]=330282&via[0][poitype]=&via[0][lnglat]=121.221769,30.328108&via[0][modxy]=&via[1][id]=regeo_1741599500360&via[1][name]=芦汀路&via[1][lnglat]=121.225307,30.323832&via[1][adcode]=330282&via[1][modxy]=&via[2][name]=宁波市金源大道&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.228522,30.332802&via[2][modxy]=&via[3][id]=regeo_1741599538143&via[3][name]=滨海四路&via[3][lnglat]=121.242805,30.334789&via[3][adcode]=330282&via[3][modxy]=&via[4][name]=宁波市金源大道&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.22867,30.335955&via[4][modxy]=&via[5][name]=宁波市滨海五路&via[5][id]=&via[5][adcode]=330282&via[5][poitype]=&via[5][lnglat]=121.219768,30.335379&via[5][modxy]="
}, {
	"label": "路线1：1230demo路线",
	"value": "route1",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[name]=宁波市滨海四路&from[id]=menufrom-from&from[adcode]=330282&from[poitype]=&from[lnglat]=121.24513,30.335071&from[modxy]=&to[id]=regeo_1733911135884-to&to[name]=滨海四路&to[lnglat]=121.245183,30.334787&to[adcode]=330282&to[modxy]=&via[0][name]=宁波市海浦路&via[0][id]=&via[0][adcode]=330282&via[0][poitype]=&via[0][lnglat]=121.216018,30.341131&via[0][modxy]=&via[1][name]=宁波市滨海大道&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.219962,30.328377&via[1][modxy]=&via[2][name]=宁波市芦汀路&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.224966,30.325673&via[2][modxy]=&via[3][name]=宁波市中兴一路&via[3][id]=&via[3][adcode]=330282&via[3][poitype]=&via[3][lnglat]=121.2101,30.317698&via[3][modxy]=&via[4][name]=宁波市滨海大道&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.224442,30.309252&via[4][modxy]=&via[5][name]=宁波市滨海二路&via[5][id]=&via[5][adcode]=330282&via[5][poitype]=&via[5][lnglat]=121.228724,30.323299&via[5][modxy]="
}]', '', 'guosongduo', '2025-04-11 14:43:55.107899', 'guosongduo', '2025-04-14 10:05:59.070757', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_e2e_fps_monitor', '{"name":"e2e fps monitor","chart_config":[{"name":"FpsMonitorStaticObstacle","alias":"Topic: /perception/detection/static_obstacle_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/static_obstacle_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"OneFrameE2EObstacle","alias":"obstacle_process_time","filter":{"type":"contains","pattern":"One frame total time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"obstacle_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"OneFrameE2ETrafficLight","alias":"traffic_light_single_frame_process_time","filter":{"type":"contains","pattern":"tl infer time cost:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"traffic_light_single_frame_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","filter":{"type":"contains","pattern":"Topic: /perception/traffic_lights"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_rviz"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","filter":{"type":"contains","pattern":"Topic: /perception/lane_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar0_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar1_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar2_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar3_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar4_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_120/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_30/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_70/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_rslidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","filter":{"type":"contains","pattern":"Topic: /localization/localization_estimate"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '', 'guosongduo', '2025-03-11 09:13:51.121295', 'guosongduo', '2025-03-21 13:33:29.313125', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_mdriver_fps_monitor', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverMworldArray","alias":"Topic: /mdriver/mworld_array_result","filter":{"type":"contains","pattern":"Topic: /mdriver/mworld_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMdriverPlanningEnvInfo","alias":"Topic: /planning/env_info","filter":{"type":"contains","pattern":"Topic: /planning/env_info"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMdriverObstacleArray","alias":"Topic: /perception/tracking/obstacle_array_result","filter":{"type":"contains","pattern":"Topic: /perception/tracking/obstacle_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"OneFrameMdriverProcess","alias":"mdriver callback time cost:","filter":{"type":"contains","pattern":"callback time cost:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"mdriver single_frame_process_time","operate":[{"type":"split","value":" ","index":8}]}]}]}', '', 'guosongduo', '2025-03-11 16:16:56.397270', 'guosongduo', '2025-03-19 20:34:14.937525', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_all_fps_monitor', '{"name":"all fps monitor","chart_config":[
{"name":"FpsAllCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFrontRight100","alias":"Topic: /sensor/cam_front_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFrontLeft100","alias":"Topic: /sensor/cam_front_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamBack70","alias":"Topic: /sensor/cam_back_70/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_70/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFront30","alias":"Topic: /sensor/cam_front_30/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_30/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamLeft200","alias":"Topic: /sensor/cam_left_200/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_left_200/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamRight200","alias":"Topic: /sensor/cam_right_200/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_right_200/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFront200","alias":"Topic: /sensor/cam_front_200/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_200/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFront120","alias":"Topic: /sensor/cam_front_120/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_120/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamBack200","alias":"Topic: /sensor/cam_back_200/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_200/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}


', '', 'guosongduo', '2025-03-11 16:31:24.458196', 'guosongduo', '2025-03-11 16:31:24.458234', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_control_fps_monitor', '{"name":"control fps monitor","chart_config":[{"name":"FpsControlVehicleReport","alias":"Topic: /sensor/vehicle_report_common","filter":{"type":"contains","pattern":"Topic: /sensor/vehicle_report_common"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"OneFrameControlProcess","alias":"Control Processing time:","filter":{"type":"contains","pattern":"Processing time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"control single_frame_process_time","operate":[{"type":"split","value":" ","index":7}]}]}]}', '', 'guosongduo', '2025-03-11 17:24:18.854076', 'guosongduo', '2025-03-19 20:34:00.996710', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_pilot_perception_map_fps_monitor', '{"name":"pilot_perception_map fps monitor","chart_config":[
{"name":"FpsPilotPerceptionMapMerger","alias":"Topic: /map/map_merger","filter":{"type":"contains","pattern":"Topic: /map/map_merger"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsPilotPerceptionMap","alias":"Topic: /perception/perception_map","filter":{"type":"contains","pattern":"Topic: /perception/perception_map"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}', '', 'guosongduo', '2025-03-11 18:15:09.688778', 'guosongduo', '2025-03-11 18:36:08.239616', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('select.processInner.moduleList', '["pilot_perceptio",
"pilot_planning_",
"localization_no",
"environment_mod",
"e2e_perceptor_n",
"mdriver_nodes_e",
"perception_fusi",
"control_node_ex"]', '', 'guosongduo', '2025-03-17 18:22:48.557243', 'guosongduo', '2025-03-17 18:22:48.557243', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:common:comm_e2e_fps_monitor', '{"name":"e2e fps monitor","chart_config":[{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorStaticObstacle","alias":"Topic: /perception/detection/static_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]}]}
', '', 'guosongduo', '2025-03-19 18:59:23.304455', 'guosongduo', '2025-03-19 18:59:23.304515', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:common:comm_mdriver_fps_monitor', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverMworldArray","alias":"Topic: /mdriver/mworld_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMdriverPlanningEnvInfo","alias":"Topic: /planning/env_info","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMdriverObstacleArray","alias":"Topic: /perception/tracking/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]}]}
', '', 'guosongduo', '2025-03-19 18:59:42.875142', 'guosongduo', '2025-03-19 18:59:42.875195', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:common:comm_control_fps_monitor', '{"name":"control fps monitor","chart_config":[{"name":"FpsControlVehicleReport","alias":"Topic: /sensor/vehicle_report_common","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]}]}
', '', 'guosongduo', '2025-03-19 18:59:56.349877', 'guosongduo', '2025-03-19 18:59:56.349921', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:comm_e2e', '{"name":"e2e single_process view","chart_config":[{"name":"OneFrameE2EObstacle","alias":"obstacle_process_time","yAxis":[{"name":"process_time","comment":"obstacle_process_time (ms)"}]},{"name":"OneFrameE2ETrafficLight","alias":"traffic_light_single_frame_process_time","yAxis":[{"name":"process_time","comment":"traffic_light_single_frame_process_time (ms)"}]}]}', '', 'guosongduo', '2025-03-19 19:36:28.015956', 'guosongduo', '2025-03-22 16:19:19.361378', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:comm_mdriver', '{"name":"mdriver single_process view","chart_config":[{"name":"OneFrameMdriverProcess","alias":"mdriver callback time cost","yAxis":[{"name":"process_time","comment":"mdriver single_frame_process_time  (ms)"}]}]}', '', 'guosongduo', '2025-03-19 19:36:10.167503', 'guosongduo', '2025-03-22 16:19:19.273074', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_e2e_perceptor', '{"name":"e2e_perceptor log parser","chart_config":[{"name":"OneFrameE2EObstacle","alias":"obstacle_process_time","filter":{"type":"contains","pattern":"One frame total time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"obstacle_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"OneFrameE2ETrafficLight","alias":"traffic_light_single_frame_process_time","filter":{"type":"contains","pattern":"tl infer time cost:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"traffic_light_single_frame_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","filter":{"type":"contains","pattern":"Topic: /localization/localization_estimate"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_rviz_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleTimestamp","alias":"Topic: /perception/detection/obstacle_timestamp_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_timestamp_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","filter":{"type":"contains","pattern":"Topic: /perception/lane_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","filter":{"type":"contains","pattern":"Topic: /perception/traffic_lights_3in1_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_70/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_120/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_30/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_rslidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar0_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar1_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar2_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar3_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar4_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsE2ePerceptorMapInputSync","alias":"Topic: /debug/profile/map_input_sync","filter":{"type":"contains","pattern":"Topic: /debug/profile/map_input_sync"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 10:11:59.000000', 'guosongduo', '2025-04-09 20:13:56.580334', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_static_obstacle', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorStaticObstacle","alias":"Topic: /perception/detection/static_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 12:01:07.000000', 'sys', '2025-03-24 12:01:07.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:cpu:temperature', '{"name":"CPU温度解析","chart_config":[{"name":"DataviewCpuTemperature","alias":"CPU 温度监控","yAxis":[{"name":"package","comment":"Package"},{"name":"core_0","comment":"Core 0(°C)"},{"name":"core_4","comment":"Core 4(°C)"},{"name":"core_8","comment":"Core 8(°C)"},{"name":"core_12","comment":"Core 12(°C)"},{"name":"core_16","comment":"Core 16(°C)"},{"name":"core_20","comment":"Core 20(°C)"},{"name":"core_24","comment":"Core 24(°C)"},{"name":"core_28","comment":"Core 28(°C)"},{"name":"core_32","comment":"Core 32(°C)"},{"name":"core_33","comment":"Core 33(°C)"},{"name":"core_34","comment":"Core 34(°C)"},{"name":"core_35","comment":"Core 35(°C)"},{"name":"core_36","comment":"Core 36(°C)"},{"name":"core_37","comment":"Core 37(°C)"},{"name":"core_38","comment":"Core 38(°C)"},{"name":"core_39","comment":"Core 39(°C)"},{"name":"core_40","comment":"Core 40(°C)"},{"name":"core_41","comment":"Core 41(°C)"},{"name":"core_42","comment":"Core 42(°C)"},{"name":"core_43","comment":"Core 43(°C)"},{"name":"core_44","comment":"Core 44(°C)"},{"name":"core_45","comment":"Core 45(°C)"},{"name":"core_46","comment":"Core 46(°C)"},{"name":"core_47","comment":"Core 47(°C)"}]}]}', '', 'guosongduo', '2025-03-25 16:55:22.472976', 'guosongduo', '2025-03-25 16:57:05.903120', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_rear_left', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarRearLeft","alias":"Topic: /sensor/radar_rear_left","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_rear_right', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarRearRight","alias":"Topic: /sensor/radar_rear_right","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_front_left', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarFrontLeft","alias":"Topic: /sensor/radar_front_right","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_front_right', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarFrontRight","alias":"Topic: /sensor/radar_front_left","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_localization_estimate', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarLocalization","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_fusion_obstacle_array', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionFusionObstacleArray","alias":"Topic: /perception/fusion/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_front_middle', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarFrontMiddle","alias":"Topic: /sensor/radar_front_middle","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_vehicle_report_common', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionVehicleReportCommon","alias":"Topic: /sensor/vehicle_report_common","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_detection_obstacle_array', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionDetectionObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_aeb_perceptor', '{"name": "aeb_perceptor log parser", "chart_config": [{"name": "FpsAebLidarObstacle", "alias": "Topic: /perception/aeb_lidar_obstacle_array", "filter": {"type": "contains", "pattern": "Topic: /perception/aeb_lidar_obstacle_array"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAebRvObstacle", "alias": "Topic: /perception/aeb_rv_obstacle_array", "filter": {"type": "contains", "pattern": "Topic: /perception/aeb_rv_obstacle_array"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAebFrontRslidar", "alias": "Topic: /sensor/front_rslidar_points", "filter": {"type": "contains", "pattern": "Topic: /sensor/front_rslidar_points"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAebCamFront120", "alias": "Topic: /sensor/cam_front_120/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_120/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-04-10 12:05:34.000000', 'sys', '2025-04-10 12:05:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:aeb_perceptor', '{"name": "aeb_perceptor file_fps viewer", "chart_config": [{"name": "FpsAebLidarObstacle", "alias": "Topic: /perception/aeb_lidar_obstacle_array", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAebRvObstacle", "alias": "Topic: /perception/aeb_rv_obstacle_array", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAebFrontRslidar", "alias": "Topic: /sensor/front_rslidar_points", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAebCamFront120", "alias": "Topic: /sensor/cam_front_120/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-04-10 12:05:34.000000', 'sys', '2025-04-10 12:05:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_back70', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeBack70","alias":"Topic: decode/sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_back_left_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeBack71","alias":"Topic: decode/sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_back_right_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeBack72","alias":"Topic: decode/sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_front_120', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeFront120","alias":"Topic: decode/sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_front_30', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeFront30","alias":"Topic: decode/sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_front_left_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeFrontLeft100","alias":"Topic: decode/sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_front_right_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeFrontRight100","alias":"Topic: decode/sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:e2e_failsafe', '{"name": "e2e_failsafe file_fps viewer", "chart_config": [{"name": "FpsE2eFailsafeCamBack70", "alias": "Topic: /failsafe/cam_back_70", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamBackLeft100", "alias": "Topic: /failsafe/cam_back_left_100", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamBackRight100", "alias": "Topic: /failsafe/cam_back_right_100", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamFront120", "alias": "Topic: /failsafe/cam_front_120", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamFront30", "alias": "Topic: /failsafe/cam_front_30", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamFrontLeft100", "alias": "Topic: /failsafe/cam_front_left_100", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamFrontRight100", "alias": "Topic: /failsafe/cam_front_right_100", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorBack70", "alias": "Topic: /sensor/cam_back_70/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorBackLeft100", "alias": "Topic: /sensor/cam_back_left_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorBackRight100", "alias": "Topic: /sensor/cam_back_right_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorFront120", "alias": "Topic: /sensor/cam_front_120/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorFront30", "alias": "Topic: /sensor/cam_front_30/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorFrontLeft100", "alias": "Topic: /sensor/cam_front_left_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorFrontRight100", "alias": "Topic: /sensor/cam_front_right_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_e2e_failsafe', '{"name": "e2e_failsafe log parser", "chart_config": [{"name": "FpsE2eFailsafeCamBack70", "alias": "Topic: /failsafe/cam_back_70", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_back_70"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamBackLeft100", "alias": "Topic: /failsafe/cam_back_left_100", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_back_left_100"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamBackRight100", "alias": "Topic: /failsafe/cam_back_right_100", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_back_right_100"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamFront120", "alias": "Topic: /failsafe/cam_front_120", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_front_120"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamFront30", "alias": "Topic: /failsafe/cam_front_30", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_front_30"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamFrontLeft100", "alias": "Topic: /failsafe/cam_front_left_100", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_front_left_100"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamFrontRight100", "alias": "Topic: /failsafe/cam_front_right_100", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_front_right_100"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorBack70", "alias": "Topic: /sensor/cam_back_70/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_70/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorBackLeft100", "alias": "Topic: /sensor/cam_back_left_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_left_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorBackRight100", "alias": "Topic: /sensor/cam_back_right_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_right_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorFront120", "alias": "Topic: /sensor/cam_front_120/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_120/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorFront30", "alias": "Topic: /sensor/cam_front_30/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_30/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorFrontLeft100", "alias": "Topic: /sensor/cam_front_left_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_left_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorFrontRight100", "alias": "Topic: /sensor/cam_front_right_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_right_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');


INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('select.onlinetop.carlist', '[{"label":"Z15","value":"z15"},{"label":"Z17","value":"z17"},{"label":"Z18","value":"z18"},{"label":"Z18_EV","value":"z18_ev"},{"label":"Z19","value":"z19"}]', '', 'guosongduo', '2025-03-06 22:18:29.918501', 'guosongduo', '2025-03-23 20:55:06.650004', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('select.onlinetop.progresslist', '["/home/<USER>/zeta-sdk/output/lib/e2e_perceptor_node/e2e_perceptor_node_exe","/home/<USER>/zeta-sdk/output/lib/rslidar_node/rslidar_node_exe","/home/<USER>/zeta-sdk/output/lib/control_node/control_node_exe","/home/<USER>/zeta-sdk/output/lib/planning_obstacle_visualize_node/planning_obstacle_visualize_node_exe","/home/<USER>/zeta-sdk/output/lib/pilot_planning_node/pilot_planning_multithread","/home/<USER>/zeta-sdk/output/lib/radar_node_for_z10/radar_node_for_z10_exe","/home/<USER>/zeta-sdk/output/lib/perception_fusion_node/perception_fusion_node_exe","/home/<USER>/zeta-sdk/output/lib/freq_monitor_node/freq_monitor_node_exe","/home/<USER>/zeta-sdk/output/lib/vehicle_data_recorder/vehicle_data_recorder_exe","/home/<USER>/zeta-sdk/output/lib/routing_node/routing_node_exe","/home/<USER>/zeta-sdk/output/lib/mdriver_nodes/mdriver_nodes_exe","/home/<USER>/zeta-sdk/output/lib/environment_model_node/environment_model_node_exe","/home/<USER>/zeta-sdk/output/lib/pilot_perception_map_e171_node/pilot_perception_map_e171_node","/home/<USER>/zeta-sdk/output/lib/localization_nodes/localization_nodes_exe","/home/<USER>/zeta-sdk/output/lib/local_map_node/local_map_node_exe","/home/<USER>/zeta-sdk/output/lib/hmi_proxy/pilot_proxy_node","/home/<USER>/zeta-sdk/output/lib/gnss_nodes/gnss_node_exe","/home/<USER>/zeta-sdk/output/lib/planning_visualize_pub_nodes/planning_visualize_pub_nodes_exe","/home/<USER>/zeta-sdk/output/lib/map_visualize/map_visualize_exe","/home/<USER>/zeta-sdk/output/lib/innovusion_sdk/innovusion_sdk_pointcloud_exe","/home/<USER>/zeta-sdk/output/lib/bywire_node/bywire_node","/home/<USER>/zeta-sdk/output/lib/perception_obstacle_visualize/perception_obstacle_visualize_exe","/home/<USER>/zeta-sdk/output/lib/routing_gaode_node/routing_gaode_node_exe","/home/<USER>/zeta-sdk/output/lib/hesai_ros_driver/hesai_ros_driver_node","/home/<USER>/zeta-sdk/output/lib/e2e_occ_node/e2e_occ_node_exe","/home/<USER>/zeta-sdk/output/lib/environment_model_node/environment_model_multithread","/home/<USER>/zeta-sdk/output/lib/e2e_failsafe_node/e2e_failsafe_node_exe"]', '', 'guosongduo', '2025-03-07 10:07:34.006951', 'liuhongyan', '2025-04-02 09:02:31.650725', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_gpu_nvidia_smi', '{"name":"Gpu日志解析","group_sign":"timestamp, memory.total","chart_config":[{"name":"GpuNvidiaSmiLog","filter":{"type":"contains","pattern":"MiB,"},"xAxis":{"group_idx":1,"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"/","newValue":"-"}]},"yAxis":[{"name":"memory_total_1","comment":"memory.total [MiB] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":2}]},{"name":"memory_used_1","comment":"memory.used [MiB] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":4}]},{"name":"memory_free_1","comment":"memory.free [MiB] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":6}]},{"name":"utilization_gpu_1","comment":"utilization.gpu [%] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"utilization_memory_1","comment":"utilization.memory [%] 1","group_idx":1,"operate":[{"type":"split","value":" ","index":10}]},{"name":"temperature_gpu_1","comment":"temperature.gpu 1","group_idx":1,"operate":[{"type":"split","value":" ","index":12}]},{"name":"memory_total_2","comment":"memory.total [MiB] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":2}]},{"name":"memory_used_2","comment":"memory.used [MiB] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":4}]},{"name":"memory_free_2","comment":"memory.free [MiB] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":6}]},{"name":"utilization_gpu_2","comment":"utilization.gpu [%] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":8}]},{"name":"utilization_memory_2","comment":"utilization.memory [%] 2","group_idx":2,"operate":[{"type":"split","value":" ","index":10}]},{"name":"temperature_gpu_2","comment":"temperature.gpu 2","group_idx":2,"operate":[{"type":"split","value":" ","index":12}]}]}]}', 'GPU日志解析配置', 'guosongduo', '2025-03-07 10:07:34.006951', 'guosongduo', '2025-03-07 10:07:34.006951', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_innovusion_fps_monitor', '{"name":"innovusion fps monitor","chart_config":[
{"name":"FpsInnovusionLidar","alias":"Topic: /sensor/front_lidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_lidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}', '', 'guosongduo', '2025-03-11 18:00:39.240741', 'guosongduo', '2025-03-11 18:00:39.240794', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_proxy_radar_fps_monitor', '{"name":"proxy radar fps monitor","chart_config":[
{"name":"FpsProxyRadar0","alias":"Topic: /sensor/radar0_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar0_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsProxyRadar1","alias":"Topic: /sensor/radar1_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar1_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsProxyRadar2","alias":"Topic: /sensor/radar2_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar2_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsProxyRadar3","alias":"Topic: /sensor/radar3_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar3_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsProxyRadar4","alias":"Topic: /sensor/radar4_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar4_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}', '', 'guosongduo', '2025-03-11 18:08:20.174320', 'guosongduo', '2025-03-11 18:08:20.174390', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:pilot_planning', '{"name":"pilot_planning single_process view","chart_config":[{"name":"OneFramePilotPlanningProcessTime","alias":"pilot planning Processing time","yAxis":[{"name":"process_time","comment":"pilot planning single_frame_process_time  (ms)"}]},{"name":"OneFramePilotPlanningSensor2Planning","alias":"pilot planning sesor to planning time","yAxis":[{"name":"process_time","comment":"pilot planning sensor_to_planning_output_time  (ms)"}]},{"name":"OneFramePilotPlanningPrediction","alias":"pilot planning prediction","yAxis":[{"name":"process_time","comment":"pilot planning pred delay  (ms)"}]},{"name":"PilotPlanningThreadTimeSpeed","alias":"PlanningThread time spend: curr: 0.261000 ms, PlanningThread average: 0.251413 ms max: 0.693000 ms","yAxis":[{"name":"curr","comment":"Current Speed(ms)"},{"name":"average","comment":"Average(ms)"},{"name":"max","comment":"Max(ms)"}]}]}', '', 'guosongduo', '2025-03-19 19:00:29.467906', 'guosongduo', '2025-03-29 10:27:46.992707', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_environment_model', '{"name":"environment_monitor monitor","chart_config":[{"name":"OneFrameEnvironmentModelProcess","alias":"Control Processing time:","filter":{"type":"contains","pattern":"NavigatorThread time spend: curr"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"environment model single_frame_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"FpsEnvTrafficLight","alias":"Topic: /perception/traffic_lights_3in1_result","filter":{"type":"contains","pattern":"Topic: /perception/traffic_lights_3in1_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsEnvPlanningEnvInfo","alias":"Topic: /planning/env_info","filter":{"type":"contains","pattern":"Topic: /planning/env_info"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '', 'guosongduo', '2025-03-19 19:05:51.016922', 'guosongduo', '2025-03-28 21:57:07.243506', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:comm_control_fps_monitor', '{"name":"control sigle frame monitor","chart_config":[{"name":"OneFrameControlProcess","alias":"control_single_frame_processing_time","yAxis":[{"name":"process_time","comment":"control_single_frame_processing_time"}]}]}
', '', 'guosongduo', '2025-03-19 19:29:17.093867', 'guosongduo', '2025-03-19 19:29:17.093936', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:all', '{"name": "all file_fps viewer", "chart_config": [{"name": "FpsAllCamBack200", "alias": "Topic: /sensor/cam_back_200/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamBack70", "alias": "Topic: /sensor/cam_back_70/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamBackLeft100", "alias": "Topic: /sensor/cam_back_left_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamBackRight100", "alias": "Topic: /sensor/cam_back_right_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFront120", "alias": "Topic: /sensor/cam_front_120/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFront200", "alias": "Topic: /sensor/cam_front_200/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFront30", "alias": "Topic: /sensor/cam_front_30/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFrontLeft100", "alias": "Topic: /sensor/cam_front_left_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamFrontRight100", "alias": "Topic: /sensor/cam_front_right_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamLeft200", "alias": "Topic: /sensor/cam_left_200/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAllCamRight200", "alias": "Topic: /sensor/cam_right_200/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:48.000000', 'sys', '2025-03-24 08:06:48.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:control', '{"name": "control file_fps viewer", "chart_config": [{"name": "FpsControlVehicleReport", "alias": "Topic: /sensor/vehicle_report_common", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:48.000000', 'sys', '2025-03-24 08:06:48.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:e2e_perceptor', '{"name":"e2e_perceptor file_fps viewer","chart_config":[{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorObstacleTimestamp","alias":"Topic: /perception/detection/obstacle_timestamp_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsE2ePerceptorMapInputSync","alias":"Topic: /debug/profile/map_input_sync","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'guosongduo', '2025-04-09 20:11:12.306617', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:gnss', '{"name": "gnss file_fps viewer", "chart_config": [{"name": "FpsGnssCorrImu", "alias": "Topic: /sensor/corr_imu", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsGnssGps", "alias": "Topic: /sensor/gps", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsGnssInsPose", "alias": "Topic: /sensor/ins_pose", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsGnssRawImu", "alias": "Topic: /sensor/raw_imu", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'sys', '2025-03-24 08:06:49.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:innovusion_lidar', '{"name":"innovusion_lidar file_fps viewer","chart_config":[{"name":"FpsInnovusionFrontLidar","alias":"Topic: /sensor/front_lidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'guosongduo', '2025-03-28 21:31:16.888617', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:localization', '{"name": "localization file_fps viewer", "chart_config": [{"name": "FpsLocalizationEstimate", "alias": "Topic: /localization/localization_estimate", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationCorrImu", "alias": "Topic: /sensor/corr_imu", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationGps", "alias": "Topic: /sensor/gps", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationInsPose", "alias": "Topic: /sensor/ins_pose", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationRawImu", "alias": "Topic: /sensor/raw_imu", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsLocalizationVehicleReport", "alias": "Topic: /sensor/vehicle_report_common", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'sys', '2025-03-24 08:06:49.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:mdriver', '{"name": "mdriver file_fps viewer", "chart_config": [{"name": "FpsMdriverMworldArray", "alias": "Topic: /mdriver/mworld_array_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsMdriverObstacleArray", "alias": "Topic: /perception/tracking/obstacle_array_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsMdriverPlanningEnvInfo", "alias": "Topic: /planning/env_info", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'sys', '2025-03-24 08:06:49.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:pilot_perception_map_e171', '{"name": "pilot_perception_map_e171 file_fps viewer", "chart_config": [{"name": "FpsPilotPerceptionMapMerger", "alias": "Topic: /map/map_merger", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsPilotPerceptionMap", "alias": "Topic: /perception/perception_map", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-24 08:06:49.000000', 'guosongduo', '2025-03-24 08:06:49.000000', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_all', '{"name": "all log parser", "chart_config": [{"name": "FpsAllCamBack200", "alias": "Topic: /sensor/cam_back_200/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_200/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamBack70", "alias": "Topic: /sensor/cam_back_70/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_70/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamBackLeft100", "alias": "Topic: /sensor/cam_back_left_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_left_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamBackRight100", "alias": "Topic: /sensor/cam_back_right_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_right_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFront120", "alias": "Topic: /sensor/cam_front_120/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_120/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFront200", "alias": "Topic: /sensor/cam_front_200/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_200/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFront30", "alias": "Topic: /sensor/cam_front_30/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_30/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFrontLeft100", "alias": "Topic: /sensor/cam_front_left_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_left_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamFrontRight100", "alias": "Topic: /sensor/cam_front_right_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_right_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamLeft200", "alias": "Topic: /sensor/cam_left_200/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_left_200/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAllCamRight200", "alias": "Topic: /sensor/cam_right_200/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_right_200/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'sys', '2025-03-24 09:00:01.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_gnss', '{"name": "gnss log parser", "chart_config": [{"name": "FpsGnssCorrImu", "alias": "Topic: /sensor/corr_imu", "filter": {"type": "contains", "pattern": "Topic: /sensor/corr_imu"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsGnssGps", "alias": "Topic: /sensor/gps", "filter": {"type": "contains", "pattern": "Topic: /sensor/gps"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsGnssInsPose", "alias": "Topic: /sensor/ins_pose", "filter": {"type": "contains", "pattern": "Topic: /sensor/ins_pose"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsGnssRawImu", "alias": "Topic: /sensor/raw_imu", "filter": {"type": "contains", "pattern": "Topic: /sensor/raw_imu"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'sys', '2025-03-24 09:00:01.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_innovusion_lidar', '{"name":"innovusion_lidar log parser","chart_config":[{"name":"FpsInnovusionFrontLidar","alias":"Topic: /sensor/front_lidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_lidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'guosongduo', '2025-03-28 21:32:21.326603', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_localization', '{"name": "localization log parser", "chart_config": [{"name": "FpsLocalizationEstimate", "alias": "Topic: /localization/localization_estimate", "filter": {"type": "contains", "pattern": "Topic: /localization/localization_estimate"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationCorrImu", "alias": "Topic: /sensor/corr_imu", "filter": {"type": "contains", "pattern": "Topic: /sensor/corr_imu"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationGps", "alias": "Topic: /sensor/gps", "filter": {"type": "contains", "pattern": "Topic: /sensor/gps"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationInsPose", "alias": "Topic: /sensor/ins_pose", "filter": {"type": "contains", "pattern": "Topic: /sensor/ins_pose"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationRawImu", "alias": "Topic: /sensor/raw_imu", "filter": {"type": "contains", "pattern": "Topic: /sensor/raw_imu"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsLocalizationVehicleReport", "alias": "Topic: /sensor/vehicle_report_common", "filter": {"type": "contains", "pattern": "Topic: /sensor/vehicle_report_common"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'sys', '2025-03-24 09:00:01.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_pilot_perception_map_e171', '{"name": "pilot_perception_map_e171 log parser", "chart_config": [{"name": "FpsPilotPerceptionMapMerger", "alias": "Topic: /map/map_merger", "filter": {"type": "contains", "pattern": "Topic: /map/map_merger"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsPilotPerceptionMap", "alias": "Topic: /perception/perception_map", "filter": {"type": "contains", "pattern": "Topic: /perception/perception_map"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'guosongduo', '2025-03-24 09:00:01.000000', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_proxy_radar', '{"name":"proxy radar fps monitor","chart_config":[{"name":"FpsProxyRadar0","alias":"Topic: /sensor/radar0_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar0_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsProxyRadar1","alias":"Topic: /sensor/radar1_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar1_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsProxyRadar2","alias":"Topic: /sensor/radar2_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar2_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsProxyRadar3","alias":"Topic: /sensor/radar3_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar3_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsProxyRadar4","alias":"Topic: /sensor/radar4_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar4_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 09:00:01.000000', 'sys', '2025-03-24 09:00:01.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_control', '{"name": "control log parser", "chart_config": [{"name":"OneFrameControlProcess","alias":"Control Processing time:","filter":{"type":"contains","pattern":"Processing time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"control single_frame_process_time","operate":[{"type":"split","value":" ","index":7}]}]},{"name": "FpsControlVehicleReport", "alias": "Topic: /sensor/vehicle_report_common", "filter": {"type": "contains", "pattern": "Topic: /sensor/vehicle_report_common"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 10:10:22.000000', 'sys', '2025-03-24 10:10:22.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_cpu_temperature', '{
	"name": "CPU温度解析",
	"group_sign": "CPU Temperatures:",
	"chart_config": [{
		"name": "DataviewCpuTemperature",
		"xAxis": { "group_idx": 0, "operate": [{ "type": "split", "value": "]", "index": 0 }, { "type": "replace", "oldValue": "[", "newValue": "" }] },
		"yAxis": [
		{"name": "package", "comment": "Package", "group_idx": 1, "operate": [{ "type": "split", "value": " ", "index": 1 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_0", "comment": "Core 0(°C)", "group_idx": 2, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_4", "comment": "Core 4(°C)", "group_idx": 3, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_8", "comment": "Core 8(°C)", "group_idx": 4, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_12", "comment": "Core 12(°C)", "group_idx": 5, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_16", "comment": "Core 16(°C)", "group_idx": 6, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_20", "comment": "Core 20(°C)", "group_idx": 7, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_24", "comment": "Core 24(°C)", "group_idx": 8, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_28", "comment": "Core 28(°C)", "group_idx": 9, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_32", "comment": "Core 32(°C)", "group_idx": 10, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_33", "comment": "Core 33(°C)", "group_idx": 11, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_34", "comment": "Core 34(°C)", "group_idx": 12, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_35", "comment": "Core 35(°C)", "group_idx": 13, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_36", "comment": "Core 36(°C)", "group_idx": 14, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_37", "comment": "Core 37(°C)", "group_idx": 15, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_38", "comment": "Core 38(°C)", "group_idx": 16, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_39", "comment": "Core 39(°C)", "group_idx": 17, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_40", "comment": "Core 40(°C)", "group_idx": 18, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_41", "comment": "Core 41(°C)", "group_idx": 19, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_42", "comment": "Core 42(°C)", "group_idx": 20, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_43", "comment": "Core 43(°C)", "group_idx": 21, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_44", "comment": "Core 44(°C)", "group_idx": 22, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_45", "comment": "Core 45(°C)", "group_idx": 23, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_46", "comment": "Core 46(°C)", "group_idx": 24, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		,{ "name": "core_47", "comment": "Core 47(°C)", "group_idx": 25, "operate": [{ "type": "split", "value": " ", "index": 2 }, { "type": "replace", "oldValue": "°C", "newValue": "" }]}
		]
	}]
}', '', 'guosongduo', '2025-03-25 16:49:53.665771', 'guosongduo', '2025-03-25 16:49:53.665935', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:perception_fusion_node', '{"name": "perception_fusion_node file_fps viewer", "chart_config": [{"name": "FpsFusionRadarRearLeft", "alias": "Topic: /sensor/radar_rear_left", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarRearRight", "alias": "Topic: /sensor/radar_rear_right", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarFrontLeft", "alias": "Topic: /sensor/radar_front_right", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarFrontRight", "alias": "Topic: /sensor/radar_front_left", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarLocalization", "alias": "Topic: /localization/localization_estimate", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionFusionObstacleArray", "alias": "Topic: /perception/fusion/obstacle_array_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionRadarFrontMiddle", "alias": "Topic: /sensor/radar_front_middle", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionVehicleReportCommon", "alias": "Topic: /sensor/vehicle_report_common", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsFusionDetectionObstacleArray", "alias": "Topic: /perception/detection/obstacle_array_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-27 04:44:59.000000', 'sys', '2025-03-27 04:44:59.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_perception_fusion_node', '{"name": "perception_fusion_node log parser", "chart_config": [{"name": "FpsFusionRadarRearLeft", "alias": "Topic: /sensor/radar_rear_left", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_rear_left"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarRearRight", "alias": "Topic: /sensor/radar_rear_right", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_rear_right"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarFrontLeft", "alias": "Topic: /sensor/radar_front_right", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_front_right"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarFrontRight", "alias": "Topic: /sensor/radar_front_left", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_front_left"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarLocalization", "alias": "Topic: /localization/localization_estimate", "filter": {"type": "contains", "pattern": "Topic: /localization/localization_estimate"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionFusionObstacleArray", "alias": "Topic: /perception/fusion/obstacle_array_result", "filter": {"type": "contains", "pattern": "Topic: /perception/fusion/obstacle_array_result"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionRadarFrontMiddle", "alias": "Topic: /sensor/radar_front_middle", "filter": {"type": "contains", "pattern": "Topic: /sensor/radar_front_middle"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionVehicleReportCommon", "alias": "Topic: /sensor/vehicle_report_common", "filter": {"type": "contains", "pattern": "Topic: /sensor/vehicle_report_common"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsFusionDetectionObstacleArray", "alias": "Topic: /perception/detection/obstacle_array_result", "filter": {"type": "contains", "pattern": "Topic: /perception/detection/obstacle_array_result"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-27 04:44:59.000000', 'sys', '2025-03-27 04:44:59.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_static_obstacle_result', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccStaticObstacleResult","alias":"Topic: /perception/detection/static_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_front_rslidar', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_localization', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccLocalization","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_back_right', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamBackRight","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_back_left', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamBackLeft","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_front_right', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamFrontRight","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:24.000000', 'sys', '2025-03-27 09:25:24.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_front_left', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'guosongduo', '2025-03-27 17:40:14.153186', 'guosongduo', '2025-03-27 17:40:14.153455', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_back_70', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:25.000000', 'sys', '2025-03-27 09:25:25.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_visualization', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccVisualization","alias":"Topic: /perception/occ/visualization","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:25.000000', 'sys', '2025-03-27 09:25:25.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_front_30', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:25.000000', 'sys', '2025-03-27 09:25:25.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_cam_front_120', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 09:25:25.000000', 'sys', '2025-03-27 09:25:25.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:e2e_occ', '{"name":"e2e_occ file_fps viewer","chart_config":[{"name":"FpsOccStaticObstacleResult","alias":"Topic: /perception/detection/static_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccDynamicObstacleResult","alias":"Topic: /perception/detection/dynamic_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccInputTimestamps","alias":"Topic: /perception/occ/input_timestamps","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccLocalization","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamBackRight","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamBackLeft","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamFrontRight","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccVisualization","alias":"Topic: /perception/occ/visualization","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]},{"name":"FpsOccCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-27 09:28:29.000000', 'guosongduo', '2025-04-08 16:10:19.451083', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_e2e_occ', '{"name":"e2e_occ log parser","chart_config":[{"name":"FpsOccStaticObstacleResult","alias":"Topic: /perception/detection/static_obstacle_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/static_obstacle_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccInputTimestamps","alias":"Topic: /perception/occ/input_timestamps","filter":{"type":"contains","pattern":"Topic: /perception/occ/input_timestamps"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_rslidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccLocalization","alias":"Topic: /localization/localization_estimate","filter":{"type":"contains","pattern":"Topic: /localization/localization_estimate"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamBackRight","alias":"Topic: /sensor/cam_back_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamBackLeft","alias":"Topic: /sensor/cam_back_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamFrontRight","alias":"Topic: /sensor/cam_front_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamBack70","alias":"Topic: /sensor/cam_back_70/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_70/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccVisualization","alias":"Topic: /perception/occ/visualization","filter":{"type":"contains","pattern":"Topic: /perception/occ/visualization"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamFront30","alias":"Topic: /sensor/cam_front_30/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_30/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccCamFront120","alias":"Topic: /sensor/cam_front_120/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_120/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsOccDynamicObstacleResult","alias":"Topic: /perception/detection/dynamic_obstacle_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/dynamic_obstacle_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '文件日志解析', 'sys', '2025-03-27 09:28:29.000000', 'guosongduo', '2025-04-08 16:10:19.549078', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:pilot_planngin_result', '{"name":"pilot_planning fps monitor","chart_config":[{"name":"FpsPlanningResult","alias":"Topic: /planning/planning_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-28 13:41:17.000000', 'sys', '2025-03-28 13:41:17.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:env_fps_traffic_light', '{"name":"environment_model fps monitor","chart_config":[{"name":"FpsEnvTrafficLight","alias":"Topic: /perception/traffic_lights_3in1_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-28 13:41:17.000000', 'sys', '2025-03-28 13:41:17.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:env_fps_env_info', '{"name":"environment_model fps monitor","chart_config":[{"name":"FpsEnvPlanningEnvInfo","alias":"Topic: /planning/env_info","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-28 13:41:17.000000', 'sys', '2025-03-28 13:41:17.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_gnss_fps_monitor', '{"name":"gnss fps monitor","chart_config":[
{"name":"FpsGnssInsPose","alias":"Topic: /sensor/ins_pose","filter":{"type":"contains","pattern":"Topic: /sensor/ins_pose"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsGnssRawImu","alias":"Topic: /sensor/raw_imu","filter":{"type":"contains","pattern":"Topic: /sensor/raw_imu"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsGnssCorrImu","alias":"Topic: /sensor/corr_imu","filter":{"type":"contains","pattern":"Topic: /sensor/corr_imu"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsGnssGps","alias":"Topic: /sensor/gps","filter":{"type":"contains","pattern":"Topic: /sensor/gps"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}


', '', 'guosongduo', '2025-03-11 17:46:27.737867', 'guosongduo', '2025-03-11 17:46:27.737909', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:control', '{"name":"control single_process view","chart_config":[{"name":"OneFrameControlProcess","alias":"Control Processing time","yAxis":[{"name":"process_time","comment":"control single_frame_process_time (ms)"}]}]}', '', 'guosongduo', '2025-03-19 19:00:43.790592', 'guosongduo', '2025-03-22 16:19:19.194831', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:environment_model', '{"name":"environment single_process view","chart_config":[{"name":"OneFrameEnvironmentModelProcess","alias":"Control Processing time ","yAxis":[{"name":"process_time","comment":"environment model single_frame_process_time （ms）"}]}]}', '', 'guosongduo', '2025-03-19 19:01:01.137658', 'guosongduo', '2025-03-22 16:19:19.103203', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_pilot_planning', '{"name":"pilot planning log monitor","chart_config":[{"name":"OneFramePilotPlanningProcessTime","alias":"pilot planning Processing time:","filter":{"type":"contains","pattern":"Processing time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"pilot planning single_frame_process_time","operate":[{"type":"split","value":" ","index":7}]}]},{"name":"OneFramePilotPlanningSensor2Planning","alias":"pilot planning sesor to planning time:","filter":{"type":"contains","pattern":"Time difference between predicition sensor and planning output"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"pilot planning sensor_to_planning_output_time","operate":[{"type":"split","value":" ","index":13}]}]},{"name":"OneFramePilotPlanningPrediction","alias":"pilot planning prediction ","filter":{"type":"contains","pattern":"pred delay :"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"pilot planning pred delay","operate":[{"type":"split","value":" ","index":8}]}]},{"name":"FpsPlanningResult","alias":"Topic: /planning/planning_result","filter":{"type":"contains","pattern":"Topic: /planning/planning_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsPilotPlanningLaneArray","alias":"Topic: /debug/env_info/lane_array","filter":{"type":"contains","pattern":"Topic: /debug/env_info/lane_array"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"PilotPlanningThreadTimeSpeed","alias":"PlanningThread time spend: curr: 0.261000 ms, PlanningThread average: 0.251413 ms max: 0.693000 ms","filter":{"type":"contains","pattern":"ms, PlanningThread average:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"curr","comment":"Current Speed(ms)","operate":[{"type":"split","value":" ","index":9}]},{"name":"average","comment":"Average(ms)","operate":[{"type":"split","value":" ","index":13}]},{"name":"max","comment":"Max(ms)","operate":[{"type":"split","value":" ","index":16}]}]}]}', '', 'guosongduo', '2025-03-19 19:08:32.752432', 'guosongduo', '2025-04-02 11:46:29.217174', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_back_200', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamBack200","alias":"Topic: /sensor/cam_back_200/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_back_70', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_back_left_100', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_back_right_100', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_120', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_200', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFront200","alias":"Topic: /sensor/cam_front_200/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_30', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_left_100', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFrontLeft100","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_front_right_100', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamFrontRight100","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_left_200', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamLeft200","alias":"Topic: /sensor/cam_left_200/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:all_fps_cam_right_200', '{"name":"all fps monitor","chart_config":[{"name":"FpsAllCamRight200","alias":"Topic: /sensor/cam_right_200/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:control_fps_vehicle_report', '{"name":"control fps monitor","chart_config":[{"name":"FpsControlVehicleReport","alias":"Topic: /sensor/vehicle_report_common","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:39.000000', 'sys', '2025-03-24 05:19:39.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_localization_estimate', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_obstacle_array', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'guosongduo', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_obstacle_rviz', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_obstacle_timestamp', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorObstacleTimestamp","alias":"Topic: /perception/detection/obstacle_timestamp_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_lane_array', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_traffic_light', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_back_70', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_back_left', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_back_right', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_front_120', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_front_30', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_front_left_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_cam_front_right_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_front_rslidar', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar0', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar1', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar2', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar3', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_radar4', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:gnss_fps_corr_imu', '{"name":"gnss fps monitor","chart_config":[{"name":"FpsGnssCorrImu","alias":"Topic: /sensor/corr_imu","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:gnss_fps_gps', '{"name":"gnss fps monitor","chart_config":[{"name":"FpsGnssGps","alias":"Topic: /sensor/gps","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:gnss_fps_ins_pose', '{"name":"gnss fps monitor","chart_config":[{"name":"FpsGnssInsPose","alias":"Topic: /sensor/ins_pose","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:gnss_fps_raw_imu', '{"name":"gnss fps monitor","chart_config":[{"name":"FpsGnssRawImu","alias":"Topic: /sensor/raw_imu","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:innovusion_lidar_se', '{"name":"innovusion_lidar fps monitor","chart_config":[{"name":"FpsInnovusionSe","alias":"Topic: /se","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'guosongduo', '2025-03-24 05:19:40.000000', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:innovusion_lidar_front_lidar_points', '{"name":"innovusion_lidar fps monitor","chart_config":[{"name":"FpsInnovusionFrontLidar","alias":"Topic: /sensor/front_lidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_localization_estimate', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationEstimate","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_corr_imu', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationCorrImu","alias":"Topic: /sensor/corr_imu","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_gps', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationGps","alias":"Topic: /sensor/gps","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:40.000000', 'sys', '2025-03-24 05:19:40.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_ins_pose', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationInsPose","alias":"Topic: /sensor/ins_pose","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_raw_imu', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationRawImu","alias":"Topic: /sensor/raw_imu","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:localization_vehicle_report', '{"name":"localization fps monitor","chart_config":[{"name":"FpsLocalizationVehicleReport","alias":"Topic: /sensor/vehicle_report_common","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:mdriver_fps_mworld_array', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverMworldArray","alias":"Topic: /mdriver/mworld_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:mdriver_fps_obtacle_array', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverObstacleArray","alias":"Topic: /perception/tracking/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:mdriver_fps_planning', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverPlanningEnvInfo","alias":"Topic: /planning/env_info","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:pilot_perception_map_fps_merger', '{"name":"pilot_perception_map_e171 fps monitor","chart_config":[{"name":"FpsPilotPerceptionMapMerger","alias":"Topic: /map/map_merger","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:pilot_perception_map_fps_perception_map', '{"name":"pilot_perception_map_e171 fps monitor","chart_config":[{"name":"FpsPilotPerceptionMap","alias":"Topic: /perception/perception_map","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_0', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar0","alias":"Topic: /sensor/radar0_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_1', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar1","alias":"Topic: /sensor/radar1_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_2', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar2","alias":"Topic: /sensor/radar2_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_3', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar3","alias":"Topic: /sensor/radar3_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:proxy_radar_fps_4', '{"name":"proxy_radar fps monitor","chart_config":[{"name":"FpsProxyRadar4","alias":"Topic: /sensor/radar4_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 05:19:41.000000', 'sys', '2025-03-24 05:19:41.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_mdriver', '{"name": "mdriver log parser", "chart_config": [{"name":"OneFrameMdriverProcess","alias":"mdriver callback time cost:","filter":{"type":"contains","pattern":"callback time cost:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"mdriver single_frame_process_time","operate":[{"type":"split","value":" ","index":8}]}]},{"name": "FpsMdriverMworldArray", "alias": "Topic: /mdriver/mworld_array_result", "filter": {"type": "contains", "pattern": "Topic: /mdriver/mworld_array_result"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsMdriverObstacleArray", "alias": "Topic: /perception/tracking/obstacle_array_result", "filter": {"type": "contains", "pattern": "Topic: /perception/tracking/obstacle_array_result"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsMdriverPlanningEnvInfo", "alias": "Topic: /planning/env_info", "filter": {"type": "contains", "pattern": "Topic: /planning/env_info"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 10:03:53.000000', 'sys', '2025-03-24 10:03:53.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:pilot_planning', '{"name": "pilot_planning file_fps viewer", "chart_config": [{"name": "FpsPilotPlanningLaneArray", "alias": "Topic: /debug/env_info/lane_array", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]},{"name": "FpsPlanningResult", "alias": "Topic: /planning/planning_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-28 13:44:04.000000', 'guosongduo', '2025-04-02 11:46:46.158223', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:environment_model', '{"name": "environment_model file_fps viewer", "chart_config": [{"name": "FpsEnvTrafficLight", "alias": "Topic: /perception/traffic_lights_3in1_result", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsEnvPlanningEnvInfo", "alias": "Topic: /planning/env_info", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-03-28 13:44:04.000000', 'sys', '2025-03-28 13:44:04.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:pilot_planning_lane_array', '{"name":"pilot_planning fps monitor","chart_config":[{"name":"FpsPilotPlanningLaneArray","alias":"Topic: /debug/env_info/lane_array","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-02 03:22:51.000000', 'sys', '2025-04-02 03:22:51.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_input_timestamps', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccInputTimestamps","alias":"Topic: /perception/occ/input_timestamps","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-02 03:22:51.000000', 'sys', '2025-04-02 03:22:51.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_map_input_sync', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsE2ePerceptorMapInputSync","alias":"Topic: /debug/profile/map_input_sync","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-02 03:22:51.000000', 'sys', '2025-04-02 03:22:51.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:occ_dynamic_obstacle_result', '{"name":"e2e_occ fps monitor","chart_config":[{"name":"FpsOccDynamicObstacleResult","alias":"Topic: /perception/detection/dynamic_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-08 08:03:51.000000', 'sys', '2025-04-08 08:03:51.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_vlm', '{"name":"vlm_ log parser","chart_config":[{"name":"OneFrameVlmLlmnode","alias":"llmnode all time:","filter":{"type":"contains","pattern":"llmnode all time:"},"xAxis":{"operate":[{"type":"split","value":",","index":0}]},"yAxis":[{"name":"llmnode","comment":"llmnode all time","operate":[{"type":"split","value":" ","index":8}]}]},{"name":"FpsVlmNodeFs","alias":"node fps","filter":{"type":"contains","pattern":"node fps:"},"xAxis":{"operate":[{"type":"split","value":",","index":0}]},"yAxis":[{"name":"fps","comment":"node fps:","operate":[{"type":"split","value":" ","index":7}]}]},{"name":"OneFrameVlmPrecess","alias":"precess time111:","filter":{"type":"contains","pattern":"precess time111:"},"xAxis":{"operate":[{"type":"split","value":",","index":0}]},"yAxis":[{"name":"precess","comment":"precess time111:","operate":[{"type":"split","value":" ","index":7}]}]},{"name":"OneFrameVlmGenerateInfer","alias":"generate_infer time:","filter":{"type":"contains","pattern":"generate_infer time:"},"xAxis":{"operate":[{"type":"split","value":",","index":0}]},"yAxis":[{"name":"generate_infer","comment":"generate_infer time:","operate":[{"type":"split","value":" ","index":7}]}]}]}', '', 'guosongduo', '2025-04-09 11:22:57.703979', 'guosongduo', '2025-04-10 14:07:22.424959', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:aeb_lidar_obstacle', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebLidarObstacle","alias":"Topic: /perception/aeb_lidar_obstacle_array","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-10 12:03:30.000000', 'sys', '2025-04-10 12:03:30.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:aeb_rv_obstacle', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebRvObstacle","alias":"Topic: /perception/aeb_rv_obstacle_array","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-10 12:03:30.000000', 'sys', '2025-04-10 12:03:30.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:aeb_front_rslidar_points', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-10 12:03:30.000000', 'sys', '2025-04-10 12:03:30.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:aeb_cam_front_120', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-10 12:03:30.000000', 'sys', '2025-04-10 12:03:30.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('select.gaode.planRoutePath', '[{
	"label": "线路9：0430 有无图一体：90%无图+隧道+主辅切换",
	"value": "route9",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=B0H31AY4WC-from&from[name]=绿地创智中心&from[lnglat]=121.215596,30.341717&from[modxy]=121.215852,30.341863&from[poitype]=120201&from[adcode]=330282&to[id]=B0H31AY4WC-to&to[name]=绿地创智中心&to[lnglat]=121.215596,30.341717&to[modxy]=121.215852,30.341863&to[poitype]=120201&to[adcode]=330282&via[0][id]=regeo_1744264440846&via[0][name]=连江路&via[0][lnglat]=121.223682,30.328393&via[0][adcode]=330282&via[0][modxy]=&via[1][id]=regeo_1744272659397&via[1][name]=中兴一路&via[1][lnglat]=121.209161,30.323129&via[1][adcode]=330282&via[1][modxy]=&via[2][name]=宁波市博华路&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.199399,30.310146&via[2][modxy]=&via[3][name]=宁波市滨海六路&via[3][id]=&via[3][adcode]=330282&via[3][poitype]=&via[3][lnglat]=121.193196,30.335946&via[3][modxy]=&via[4][name]=宁波市越耕路&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.191696,30.348548&via[4][modxy]=&via[5][name]=宁波市水云边路&via[5][id]=&via[5][adcode]=330282&via[5][poitype]=&via[5][lnglat]=121.211218,30.350784&via[5][modxy]="
}, {
	"label": "线路8：0430 有无图一体：小路+VRU+隧道+主辅切换+有图S弯",
	"value": "route8",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=regeo_1744084694587-from&from[name]=海浦路&from[lnglat]=121.215854,30.341298&from[adcode]=330282&from[modxy]=&to[id]=regeo_1744084718613-to&to[name]=海浦路&to[lnglat]=121.215906,30.340819&to[adcode]=330282&to[modxy]=&via[0][id]=regeo_1744085243702&via[0][name]=水云边路&via[0][lnglat]=121.205992,30.350572&via[0][adcode]=330282&via[0][modxy]=&via[1][name]=宁波市滨海六路辅路&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.2218,30.339032&via[1][modxy]=&via[2][id]=regeo_1744336786255&via[2][name]=滨海四路&via[2][lnglat]=121.243254,30.334976&via[2][adcode]=330282&via[2][modxy]=&via[3][name]=宁波市金合路&via[3][id]=&via[3][adcode]=330282&via[3][poitype]=&via[3][lnglat]=121.231594,30.325084&via[3][modxy]=&via[4][name]=宁波市海沧路&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.226001,30.318053&via[4][modxy]=&via[5][id]=regeo_1744088777903&via[5][name]=连江路&via[5][lnglat]=121.224293,30.32846&via[5][adcode]=330282&via[5][modxy]="
}, {
	"label": "线路7：0430 有无图一体：小路+VRU+隧道+主辅切换",
	"value": "route7",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=regeo_1744084694587-from&from[name]=海浦路&from[lnglat]=121.215854,30.341298&from[adcode]=330282&from[modxy]=&to[id]=regeo_1744084718613-to&to[name]=海浦路&to[lnglat]=121.215906,30.340819&to[adcode]=330282&to[modxy]=&via[0][id]=regeo_1744085243702&via[0][name]=水云边路&via[0][lnglat]=121.205992,30.350572&via[0][adcode]=330282&via[0][modxy]=&via[1][name]=宁波市滨海六路辅路&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.2218,30.339032&via[1][modxy]=&via[2][name]=宁波市贵安路&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.232366,30.330706&via[2][modxy]=&via[3][name]=宁波市金合路&via[3][id]=&via[3][adcode]=330282&via[3][poitype]=&via[3][lnglat]=121.231594,30.325084&via[3][modxy]=&via[4][name]=宁波市海沧路&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.226001,30.318053&via[4][modxy]=&via[5][id]=regeo_1744088777903&via[5][name]=连江路&via[5][lnglat]=121.224293,30.32846&via[5][adcode]=330282&via[5][modxy]="
}, {
	"label": "线路6：0430 有无图一体：小路+VRU",
	"value": "route6",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[name]=浙江省宁波市慈溪市博湾路159号&from[lnglat]=121.21583819389346,30.341332605258&from[id]=1744015349000-from&to[name]=浙江省宁波市慈溪市博湾路159号&to[lnglat]=121.21550694108011,30.341150890894564&to[id]=1744015349000-to&via[0][name]=宁波市滨海六路辅路&via[0][id]=&via[0][adcode]=330282&via[0][poitype]=&via[0][lnglat]=121.221875,30.339058&via[0][modxy]=&via[1][name]=宁波市贵安路&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.233091,30.330809&via[1][modxy]=&via[2][name]=宁波市滨海二路&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.231594,30.32494&via[2][modxy]=&via[3][id]=regeo_1744015546927&via[3][name]=滨海二路&via[3][lnglat]=121.222188,30.322636&via[3][adcode]=330282&via[3][modxy]=&via[4][name]=宁波市连江路&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.22376,30.328371&via[4][modxy]="
}, {
	"label": "路线5：0430 无图：隧道+桥，纯无图方案",
	"value": "route5",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=regeo_1743998763443&from[name]=海浦路&from[lnglat]=121.215831,30.341305&from[adcode]=330282&from[modxy]=&to[id]=regeo_1743996160160-to&to[name]=海浦路&to[lnglat]=121.21584,30.341409&to[adcode]=330282&to[modxy]=&via[0][id]=regeo_1743996302441&via[0][name]=滨海七路&via[0][lnglat]=121.212685,30.344847&via[0][adcode]=330282&via[0][modxy]=&via[1][id]=regeo_1743996289955&via[1][name]=水云边路&via[1][lnglat]=121.215021,30.350249&via[1][adcode]=330282&via[1][modxy]="
}, {
	"label": "路线4：0330demo 演示路线：绿地B座-吉利研究院",
	"value": "route4",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[name]=宁波市海浦路&from[id]=menufrom-from&from[adcode]=330282&from[poitype]=&from[lnglat]=121.21579,30.34098&from[modxy]=&to[name]=宁波市滨海四路&to[id]=menuto-to&to[adcode]=330282&to[poitype]=&to[lnglat]=121.247121,30.334918&to[modxy]=&via[0][id]=regeo_1743416238455&via[0][name]=滨海四路&via[0][lnglat]=121.222533,30.333064&via[0][adcode]=330282&via[0][modxy]=&via[1][id]=regeo_1743416381360&via[1][name]=滨海二路&via[1][lnglat]=121.228901,30.323347&via[1][adcode]=330282&via[1][modxy]=&via[2][id]=regeo_1743416309896&via[2][name]=芦苇路&via[2][lnglat]=121.234717,30.330372&via[2][adcode]=330282&via[2][modxy]="
}, {
	"label": "路线3：0330demo 演示路线：吉利研究院-吉利研究院",
	"value": "route3",
	"gaodeUrl": "https://www.amap.com/dir?from[id]=regeo_1743415851589-from&from[name]=滨海四路&from[lnglat]=121.248547,30.335151&from[adcode]=330282&from[modxy]=&to[id]=regeo_1743415855888-to&to[name]=滨海四路&to[lnglat]=121.248455,30.334947&to[adcode]=330282&to[modxy]=&via[0][name]=宁波市金源大道&via[0][id]=&via[0][adcode]=330282&via[0][poitype]=&via[0][lnglat]=121.228556,30.334419&via[0][modxy]=&via[1][name]=宁波市滨海六路&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.221671,30.339299&via[1][modxy]=&via[2][name]=宁波市滨海大道&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.219192,30.333135&via[2][modxy]=&via[3][id]=regeo_1743415833641&via[3][name]=芦汀路&via[3][lnglat]=121.224008,30.330894&via[3][adcode]=330282&via[3][modxy]=&via[4][id]=regeo_1743415919334&via[4][name]=滨海二路&via[4][lnglat]=121.232655,30.323811&via[4][adcode]=330282&via[4][modxy]=&type=car&policy=1"
}, {
	"label": "路线2：0315demo演示路线",
	"value": "route2",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[id]=regeo_1741599585793&from[name]=海浦路&from[lnglat]=121.215836,30.341018&from[adcode]=330282&from[modxy]=&to[id]=regeo_1741599563759-to&to[name]=海浦路&to[lnglat]=121.216224,30.341103&to[adcode]=330282&to[modxy]=&via[0][name]=宁波市连江路&via[0][id]=&via[0][adcode]=330282&via[0][poitype]=&via[0][lnglat]=121.221769,30.328108&via[0][modxy]=&via[1][id]=regeo_1741599500360&via[1][name]=芦汀路&via[1][lnglat]=121.225307,30.323832&via[1][adcode]=330282&via[1][modxy]=&via[2][name]=宁波市金源大道&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.228522,30.332802&via[2][modxy]=&via[3][id]=regeo_1741599538143&via[3][name]=滨海四路&via[3][lnglat]=121.242805,30.334789&via[3][adcode]=330282&via[3][modxy]=&via[4][name]=宁波市金源大道&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.22867,30.335955&via[4][modxy]=&via[5][name]=宁波市滨海五路&via[5][id]=&via[5][adcode]=330282&via[5][poitype]=&via[5][lnglat]=121.219768,30.335379&via[5][modxy]="
}, {
	"label": "路线1：1230demo路线",
	"value": "route1",
	"gaodeUrl": "https://www.amap.com/dir?type=car&policy=1&from[name]=宁波市滨海四路&from[id]=menufrom-from&from[adcode]=330282&from[poitype]=&from[lnglat]=121.24513,30.335071&from[modxy]=&to[id]=regeo_1733911135884-to&to[name]=滨海四路&to[lnglat]=121.245183,30.334787&to[adcode]=330282&to[modxy]=&via[0][name]=宁波市海浦路&via[0][id]=&via[0][adcode]=330282&via[0][poitype]=&via[0][lnglat]=121.216018,30.341131&via[0][modxy]=&via[1][name]=宁波市滨海大道&via[1][id]=&via[1][adcode]=330282&via[1][poitype]=&via[1][lnglat]=121.219962,30.328377&via[1][modxy]=&via[2][name]=宁波市芦汀路&via[2][id]=&via[2][adcode]=330282&via[2][poitype]=&via[2][lnglat]=121.224966,30.325673&via[2][modxy]=&via[3][name]=宁波市中兴一路&via[3][id]=&via[3][adcode]=330282&via[3][poitype]=&via[3][lnglat]=121.2101,30.317698&via[3][modxy]=&via[4][name]=宁波市滨海大道&via[4][id]=&via[4][adcode]=330282&via[4][poitype]=&via[4][lnglat]=121.224442,30.309252&via[4][modxy]=&via[5][name]=宁波市滨海二路&via[5][id]=&via[5][adcode]=330282&via[5][poitype]=&via[5][lnglat]=121.228724,30.323299&via[5][modxy]="
}]', '', 'guosongduo', '2025-04-11 14:43:55.107899', 'guosongduo', '2025-04-14 10:05:59.070757', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_e2e_fps_monitor', '{"name":"e2e fps monitor","chart_config":[{"name":"FpsMonitorStaticObstacle","alias":"Topic: /perception/detection/static_obstacle_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/static_obstacle_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"OneFrameE2EObstacle","alias":"obstacle_process_time","filter":{"type":"contains","pattern":"One frame total time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"obstacle_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"OneFrameE2ETrafficLight","alias":"traffic_light_single_frame_process_time","filter":{"type":"contains","pattern":"tl infer time cost:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"traffic_light_single_frame_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","filter":{"type":"contains","pattern":"Topic: /perception/traffic_lights"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_rviz"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","filter":{"type":"contains","pattern":"Topic: /perception/lane_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar0_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar1_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar2_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar3_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar4_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_120/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_30/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_70/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_rslidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","filter":{"type":"contains","pattern":"Topic: /localization/localization_estimate"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '', 'guosongduo', '2025-03-11 09:13:51.121295', 'guosongduo', '2025-03-21 13:33:29.313125', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_mdriver_fps_monitor', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverMworldArray","alias":"Topic: /mdriver/mworld_array_result","filter":{"type":"contains","pattern":"Topic: /mdriver/mworld_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMdriverPlanningEnvInfo","alias":"Topic: /planning/env_info","filter":{"type":"contains","pattern":"Topic: /planning/env_info"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMdriverObstacleArray","alias":"Topic: /perception/tracking/obstacle_array_result","filter":{"type":"contains","pattern":"Topic: /perception/tracking/obstacle_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"OneFrameMdriverProcess","alias":"mdriver callback time cost:","filter":{"type":"contains","pattern":"callback time cost:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"mdriver single_frame_process_time","operate":[{"type":"split","value":" ","index":8}]}]}]}', '', 'guosongduo', '2025-03-11 16:16:56.397270', 'guosongduo', '2025-03-19 20:34:14.937525', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_all_fps_monitor', '{"name":"all fps monitor","chart_config":[
{"name":"FpsAllCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFrontRight100","alias":"Topic: /sensor/cam_front_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFrontLeft100","alias":"Topic: /sensor/cam_front_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamBack70","alias":"Topic: /sensor/cam_back_70/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_70/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFront30","alias":"Topic: /sensor/cam_front_30/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_30/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamLeft200","alias":"Topic: /sensor/cam_left_200/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_left_200/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamRight200","alias":"Topic: /sensor/cam_right_200/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_right_200/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFront200","alias":"Topic: /sensor/cam_front_200/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_200/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamFront120","alias":"Topic: /sensor/cam_front_120/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_120/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsAllCamBack200","alias":"Topic: /sensor/cam_back_200/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_200/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}


', '', 'guosongduo', '2025-03-11 16:31:24.458196', 'guosongduo', '2025-03-11 16:31:24.458234', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_control_fps_monitor', '{"name":"control fps monitor","chart_config":[{"name":"FpsControlVehicleReport","alias":"Topic: /sensor/vehicle_report_common","filter":{"type":"contains","pattern":"Topic: /sensor/vehicle_report_common"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"OneFrameControlProcess","alias":"Control Processing time:","filter":{"type":"contains","pattern":"Processing time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"control single_frame_process_time","operate":[{"type":"split","value":" ","index":7}]}]}]}', '', 'guosongduo', '2025-03-11 17:24:18.854076', 'guosongduo', '2025-03-19 20:34:00.996710', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_pilot_perception_map_fps_monitor', '{"name":"pilot_perception_map fps monitor","chart_config":[
{"name":"FpsPilotPerceptionMapMerger","alias":"Topic: /map/map_merger","filter":{"type":"contains","pattern":"Topic: /map/map_merger"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},
{"name":"FpsPilotPerceptionMap","alias":"Topic: /perception/perception_map","filter":{"type":"contains","pattern":"Topic: /perception/perception_map"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}
]}', '', 'guosongduo', '2025-03-11 18:15:09.688778', 'guosongduo', '2025-03-11 18:36:08.239616', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('select.processInner.moduleList', '["pilot_perceptio",
"pilot_planning_",
"localization_no",
"environment_mod",
"e2e_perceptor_n",
"mdriver_nodes_e",
"perception_fusi",
"control_node_ex"]', '', 'guosongduo', '2025-03-17 18:22:48.557243', 'guosongduo', '2025-03-17 18:22:48.557243', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:common:comm_e2e_fps_monitor', '{"name":"e2e fps monitor","chart_config":[{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorStaticObstacle","alias":"Topic: /perception/detection/static_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]}]}
', '', 'guosongduo', '2025-03-19 18:59:23.304455', 'guosongduo', '2025-03-19 18:59:23.304515', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:common:comm_mdriver_fps_monitor', '{"name":"mdriver fps monitor","chart_config":[{"name":"FpsMdriverMworldArray","alias":"Topic: /mdriver/mworld_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMdriverPlanningEnvInfo","alias":"Topic: /planning/env_info","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]},{"name":"FpsMdriverObstacleArray","alias":"Topic: /perception/tracking/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]}]}
', '', 'guosongduo', '2025-03-19 18:59:42.875142', 'guosongduo', '2025-03-19 18:59:42.875195', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:common:comm_control_fps_monitor', '{"name":"control fps monitor","chart_config":[{"name":"FpsControlVehicleReport","alias":"Topic: /sensor/vehicle_report_common","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay"},{"name":"maxdelay","comment":"MaxDelay"}]}]}
', '', 'guosongduo', '2025-03-19 18:59:56.349877', 'guosongduo', '2025-03-19 18:59:56.349921', '1');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:comm_e2e', '{"name":"e2e single_process view","chart_config":[{"name":"OneFrameE2EObstacle","alias":"obstacle_process_time","yAxis":[{"name":"process_time","comment":"obstacle_process_time (ms)"}]},{"name":"OneFrameE2ETrafficLight","alias":"traffic_light_single_frame_process_time","yAxis":[{"name":"process_time","comment":"traffic_light_single_frame_process_time (ms)"}]}]}', '', 'guosongduo', '2025-03-19 19:36:28.015956', 'guosongduo', '2025-03-22 16:19:19.361378', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:single_process:comm_mdriver', '{"name":"mdriver single_process view","chart_config":[{"name":"OneFrameMdriverProcess","alias":"mdriver callback time cost","yAxis":[{"name":"process_time","comment":"mdriver single_frame_process_time  (ms)"}]}]}', '', 'guosongduo', '2025-03-19 19:36:10.167503', 'guosongduo', '2025-03-22 16:19:19.273074', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_e2e_perceptor', '{"name":"e2e_perceptor log parser","chart_config":[{"name":"OneFrameE2EObstacle","alias":"obstacle_process_time","filter":{"type":"contains","pattern":"One frame total time:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"obstacle_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"OneFrameE2ETrafficLight","alias":"traffic_light_single_frame_process_time","filter":{"type":"contains","pattern":"tl infer time cost:"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"process_time","comment":"traffic_light_single_frame_process_time","operate":[{"type":"split","value":" ","index":9}]}]},{"name":"FpsMonitorLocalizationEstimate","alias":"Topic: /localization/localization_estimate","filter":{"type":"contains","pattern":"Topic: /localization/localization_estimate"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleRviz","alias":"Topic: /perception/detection/obstacle_rviz_array_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_rviz_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorObstacleTimestamp","alias":"Topic: /perception/detection/obstacle_timestamp_result","filter":{"type":"contains","pattern":"Topic: /perception/detection/obstacle_timestamp_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorLaneArray","alias":"Topic: /perception/lane_array_result","filter":{"type":"contains","pattern":"Topic: /perception/lane_array_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorTrafficLights","alias":"Topic: /perception/traffic_lights_3in1_result","filter":{"type":"contains","pattern":"Topic: /perception/traffic_lights_3in1_result"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBack70","alias":"Topic: /sensor/cam_back_70/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_70/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBackLeft100","alias":"Topic: /sensor/cam_back_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamBackRight100","alias":"Topic: /sensor/cam_back_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_back_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFront120","alias":"Topic: /sensor/cam_front_120/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_120/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFront30","alias":"Topic: /sensor/cam_front_30/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_30/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamFrontLeft","alias":"Topic: /sensor/cam_front_left_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_left_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorCamRight100","alias":"Topic: /sensor/cam_front_right_100/h264","filter":{"type":"contains","pattern":"Topic: /sensor/cam_front_right_100/h264"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorFrontRslidar","alias":"Topic: /sensor/front_rslidar_points","filter":{"type":"contains","pattern":"Topic: /sensor/front_rslidar_points"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar0","alias":"Topic: /sensor/radar0_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar0_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar1","alias":"Topic: /sensor/radar1_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar1_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar2","alias":"Topic: /sensor/radar2_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar2_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar3","alias":"Topic: /sensor/radar3_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar3_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsMonitorRadar4","alias":"Topic: /sensor/radar4_object","filter":{"type":"contains","pattern":"Topic: /sensor/radar4_object"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]},{"name":"FpsE2ePerceptorMapInputSync","alias":"Topic: /debug/profile/map_input_sync","filter":{"type":"contains","pattern":"Topic: /debug/profile/map_input_sync"},"xAxis":{"operate":[{"type":"split","value":".","index":0},{"type":"replace","oldValue":"[","newValue":""}]},"yAxis":[{"name":"fps","comment":"FPS","group_idx":1,"operate":[{"type":"split","value":" ","index":8}]},{"name":"delay","comment":"Delay","group_idx":1,"operate":[{"type":"split","value":" ","index":12},{"type":"replace","oldValue":"s,","newValue":""}]},{"name":"maxdelay","comment":"MaxDelay","operate":[{"type":"split","value":" ","index":15},{"type":"replace","oldValue":"s","newValue":""}]}]}]}', '文件日志解析', 'sys', '2025-03-24 10:11:59.000000', 'guosongduo', '2025-04-09 20:13:56.580334', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_fps_static_obstacle', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsMonitorStaticObstacle","alias":"Topic: /perception/detection/static_obstacle_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-24 12:01:07.000000', 'sys', '2025-03-24 12:01:07.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:cpu:temperature', '{"name":"CPU温度解析","chart_config":[{"name":"DataviewCpuTemperature","alias":"CPU 温度监控","yAxis":[{"name":"package","comment":"Package"},{"name":"core_0","comment":"Core 0(°C)"},{"name":"core_4","comment":"Core 4(°C)"},{"name":"core_8","comment":"Core 8(°C)"},{"name":"core_12","comment":"Core 12(°C)"},{"name":"core_16","comment":"Core 16(°C)"},{"name":"core_20","comment":"Core 20(°C)"},{"name":"core_24","comment":"Core 24(°C)"},{"name":"core_28","comment":"Core 28(°C)"},{"name":"core_32","comment":"Core 32(°C)"},{"name":"core_33","comment":"Core 33(°C)"},{"name":"core_34","comment":"Core 34(°C)"},{"name":"core_35","comment":"Core 35(°C)"},{"name":"core_36","comment":"Core 36(°C)"},{"name":"core_37","comment":"Core 37(°C)"},{"name":"core_38","comment":"Core 38(°C)"},{"name":"core_39","comment":"Core 39(°C)"},{"name":"core_40","comment":"Core 40(°C)"},{"name":"core_41","comment":"Core 41(°C)"},{"name":"core_42","comment":"Core 42(°C)"},{"name":"core_43","comment":"Core 43(°C)"},{"name":"core_44","comment":"Core 44(°C)"},{"name":"core_45","comment":"Core 45(°C)"},{"name":"core_46","comment":"Core 46(°C)"},{"name":"core_47","comment":"Core 47(°C)"}]}]}', '', 'guosongduo', '2025-03-25 16:55:22.472976', 'guosongduo', '2025-03-25 16:57:05.903120', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_rear_left', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarRearLeft","alias":"Topic: /sensor/radar_rear_left","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_rear_right', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarRearRight","alias":"Topic: /sensor/radar_rear_right","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_front_left', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarFrontLeft","alias":"Topic: /sensor/radar_front_right","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_front_right', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarFrontRight","alias":"Topic: /sensor/radar_front_left","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_localization_estimate', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarLocalization","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_fusion_obstacle_array', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionFusionObstacleArray","alias":"Topic: /perception/fusion/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_radar_front_middle', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionRadarFrontMiddle","alias":"Topic: /sensor/radar_front_middle","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_vehicle_report_common', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionVehicleReportCommon","alias":"Topic: /sensor/vehicle_report_common","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:fusion_detection_obstacle_array', '{"name":"perception_fusion_node fps monitor","chart_config":[{"name":"FpsFusionDetectionObstacleArray","alias":"Topic: /perception/detection/obstacle_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-03-27 04:41:34.000000', 'sys', '2025-03-27 04:41:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_aeb_perceptor', '{"name": "aeb_perceptor log parser", "chart_config": [{"name": "FpsAebLidarObstacle", "alias": "Topic: /perception/aeb_lidar_obstacle_array", "filter": {"type": "contains", "pattern": "Topic: /perception/aeb_lidar_obstacle_array"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAebRvObstacle", "alias": "Topic: /perception/aeb_rv_obstacle_array", "filter": {"type": "contains", "pattern": "Topic: /perception/aeb_rv_obstacle_array"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAebFrontRslidar", "alias": "Topic: /sensor/front_rslidar_points", "filter": {"type": "contains", "pattern": "Topic: /sensor/front_rslidar_points"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsAebCamFront120", "alias": "Topic: /sensor/cam_front_120/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_120/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-04-10 12:05:34.000000', 'sys', '2025-04-10 12:05:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:aeb_perceptor', '{"name": "aeb_perceptor file_fps viewer", "chart_config": [{"name": "FpsAebLidarObstacle", "alias": "Topic: /perception/aeb_lidar_obstacle_array", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAebRvObstacle", "alias": "Topic: /perception/aeb_rv_obstacle_array", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAebFrontRslidar", "alias": "Topic: /sensor/front_rslidar_points", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsAebCamFront120", "alias": "Topic: /sensor/cam_front_120/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-04-10 12:05:34.000000', 'sys', '2025-04-10 12:05:34.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_back70', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeBack70","alias":"Topic: decode/sensor/cam_back_70/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_back_left_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeBack71","alias":"Topic: decode/sensor/cam_back_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_back_right_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeBack72","alias":"Topic: decode/sensor/cam_back_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_front_120', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeFront120","alias":"Topic: decode/sensor/cam_front_120/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_front_30', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeFront30","alias":"Topic: decode/sensor/cam_front_30/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_front_left_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeFrontLeft100","alias":"Topic: decode/sensor/cam_front_left_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:fps:e2e_perceptor_decode_front_right_100', '{"name":"e2e_perceptor fps monitor","chart_config":[{"name":"FpsPerceptorDecodeFrontRight100","alias":"Topic: decode/sensor/cam_front_right_100/h264","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_view:file_fps:e2e_failsafe', '{"name": "e2e_failsafe file_fps viewer", "chart_config": [{"name": "FpsE2eFailsafeCamBack70", "alias": "Topic: /failsafe/cam_back_70", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamBackLeft100", "alias": "Topic: /failsafe/cam_back_left_100", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamBackRight100", "alias": "Topic: /failsafe/cam_back_right_100", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamFront120", "alias": "Topic: /failsafe/cam_front_120", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamFront30", "alias": "Topic: /failsafe/cam_front_30", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamFrontLeft100", "alias": "Topic: /failsafe/cam_front_left_100", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeCamFrontRight100", "alias": "Topic: /failsafe/cam_front_right_100", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorBack70", "alias": "Topic: /sensor/cam_back_70/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorBackLeft100", "alias": "Topic: /sensor/cam_back_left_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorBackRight100", "alias": "Topic: /sensor/cam_back_right_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorFront120", "alias": "Topic: /sensor/cam_front_120/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorFront30", "alias": "Topic: /sensor/cam_front_30/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorFrontLeft100", "alias": "Topic: /sensor/cam_front_left_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}, {"name": "FpsE2eFailsafeSensorFrontRight100", "alias": "Topic: /sensor/cam_front_right_100/h264", "yAxis": [{"name": "fps", "comment": "FPS"}, {"name": "delay", "comment": "Delay(s)"}, {"name": "maxdelay", "comment": "MaxDelay(s)"}]}]}', '文件日志解析', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');
INSERT INTO megtool.basic_key_value (name, value, remark, create_by, create_time, update_by, update_time, is_delete) VALUES ('log_parse:common:comm_e2e_failsafe', '{"name": "e2e_failsafe log parser", "chart_config": [{"name": "FpsE2eFailsafeCamBack70", "alias": "Topic: /failsafe/cam_back_70", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_back_70"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamBackLeft100", "alias": "Topic: /failsafe/cam_back_left_100", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_back_left_100"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamBackRight100", "alias": "Topic: /failsafe/cam_back_right_100", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_back_right_100"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamFront120", "alias": "Topic: /failsafe/cam_front_120", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_front_120"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamFront30", "alias": "Topic: /failsafe/cam_front_30", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_front_30"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamFrontLeft100", "alias": "Topic: /failsafe/cam_front_left_100", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_front_left_100"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeCamFrontRight100", "alias": "Topic: /failsafe/cam_front_right_100", "filter": {"type": "contains", "pattern": "Topic: /failsafe/cam_front_right_100"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorBack70", "alias": "Topic: /sensor/cam_back_70/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_70/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorBackLeft100", "alias": "Topic: /sensor/cam_back_left_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_left_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorBackRight100", "alias": "Topic: /sensor/cam_back_right_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_back_right_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorFront120", "alias": "Topic: /sensor/cam_front_120/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_120/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorFront30", "alias": "Topic: /sensor/cam_front_30/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_30/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorFrontLeft100", "alias": "Topic: /sensor/cam_front_left_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_left_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}, {"name": "FpsE2eFailsafeSensorFrontRight100", "alias": "Topic: /sensor/cam_front_right_100/h264", "filter": {"type": "contains", "pattern": "Topic: /sensor/cam_front_right_100/h264"}, "xAxis": {"operate": [{"type": "split", "value": ".", "index": 0}, {"type": "replace", "oldValue": "[", "newValue": ""}]}, "yAxis": [{"name": "fps", "comment": "FPS", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 8}]}, {"name": "delay", "comment": "Delay", "group_idx": 1, "operate": [{"type": "split", "value": " ", "index": 12}, {"type": "replace", "oldValue": "s,", "newValue": ""}]}, {"name": "maxdelay", "comment": "MaxDelay", "operate": [{"type": "split", "value": " ", "index": 15}, {"type": "replace", "oldValue": "s", "newValue": ""}]}]}]}', '文件日志解析', 'sys', '2025-04-14 06:51:18.000000', 'sys', '2025-04-14 06:51:18.000000', '0');


