#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import warnings
import sys
import os

# 在导入任何其他模块之前抑制所有pkg_resources相关的警告
warnings.filterwarnings("ignore", category=UserWarning, module="pkg_resources")
warnings.filterwarnings("ignore", message=".*pkg_resources is deprecated.*")
warnings.filterwarnings("ignore", message=".*The pkg_resources package is slated for removal.*")

# 设置环境变量来进一步抑制相关警告
os.environ['PYTHONWARNINGS'] = 'ignore::UserWarning:pkg_resources.*'

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入并运行主程序
from src.main import main

if __name__ == "__main__":
    main() 