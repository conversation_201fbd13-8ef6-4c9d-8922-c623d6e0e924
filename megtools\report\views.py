import datetime
import json
from django.http import HttpResponse
from django.views.decorators.http import require_POST

from basic.middle.exceptions import BusinessException
from basic.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, PageUtils, JsonEncoder, JsonUtils
from report.models import ReportFeishuInfo, ReportFeishuInfoLog
from report.services import FeishuReportGenerator


# Create your views here.

@require_POST
def feishuReportPage(request):
    """
    分页查询 飞书报告 信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    condition = OrmFilter.and_condition(ReportFeishuInfo, req, 1)
    condition.children.append(('is_delete', 0))
    feishuInfo = ReportFeishuInfo.objects.filter(condition)
    result = PageUtils.page(feishuInfo, req)
    return HttpResponse(json.dumps(result, cls=JsonEncoder), content_type='application/json')


@require_POST
def modifyFeishuReport(request):
    """
    分页查询文件上传信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user["login_name"] if "user" in request.__dict__ else "remote"
    feishuInfo = ReportFeishuInfo.objects.filter(name=req.get('id', -1), is_delete=0).first()
    req['update_by'] = who
    req['update_time'] = datetime.datetime.now()
    if feishuInfo:
        ReportFeishuInfo.objects.filter(id=req['id']).update(**req)
    else:
        req['create_by'] = who
        req['create_time'] = datetime.datetime.now()
        feishuInfo = ReportFeishuInfo(**req)
        feishuInfo.save()
    # Redis 数据库还没有建设好， 因此这个地方先这样。
    # set_global_kv(req["name"], req['value'])
    return HttpResponse(json.dumps(JsonUtils.convert2Json(feishuInfo)), content_type='application/json')


@require_POST
def delFeishuReport(request):
    """
    删除上传文件信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user['login_name'] if "user" in request.__dict__ else "remote"
    ReportFeishuInfo.objects.filter(id=req["id"]).update(is_delete=1, create_by = who)
    return HttpResponse(json.dumps("{}"), content_type='application/json')

@require_POST
def generateFeishuReport(request):
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user['login_name'] if "user" in request.__dict__ else "remote"
    reportConfig = ReportFeishuInfo.objects.filter(id=req["id"]).first()
    result, entity = FeishuReportGenerator(reportConfig).prepare()
    del entity["target_token"]
    entity['params'] = req.get('params', '')
    entity['create_by'] = who
    entity['update_by'] = who
    # 添加手动生成的逻辑
    if (req.get('status', '') == '4' or req.get('status', '') == '5' ) and entity['status'] == '0':
        entity['status'] = req.get('status', '')
    ReportFeishuInfoLog(**entity).save()
    resp = {
        "status": result,
        "message": entity.get('remark', ""),
    }
    return HttpResponse(json.dumps(resp), content_type='application/json')



@require_POST
def getFeishuReportLogPage(request):
    """
    分页查询 飞书报告 信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    condition = OrmFilter.and_condition(ReportFeishuInfoLog, req, 1)
    condition.children.append(('is_delete', 0))
    feishuLogInfo = ReportFeishuInfoLog.objects.filter(condition)
    result = PageUtils.page(feishuLogInfo, req)
    return HttpResponse(json.dumps(result, cls=JsonEncoder), content_type='application/json')


@require_POST
def rerunFeishuReportApi(request):
    """
    分页查询 飞书报告 信息
    """
    req = json.loads(request.body.decode("UTF-8"))
    who = request.user['login_name'] if "user" in request.__dict__ else "remote"
    ReportFeishuInfoLog.objects.filter(id=req["id"], is_delete=0).update(status=req.get('status', '0'),
                                                                         params=req.get('params', ''),
                                                                         update_by=who,
                                                                         update_time=datetime.datetime.now())
    return HttpResponse(json.dumps({ "status": True, }, cls=JsonEncoder), content_type='application/json')


@require_POST
def delFeishuReportLogApi(request):
    """
    删除飞书报告的日志
    """
    req = json.loads(request.body.decode("UTF-8"))
    ReportFeishuInfoLog.objects.filter(id=req["id"], is_delete=0).update(is_delete=1)
    return HttpResponse(json.dumps({}, cls=JsonEncoder), content_type='application/json')

@require_POST
def modifyReportRemark(request):
    """
    修改飞书报告的remark
    """
    req = json.loads(request.body.decode("UTF-8"))
    ReportFeishuInfoLog.objects.filter(id=req["id"]).update( remark=req['remark'])
    return HttpResponse(json.dumps({}, cls=JsonEncoder), content_type='application/json')
