average rate: 50.009
	min: 0.019s max: 0.021s std dev: 0.00026s window: 52
average rate: 50.003
	min: 0.019s max: 0.021s std dev: 0.00034s window: 102
average rate: 49.999
	min: 0.018s max: 0.022s std dev: 0.00040s window: 152
average rate: 50.000
	min: 0.010s max: 0.030s std dev: 0.00104s window: 203
average rate: 49.997
	min: 0.010s max: 0.030s std dev: 0.00094s window: 254
average rate: 49.835
	min: 0.010s max: 0.040s std dev: 0.00143s window: 304
average rate: 49.857
	min: 0.010s max: 0.040s std dev: 0.00133s window: 354
average rate: 49.876
	min: 0.010s max: 0.040s std dev: 0.00125s window: 405
average rate: 49.888
	min: 0.010s max: 0.040s std dev: 0.00120s window: 456
average rate: 49.900
	min: 0.010s max: 0.040s std dev: 0.00115s window: 507
average rate: 49.910
	min: 0.010s max: 0.040s std dev: 0.00110s window: 558
average rate: 49.916
	min: 0.010s max: 0.040s std dev: 0.00106s window: 608
average rate: 49.922
	min: 0.010s max: 0.040s std dev: 0.00102s window: 658
average rate: 49.926
	min: 0.010s max: 0.040s std dev: 0.00099s window: 708
average rate: 49.922
	min: 0.010s max: 0.040s std dev: 0.00096s window: 759
average rate: 49.936
	min: 0.010s max: 0.040s std dev: 0.00094s window: 810
average rate: 49.940
	min: 0.010s max: 0.040s std dev: 0.00092s window: 861
average rate: 49.944
	min: 0.010s max: 0.040s std dev: 0.00089s window: 912
average rate: 49.946
	min: 0.010s max: 0.040s std dev: 0.00087s window: 962
average rate: 49.948
	min: 0.010s max: 0.040s std dev: 0.00085s window: 1012
average rate: 49.951
	min: 0.010s max: 0.040s std dev: 0.00083s window: 1062
average rate: 49.953
	min: 0.010s max: 0.040s std dev: 0.00083s window: 1113
average rate: 49.955
	min: 0.010s max: 0.040s std dev: 0.00082s window: 1163
average rate: 49.957
	min: 0.010s max: 0.040s std dev: 0.00080s window: 1214
average rate: 49.959
	min: 0.010s max: 0.040s std dev: 0.00079s window: 1265
average rate: 49.960
	min: 0.010s max: 0.040s std dev: 0.00077s window: 1315
average rate: 49.961
	min: 0.010s max: 0.040s std dev: 0.00076s window: 1366
average rate: 49.963
	min: 0.010s max: 0.040s std dev: 0.00075s window: 1417
average rate: 49.964
	min: 0.010s max: 0.040s std dev: 0.00074s window: 1468
average rate: 49.965
	min: 0.010s max: 0.040s std dev: 0.00073s window: 1518
average rate: 49.966
	min: 0.010s max: 0.040s std dev: 0.00072s window: 1569
average rate: 49.967
	min: 0.010s max: 0.040s std dev: 0.00071s window: 1619
average rate: 49.938
	min: 0.010s max: 0.040s std dev: 0.00086s window: 1669
average rate: 49.940
	min: 0.010s max: 0.040s std dev: 0.00085s window: 1720
average rate: 49.941
	min: 0.010s max: 0.040s std dev: 0.00084s window: 1771
average rate: 49.942
	min: 0.010s max: 0.040s std dev: 0.00083s window: 1821
average rate: 49.945
	min: 0.010s max: 0.040s std dev: 0.00082s window: 1872
average rate: 49.946
	min: 0.010s max: 0.040s std dev: 0.00081s window: 1923
average rate: 49.947
	min: 0.010s max: 0.040s std dev: 0.00080s window: 1973
average rate: 49.948
	min: 0.010s max: 0.040s std dev: 0.00080s window: 2023
average rate: 49.950
	min: 0.010s max: 0.040s std dev: 0.00079s window: 2074
average rate: 49.951
	min: 0.010s max: 0.040s std dev: 0.00079s window: 2124
average rate: 49.952
	min: 0.010s max: 0.040s std dev: 0.00078s window: 2174
average rate: 49.952
	min: 0.010s max: 0.040s std dev: 0.00077s window: 2225
average rate: 49.954
	min: 0.010s max: 0.040s std dev: 0.00076s window: 2276
average rate: 49.955
	min: 0.010s max: 0.040s std dev: 0.00076s window: 2326
average rate: 49.956
	min: 0.010s max: 0.040s std dev: 0.00075s window: 2376
average rate: 49.957
	min: 0.010s max: 0.040s std dev: 0.00075s window: 2426
average rate: 49.957
	min: 0.010s max: 0.040s std dev: 0.00074s window: 2477
average rate: 49.958
	min: 0.010s max: 0.040s std dev: 0.00073s window: 2528
average rate: 49.959
	min: 0.010s max: 0.040s std dev: 0.00073s window: 2578
average rate: 49.960
	min: 0.010s max: 0.040s std dev: 0.00073s window: 2629
average rate: 49.961
	min: 0.010s max: 0.040s std dev: 0.00072s window: 2679
average rate: 49.961
	min: 0.010s max: 0.040s std dev: 0.00072s window: 2729
average rate: 49.962
	min: 0.010s max: 0.040s std dev: 0.00071s window: 2780
average rate: 49.962
	min: 0.010s max: 0.040s std dev: 0.00071s window: 2830
average rate: 49.963
	min: 0.010s max: 0.040s std dev: 0.00070s window: 2881
average rate: 49.964
	min: 0.010s max: 0.040s std dev: 0.00070s window: 2931
average rate: 49.964
	min: 0.010s max: 0.040s std dev: 0.00069s window: 2982
average rate: 49.965
	min: 0.010s max: 0.040s std dev: 0.00069s window: 3033
average rate: 49.965
	min: 0.010s max: 0.040s std dev: 0.00068s window: 3084
average rate: 49.966
	min: 0.010s max: 0.040s std dev: 0.00068s window: 3134
average rate: 49.966
	min: 0.010s max: 0.040s std dev: 0.00067s window: 3184
average rate: 49.967
	min: 0.010s max: 0.040s std dev: 0.00067s window: 3234
average rate: 49.967
	min: 0.010s max: 0.040s std dev: 0.00067s window: 3285
average rate: 49.968
	min: 0.010s max: 0.040s std dev: 0.00066s window: 3336
average rate: 49.968
	min: 0.010s max: 0.040s std dev: 0.00066s window: 3386
average rate: 49.969
	min: 0.010s max: 0.040s std dev: 0.00066s window: 3437
average rate: 49.969
	min: 0.010s max: 0.040s std dev: 0.00065s window: 3487
average rate: 49.969
	min: 0.010s max: 0.040s std dev: 0.00066s window: 3538
average rate: 49.970
	min: 0.010s max: 0.040s std dev: 0.00067s window: 3589
average rate: 49.970
	min: 0.010s max: 0.040s std dev: 0.00066s window: 3639
average rate: 49.971
	min: 0.010s max: 0.040s std dev: 0.00066s window: 3690
average rate: 49.971
	min: 0.010s max: 0.040s std dev: 0.00066s window: 3740
average rate: 49.971
	min: 0.010s max: 0.040s std dev: 0.00065s window: 3790
average rate: 49.972
	min: 0.010s max: 0.040s std dev: 0.00065s window: 3841
average rate: 49.972
	min: 0.010s max: 0.040s std dev: 0.00065s window: 3891
average rate: 49.972
	min: 0.010s max: 0.040s std dev: 0.00064s window: 3941
average rate: 49.970
	min: 0.010s max: 0.040s std dev: 0.00064s window: 3992
average rate: 49.973
	min: 0.010s max: 0.040s std dev: 0.00064s window: 4043
average rate: 49.973
	min: 0.010s max: 0.040s std dev: 0.00064s window: 4093
average rate: 49.974
	min: 0.010s max: 0.040s std dev: 0.00064s window: 4143
average rate: 49.974
	min: 0.010s max: 0.040s std dev: 0.00064s window: 4194
average rate: 49.974
	min: 0.010s max: 0.040s std dev: 0.00063s window: 4244
average rate: 49.975
	min: 0.010s max: 0.040s std dev: 0.00063s window: 4295
average rate: 49.975
	min: 0.010s max: 0.040s std dev: 0.00063s window: 4345
average rate: 49.975
	min: 0.010s max: 0.040s std dev: 0.00063s window: 4396
average rate: 49.975
	min: 0.010s max: 0.040s std dev: 0.00063s window: 4446
average rate: 49.976
	min: 0.010s max: 0.040s std dev: 0.00063s window: 4496
average rate: 49.976
	min: 0.010s max: 0.040s std dev: 0.00063s window: 4547
average rate: 49.976
	min: 0.010s max: 0.040s std dev: 0.00062s window: 4597
average rate: 49.976
	min: 0.010s max: 0.040s std dev: 0.00062s window: 4647
average rate: 49.976
	min: 0.010s max: 0.040s std dev: 0.00062s window: 4697
average rate: 49.977
	min: 0.010s max: 0.040s std dev: 0.00062s window: 4747
average rate: 49.977
	min: 0.010s max: 0.040s std dev: 0.00062s window: 4797
average rate: 49.977
	min: 0.010s max: 0.040s std dev: 0.00062s window: 4848
average rate: 49.977
	min: 0.010s max: 0.040s std dev: 0.00061s window: 4898
average rate: 49.978
	min: 0.010s max: 0.040s std dev: 0.00061s window: 4949
average rate: 49.978
	min: 0.010s max: 0.040s std dev: 0.00061s window: 5000
average rate: 49.978
	min: 0.009s max: 0.040s std dev: 0.00065s window: 5050
average rate: 49.978
	min: 0.009s max: 0.040s std dev: 0.00065s window: 5101
average rate: 49.978
	min: 0.009s max: 0.040s std dev: 0.00064s window: 5151
average rate: 49.979
	min: 0.009s max: 0.040s std dev: 0.00064s window: 5201
average rate: 49.979
	min: 0.009s max: 0.040s std dev: 0.00066s window: 5251
average rate: 49.979
	min: 0.009s max: 0.040s std dev: 0.00065s window: 5302
average rate: 49.979
	min: 0.009s max: 0.040s std dev: 0.00065s window: 5353
average rate: 49.979
	min: 0.009s max: 0.040s std dev: 0.00065s window: 5404
average rate: 49.979
	min: 0.009s max: 0.040s std dev: 0.00065s window: 5454
average rate: 49.980
	min: 0.009s max: 0.040s std dev: 0.00065s window: 5505
average rate: 49.980
	min: 0.009s max: 0.040s std dev: 0.00065s window: 5555
average rate: 49.980
	min: 0.009s max: 0.040s std dev: 0.00065s window: 5606
average rate: 49.980
	min: 0.009s max: 0.040s std dev: 0.00064s window: 5656
average rate: 49.980
	min: 0.009s max: 0.040s std dev: 0.00064s window: 5706
average rate: 49.980
	min: 0.009s max: 0.040s std dev: 0.00064s window: 5757
average rate: 49.980
	min: 0.009s max: 0.040s std dev: 0.00064s window: 5807
average rate: 49.981
	min: 0.009s max: 0.040s std dev: 0.00064s window: 5858
average rate: 49.981
	min: 0.009s max: 0.040s std dev: 0.00064s window: 5908
average rate: 49.981
	min: 0.009s max: 0.040s std dev: 0.00064s window: 5959
average rate: 49.981
	min: 0.009s max: 0.040s std dev: 0.00064s window: 6010
average rate: 49.981
	min: 0.009s max: 0.040s std dev: 0.00064s window: 6061
average rate: 49.981
	min: 0.009s max: 0.040s std dev: 0.00063s window: 6111
average rate: 49.982
	min: 0.009s max: 0.040s std dev: 0.00063s window: 6162
average rate: 49.982
	min: 0.009s max: 0.040s std dev: 0.00063s window: 6212
average rate: 49.982
	min: 0.009s max: 0.040s std dev: 0.00063s window: 6263
average rate: 49.974
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6312
average rate: 49.974
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6363
average rate: 49.974
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6413
average rate: 49.975
	min: 0.009s max: 0.041s std dev: 0.00067s window: 6463
average rate: 49.975
	min: 0.009s max: 0.041s std dev: 0.00067s window: 6513
average rate: 49.975
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6564
average rate: 49.975
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6614
average rate: 49.975
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6665
average rate: 49.975
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6715
average rate: 49.975
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6766
average rate: 49.976
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6817
average rate: 49.976
	min: 0.009s max: 0.041s std dev: 0.00068s window: 6868
average rate: 49.976
	min: 0.009s max: 0.041s std dev: 0.00067s window: 6919
average rate: 49.976
	min: 0.009s max: 0.041s std dev: 0.00067s window: 6969
average rate: 49.976
	min: 0.009s max: 0.041s std dev: 0.00068s window: 7019
average rate: 49.977
	min: 0.009s max: 0.041s std dev: 0.00067s window: 7069
average rate: 49.970
	min: 0.009s max: 0.041s std dev: 0.00071s window: 7118
average rate: 49.963
	min: 0.009s max: 0.041s std dev: 0.00075s window: 7168
average rate: 49.963
	min: 0.009s max: 0.041s std dev: 0.00075s window: 7219
average rate: 49.963
	min: 0.009s max: 0.041s std dev: 0.00075s window: 7270
average rate: 49.964
	min: 0.009s max: 0.041s std dev: 0.00074s window: 7320
average rate: 49.964
	min: 0.009s max: 0.041s std dev: 0.00075s window: 7371
average rate: 49.964
	min: 0.009s max: 0.041s std dev: 0.00075s window: 7421
average rate: 49.964
	min: 0.009s max: 0.041s std dev: 0.00074s window: 7471
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00074s window: 7522
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00074s window: 7572
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00074s window: 7623
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00074s window: 7674
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00073s window: 7724
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00073s window: 7774
average rate: 49.959
	min: 0.009s max: 0.041s std dev: 0.00077s window: 7824
average rate: 49.960
	min: 0.009s max: 0.041s std dev: 0.00076s window: 7874
average rate: 49.960
	min: 0.009s max: 0.041s std dev: 0.00076s window: 7924
average rate: 49.960
	min: 0.009s max: 0.041s std dev: 0.00076s window: 7975
average rate: 49.960
	min: 0.009s max: 0.041s std dev: 0.00076s window: 8026
average rate: 49.961
	min: 0.009s max: 0.041s std dev: 0.00076s window: 8076
average rate: 49.961
	min: 0.009s max: 0.041s std dev: 0.00076s window: 8126
average rate: 49.961
	min: 0.009s max: 0.041s std dev: 0.00076s window: 8177
average rate: 49.961
	min: 0.009s max: 0.041s std dev: 0.00076s window: 8227
average rate: 49.961
	min: 0.009s max: 0.041s std dev: 0.00076s window: 8277
average rate: 49.962
	min: 0.009s max: 0.041s std dev: 0.00076s window: 8328
average rate: 49.962
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8379
average rate: 49.962
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8429
average rate: 49.962
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8480
average rate: 49.963
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8531
average rate: 49.963
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8581
average rate: 49.963
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8631
average rate: 49.963
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8682
average rate: 49.963
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8733
average rate: 49.964
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8783
average rate: 49.964
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8834
average rate: 49.964
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8885
average rate: 49.964
	min: 0.009s max: 0.041s std dev: 0.00075s window: 8935
average rate: 49.964
	min: 0.009s max: 0.041s std dev: 0.00074s window: 8986
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00074s window: 9037
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00074s window: 9087
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9137
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9188
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9238
average rate: 49.965
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9289
average rate: 49.966
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9340
average rate: 49.966
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9390
average rate: 49.966
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9441
average rate: 49.966
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9492
average rate: 49.966
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9542
average rate: 49.967
	min: 0.009s max: 0.041s std dev: 0.00076s window: 9593
average rate: 49.967
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9643
average rate: 49.967
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9694
average rate: 49.967
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9745
average rate: 49.967
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9795
average rate: 49.967
	min: 0.009s max: 0.041s std dev: 0.00076s window: 9845
average rate: 49.968
	min: 0.009s max: 0.041s std dev: 0.00076s window: 9895
average rate: 49.968
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9946
average rate: 49.968
	min: 0.009s max: 0.041s std dev: 0.00075s window: 9997
average rate: 49.968
	min: 0.009s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.968
	min: 0.009s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.968
	min: 0.009s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.968
	min: 0.009s max: 0.041s std dev: 0.00074s window: 10000
average rate: 49.968
	min: 0.009s max: 0.041s std dev: 0.00074s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00071s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00071s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00071s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00071s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00071s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00071s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00071s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00071s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.973
	min: 0.009s max: 0.041s std dev: 0.00072s window: 10000
average rate: 49.968
	min: 0.009s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.968
	min: 0.008s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.968
	min: 0.008s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.968
	min: 0.008s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.968
	min: 0.008s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.968
	min: 0.008s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.968
	min: 0.008s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.968
	min: 0.008s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00074s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00074s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00074s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.972
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.008s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.972
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.968
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.968
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.968
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.968
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.968
	min: 0.006s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.963
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.968
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.973
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00075s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.979
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00076s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.977
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.977
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00077s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.977
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.006s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.982
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.984
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.006s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.981
	min: 0.006s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.977
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00081s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.007s max: 0.041s std dev: 0.00083s window: 10000
average rate: 49.983
	min: 0.007s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.007s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.982
	min: 0.007s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.007s max: 0.041s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00076s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00076s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00076s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00076s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00076s window: 10000
average rate: 49.992
	min: 0.007s max: 0.040s std dev: 0.00076s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00076s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.992
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.992
	min: 0.007s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00077s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.992
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.992
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.992
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.992
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.006s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00087s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00087s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00086s window: 10000
average rate: 49.988
	min: 0.006s max: 0.040s std dev: 0.00086s window: 10000
average rate: 49.987
	min: 0.006s max: 0.040s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.006s max: 0.040s std dev: 0.00088s window: 10000
average rate: 49.983
	min: 0.006s max: 0.040s std dev: 0.00088s window: 10000
average rate: 49.982
	min: 0.006s max: 0.040s std dev: 0.00088s window: 10000
average rate: 49.983
	min: 0.006s max: 0.040s std dev: 0.00088s window: 10000
average rate: 49.983
	min: 0.006s max: 0.040s std dev: 0.00088s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00086s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00086s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00086s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.987
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00084s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.988
	min: 0.006s max: 0.036s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.982
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.006s max: 0.042s std dev: 0.00087s window: 10000
average rate: 49.978
	min: 0.006s max: 0.042s std dev: 0.00089s window: 10000
average rate: 49.978
	min: 0.006s max: 0.042s std dev: 0.00089s window: 10000
average rate: 49.978
	min: 0.006s max: 0.042s std dev: 0.00089s window: 10000
average rate: 49.978
	min: 0.006s max: 0.042s std dev: 0.00089s window: 10000
average rate: 49.978
	min: 0.006s max: 0.042s std dev: 0.00089s window: 10000
average rate: 49.978
	min: 0.006s max: 0.042s std dev: 0.00089s window: 10000
average rate: 49.978
	min: 0.006s max: 0.042s std dev: 0.00089s window: 10000
average rate: 49.978
	min: 0.006s max: 0.042s std dev: 0.00088s window: 10000
average rate: 49.973
	min: 0.006s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.973
	min: 0.006s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.973
	min: 0.006s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.973
	min: 0.006s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.967
	min: 0.006s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.967
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00096s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00096s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00096s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00096s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00096s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00096s window: 10000
average rate: 49.967
	min: 0.006s max: 0.042s std dev: 0.00096s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00096s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.967
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.006s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.968
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.962
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.962
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.965
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00091s window: 10000
average rate: 49.963
	min: 0.007s max: 0.042s std dev: 0.00092s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.967
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00097s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.962
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.963
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00093s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.967
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.967
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.969
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00095s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.968
	min: 0.005s max: 0.042s std dev: 0.00094s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00093s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00093s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00093s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.973
	min: 0.005s max: 0.040s std dev: 0.00092s window: 10000
average rate: 49.978
	min: 0.005s max: 0.040s std dev: 0.00090s window: 10000
average rate: 49.978
	min: 0.005s max: 0.040s std dev: 0.00090s window: 10000
average rate: 49.978
	min: 0.005s max: 0.040s std dev: 0.00090s window: 10000
average rate: 49.978
	min: 0.005s max: 0.040s std dev: 0.00090s window: 10000
average rate: 49.978
	min: 0.005s max: 0.040s std dev: 0.00090s window: 10000
average rate: 49.978
	min: 0.005s max: 0.040s std dev: 0.00090s window: 10000
average rate: 49.978
	min: 0.005s max: 0.040s std dev: 0.00090s window: 10000
average rate: 49.978
	min: 0.005s max: 0.040s std dev: 0.00090s window: 10000
average rate: 49.978
	min: 0.005s max: 0.040s std dev: 0.00089s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00086s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.987
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.987
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.987
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.987
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.005s max: 0.039s std dev: 0.00083s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00082s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.992
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00081s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00080s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00080s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00079s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00080s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00080s window: 10000
average rate: 49.993
	min: 0.005s max: 0.039s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.005s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.005s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.005s max: 0.040s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.982
	min: 0.011s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.984
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.983
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.987
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.987
	min: 0.011s max: 0.040s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.989
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.987
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.987
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.987
	min: 0.011s max: 0.040s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.011s max: 0.040s std dev: 0.00080s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.987
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00082s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.987
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.988
	min: 0.009s max: 0.040s std dev: 0.00083s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00087s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.982
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00086s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00085s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00082s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00082s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.977
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00083s window: 10000
average rate: 49.978
	min: 0.009s max: 0.044s std dev: 0.00084s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00081s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00081s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00081s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00081s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00081s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.987
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.989
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00077s window: 10000
average rate: 49.988
	min: 0.009s max: 0.044s std dev: 0.00077s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00079s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.982
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00078s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00077s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.009s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00073s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00073s window: 10000
average rate: 49.982
	min: 0.011s max: 0.044s std dev: 0.00073s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00073s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00073s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00073s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00073s window: 10000
average rate: 49.983
	min: 0.011s max: 0.044s std dev: 0.00073s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00074s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.981
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00076s window: 10000
average rate: 49.984
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.982
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.983
	min: 0.010s max: 0.044s std dev: 0.00075s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00070s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00070s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00070s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00070s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.010s max: 0.039s std dev: 0.00068s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00070s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00070s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00070s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00070s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00070s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00070s window: 10000
average rate: 49.982
	min: 0.010s max: 0.040s std dev: 0.00070s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00070s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00070s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00069s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00069s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00069s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00069s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00069s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00069s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00069s window: 10000
average rate: 49.983
	min: 0.010s max: 0.040s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.987
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.010s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00069s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00068s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00066s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.988
	min: 0.007s max: 0.040s std dev: 0.00067s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.992
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00063s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00064s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.992
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00062s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00060s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00060s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00059s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00059s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00059s window: 10000
average rate: 49.992
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00057s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00059s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00058s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00060s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00060s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00060s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00060s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00060s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00061s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00060s window: 10000
average rate: 49.993
	min: 0.007s max: 0.040s std dev: 0.00060s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.997
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00056s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
average rate: 49.998
	min: 0.007s max: 0.034s std dev: 0.00055s window: 10000
