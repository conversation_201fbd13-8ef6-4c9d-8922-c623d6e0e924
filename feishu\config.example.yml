# 飞书表格监控系统配置文件示例
# 复制此文件为 config.yml 并修改相应配置

# 飞书配置
feishu:
  app_id: "your_app_id_here"
  app_secret: "your_app_secret_here"
  app_token: "your_app_token_here"
  table_id: "your_table_id_here"

# GitLab配置
gitlab:
  url: "https://********************.com"
  token: "your_gitlab_token_here"
  timeout: 10

# 轮询配置
polling:
  interval: 60          # 轮询间隔（秒）
  max_retries: 5        # 最大重试次数
  retry_delay: 30       # 重试延迟（秒）
  batch_size: 100       # 批处理大小

# 日志配置
logging:
  level: "INFO"         # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/feishu_monitor.log"  # 日志文件路径，不设置则只输出到控制台
  max_file_size: 10485760  # 日志文件最大大小（字节）
  backup_count: 5       # 保留的日志文件数量

# 调试模式
debug: false
