import json
import logging
import time

import requests
from requests_toolbelt import MultipartEncoder
from django.conf import settings

from basic.utils import Json<PERSON>ncoder, generate_md5
from django.core.cache import cache



def get_tenant_token(is_force=False):
    """
    获取飞书tenant_access_token
    """
    url = settings.FEISHU_CONFIG["url"]
    app_id = settings.FEISHU_CONFIG["app_id"]
    app_secret = settings.FEISHU_CONFIG["app_secret"]
    return _get_tenant_token(url, app_id, app_secret, is_force)

def _get_tenant_token(url, app_id, app_secret, is_force=False):
    tenant_token_key = f'feishu:_get_tenant_token:{generate_md5(f"{app_id},{app_secret}")}'
    redis_value = cache.get(tenant_token_key)
    if redis_value and not is_force:
        return redis_value
    headers = {"Content-Type": "application/json"}
    body = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    url1 = f"{url}/auth/v3/tenant_access_token/internal"
    resp = requests.post(url1, headers=headers, json=body)
    tenant_access_token = resp.json()["tenant_access_token"]
    cache.set(tenant_token_key, tenant_access_token, timeout=60 * 10)
    return tenant_access_token

class FeishuApi:
    def __init__(self, target_file_token=''):
        self.tenant_access_token = get_tenant_token()
        self.headers = {
            'Authorization': f"Bearer {self.tenant_access_token}",
            'Content-Type': "application/json; charset=utf-8"
        }
        self.target_file_token = target_file_token
        self.url = settings.FEISHU_CONFIG["url"]

    def set_target_file_token(self, target_file_token):
        self.target_file_token = target_file_token

    def get_target_file_token(self):
        return self.target_file_token

    def get_document_all_blocks(self):
        """
        获取文档中所有的block
        """
        has_more = True
        params = {'page_size': 100}
        blocks = []
        # 获取包含的变量信息
        while has_more:
            all_blocks = self.get_document_blocks(params)
            has_more = all_blocks['data']['has_more']
            page_token = all_blocks['data']['page_token'] if 'page_token' in all_blocks['data'] else ''
            blocks.extend(all_blocks['data']['items'])
            if has_more:
                params['page_token'] = page_token
        return blocks

    def get_document_blocks(self, params):
        """
        获取文档的block 默认值最多500个
        """
        url = f"{self.url}/docx/v1/documents/{self.target_file_token}/blocks"
        headers = {'Authorization': f"Bearer {self.tenant_access_token}"}
        data = self.request_api('GET', url, params, headers=headers)
        return data

    def get_block_all_blocks(self, block_id):
        """
        获取某一个block 下的所有block
        """
        has_more = True
        params = {'page_size': 100}
        blocks = []
        # 获取包含的变量信息
        while has_more:
            all_blocks = self.get_block_blocks(block_id, params)
            logging.info(f"all blocks {all_blocks}")
            has_more = all_blocks['data']['has_more']
            page_token = all_blocks['data']['page_token'] if 'page_token' in all_blocks['data'] else ''
            blocks.extend(all_blocks['data']['items'])
            if has_more:
                params['page_token'] = page_token
        return blocks

    def get_block_blocks(self, block_id, params):
        """
        获取文档的block 默认值最多500个
        """
        url = f"{self.url}/docx/v1/documents/{self.target_file_token}/blocks/{block_id}/children"
        headers = {'Authorization': f"Bearer {self.tenant_access_token}"}
        data = self.request_api('GET', url, params, headers=headers)
        return data

    def delete_document_block(self, block_id, body):
        """
        删除块
        """
        url = f"{self.url}/docx/v1/documents/{self.target_file_token}/blocks/{block_id}/children/batch_delete"
        return self.request_api('DELETE', url, body)

    def delete_document(self, type):
        """
        删除块
        """
        url = f"{self.url}/drive/v1/files/{self.target_file_token}"
        headers = {'Authorization': f"Bearer {self.tenant_access_token}"}
        result = requests.request('DELETE', url, headers=headers, params={"type": type})
        logging.info(result.text)

    def create_target_file(self, body):
        """
        复制模板文档，生成新的文档
        """
        url = f"{self.url}/drive/v1/files/{self.target_file_token}/copy"
        return self.request_api('POST', url, body)

    def update_document_block(self, block_id, body):
        """
        更新块内容
        """
        url = f"{self.url}/docx/v1/documents/{self.target_file_token}/blocks/{block_id}"
        return self.request_api('PATCH', url, body)

    def bulk_update_document_block(self, body):
        """
        批量更新块内容
        """
        url = f"{self.url}/docx/v1/documents/{self.target_file_token}/blocks/batch_update"
        return self.request_api('PATCH', url, body)

    def create_document_block(self, block_id, body):
        """
        创建块
        """
        url = f"{self.url}/docx/v1/documents/{self.target_file_token}/blocks/{block_id}/children"
        return self.request_api('POST', url, body)

    def wiki_get_node(self, token, obj_type):
        """
        Wiki 中获取表格，需要获取下一级才能得到表格的id
        """
        url = f"{self.url}/wiki/v2/spaces/get_node"
        headers = {'Authorization': f"Bearer {self.tenant_access_token}"}
        params = {"obj_type": obj_type, "token": token}
        data = self.request_api('GET', url, params, headers=headers)
        return data

    def wiki_sub_nodes(self, space_id, parent_node):
        """
        查询子节点的所有信息
        """
        url = f"{self.url}/wiki/v2/spaces/{space_id}/nodes"
        headers = {'Authorization': f"Bearer {self.tenant_access_token}"}
        params = {"parent_node_token": parent_node, "page_size": 50}
        result = []
        while True:
            data = self.request_api('GET', url, params, headers=headers)
            params["page_token"] = data.get("data", {}).get("page_token", "")
            result.extend(data.get("data", {}).get("items", []))
            if not data.get("data", {}).get("has_more", False):
                break
        return result

    def create_duplicate(self, space_id, template_token, target_parent_token, target_space_id, title):
        """
        创建文档副本
        """
        url = f"{self.url}/wiki/v2/spaces/{space_id}/nodes/{template_token}/copy"
        headers = {'Authorization': f"Bearer {self.tenant_access_token}"}
        params = {
            "target_parent_token": target_parent_token,
            "target_space_id": target_space_id,
            "title": title
        }
        data = self.request_api('POST', url, params, headers=headers)
        return data


    def bitable_records_all(self, app_token, table_id, body, params=None):
        result = []
        if params is None:
            params = {"page_size": 500}
        while True:
            page_data = self.bitable_records_search(app_token, table_id, body, params)
            has_more = page_data.get("data", {}).get("has_more", False)
            page_token = page_data.get("data", {}).get("page_token", "")
            result.extend(page_data.get("data", {}).get("items", []))
            params['page_token'] = page_token
            if not has_more:
                break
        return result


    def bitable_records_search(self, app_token, table_id, body, params=None):
        url = f"{self.url}/bitable/v1/apps/{app_token}/tables/{table_id}/records/search"
        # 查询数据
        if params is None:
            params = {"page_size": 20}
        return self.request_api("POST", url, body, params=params)

    def bitable_batch_update_all(self, app_token, table_id, body):
        limit = 500
        idx = 0
        while True:
            if len(body) > idx:
                tmp = body[idx:idx + limit]
                req_body = {"records": tmp}
                self.bitable_batch_update(app_token, table_id, req_body)
            else:
                break
            idx += limit

    def bitable_batch_update(self, app_token, table_id, body):
        # 更新单条记录 self.url/bitable/v1/apps/:app_token /tables/:table_id/ records /:record_id
        # 更新多条记录
        url = f"{self.url}/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_update"
        params = {"app_token": app_token, "table_id": table_id}
        return self.request_api("POST", url, body, params=params)

    def bitable_batch_insert_all(self, app_token, table_id, body, index=0):
        limit = 500
        idx = 0
        while True:
            if len(body) > idx:
                tmp = body[idx:idx+limit]
                req_body = {"records": tmp}
                self.bitable_batch_insert(app_token, table_id, req_body, index=index)
            else:
                break
            idx += limit

    def bitable_batch_insert(self, app_token, table_id, body, index=0):
        """
        新增多条记录最多新增 500 条记录
        params :body {records:[{fields:{}},{fields:{}},{fields:{}}]}
        """
        url = f"{self.url}/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_create"
        params = {"app_token": app_token, "table_id": table_id}
        return self.request_api("POST", url, body, params=params)


    def bitable_batch_delete_all(self, app_token, table_id, body):
        limit = 500
        idx = 0
        while True:
            if len(body) > idx:
                tmp = body[idx:idx+limit]
                req_body = {"records": tmp}
                self.bitable_batch_delete(app_token, table_id, req_body)
            else:
                break
            idx += limit
        return

    def bitable_batch_delete(self, app_token, table_id, body):
        """
        删除多条记录
           params :body {records:[record_id,record_id,record_id]}
         https://open.feishu.cn/open-apis/bitable/v1/apps/:app_token/tables/:table_id/records/batch_delete
        """
        url = f"{self.url}/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_delete"
        params = {"app_token": app_token, "table_id": table_id}
        return self.request_api("POST", url, body, params=params)

    def bita_field_id_delete(self, app_token, table_id, body):
        """
        删除字段 DELETE bitable        /v1/apps/:app_token/tables/:table_id/fields/:field_id
        """
        for field_id in body:
            url = f"{self.url}/bitable/v1/apps/{app_token}/tables/{table_id}/fields/{field_id}"
            self.request_api("DELETE", url, {})
        return

    def bita_field_id_create(self, app_token, table_id, body):
        """
        新增字段 body {"field_name":"多行文本","type":1}
        """
        for info in body:

            url = f"{self.url}/bitable/v1/apps/{app_token}/tables/{table_id}/fields"
            self.request_api("POST", url, info)
        return

    def bita_field_id_query(self, app_token, table_id):
        """
        查询字段 get  bitable/v1/apps/:app_token/tables/:table_id/fields
        """
        url = f"{self.url}/bitable/v1/apps/{app_token}/tables/{table_id}/fields"
        field_ids = []
        page_data = self.request_api("GET", url, {})
        rows = page_data.get("data", {}).get("items", [])
        for info in rows:
            logging.debug(f"bita_field_id_query{info}")
            field_ids.append(info.get("field_id"))
        return field_ids

    def sheet_info(self, token, sheet_id):
        url = f"{self.url}/sheets/v3/spreadsheets/{token}/sheets/{sheet_id}"
        headers = {'Authorization': f"Bearer {self.tenant_access_token}"}
        data = self.request_api('GET', url, None, headers=headers)
        return data

    def sheet_single_area_data(self, range):
        url = f"{self.url}/sheets/v2/spreadsheets/{self.target_file_token}/values/{range}"
        params = {
            "valueRenderOption": "FormattedValue",
            "dateTimeRenderOption": "FormattedString",
        }
        data = self.request_api('GET', url, params)
        return data

    def sheet_multi_area_data(self, range):
        url = f"{self.url}/sheets/v2/spreadsheets/{self.target_file_token}/values_batch_get"
        params = {
            "valueRenderOption": "ToString",
            "dateTimeRenderOption": "FormattedString",
            "ranges": ",".join(range)
        }
        data = self.request_api('GET', url, params)
        return data

    def sheet_add_cells(self, body):
        url = f"{self.url}/sheets/v2/spreadsheets/{self.target_file_token}/dimension_range"
        data = self.request_api('POST', url, body)
        return data

    def sheet_write_cells(self, body):
        url = f"{self.url}/sheets/v2/spreadsheets/{self.target_file_token}/values"
        data = self.request_api('PUT', url, body)
        return data

    def sheet_batch_write_cells(self, body):
        url = f"{self.url}/sheets/v2/spreadsheets/{self.target_file_token}/values_batch_update"
        data = self.request_api('POST', url, body)
        return data

    def sheet_update_dimension(self, body):
        url = f"{self.url}/sheets/v2/spreadsheets/{self.target_file_token}/dimension_range"
        data = self.request_api('PUT', url, body)
        return data

    def set_cell_style(self, body):
        url = f"{self.url}/sheets/v2/spreadsheets/{self.target_file_token}/style"
        data = self.request_api('PUT', url, body)
        return data

    # 合并单元格
    def merge_cells(self, body):
        url = f"{self.url}/sheets/v2/spreadsheets/{self.target_file_token}/merge_cells"
        data = self.request_api('POST', url, body)
        return data


    def upload_document_img(self, file_bytes, file_name, parent_node, parent_type='docx_image'):
        """
        上传素材
        """
        url = f"{self.url}/drive/v1/medias/upload_all"
        form = {
            'file_name': file_name,
            'parent_type': parent_type,
            'parent_node': parent_node,
            'size': str(file_bytes.getbuffer().nbytes),
            'file': file_bytes
        }
        multi_form = MultipartEncoder(form)
        headers = {
            'Authorization': f"Bearer {self.tenant_access_token}",
            'Content-Type': multi_form.content_type
        }

        start_time = time.time()
        result = requests.request("POST", url, headers=headers, data=multi_form)
        end_time = time.time()
        if end_time - start_time < 0.334:
            time.sleep(0.334 - (end_time - start_time))
        data = result.json()
        if data['code'] != 0:
            logging.info(f"upload feishu img api code is {data['code']}")
            return []
        return data['data']['file_token']

    def update_permission(self, params, type):
        """
        更新文档权限
        """
        url = f"{self.url}/drive/v1/permissions/{self.target_file_token}/members?need_notification=false&type={type}"
        return self.request_api('POST', url, params)

    def get_sheets(self):
        """
        获取excel中的所有sheet
        """
        url = f"{self.url}/sheets/v3/spreadsheets/{self.target_file_token}/sheets/query"
        headers = {'Authorization': f"Bearer {self.tenant_access_token}"}
        return self.request_api('GET', url, None, headers=headers)

    def add_sheet(self, title_name, sheet_idx=0):
        body = {"requests": [{"addSheet": {"properties": {"title": title_name,"index": sheet_idx}}}]}
        resp_body = self.sheets_batch_update(body)
        return resp_body.get("data",{}).get("replies", [{}])[0].get("addSheet", {}).get("properties", {}).get("sheetId", "")

    def copy_sheet(self, from_sheet_id, title_name):
        body = {"requests": [{ "copySheet": { "source": { "sheetId": from_sheet_id }, "destination": { "title": title_name } } }]}
        resp_body = self.sheets_batch_update(body)
        return resp_body.get("data",{}).get("replies", [{}])[0].get("copySheet", {}).get("properties", {}).get("sheetId", "")

    def delete_sheet(self, del_sheet_id):
        body = {"requests": [{"deleteSheet": {"sheetId": del_sheet_id}}]}
        resp_body = self.sheets_batch_update(body)
        return resp_body.get("data", {}).get("replies", [{}])[0].get("deleteSheet", {}).get("result", False)

    def sheets_batch_update(self, body):
        """
        获取excel中的所有sheet
        """
        url = f"{self.url}/sheets/v2/spreadsheets/{self.target_file_token}/sheets_batch_update"
        headers = {'Authorization': f"Bearer {self.tenant_access_token}"}
        return self.request_api('POST', url, body, headers=headers)

    def replace_sheet(self, sheet_id, body):
        """
        替换单元格的值
        """
        url = f"{self.url}/sheets/v3/spreadsheets/{self.target_file_token}/sheets/{sheet_id}/replace"
        return self.request_api('POST', url, body)


    def get_open_id(self, email):
        """
        根据Email 查询对应的OpenId
        """
        url = f"{self.url}/contact/v3/users/batch_get_id"
        if isinstance(email, list):
            body = {"emails": email}
        else:
            body = {"emails": [email.lower()]}
        result = self.request_api('POST', url, body)
        for item in result.get("data"):
            pass
        # 这个暂时不实现了， 因为企业飞书没有录入邮箱，

    def get_user_by_open_id(self, open_id):
        """
        根据Email 查询对应的OpenId
        """
        url = f"{self.url}/contact/v3/users/{open_id}"
        body = {
            "user_id_type": "open_id"
        }
        result = self.request_api('GET', url, body)
        return result.get("data", {}).get("user", None)

    def send_message(self, open_id, data):
        receive_type = "chat_id" if open_id.startswith("oc_") else "open_id"
        params = {"receive_id_type": receive_type}
        content = {
            "msg_type": "card",
            "type": "template",
            "data": json.loads(data)
        }
        body = {
            "receive_id": open_id,
            "msg_type": "interactive",
            "content": json.dumps(content)
        }
        result = self.request_api('POST', f"{self.url}/im/v1/messages", body, params=params)
        logging.info(result)
        return result


    def request_api(self, method, url, body, headers=None, index=0, params=None):
        """
        请求接口
        """
        headers = self.headers if headers is None else headers
        start_time = time.time()
        try:
            if method == 'GET':
                result = requests.request(method, url, headers=headers, params=body)
            elif method == 'POST' and params is not None and body is not None:
                result = requests.request(method, url, headers=headers, params=params, data=json.dumps(body))
            else:
                result = requests.request(method, url, headers=headers, data=json.dumps(body, cls=JsonEncoder))
        except Exception as e:
            if index < 3:
                time.sleep(1)
                return self.request_api(method, url, body, headers, index + 1)
            raise e

        end_time = time.time()
        if end_time - start_time < 0.334:
            time.sleep(0.334 - (end_time - start_time))
        logging.info(result.text)
        data = result.json()
        if data['code'] == 99991400 and index < 3:
            # 超时重试三次
            time.sleep(1)
            return self.request_api(method, url, body, headers, index + 1)
        if data['code'] == 99991663 and index < 3:
            # 说明会话国企，需要重新获取
            self.tenant_access_token = get_tenant_token(is_force=True)
            self.headers = {
                'Authorization': f"Bearer {self.tenant_access_token}",
                'Content-Type': "application/json; charset=utf-8"
            }
            time.sleep(1)
            return self.request_api(method, url, body, headers, index + 1)
        if data['code'] != 0:
            logging.error(f"feishu request {url} code is {data['code']}")
            raise Exception(f"query feishu fail !!!!{url} {body} {params} {data}")
        return data


    def upload_file(self, file_name, parent_node, file_bytes, parent_type="explorer"):
        """
        上传文件
        """
        url = f"{self.url}/drive/v1/files/upload_all"
        form = {
            'file_name': file_name,
            'parent_type': parent_type,
            'parent_node': parent_node,
            'size': str(file_bytes.getbuffer().nbytes),
            'file': file_bytes
        }

        multi_form = MultipartEncoder(form)
        headers = {
            'Authorization': f"Bearer {self.tenant_access_token}",
            'Content-Type': multi_form.content_type
        }

        start_time = time.time()
        result = requests.request("POST", url, headers=headers, data=multi_form)
        end_time = time.time()
        if end_time - start_time < 0.334:
            time.sleep(0.334 - (end_time - start_time))
        data = result.json()
        if data['code'] != 0:
            logging.info(f"upload feishu file api code is {data['code']}")
            return []
        return data['data']['file_token']

