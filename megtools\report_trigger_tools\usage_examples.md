# 飞书报告触发脚本使用示例

本文档展示如何在其他程序中调用飞书报告触发脚本。

## 可用脚本

1. **simple_trigger.py** - Python脚本，支持模块导入和命令行调用
2. **trigger_report_script.py** - 完整功能的Python脚本
3. **trigger_report.ps1** - PowerShell脚本
4. **trigger_report.bat** - Windows批处理脚本

## Python程序调用示例

### 1. 作为模块导入使用

```python
# 导入模块
from simple_trigger import trigger_report

# 基本调用
result = trigger_report(
    report_id=1,
    vin="TEST123456789"
)

if result['success']:
    print("报告触发成功")
    print(f"响应数据: {result['data']}")
else:
    print(f"报告触发失败: {result['error']}")

# 完整参数调用
result = trigger_report(
    report_id=1,
    vin="TEST123456789",
    start_time="2024-01-01 00:00:00",
    end_time="2024-01-01 23:59:59",
    custom_params="额外参数",
    status="4",
    base_url="http://localhost:8000",
    username="admin",
    password="password"
)
```

### 2. 使用ReportTrigger类

```python
from simple_trigger import ReportTrigger

# 创建触发器实例
trigger = ReportTrigger(
    base_url="http://localhost:8000",
    username="admin",
    password="password"
)

# 登录
if trigger.login():
    # 触发多个报告
    reports = [
        {"report_id": 1, "vin": "VIN001"},
        {"report_id": 2, "vin": "VIN002"},
        {"report_id": 3, "vin": "VIN003"}
    ]
    
    for report in reports:
        result = trigger.trigger_report(**report)
        print(f"报告 {report['report_id']}: {'成功' if result['success'] else '失败'}")
else:
    print("登录失败")
```

## 命令行调用示例

### 1. Python脚本调用

```bash
# 基本调用
python simple_trigger.py --report-id 1 --vin TEST123

# 完整参数调用
python simple_trigger.py --report-id 1 --vin TEST123 --start-time "2024-01-01 00:00:00" --end-time "2024-01-01 23:59:59"

# JSON参数调用
python simple_trigger.py --json '{"report_id": 1, "vin": "TEST123", "start_time": "2024-01-01 00:00:00"}'

# 静默模式（只输出JSON结果）
python simple_trigger.py --report-id 1 --vin TEST123 --quiet

# 格式化输出
python simple_trigger.py --report-id 1 --vin TEST123 --pretty
```

### 2. PowerShell脚本调用

```powershell
# 基本调用
.\trigger_report.ps1 -ReportId 1 -Vin "TEST123"

# 完整参数调用
.\trigger_report.ps1 -ReportId 1 -Vin "TEST123" -StartTime "2024-01-01 00:00:00" -EndTime "2024-01-01 23:59:59"

# 使用配置文件
.\trigger_report.ps1 -ConfigFile "config.json"

# 批量触发
.\trigger_report.ps1 -ConfigFile "config.json" -Batch

# JSON输出
.\trigger_report.ps1 -ReportId 1 -Vin "TEST123" -JsonOutput

# 静默模式
.\trigger_report.ps1 -ReportId 1 -Vin "TEST123" -Quiet
```

## 其他编程语言调用示例

### 1. C# 调用

```csharp
using System;
using System.Diagnostics;
using System.Text.Json;

public class ReportTrigger
{
    public static bool TriggerReport(int reportId, string vin, string startTime = "", string endTime = "")
    {
        var startInfo = new ProcessStartInfo
        {
            FileName = "python",
            Arguments = $"simple_trigger.py --report-id {reportId} --vin {vin} --start-time \"{startTime}\" --end-time \"{endTime}\" --quiet",
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        using var process = Process.Start(startInfo);
        process.WaitForExit();
        
        var output = process.StandardOutput.ReadToEnd();
        var result = JsonSerializer.Deserialize<JsonElement>(output);
        
        return result.GetProperty("success").GetBoolean();
    }
}

// 使用示例
bool success = ReportTrigger.TriggerReport(1, "TEST123", "2024-01-01 00:00:00", "2024-01-01 23:59:59");
Console.WriteLine($"报告触发: {(success ? "成功" : "失败")}");
```

### 2. Java 调用

```java
import java.io.*;
import java.util.concurrent.TimeUnit;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class ReportTrigger {
    public static boolean triggerReport(int reportId, String vin, String startTime, String endTime) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                "python", "simple_trigger.py",
                "--report-id", String.valueOf(reportId),
                "--vin", vin,
                "--start-time", startTime,
                "--end-time", endTime,
                "--quiet"
            );
            
            Process process = pb.start();
            process.waitFor(60, TimeUnit.SECONDS);
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String output = reader.readLine();
            
            JsonObject result = JsonParser.parseString(output).getAsJsonObject();
            return result.get("success").getAsBoolean();
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    // 使用示例
    public static void main(String[] args) {
        boolean success = triggerReport(1, "TEST123", "2024-01-01 00:00:00", "2024-01-01 23:59:59");
        System.out.println("报告触发: " + (success ? "成功" : "失败"));
    }
}
```

### 3. Node.js 调用

```javascript
const { spawn } = require('child_process');

function triggerReport(reportId, vin, startTime = '', endTime = '') {
    return new Promise((resolve, reject) => {
        const args = [
            'simple_trigger.py',
            '--report-id', reportId.toString(),
            '--vin', vin,
            '--quiet'
        ];
        
        if (startTime) {
            args.push('--start-time', startTime);
        }
        if (endTime) {
            args.push('--end-time', endTime);
        }
        
        const process = spawn('python', args);
        let output = '';
        
        process.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        process.on('close', (code) => {
            try {
                const result = JSON.parse(output.trim());
                resolve(result.success);
            } catch (error) {
                reject(error);
            }
        });
        
        process.on('error', reject);
    });
}

// 使用示例
(async () => {
    try {
        const success = await triggerReport(1, 'TEST123', '2024-01-01 00:00:00', '2024-01-01 23:59:59');
        console.log(`报告触发: ${success ? '成功' : '失败'}`);
    } catch (error) {
        console.error('调用失败:', error);
    }
})();
```

## 配置文件示例

### config.json
```json
{
    "base_url": "http://localhost:8000",
    "username": "admin",
    "password": "password",
    "reports": [
        {
            "report_id": 1,
            "vin": "TEST123456789",
            "start_time": "2024-01-01 00:00:00",
            "end_time": "2024-01-01 23:59:59",
            "status": "4",
            "custom_params": "额外参数"
        },
        {
            "report_id": 2,
            "vin": "TEST987654321",
            "start_time": "2024-01-02 00:00:00",
            "end_time": "2024-01-02 23:59:59",
            "status": "4",
            "custom_params": ""
        }
    ]
}
```

## 返回值格式

### 成功响应
```json
{
    "success": true,
    "data": {
        "status": 200,
        "message": "报告触发成功",
        "data": {
            "report_id": 1,
            "task_id": "task_123456"
        }
    }
}
```

### 失败响应
```json
{
    "success": false,
    "error": "登录失败"
}
```

## 注意事项

1. **依赖要求**: 确保安装了 `requests` 库：`pip install requests`
2. **网络连接**: 确保能够访问 MegTool Backend 服务
3. **认证信息**: 确保用户名和密码正确
4. **参数格式**: 时间格式必须为 `YYYY-MM-DD HH:MM:SS`
5. **错误处理**: 建议在调用时添加适当的错误处理逻辑
6. **超时设置**: 网络请求有60秒超时限制
7. **日志记录**: 脚本会自动生成 `trigger_report.log` 日志文件

## 性能建议

1. **复用连接**: 如需触发多个报告，建议使用 `ReportTrigger` 类复用登录状态
2. **并发控制**: 避免同时触发过多报告，建议控制并发数量
3. **错误重试**: 对于网络错误，可以实现重试机制
4. **参数验证**: 在调用前验证必要参数的有效性