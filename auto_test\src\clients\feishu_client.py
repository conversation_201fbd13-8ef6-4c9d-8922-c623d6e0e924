import warnings
import os
import json
import time
from datetime import datetime
from typing import List, Tuple, Optional

# 抑制pkg_resources相关的警告
warnings.filterwarnings("ignore", category=UserWarning, module="pkg_resources")
warnings.filterwarnings("ignore", message=".*pkg_resources is deprecated.*")

# 延迟导入lark_oapi以减少启动时间
try:
    import lark_oapi as lark
    from lark_oapi.api.drive.v1 import *
except ImportError as e:
    print(f"警告: 无法导入lark_oapi: {e}")
    lark = None

from src.clients.base_client import BaseClient
from src.utils.logger import get_logger, log_success, log_error, log_warning, log_info, log_start
from src.utils.exceptions import FeishuConnectionError

logger = get_logger(__name__)


class FeishuClient(BaseClient):
    """飞书客户端"""
    
    def _initialize_client(self) -> None:
        """初始化飞书客户端连接"""
        try:
            # 检查lark_oapi是否可用
            if lark is None:
                log_error("lark_oapi库未正确安装或导入失败")
                return
                
            app_id = self.get_config('Feishu', 'app_id')
            app_secret = self.get_config('Feishu', 'app_secret')
            
            if not app_id or not app_secret:
                log_warning("飞书配置信息不完整")
                return
            
            self.client = lark.Client.builder() \
                .app_id(app_id) \
                .app_secret(app_secret) \
                .log_level(lark.LogLevel.INFO) \
                .timeout(30) \
                .build()
            
            log_success("飞书客户端初始化成功")
            
        except Exception as e:
            log_error(f"初始化飞书客户端失败: {str(e)}")
            raise FeishuConnectionError(f"飞书连接失败: {str(e)}")
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.client is not None
    
    def test_connection(self) -> bool:
        """测试连接"""
        if not self.is_connected():
            return False
        
        try:
            # 尝试获取应用信息来测试连接
            # 这里可以添加具体的测试逻辑
            log_success("飞书连接测试成功")
            return True
        except Exception as e:
            log_error(f"飞书连接测试失败: {str(e)}")
            return False
    
    def upload_document(self, file_path: str, mr_labels: List[str]) -> Tuple[bool, Optional[str]]:
        """上传文档到飞书并返回文档链接"""
        folder_token = self.get_config('Feishu', 'folder_token')
        
        if not self.is_connected() or not folder_token:
            log_warning("飞书配置不完整，跳过上传")
            return False, None
        
        if not os.path.exists(file_path):
            log_error(f"文件不存在: {file_path}")
            return False, None
        
        try:
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            # 检查文件大小
            if file_size > 100 * 1024 * 1024:  # 100MB限制
                log_error(f"文件过大: {file_size} 字节，超过100MB限制")
                return False, None
            
            # 生成文档名称
            date_str = datetime.now().strftime("%Y%m%d")
            doc_name = f"{mr_labels[0]}_{date_str}_测试报告" if mr_labels else f"测试报告_{date_str}"
            
            log_start(f"正在上传文件: {file_name} ({file_size}字节)")
            
            # 上传文件
            with open(file_path, "rb") as file:
                upload_request = UploadAllMediaRequest.builder() \
                    .request_body(UploadAllMediaRequestBody.builder()
                                 .file_name(file_name)
                                 .parent_type("explorer")
                                 .parent_node(folder_token)
                                 .size(str(file_size))
                                 .extra(json.dumps({
                                     "file_extension": "md",
                                     "obj_type": "docx"
                                 }))
                                 .file(file)
                                 .build()) \
                    .build()
                
                log_info("发送上传请求...")
                upload_response = self.client.drive.v1.media.upload_all(upload_request)
                
                if not upload_response.success():
                    error_msg = f"文件上传失败: code={upload_response.code}, msg={upload_response.msg}"
                    if upload_response.code == 400:
                        error_msg += " (可能是权限问题或文件格式不支持)"
                    elif upload_response.code == 401:
                        error_msg += " (认证失败，请检查App ID和App Secret)"
                    elif upload_response.code == 403:
                        error_msg += " (权限不足，请检查应用权限)"
                    elif upload_response.code == 429:
                        error_msg += " (请求过于频繁，请稍后重试)"
                    log_error(error_msg)
                    return False, None
                
                file_token = upload_response.data.file_token
                log_success(f"文件上传成功! 文件token: {file_token}")
            
            # 创建转换任务
            log_start(f"正在创建转换任务: {doc_name}")
            
            convert_request = CreateImportTaskRequest.builder() \
                .request_body(ImportTask.builder()
                             .file_extension("md")
                             .file_token(file_token)
                             .type("docx")
                             .file_name(doc_name)
                             .point(ImportTaskMountPoint.builder()
                                    .mount_type(1)
                                    .mount_key(folder_token)
                                    .build())
                             .build()) \
                .build()
            
            log_info("发送转换请求...")
            convert_response = self.client.drive.v1.import_task.create(convert_request)
            
            if not convert_response.success():
                error_msg = f"创建转换任务失败: code={convert_response.code}, msg={convert_response.msg}"
                log_error(error_msg)
                return False, None
            
            ticket = convert_response.data.ticket
            log_success(f"转换任务创建成功! 任务ID: {ticket}")
            
            
            # 清理操作
            self._cleanup_resources(file_path, file_token)
            
            return  True, file_token
            
        except Exception as e:
            log_error(f"飞书操作出错: {str(e)}")
            # 添加更详细的错误信息
            if "timeout" in str(e).lower():
                log_error("请求超时，请检查网络连接")
            elif "connection" in str(e).lower():
                log_error("连接失败，请检查网络和API地址")
            elif "ssl" in str(e).lower():
                log_error("SSL证书验证失败")
            return False, None
    
    
    def delete_file(self, file_token: str) -> bool:
        """删除飞书文件"""
        if not self.is_connected():
            log_error("飞书客户端未初始化")
            return False
        
        try:
            request = DeleteFileRequest.builder() \
                .file_token(file_token) \
                .type("file") \
                .build()
            
            response = self.client.drive.v1.file.delete(request)
            
            if not response.success():
                log_error(f"文件删除失败: code={response.code}, msg={response.msg}")
                return False
            return True
        except Exception as e:
            log_error(f"删除飞书文件出错: {str(e)}")
            return False
    
    def upload_and_convert(self, file_path: str, mr_labels: List[str]) -> Tuple[bool, Optional[str]]:
        """上传并转换为飞书文档（兼容旧方法）"""
        return self.upload_document(file_path, mr_labels)
    
    def _cleanup_resources(self, file_path: str, file_token: Optional[str]):
        """清理本地和远程资源"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                log_info(f"已删除本地文件: {file_path}")
        except Exception as e:
            log_error(f"本地文件删除失败: {str(e)}")
        
        if file_token:
            if self.delete_file(file_token):
                log_info("已删除飞书原始文件")
            else:
                log_warning("警告：飞书原始文件删除失败，请手动清理")
    
    def get_folder_info(self) -> Optional[dict]:
        """获取文件夹信息"""
        if not self.is_connected():
            return None
        
        try:
            folder_token = self.get_config('Feishu', 'folder_token')
            if not folder_token:
                return None
            
            # 这里可以添加获取文件夹信息的逻辑
            return {
                'folder_token': folder_token,
                'folder_name': '测试报告文件夹'
            }
        except Exception as e:
            log_error(f"获取文件夹信息失败: {str(e)}")
            return None 