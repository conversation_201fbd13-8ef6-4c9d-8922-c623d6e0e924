import re
from typing import List, Optional, Dict
from jira import JIRA
from src.clients.base_client import BaseClient
from src.utils.logger import get_logger, log_connection, log_success, log_error, log_info
from src.utils.exceptions import JiraConnectionError

logger = get_logger(__name__)


class JiraClient(BaseClient):
    """Jira客户端"""
    
    def _initialize_client(self) -> None:
        """初始化Jira客户端连接"""
        try:
            jira_url = self.get_config('Jira', 'url')
            jira_user = self.get_config('Jira', 'user')
            jira_password = self.get_config('Jira', 'password')
            
            if not all([jira_url, jira_user, jira_password]):
                log_error("Jira配置信息不完整")
                return
            
            timeout = self.get_config_int('Jira', 'timeout', 20)
            
            self.client = JIRA(
                server=jira_url,
                basic_auth=(jira_user, jira_password),
                options={'verify': True},
                timeout=timeout
            )
            log_success("Jira客户端初始化成功")
            
        except Exception as e:
            log_error(f"初始化Jira客户端失败: {str(e)}")
            raise JiraConnectionError(f"Jira连接失败: {str(e)}")
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.client is not None
    
    def test_connection(self) -> bool:
        """测试连接"""
        if not self.is_connected():
            return False
        
        try:
            # 尝试获取当前用户信息来测试连接
            current_user = self.client.current_user()
            log_success(f"Jira连接测试成功，当前用户: {current_user}")
            return True
        except Exception as e:
            log_error(f"Jira连接测试失败: {str(e)}")
            return False
    
    def get_issues(self) -> List:
        """获取所有匹配的问题"""
        if not self.is_connected():
            log_error("Jira连接未建立，无法获取问题")
            return []
        
        try:
            base_jql = self.get_config('Jira', 'jql')
            max_results = self.get_config_int('Jira', 'max_results', 100)
            
            log_connection(f"正在执行 JQL 查询: {base_jql}")
            
            all_issues = []
            start_at = 0
            total = None
            
            while total is None or start_at < total:
                issues = self.client.search_issues(
                    base_jql,
                    startAt=start_at,
                    maxResults=max_results,
                    fields="summary,labels,created,status,priority,assignee,customfield_13000,customfield_13001,customfield_13002,algorithm_labels,algorithm_tags,problem_type,issue_type"
                )
                all_issues.extend(issues)
                if total is None:
                    total = issues.total
                start_at += len(issues)
                log_info(f"已获取 {len(all_issues)}/{total} 个问题")
            
            log_success(f"找到 {len(all_issues)} 个匹配问题")
            return all_issues
        
        except Exception as e:
            log_error(f"获取问题失败: {str(e)}")
            raise JiraConnectionError(f"获取Jira问题失败: {str(e)}")
    
    def print_issues(self, issues: List):
        """打印问题列表"""
        if not issues:
            log_info("没有可用的问题")
            return
        
        print("\n" + "="*80)
        print("📋 所有问题列表".center(80))
        print("="*80)
        
        for i, issue in enumerate(issues, 1):
            print(f"{i:2d}. {issue.key:<15} | {issue.fields.summary[:60]:<60}")
        
        print("="*80)
        print(f"总计: {len(issues)} 个问题\n")
    
    def extract_mr_labels(self, issues: List) -> List[str]:
        """提取MR标签"""
        if not issues:
            return []
        
        pattern = re.compile(r'^MR_.*', re.IGNORECASE)
        labels = set()
        
        for issue in issues:
            issue_labels = issue.fields.labels or []
            for label in issue_labels:
                if pattern.match(label):
                    labels.add(label)
        
        return sorted(list(labels))
    
    def _get_algorithm_labels_from_customfield(self, issue) -> List[str]:
        """从多个可能的自定义字段获取算法问题标签"""
        try:
            # 尝试多个可能的字段名称
            possible_fields = [
                'customfield_13000',
                'customfield_13001',
                'customfield_13002',
                'algorithm_labels',
                'algorithm_tags',
                'problem_type',
                'issue_type'
            ]
            
            algorithm_labels = []
            
            for field_name in possible_fields:
                try:
                    custom_field = getattr(issue.fields, field_name, None)
                    
                    if custom_field:
                        log_info(f"问题 {issue.key} 字段 {field_name} 有值: {custom_field} (类型: {type(custom_field)})")
                        
                        # 处理不同类型的自定义字段值
                        if isinstance(custom_field, list):
                            field_labels = [str(label).strip() for label in custom_field if label]
                            algorithm_labels.extend(field_labels)
                        elif isinstance(custom_field, str):
                            # 支持多种分隔符：逗号、空格、分号
                            separators = [',', ';', ' ']
                            field_labels = []
                            for sep in separators:
                                if sep in custom_field:
                                    field_labels = [label.strip() for label in custom_field.split(sep) if label.strip()]
                                    break
                            if not field_labels:
                                field_labels = [custom_field.strip()]
                            algorithm_labels.extend(field_labels)
                        elif hasattr(custom_field, 'value'):
                            if custom_field.value:
                                algorithm_labels.append(str(custom_field.value).strip())
                        else:
                            if custom_field:
                                algorithm_labels.append(str(custom_field).strip())
                        
                        # 如果找到了数据，记录日志并停止搜索
                        if algorithm_labels:
                            log_info(f"从字段 {field_name} 获取到算法标签: {algorithm_labels}")
                            break
                    else:
                        log_info(f"问题 {issue.key} 字段 {field_name} 为空")
                            
                except Exception as e:
                    log_error(f"获取字段 {field_name} 失败: {str(e)}")
                    continue
            
            # 如果没有找到算法标签，尝试从摘要中提取
            if not algorithm_labels:
                summary = issue.fields.summary.lower()
                keywords = ['算法', 'algorithm', 'algo', '问题', 'issue', 'problem', '检测', 'detect']
                if any(keyword in summary for keyword in keywords):
                    algorithm_labels.append('算法问题_自动识别')
                    log_info(f"从摘要中自动识别算法问题: {issue.key}")
            
            # 如果仍然没有标签，强制添加一个默认标签
            if not algorithm_labels:
                algorithm_labels.append('算法问题_默认分类')
                log_info(f"为问题 {issue.key} 添加默认分类")
            
            return algorithm_labels
                
        except Exception as e:
            log_error(f"获取算法标签失败 {issue.key}: {str(e)}")
            return ['算法问题_错误分类']

    def extract_algorithm_labels(self, issues: List) -> Dict[str, List]:
        """提取算法问题标签并分类"""
        if not issues:
            return {}
        
        algorithm_categories = {}
        total_processed = 0
        total_with_labels = 0
        
        for issue in issues:
            total_processed += 1
            # 从多个可能的自定义字段获取算法标签
            algorithm_labels = self._get_algorithm_labels_from_customfield(issue)
            
            # 调试信息
            if algorithm_labels:
                log_info(f"问题 {issue.key} 提取到标签: {algorithm_labels}")
                total_with_labels += 1
            else:
                log_info(f"问题 {issue.key} 未提取到任何标签")
            
            # 如果有算法标签，进行分类
            if algorithm_labels:
                # 使用第一个算法标签作为主要分类
                primary_category = algorithm_labels[0]
                
                if primary_category not in algorithm_categories:
                    algorithm_categories[primary_category] = []
                
                # 记录问题信息
                issue_info = {
                    'key': issue.key,
                    'summary': issue.fields.summary,
                    'status': issue.fields.status.name if issue.fields.status else 'Unknown',
                    'all_algorithm_labels': algorithm_labels,
                    'label_count': len(algorithm_labels)
                }
                
                algorithm_categories[primary_category].append(issue_info)
        
        log_info(f"处理了 {total_processed} 个问题，其中 {total_with_labels} 个有标签")
        log_info(f"分类结果: {list(algorithm_categories.keys())}")
        
        return algorithm_categories
    
    def get_algorithm_issue_statistics(self, issues: List) -> Dict:
        """获取算法问题统计信息"""
        algorithm_categories = self.extract_algorithm_labels(issues)
        
        statistics = {
            'total_algorithm_issues': 0,
            'categories': {},
            'multi_label_issues': []
        }
        
        for category, category_issues in algorithm_categories.items():
            statistics['total_algorithm_issues'] += len(category_issues)
            statistics['categories'][category] = {
                'count': len(category_issues),
                'issues': category_issues
            }
            
            # 检查多标签问题
            for issue in category_issues:
                if issue['label_count'] > 1:
                    statistics['multi_label_issues'].append(issue)
        
        return statistics
    
    def print_algorithm_analysis(self, issues: List):
        """打印算法问题分析"""
        if not issues:
            log_info("没有可用的问题")
            return
        
        statistics = self.get_algorithm_issue_statistics(issues)
        
        print("\n" + "="*80)
        print("🤖 算法问题分析".center(80))
        print("="*80)
        
        print(f"📊 总算法问题数: {statistics['total_algorithm_issues']}")
        print(f"📂 分类数量: {len(statistics['categories'])}")
        print(f"🏷️  多标签问题数: {len(statistics['multi_label_issues'])}")
        
        print("\n📋 分类详情:")
        print("-" * 80)
        
        for category, data in statistics['categories'].items():
            print(f"\n🔹 {category} ({data['count']}个问题):")
            for issue in data['issues']:
                label_info = ""
                if issue['label_count'] > 1:
                    label_info = f" [多标签: {', '.join(issue['all_algorithm_labels'])}]"
                print(f"   • {issue['key']} | {issue['summary'][:50]}... | {issue['status']}{label_info}")
        
        if statistics['multi_label_issues']:
            print(f"\n🏷️  多标签问题详情 ({len(statistics['multi_label_issues'])}个):")
            print("-" * 80)
            for issue in statistics['multi_label_issues']:
                print(f"   • {issue['key']} | {issue['summary'][:50]}...")
                print(f"     标签: {', '.join(issue['all_algorithm_labels'])}")
        
        print("="*80)
    
    def get_issue_details(self, issue_key: str) -> Optional[dict]:
        """获取问题详细信息"""
        if not self.is_connected():
            return None
        
        try:
            issue = self.client.issue(issue_key)
            return {
                'key': issue.key,
                'summary': issue.fields.summary,
                'status': issue.fields.status.name if issue.fields.status else None,
                'priority': issue.fields.priority.name if issue.fields.priority else None,
                'assignee': issue.fields.assignee.displayName if issue.fields.assignee else None,
                'created': issue.fields.created,
                'labels': issue.fields.labels or []
            }
        except Exception as e:
            log_error(f"获取问题详情失败 {issue_key}: {str(e)}")
            return None
    
    def get_issues_by_status(self, status: str) -> List:
        """根据状态获取问题"""
        if not self.is_connected():
            return []
        
        try:
            jql = f"status = '{status}'"
            issues = self.client.search_issues(jql, maxResults=1000)
            return issues
        except Exception as e:
            log_error(f"根据状态获取问题失败: {str(e)}")
            return [] 