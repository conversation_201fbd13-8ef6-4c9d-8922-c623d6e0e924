"""
MCAP自动驾驶数据解析器
高性能、可扩展的MCAP数据解析框架

主要功能:
- 支持43种自动驾驶消息类型
- 高性能流式处理
- 智能解析器路由
- 完整的数据结构定义
- 可扩展的插件架构

Author: MCAP SDK Team
Version: 3.0.0
Created: 2025-01-30
"""

import logging

__version__ = "3.0.0"
__author__ = "MCAP SDK Team"

from .main import get_version, quick_analyze
from .core.sdk import McapAutoDriveSDK
from .core.parser_manager import ParserManager
from .core.message_router import MessageRouter as CoreMessageRouter
from .data_structures import *
from .parsers import *
from .tools import *
from .cli import *

# 可选导入
try:
    from .parsers.fast_parser import FastMcapParser
except ImportError:
    logger.info("FastMcapParser 不可用")
    FastMcapParser = None

try:
    from .tools.demo import McapDemo
except ImportError:
    logger.info("McapDemo 不可用")
    McapDemo = None

__all__ = [
    # 核心类
    'McapAutoDriveSDK',
    'ParserManager', 
    'MessageRouter',
    'ParserRegistry',
    'BaseParser',
    
    # 主要功能
    'get_version',
    'quick_analyze',
    
    # 工具函数
    'extract_message_types',
    'collect_sample_data',
    'generate_json_files',
    'analyze_mcap_file',
    'sanitize_filename',
    'PerformanceMonitor',
    'get_logger',
    
    # CLI
    'cli_main',
    
    # 版本信息
    '__version__',
    '__author__'
]
