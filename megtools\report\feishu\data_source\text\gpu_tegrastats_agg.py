import numpy as np

from basic.utils import SqlUtil
from report.feishu.data_source.image.line_chart_image import LineChartImage


class GpuTegrastatsAgg(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.handle_conditions(variables, params)

    def apply(self):
        # 查询生成图表的数据
        chart_data = self.query_imgdata()
        num_arr = [item[self.condition['indicator']] for item in chart_data]
        chart_np = np.array(num_arr, dtype=np.float64)
        if self.condition['type'].upper() == "P99":
            return f"{np.nanpercentile(chart_np, 99, interpolation='nearest')}"
        elif self.condition['type'].upper() == "AVG":
            return f"{round(np.nanmean(chart_np), 2)}"

    def query_imgdata(self):
        sql = f""" select record_time , round(max({self.condition['indicator']}), 4) {self.condition['indicator']} from dataview_gpu_tegrastats_monitor 
        where vin = %s and record_time >= %s and record_time <= %s  group by record_time order by record_time asc"""
        result = SqlUtil.query_all_dict(sql, (self.condition['vin'],
                                            self.condition['start_time'],
                                            self.condition['end_time'],))
        return result


    def handle_conditions(self, variables, params):
        params = params.split(",")
        # 如果没有指定VIN，直接生成图片
        self.condition["vin"] = variables.get('vin', '')
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.condition['indicator'] = params[0]
        # P99 AVG
        self.condition['type'] = params[1]
