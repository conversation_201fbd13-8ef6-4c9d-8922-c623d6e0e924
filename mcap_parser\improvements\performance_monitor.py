#!/usr/bin/env python3
"""
性能监控和分析模块
提供详细的性能指标和优化建议
"""

import time
import psutil
import threading
from contextlib import contextmanager
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Callable
from collections import defaultdict, deque
import json
from pathlib import Path


@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    memory_before: int
    memory_after: int
    memory_peak: int
    cpu_percent: float
    messages_processed: int = 0
    bytes_processed: int = 0
    
    @property
    def throughput_msg_per_sec(self) -> float:
        """消息处理吞吐量（消息/秒）"""
        return self.messages_processed / self.duration if self.duration > 0 else 0
    
    @property
    def throughput_mb_per_sec(self) -> float:
        """数据处理吞吐量（MB/秒）"""
        return (self.bytes_processed / 1024 / 1024) / self.duration if self.duration > 0 else 0
    
    @property
    def memory_usage_mb(self) -> float:
        """内存使用量（MB）"""
        return (self.memory_after - self.memory_before) / 1024 / 1024
    
    @property
    def memory_peak_mb(self) -> float:
        """峰值内存使用量（MB）"""
        return self.memory_peak / 1024 / 1024


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, enable_detailed_monitoring: bool = True):
        self.enable_detailed_monitoring = enable_detailed_monitoring
        self.metrics_history: List[PerformanceMetrics] = []
        self.current_operation: Optional[str] = None
        self.operation_stack: List[Dict[str, Any]] = []
        self._monitoring_thread: Optional[threading.Thread] = None
        self._stop_monitoring = threading.Event()
        self._memory_samples: deque = deque(maxlen=1000)
        self._cpu_samples: deque = deque(maxlen=1000)
    
    @contextmanager
    def monitor_operation(self, operation_name: str, 
                         expected_messages: Optional[int] = None,
                         expected_bytes: Optional[int] = None):
        """监控操作的上下文管理器"""
        
        # 开始监控
        start_metrics = self._start_operation(operation_name)
        
        try:
            yield self
        finally:
            # 结束监控
            self._end_operation(start_metrics, expected_messages, expected_bytes)
    
    def _start_operation(self, operation_name: str) -> Dict[str, Any]:
        """开始操作监控"""
        process = psutil.Process()
        
        start_metrics = {
            'operation_name': operation_name,
            'start_time': time.time(),
            'memory_before': process.memory_info().rss,
            'cpu_before': process.cpu_percent(),
            'messages_processed': 0,
            'bytes_processed': 0
        }
        
        self.operation_stack.append(start_metrics)
        self.current_operation = operation_name
        
        # 启动详细监控
        if self.enable_detailed_monitoring:
            self._start_detailed_monitoring()
        
        return start_metrics
    
    def _end_operation(self, start_metrics: Dict[str, Any],
                      expected_messages: Optional[int] = None,
                      expected_bytes: Optional[int] = None):
        """结束操作监控"""
        end_time = time.time()
        process = psutil.Process()
        
        # 停止详细监控
        if self.enable_detailed_monitoring:
            self._stop_detailed_monitoring()
        
        # 计算指标
        metrics = PerformanceMetrics(
            operation_name=start_metrics['operation_name'],
            start_time=start_metrics['start_time'],
            end_time=end_time,
            duration=end_time - start_metrics['start_time'],
            memory_before=start_metrics['memory_before'],
            memory_after=process.memory_info().rss,
            memory_peak=max(self._memory_samples) if self._memory_samples else process.memory_info().rss,
            cpu_percent=process.cpu_percent(),
            messages_processed=start_metrics.get('messages_processed', 0),
            bytes_processed=start_metrics.get('bytes_processed', 0)
        )
        
        self.metrics_history.append(metrics)
        self.operation_stack.pop()
        self.current_operation = self.operation_stack[-1]['operation_name'] if self.operation_stack else None
        
        # 输出性能报告
        self._log_performance_metrics(metrics)
    
    def _start_detailed_monitoring(self):
        """启动详细监控线程"""
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            return
        
        self._stop_monitoring.clear()
        self._monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self._monitoring_thread.daemon = True
        self._monitoring_thread.start()
    
    def _stop_detailed_monitoring(self):
        """停止详细监控线程"""
        self._stop_monitoring.set()
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=1.0)
    
    def _monitoring_loop(self):
        """监控循环"""
        process = psutil.Process()
        
        while not self._stop_monitoring.is_set():
            try:
                # 采样内存和CPU
                memory_info = process.memory_info()
                self._memory_samples.append(memory_info.rss)
                self._cpu_samples.append(process.cpu_percent())
                
                time.sleep(0.1)  # 100ms采样间隔
            except Exception:
                break
    
    def _log_performance_metrics(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        print(f"⚡ 性能报告 - {metrics.operation_name}:")
        print(f"   耗时: {metrics.duration:.3f}s")
        print(f"   内存使用: {metrics.memory_usage_mb:.1f}MB")
        print(f"   峰值内存: {metrics.memory_peak_mb:.1f}MB")
        print(f"   CPU使用率: {metrics.cpu_percent:.1f}%")
        
        if metrics.messages_processed > 0:
            print(f"   处理消息: {metrics.messages_processed}")
            print(f"   消息吞吐量: {metrics.throughput_msg_per_sec:.1f} msg/s")
        
        if metrics.bytes_processed > 0:
            print(f"   处理数据: {metrics.bytes_processed / 1024 / 1024:.1f}MB")
            print(f"   数据吞吐量: {metrics.throughput_mb_per_sec:.1f} MB/s")
    
    def update_progress(self, messages_processed: int = 0, bytes_processed: int = 0):
        """更新处理进度"""
        if self.operation_stack:
            current_op = self.operation_stack[-1]
            current_op['messages_processed'] += messages_processed
            current_op['bytes_processed'] += bytes_processed
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def get_cpu_usage(self) -> float:
        """获取当前CPU使用率"""
        process = psutil.Process()
        return process.cpu_percent()
    
    def __enter__(self):
        """进入上下文管理器"""
        self.start_time = time.time()
        self.memory_usage = self.get_memory_usage()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        self.elapsed_time = time.time() - self.start_time
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {
            'active_operations': len(self.operation_stack),
            'completed_operations': len(self.metrics_history),
            'memory_usage': self.get_memory_usage(),
            'cpu_usage': self.get_cpu_usage()
        }
        
        # 如果有elapsed_time属性，添加到统计中
        if hasattr(self, 'elapsed_time'):
            stats['elapsed_time'] = self.elapsed_time
            
        return stats
    
    def get_operation_summary(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """获取操作摘要"""
        if operation_name:
            relevant_metrics = [m for m in self.metrics_history if m.operation_name == operation_name]
        else:
            relevant_metrics = self.metrics_history
        
        if not relevant_metrics:
            return {}
        
        total_duration = sum(m.duration for m in relevant_metrics)
        total_messages = sum(m.messages_processed for m in relevant_metrics)
        total_bytes = sum(m.bytes_processed for m in relevant_metrics)
        avg_memory = sum(m.memory_usage_mb for m in relevant_metrics) / len(relevant_metrics)
        
        return {
            'operation_count': len(relevant_metrics),
            'total_duration': total_duration,
            'avg_duration': total_duration / len(relevant_metrics),
            'total_messages': total_messages,
            'total_bytes': total_bytes,
            'avg_memory_mb': avg_memory,
            'avg_throughput_msg_per_sec': total_messages / total_duration if total_duration > 0 else 0,
            'avg_throughput_mb_per_sec': (total_bytes / 1024 / 1024) / total_duration if total_duration > 0 else 0
        }
    
    def generate_performance_report(self, output_file: Optional[Path] = None) -> Dict[str, Any]:
        """生成性能报告"""
        report = {
            'timestamp': time.time(),
            'total_operations': len(self.metrics_history),
            'operations_summary': {},
            'detailed_metrics': []
        }
        
        # 按操作类型分组统计
        operations = defaultdict(list)
        for metrics in self.metrics_history:
            operations[metrics.operation_name].append(metrics)
        
        for op_name, op_metrics in operations.items():
            report['operations_summary'][op_name] = self.get_operation_summary(op_name)
        
        # 详细指标
        for metrics in self.metrics_history:
            report['detailed_metrics'].append({
                'operation_name': metrics.operation_name,
                'duration': metrics.duration,
                'memory_usage_mb': metrics.memory_usage_mb,
                'memory_peak_mb': metrics.memory_peak_mb,
                'cpu_percent': metrics.cpu_percent,
                'messages_processed': metrics.messages_processed,
                'bytes_processed': metrics.bytes_processed,
                'throughput_msg_per_sec': metrics.throughput_msg_per_sec,
                'throughput_mb_per_sec': metrics.throughput_mb_per_sec
            })
        
        # 保存报告
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report
    
    def get_optimization_suggestions(self) -> List[str]:
        """获取优化建议"""
        suggestions = []
        
        if not self.metrics_history:
            return ["暂无性能数据，无法提供优化建议"]
        
        # 分析内存使用
        avg_memory = sum(m.memory_usage_mb for m in self.metrics_history) / len(self.metrics_history)
        if avg_memory > 500:  # 500MB
            suggestions.append("内存使用较高，建议启用批处理模式或减少批处理大小")
        
        # 分析处理速度
        parsing_metrics = [m for m in self.metrics_history if 'parse' in m.operation_name.lower()]
        if parsing_metrics:
            avg_throughput = sum(m.throughput_msg_per_sec for m in parsing_metrics) / len(parsing_metrics)
            if avg_throughput < 100:  # 100 msg/s
                suggestions.append("消息处理速度较慢，建议启用快速模式或增加工作线程数")
        
        # 分析CPU使用
        avg_cpu = sum(m.cpu_percent for m in self.metrics_history) / len(self.metrics_history)
        if avg_cpu < 30:
            suggestions.append("CPU使用率较低，可以增加并行处理线程数")
        elif avg_cpu > 90:
            suggestions.append("CPU使用率过高，建议减少并行处理线程数")
        
        return suggestions if suggestions else ["性能表现良好，无需特别优化"]


# 全局性能监控器
_global_monitor: Optional[PerformanceMonitor] = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor

def monitor_operation(operation_name: str, **kwargs):
    """监控操作的装饰器"""
    return get_performance_monitor().monitor_operation(operation_name, **kwargs)


if __name__ == "__main__":
    # 测试性能监控器
    monitor = PerformanceMonitor()
    
    # 模拟操作
    with monitor.monitor_operation("测试解析", expected_messages=1000):
        time.sleep(1.0)  # 模拟处理时间
        monitor.update_progress(messages_processed=500, bytes_processed=1024*1024)
        time.sleep(0.5)
        monitor.update_progress(messages_processed=500, bytes_processed=1024*1024)
    
    # 生成报告
    report = monitor.generate_performance_report()
    print(f"\n📊 性能报告摘要:")
    print(f"   总操作数: {report['total_operations']}")
    
    # 优化建议
    suggestions = monitor.get_optimization_suggestions()
    print(f"\n💡 优化建议:")
    for suggestion in suggestions:
        print(f"   - {suggestion}")
