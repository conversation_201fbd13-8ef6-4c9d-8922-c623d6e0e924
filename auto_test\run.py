#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的启动脚本
解决pkg_resources警告和启动卡顿问题
"""

import warnings
import sys
import os

# 在导入任何模块之前抑制所有相关警告
warnings.filterwarnings("ignore", category=UserWarning, module="pkg_resources")
warnings.filterwarnings("ignore", message=".*pkg_resources is deprecated.*")
warnings.filterwarnings("ignore", message=".*The pkg_resources package is slated for removal.*")

# 设置环境变量
os.environ['PYTHONWARNINGS'] = 'ignore::UserWarning:pkg_resources.*'

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 延迟导入主程序以减少启动时间
try:
    from src.main import main
    print("🚀 程序启动中...")
    main()
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖已正确安装")
    sys.exit(1)
except Exception as e:
    print(f"❌ 程序运行错误: {e}")
    sys.exit(1) 