"""
Django settings for tool_space project.

Generated by 'django-admin startproject' using Django 4.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-nwypobt_)^a(^a=+&#27$n*g&5@#91#7qi4%v_$reg(i^+ws5t'

# SECURITY WARNING: don't run with debug turned on in production!


ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    'corsheaders',
    'basic.apps.BasicConfig',
    'dataview.apps.DataviewConfig',
    'cron_task.apps.CronTaskConfig',
    'report.apps.ReportConfig',
    'gaode.apps.GaodeConfig',
    'outer_interface.apps.OuterInterfaceConfig',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'basic.middle.middleware.MessageMiddleware',
]

CORS_ALLOWED_ORIGINS = [
    "https://blk_67bbd946c441401c67c424e9.feishupkg.com",
    "http://localhost:8080",
]

CORS_ALLOW_ALL_METHODS = True
CORS_ALLOW_ALL_HEADERS = True

ROOT_URLCONF = 'megtool_backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'megtool_backend.wsgi.application'

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True

EXCLUDE_URL = [
    "/misc/ping",
    "/actuator/health",
    "/api/basic/auth/login",
    "/api/basic/queryJira",
    "/api/basic/send_feishu_message",
    "/api/outer/parsePerformance",
]

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

import os

LOG_DIR = os.path.join(BASE_DIR, "log")
# 下面就是logging的配置
LOGGING = {
    'version': 1,  # 指明dictConnfig的版本，目前就只有一个版本，哈哈
    'disable_existing_loggers': True,  # 表示是否禁用所有的已经存在的日志配置
    'formatters': {  # 格式器
        'standard': {
            'format': '[%(asctime)s] [%(thread)d] [%(filename)s:%(lineno)d] [%(module)s:%(funcName)s] [%(levelname)s]- %(message)s'
        },
        'simple': {  # 简单格式
            'format': '%(thread)d %(levelname)s %(message)s'
        },
    },
    # handlers：用来定义具体处理日志的方式，可以定义多种，"default"就是默认方式，"console"就是打印到控制台方式。file是写入到文件的方式，注意使用的class不同
    'handlers': {  # 处理器，在这里定义了两个个处理器
        'file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            # 'filename': os.path.join(BASE_DIR, 'log/megtool_backend.log'),
            'filename': os.path.join(BASE_DIR, 'log/megtool_backend_{}.log'.format(str(os.getpid()))),
            'maxBytes': 1024*1024*10,
            'backupCount': 10,
            'formatter': 'standard',
            'encoding': 'utf-8',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
    },
    'loggers': {  # log记录器，配置之后就会对应的输出日志
        '': {
            'level': 'DEBUG',
            'handlers': ['console', 'file'],
        },
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
