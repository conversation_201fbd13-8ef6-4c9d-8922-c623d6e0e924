"""Jira API客户端模块"""

import requests
import logging
from typing import Dict, List, Optional, Any
from .config import SDKConfig
from .exceptions import JiraApiException


class JiraApiClient:
    """Jira API客户端"""
    
    def __init__(self, config: SDKConfig):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {config.bearer_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        self.session.timeout = config.timeout
        
        self.logger = logging.getLogger(__name__)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发起HTTP请求"""
        url = f"{self.config.base_url}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            self.logger.debug(f"{method} {url} - {response.status_code}")
            return response
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求失败: {method} {url} - {str(e)}")
            raise JiraApiException(f"网络请求失败: {str(e)}")
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        try:
            response = self._make_request('GET', '/rest/api/2/myself')
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.warning(f"获取用户信息失败: {response.status_code}")
                return None
        except Exception as e:
            self.logger.error(f"获取用户信息异常: {str(e)}")
            return None
    
    def get_issue(self, issue_key: str, expand: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """获取问题详情"""
        params = {}
        if expand:
            params['expand'] = expand
            
        try:
            response = self._make_request('GET', f'/rest/api/2/issue/{issue_key}', params=params)
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.warning(f"获取问题失败: {issue_key} - {response.status_code}")
                return None
        except Exception as e:
            self.logger.error(f"获取问题异常: {issue_key} - {str(e)}")
            return None
    
    def create_issue(self, fields: Dict[str, Any]) -> Dict[str, Any]:
        """创建问题"""
        data = {'fields': fields}
        response = self._make_request('POST', '/rest/api/2/issue', json=data)
        
        if response.status_code == 201:
            return response.json()
        else:
            # 详细错误信息
            try:
                error_data = response.json()
                errors = error_data.get('errors', {})
                error_messages = error_data.get('errorMessages', [])
                
                error_details = []
                if error_messages:
                    error_details.extend(error_messages)
                
                for field, message in errors.items():
                    error_details.append(f"{field}: {message}")
                
                error_msg = f"创建问题失败 - 状态码: {response.status_code}\n错误详情: {'; '.join(error_details)}"
            except:
                error_msg = f"创建问题失败 - 状态码: {response.status_code}\n响应内容: {response.text[:500]}"
            
            self.logger.error(error_msg)
            raise JiraApiException(error_msg, response.status_code, response.text)
    
    def update_issue(self, issue_key: str, fields: Dict[str, Any]) -> bool:
        """更新问题"""
        data = {'fields': fields}
        response = self._make_request('PUT', f'/rest/api/2/issue/{issue_key}', json=data)
        
        if response.status_code == 204:
            return True
        else:
            self.logger.error(f"更新问题失败: {issue_key} - {response.status_code}")
            return False
    
    def create_issue_link(self, source_key: str, target_key: str, link_type: str = "Cloners") -> bool:
        """创建问题链接"""
        data = {
            "type": {"name": link_type},
            "inwardIssue": {"key": source_key},
            "outwardIssue": {"key": target_key}
        }
        
        response = self._make_request('POST', '/rest/api/2/issueLink', json=data)
        
        if response.status_code == 201:
            return True
        else:
            self.logger.warning(f"创建问题链接失败: {source_key} -> {target_key} - {response.status_code}")
            return False
    
    def search_issues(self, jql: str, max_results: int = 50, fields: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """搜索问题"""
        data = {
            'jql': jql,
            'maxResults': max_results,
            'fields': fields or ['key', 'summary']
        }
        
        response = self._make_request('POST', '/rest/api/2/search', json=data)
        
        if response.status_code == 200:
            return response.json().get('issues', [])
        else:
            self.logger.error(f"搜索问题失败: {response.status_code}")
            return []
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            user = self.get_current_user()
            return user is not None
        except:
            return False