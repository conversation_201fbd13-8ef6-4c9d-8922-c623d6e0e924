# 多阶段构建的MCAP解析器Docker镜像

# 构建阶段
FROM python:3.10-slim as builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt pyproject.toml ./

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt

# 复制源代码
COPY src/ ./src/
COPY mcap_parser/ ./mcap_parser/
COPY tools/ ./tools/
COPY examples/ ./examples/

# 安装包
RUN pip install --no-cache-dir -e .

# 运行阶段
FROM python:3.10-slim as runtime

# 设置标签
LABEL maintainer="MCAP Parser Team"
LABEL description="High-performance MCAP automotive data parser"
LABEL version="1.0.0"

# 创建非root用户
RUN groupadd -r mcapuser && useradd -r -g mcapuser mcapuser

# 设置工作目录
WORKDIR /app

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制Python环境
COPY --from=builder /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 复制应用代码
COPY --from=builder /app/src ./src
COPY --from=builder /app/mcap_parser ./mcap_parser
COPY --from=builder /app/tools ./tools
COPY --from=builder /app/examples ./examples
COPY --from=builder /app/pyproject.toml ./

# 创建数据目录
RUN mkdir -p /app/data /app/output && \
    chown -R mcapuser:mcapuser /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV MCAP_PARSER_LOG_LEVEL=INFO
ENV MCAP_PARSER_OUTPUT_DIR=/app/output

# 切换到非root用户
USER mcapuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import mcap_parser; print('OK')" || exit 1

# 暴露端口（如果有Web界面）
# EXPOSE 8080

# 设置入口点
ENTRYPOINT ["python", "-m", "mcap_parser.cli.main"]

# 默认命令
CMD ["--help"]

# 使用示例:
# docker build -t mcap-parser .
# docker run -v /path/to/mcap/files:/app/data -v /path/to/output:/app/output mcap-parser parse /app/data/file.mcap
# docker run -v /path/to/mcap/files:/app/data mcap-parser analyze /app/data/file.mcap
# docker run mcap-parser list