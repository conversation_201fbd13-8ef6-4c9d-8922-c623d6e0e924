#!/usr/bin/env python3
"""Jira Clone SDK 安装配置"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="jira-clone-sdk",
    version="1.0.0",
    author="Jira Clone SDK Team",
    author_email="<EMAIL>",
    description="现代化的 Jira 问题克隆 SDK",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/jira-clone-sdk",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
        "Topic :: Office/Business :: Groupware",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "test": [
            "pytest>=6.0",
            "pytest-mock>=3.0",
            "responses>=0.18",
        ],
    },
    entry_points={
        "console_scripts": [
            "jira-clone=launcher:main",
        ],
    },
    include_package_data=True,
    package_data={
        "jira_clone_sdk": ["py.typed"],
    },
    keywords="jira clone sdk api automation",
    project_urls={
        "Bug Reports": "https://github.com/example/jira-clone-sdk/issues",
        "Source": "https://github.com/example/jira-clone-sdk",
        "Documentation": "https://jira-clone-sdk.readthedocs.io/",
    },
)