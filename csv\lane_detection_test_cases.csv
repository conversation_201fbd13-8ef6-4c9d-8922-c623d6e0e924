﻿测试用例ID,场景类型,具体场景,目标类型,目标子类,算法问题类型,算法问题子类,测试要求,预期结果,备注
SYS-0001,直道场景,可变车道,车道线,-,系统问题,可视化问题,实车采集可变车道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0002,直道场景,可变车道,车道线,-,系统问题,系统性问题,实车采集可变车道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0003,直道场景,应急车道,车道线,-,系统问题,可视化问题,实车采集应急车道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有应急车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0004,直道场景,应急车道,车道线,-,系统问题,系统性问题,实车采集应急车道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0005,直道场景,常规车道,车道线,-,系统问题,可视化问题,实车采集常规车道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有常规车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0006,直道场景,常规车道,车道线,-,系统问题,系统性问题,实车采集常规车道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0007,直道场景,公交车道,车道线,-,系统问题,可视化问题,实车采集公交车道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有公交车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0008,直道场景,公交车道,车道线,-,系统问题,系统性问题,实车采集公交车道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0009,直道场景,潮汐车道,车道线,-,系统问题,可视化问题,实车采集潮汐车道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有潮汐车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0010,直道场景,潮汐车道,车道线,-,系统问题,系统性问题,实车采集潮汐车道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0011,直道场景,超宽车道,车道线,-,系统问题,可视化问题,实车采集超宽车道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有超宽车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0012,直道场景,超宽车道,车道线,-,系统问题,系统性问题,实车采集超宽车道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0013,直道场景,超窄车道,车道线,-,系统问题,可视化问题,实车采集超窄车道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有超窄车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0014,直道场景,超窄车道,车道线,-,系统问题,系统性问题,实车采集超窄车道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0015,直道场景,鱼骨线,车道线,-,系统问题,可视化问题,实车采集鱼骨线场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有鱼骨线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0016,直道场景,鱼骨线,车道线,-,系统问题,系统性问题,实车采集鱼骨线场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0017,直道场景,车道线不清晰,车道线,-,系统问题,可视化问题,实车采集车道线不清晰场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0018,直道场景,车道线不清晰,车道线,-,系统问题,系统性问题,实车采集车道线不清晰场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0019,直道场景,新旧车道线,车道线,-,系统问题,可视化问题,实车采集新旧车道线场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有新旧车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0020,直道场景,新旧车道线,车道线,-,系统问题,系统性问题,实车采集新旧车道线场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0021,直道场景,禁停区,车道线,-,系统问题,可视化问题,实车采集禁停区场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有禁停区车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0022,直道场景,禁停区,车道线,-,系统问题,系统性问题,实车采集禁停区场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0023,直道场景,导流区,车道线,-,系统问题,可视化问题,实车采集导流区场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有导流区车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0024,直道场景,导流区,车道线,-,系统问题,系统性问题,实车采集导流区场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0025,直道场景,高架,车道线,-,系统问题,可视化问题,实车采集高架场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有高架车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0026,直道场景,高架,车道线,-,系统问题,系统性问题,实车采集高架场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0027,直道场景,停止线,车道线,-,系统问题,可视化问题,实车采集停止线场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有停止线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0028,直道场景,停止线,车道线,-,系统问题,系统性问题,实车采集停止线场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0029,直道场景,斑马线,车道线,-,系统问题,可视化问题,实车采集斑马线场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有斑马线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0030,直道场景,斑马线,车道线,-,系统问题,系统性问题,实车采集斑马线场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0031,直道场景,隧道,车道线,-,系统问题,可视化问题,实车采集隧道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有隧道车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0032,直道场景,隧道,车道线,-,系统问题,系统性问题,实车采集隧道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0033,直道场景,弯道,车道线,-,系统问题,可视化问题,实车采集弯道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有弯道车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0034,直道场景,弯道,车道线,-,系统问题,系统性问题,实车采集弯道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0035,直道变化场景,少分多,车道线,-,系统问题,可视化问题,实车采集少分多场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有少分多车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0036,直道变化场景,少分多,车道线,-,系统问题,系统性问题,实车采集少分多场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0037,直道变化场景,多分少,车道线,-,系统问题,可视化问题,实车采集多分少场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有多分少车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0038,直道变化场景,多分少,车道线,-,系统问题,系统性问题,实车采集多分少场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0039,直道变化场景,分流,车道线,-,系统问题,可视化问题,实车采集分流场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有分流车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0040,直道变化场景,分流,车道线,-,系统问题,系统性问题,实车采集分流场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0041,直道变化场景,合流,车道线,-,系统问题,可视化问题,实车采集合流场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有合流车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0042,直道变化场景,合流,车道线,-,系统问题,系统性问题,实车采集合流场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0043,直道变化场景,环岛,车道线,-,系统问题,可视化问题,实车采集环岛场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有环岛车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0044,直道变化场景,环岛,车道线,-,系统问题,系统性问题,实车采集环岛场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0045,直道变化场景,导流区,车道线,-,系统问题,可视化问题,实车采集导流区场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有导流区车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0046,直道变化场景,导流区,车道线,-,系统问题,系统性问题,实车采集导流区场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0047,直道变化场景,主路进入辅路,车道线,-,系统问题,可视化问题,实车采集主路进入辅路场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有主路进入辅路车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0048,直道变化场景,主路进入辅路,车道线,-,系统问题,系统性问题,实车采集主路进入辅路场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0049,直道变化场景,辅路进入主路,车道线,-,系统问题,可视化问题,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有辅路进入主路车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0050,直道变化场景,辅路进入主路,车道线,-,系统问题,系统性问题,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0051,直道变化场景,匝道,车道线,-,系统问题,可视化问题,实车采集匝道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有匝道车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0052,直道变化场景,匝道,车道线,-,系统问题,系统性问题,实车采集匝道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0053,直道变化场景,收费站,车道线,-,系统问题,可视化问题,实车采集收费站场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有收费站车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0054,直道变化场景,收费站,车道线,-,系统问题,系统性问题,实车采集收费站场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0055,直道变化场景,高速反光柱,车道线,-,系统问题,可视化问题,实车采集高速反光柱场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有高速反光柱车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0056,直道变化场景,高速反光柱,车道线,-,系统问题,系统性问题,实车采集高速反光柱场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0057,直道变化场景,弯道,车道线,-,系统问题,可视化问题,实车采集弯道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有弯道车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0058,直道变化场景,弯道,车道线,-,系统问题,系统性问题,实车采集弯道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0059,直道变化场景,大曲率弯道,车道线,-,系统问题,可视化问题,实车采集大曲率弯道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有大曲率弯道车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0060,直道变化场景,大曲率弯道,车道线,-,系统问题,系统性问题,实车采集大曲率弯道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0061,直道变化场景,联排水马,车道线,-,系统问题,可视化问题,实车采集联排水马场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有联排水马车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0062,直道变化场景,联排水马,车道线,-,系统问题,系统性问题,实车采集联排水马场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0063,直道变化场景,联排锥桶,车道线,-,系统问题,可视化问题,实车采集联排锥桶场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有联排锥桶车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0064,直道变化场景,联排锥桶,车道线,-,系统问题,系统性问题,实车采集联排锥桶场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0065,路口场景,超大路口,车道线,-,系统问题,可视化问题,实车采集超大路口场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有超大路口车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0066,路口场景,超大路口,车道线,-,系统问题,系统性问题,实车采集超大路口场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0067,路口场景,十字路口,车道线,-,系统问题,可视化问题,实车采集十字路口场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有十字路口车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0068,路口场景,十字路口,车道线,-,系统问题,系统性问题,实车采集十字路口场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0069,路口场景,三岔路口,车道线,-,系统问题,可视化问题,实车采集三岔路口场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有三岔路口车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0070,路口场景,三岔路口,车道线,-,系统问题,系统性问题,实车采集三岔路口场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0071,路口场景,丁字路口,车道线,-,系统问题,可视化问题,实车采集丁字路口场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有丁字路口车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0072,路口场景,丁字路口,车道线,-,系统问题,系统性问题,实车采集丁字路口场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0073,路口场景,多叉路口,车道线,-,系统问题,可视化问题,实车采集多叉路口场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有多叉路口车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0074,路口场景,多叉路口,车道线,-,系统问题,系统性问题,实车采集多叉路口场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0075,路口场景,右转专用道,车道线,-,系统问题,可视化问题,实车采集右转专用道场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有右转专用道车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0076,路口场景,右转专用道,车道线,-,系统问题,系统性问题,实车采集右转专用道场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0077,路口场景,错位路口,车道线,-,系统问题,可视化问题,实车采集错位路口场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有错位路口车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0078,路口场景,错位路口,车道线,-,系统问题,系统性问题,实车采集错位路口场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0079,路口场景,左转待转区,车道线,-,系统问题,可视化问题,实车采集左转待转区场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有左转待转区车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0080,路口场景,左转待转区,车道线,-,系统问题,系统性问题,实车采集左转待转区场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0081,路口场景,直行待行区,车道线,-,系统问题,可视化问题,实车采集直行待行区场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有直行待行区车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0082,路口场景,直行待行区,车道线,-,系统问题,系统性问题,实车采集直行待行区场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0083,车辆行为,路口左转,车道线,-,系统问题,可视化问题,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有路口左转车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0084,车辆行为,路口左转,车道线,-,系统问题,系统性问题,实车采集路口左转场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0085,车辆行为,路口右转,车道线,-,系统问题,可视化问题,实车采集路口右转场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有路口右转车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0086,车辆行为,路口右转,车道线,-,系统问题,系统性问题,实车采集路口右转场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0087,车辆行为,路口直行,车道线,-,系统问题,可视化问题,实车采集路口直行场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有路口直行车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0088,车辆行为,路口直行,车道线,-,系统问题,系统性问题,实车采集路口直行场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0089,车辆行为,路口掉头,车道线,-,系统问题,可视化问题,实车采集路口掉头场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有路口掉头车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0090,车辆行为,路口掉头,车道线,-,系统问题,系统性问题,实车采集路口掉头场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0091,道路属性,高速,车道线,-,系统问题,可视化问题,实车采集高速场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有高速车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0092,道路属性,高速,车道线,-,系统问题,系统性问题,实车采集高速场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0093,道路属性,城区,车道线,-,系统问题,可视化问题,实车采集城区场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有城区车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0094,道路属性,城区,车道线,-,系统问题,系统性问题,实车采集城区场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题
SYS-0095,道路属性,施工,车道线,-,系统问题,可视化问题,实车采集施工场景数据，自动化运行车道线检测算法，人工复核可视化效果，记录所有可视化异常。,所有施工车道线可正确显示，无闪烁、错位、消失等可视化异常,可视化问题
SYS-0096,道路属性,施工,车道线,-,系统问题,系统性问题,实车采集施工场景数据，自动化运行车道线检测算法，监控系统运行稳定性，记录系统崩溃、卡死等异常。,算法全程稳定运行，无崩溃、卡死、重启等系统性异常,系统性问题

TC-0098,直道变化场景,弯道,车道线,左虚右实双白线,误检,实例多联,实车采集弯道场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%

TC-0100,道路属性,施工,车道线,左虚右实双白线,漏检,漏检导流线,实车采集施工场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在漏检导流线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0101,道路属性,城区,车道线,左实右虚双黄线,类型错误,类型整体错误,实车采集城区场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0102,直道变化场景,大曲率弯道,路沿,路沿线,误检,实例断联,实车采集大曲率弯道场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0103,直道场景,导流区,路沿,锥桶,误检,实例弯折,实车采集导流区场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0104,道路属性,城区,离散目标,driveline,误检,位置错误,实车采集城区场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0105,直道变化场景,大曲率弯道,路沿,路沿线,漏检,漏检路沿线,实车采集大曲率弯道场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在漏检，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0106,路口场景,丁字路口,离散目标,地面箭头,误检,实例多联,实车采集丁字路口场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0107,直道变化场景,匝道,离散目标,driveline,漏检,漏检地面箭头,实车采集匝道场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在漏检地面箭头，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0108,车辆行为,路口直行,离散目标,斑马线,漏检,检出距离不足,实车采集路口直行场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0109,路口场景,超大路口,车道线,黄色实线,类型错误,类型部分错误,实车采集超大路口场景数据，自动化运行车道线检测算法，人工复核黄色实线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0110,路口场景,十字路口,车道线,左实右虚双白线,类型错误,类型整体错误,实车采集十字路口场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%

TC-0112,直道变化场景,匝道,路沿,路沿线,误检,实例乱线,采集直道变化场景-匝道场景视频，运行算法，人工核查路沿路沿线是否存在实例乱线，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0113,直道场景,车道线不清晰,离散目标,driveline,误检,实例乱线,采集直道场景-车道线不清晰场景视频，运行算法，人工核查离散目标driveline是否存在实例乱线，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0114,路口场景,直行待行区,离散目标,driveline,漏检,漏检停止线,采集路口场景-直行待行区场景视频，运行算法，人工核查离散目标driveline是否存在漏检停止线，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0115,直道变化场景,联排锥桶,离散目标,停止线,误检,实例弯折,采集直道变化场景-联排锥桶场景视频，运行算法，人工核查离散目标停止线是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0116,车辆行为,路口左转,离散目标,driveline,漏检,检出距离不足,采集车辆行为-路口左转场景视频，运行算法，人工核查离散目标driveline是否存在检出距离不足，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0117,路口场景,超大路口,离散目标,斑马线,漏检,漏检斑马线,采集路口场景-超大路口场景视频，运行算法，人工核查离散目标斑马线是否存在漏检斑马线，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%

TC-0119,车辆行为,路口掉头,车道线,白色实线,误检,实例弯折,采集车辆行为-路口掉头场景视频，运行算法，人工核查车道线白色实线是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0120,直道场景,应急车道,路沿,锥桶,误检,曲率错误,实车采集应急车道场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0121,直道变化场景,合流,离散目标,禁停区,漏检,漏检driveline,实车采集合流场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在漏检driveline，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0122,直道变化场景,环岛,路沿,护栏,误检,实例断联,实车采集环岛场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0123,道路属性,高速,车道线,左虚右实双白线,误检,位置错误,实车采集高速场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0124,车辆行为,路口左转,车道线,左虚右实双黄线,误检,实例断联,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0125,直道变化场景,环岛,车道线,黄色实线,误检,实例弯折,实车采集环岛场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0126,路口场景,超大路口,车道线,左实右虚双白线,系统问题,可视化问题,实车采集超大路口场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0127,直道场景,弯道,车道线,双黄线,误检,实例多联,实车采集弯道场景数据，自动化运行车道线检测算法，人工复核双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%

TC-0129,直道场景,可变车道,路沿,路沿线,误检,实例多联,实车采集可变车道场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0130,直道场景,潮汐车道,路沿,路沿线,误检,实例弯折,实车采集潮汐车道场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0131,直道场景,禁停区,车道线,黄色虚线,漏检,漏检导流线,实车采集禁停区场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在漏检导流线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0132,车辆行为,路口掉头,离散目标,停止线,误检,位置错误,实车采集路口掉头场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0133,直道变化场景,少分多,路沿,锥桶,误检,多线重合,实车采集少分多场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0134,直道变化场景,高速反光柱,车道线,白色实线,类型错误,类型跳变错误,实车采集高速反光柱场景数据，自动化运行车道线检测算法，人工复核白色实线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0135,车辆行为,路口右转,车道线,左实右虚双黄线,类型错误,类型部分错误,实车采集路口右转场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0136,直道变化场景,合流,车道线,导流线,系统问题,系统性问题,采集直道变化场景-合流场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0137,直道场景,禁停区,离散目标,地面箭头,误检,实例弯折,采集直道场景-禁停区场景视频，运行算法，人工核查离散目标地面箭头是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0138,路口场景,直行待行区,车道线,导流线,系统问题,系统性问题,采集路口场景-直行待行区场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0139,路口场景,多叉路口,车道线,左虚右实双黄线,类型错误,类型部分错误,实车采集多叉路口场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0140,直道变化场景,高速反光柱,路沿,护栏,漏检,漏检护栏,实车采集高速反光柱场景数据，自动化运行路沿检测算法，人工复核护栏是否存在漏检，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0141,直道场景,应急车道,车道线,左实右虚双黄线,误检,实例断联,实车采集应急车道场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0142,直道变化场景,分流,离散目标,停止线,误检,实例乱线,实车采集分流场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0143,车辆行为,路口右转,车道线,左虚右实双白线,系统问题,可视化问题,实车采集路口右转场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0144,道路属性,城区,车道线,左实右虚双黄线,系统问题,系统性问题,实车采集城区场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题


TC-0147,直道变化场景,联排水马,路沿,水马,误检,实例乱线,实车采集联排水马场景数据，自动化运行路沿检测算法，人工复核水马是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0148,车辆行为,路口掉头,离散目标,斑马线,漏检,漏检地面箭头,实车采集路口掉头场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检地面箭头，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0149,直道变化场景,环岛,路沿,水马,误检,实例断联,实车采集环岛场景数据，自动化运行路沿检测算法，人工复核水马是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0150,道路属性,施工,车道线,左虚右实双黄线,误检,位置错误,实车采集施工场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0151,车辆行为,路口右转,路沿,护栏,误检,多线重合,实车采集路口右转场景数据，自动化运行路沿检测算法，人工复核护栏是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0152,车辆行为,路口直行,车道线,左实右虚双黄线,类型错误,类型部分错误,实车采集路口直行场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0153,车辆行为,路口直行,路沿,锥桶,误检,实例断联,实车采集路口直行场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0154,直道场景,应急车道,路沿,护栏,误检,实例乱线,实车采集应急车道场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0155,直道场景,可变车道,车道线,导流线,误检,实例多联,实车采集可变车道场景数据，自动化运行车道线检测算法，人工复核导流线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0156,直道变化场景,合流,车道线,左实右虚双黄线,误检,实例多联,实车采集合流场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%

TC-0158,直道场景,鱼骨线,车道线,黄色虚线,误检,实例乱线,实车采集鱼骨线场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0159,车辆行为,路口直行,路沿,护栏,误检,多线重合,实车采集路口直行场景数据，自动化运行路沿检测算法，人工复核护栏是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0160,路口场景,右转专用道,路沿,锥桶,误检,实例多联,实车采集右转专用道场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0161,直道变化场景,多分少,车道线,左虚右实双黄线,系统问题,可视化问题,实车采集多分多少分少场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0162,车辆行为,路口掉头,路沿,路沿线,误检,多线重合,实车采集路口掉头场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%

TC-0164,直道变化场景,环岛,车道线,白色虚线,漏检,漏检水马,实车采集环岛场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0165,直道场景,潮汐车道,路沿,路沿线,误检,实例多联,实车采集潮汐车道场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0166,路口场景,三岔路口,离散目标,禁停区,误检,实例乱线,实车采集三岔路口场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0167,直道变化场景,少分多,路沿,水马,误检,实例乱线,实车采集少分多场景数据，自动化运行路沿检测算法，人工复核水马是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0168,直道场景,高架,路沿,路沿线,误检,曲率错误,实车采集高架场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0169,直道变化场景,大曲率弯道,离散目标,地面箭头,误检,曲率错误,实车采集大曲率弯道场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0170,直道场景,停止线,离散目标,斑马线,漏检,漏检禁停区,实车采集停止线场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检禁停区，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0171,道路属性,施工,车道线,左实右虚双白线,误检,实例乱线,实车采集施工场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0172,直道变化场景,高速反光柱,车道线,黄色虚线,误检,曲率错误,实车采集高速反光柱场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0173,直道场景,应急车道,车道线,黄色虚线,类型错误,类型部分错误,实车采集应急车道场景数据，自动化运行车道线检测算法，人工复核黄色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0174,道路属性,施工,路沿,水马,误检,实例多联,实车采集施工场景数据，自动化运行路沿检测算法，人工复核水马是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0175,直道场景,鱼骨线,离散目标,斑马线,误检,实例乱线,实车采集鱼骨线场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0176,路口场景,超大路口,车道线,左实右虚双黄线,系统问题,系统性问题,实车采集超大路口场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0177,直道变化场景,收费站,路沿,水马,误检,实例断联,实车采集收费站场景数据，自动化运行路沿检测算法，人工复核水马是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0178,路口场景,错位路口,路沿,锥桶,误检,实例乱线,实车采集错位路口场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0179,车辆行为,路口左转,车道线,左实右虚双白线,漏检,漏检导流线,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线是否存在漏检导流线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0180,直道变化场景,联排水马,车道线,左实右虚双黄线,误检,实例弯折,实车采集联排水马场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0181,路口场景,错位路口,路沿,锥桶,误检,多线重合,实车采集错位路口场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0182,路口场景,左转待转区,车道线,左实右虚双黄线,系统问题,系统性问题,实车采集左转待转区场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0183,直道变化场景,弯道,车道线,左实右虚双白线,系统问题,可视化问题,实车采集弯道场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0184,直道变化场景,辅路进入主路,车道线,双黄线,类型错误,类型整体错误,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，人工复核双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0185,车辆行为,路口左转,离散目标,禁停区,误检,实例弯折,实车采集路口左转场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0186,直道变化场景,主路进入辅路,离散目标,driveline,误检,实例断联,实车采集主路进入辅路场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0187,路口场景,右转专用道,离散目标,禁停区,误检,实例乱线,实车采集右转专用道场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0188,道路属性,高速,车道线,黄色虚线,漏检,漏检导流线,实车采集高速场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在漏检导流线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0189,直道场景,停止线,车道线,白色实线,系统问题,可视化问题,实车采集停止线场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0190,路口场景,超大路口,车道线,白色虚线,误检,多线重合,实车采集超大路口场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0191,车辆行为,路口右转,车道线,双黄线,误检,实例多联,实车采集路口右转场景数据，自动化运行车道线检测算法，人工复核双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0192,道路属性,高速,车道线,左实右虚双黄线,类型错误,类型跳变错误,实车采集高速场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0193,车辆行为,路口直行,车道线,左实右虚双白线,误检,实例多联,实车采集路口直行场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0194,车辆行为,路口直行,车道线,左实右虚双白线,类型错误,类型整体错误,实车采集路口直行场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0195,直道变化场景,收费站,路沿,护栏,误检,实例弯折,实车采集收费站场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0196,路口场景,左转待转区,路沿,锥桶,误检,实例断联,实车采集左转待转区场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0197,直道场景,潮汐车道,车道线,左虚右实双黄线,类型错误,类型整体错误,实车采集潮汐车道场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0198,路口场景,错位路口,离散目标,地面箭头,误检,实例弯折,实车采集错位路口场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0199,直道变化场景,辅路进入主路,车道线,左实右虚双白线,类型错误,类型跳变错误,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0200,直道变化场景,高速反光柱,车道线,左实右虚双黄线,类型错误,类型跳变错误,实车采集高速反光柱场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0201,车辆行为,路口左转,车道线,黄色实线,误检,多线重合,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0202,直道场景,导流区,离散目标,地面箭头,误检,实例断联,实车采集导流区场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0203,路口场景,丁字路口,车道线,左实右虚双黄线,误检,实例弯折,实车采集丁字路口场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0204,直道变化场景,高速反光柱,离散目标,斑马线,误检,位置错误,实车采集高速反光柱场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0205,道路属性,高速,离散目标,driveline,漏检,漏检停止线,实车采集高速场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在漏检停止线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0206,直道变化场景,匝道,路沿,护栏,误检,实例弯折,实车采集匝道场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0207,路口场景,超大路口,车道线,黄色虚线,误检,曲率错误,实车采集超大路口场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0208,车辆行为,路口掉头,车道线,白色实线,类型错误,类型跳变错误,实车采集路口掉头场景数据，自动化运行车道线检测算法，人工复核白色实线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0209,直道变化场景,多分少,车道线,黄色虚线,类型错误,类型部分错误,实车采集多分少场景数据，自动化运行车道线检测算法，人工复核黄色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0210,路口场景,左转待转区,路沿,水马,误检,多线重合,实车采集左转待转区场景数据，自动化运行路沿检测算法，人工复核水马是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0211,车辆行为,路口左转,车道线,白色实线,误检,实例断联,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核白色实线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0212,直道场景,导流区,车道线,左虚右实双白线,误检,实例多联,实车采集导流区场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0213,道路属性,施工,车道线,双黄线,系统问题,可视化问题,实车采集施工场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0214,直道场景,导流区,离散目标,地面箭头,误检,实例乱线,实车采集导流区场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0215,直道场景,超宽车道,车道线,左实右虚双白线,系统问题,可视化问题,实车采集超宽车道场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题


TC-0218,直道变化场景,分流,离散目标,停止线,误检,多线重合,采集直道变化场景-分流场景视频，运行算法，人工核查离散目标停止线是否存在多线重合，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0219,直道场景,鱼骨线,路沿,路沿线,误检,曲率错误,采集直道场景-鱼骨线场景视频，运行算法，人工核查路沿路沿线是否存在曲率错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0220,路口场景,丁字路口,车道线,黄色虚线,类型错误,类型部分错误,采集路口场景-丁字路口场景视频，运行算法，人工核查车道线黄色虚线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0221,直道场景,可变车道,路沿,水马,漏检,漏检护栏,采集直道场景-可变车道场景视频，运行算法，人工核查路沿水马是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0222,车辆行为,路口直行,车道线,左虚右实双黄线,误检,曲率错误,采集车辆行为-路口直行场景视频，运行算法，人工核查车道线左虚右实双黄线是否存在曲率错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0223,路口场景,丁字路口,路沿,护栏,误检,实例多联,采集路口场景-丁字路口场景视频，运行算法，人工核查路沿护栏是否存在实例多联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0224,直道变化场景,合流,车道线,双黄线,类型错误,类型整体错误,采集直道变化场景-合流场景视频，运行算法，人工核查车道线双黄线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0225,直道变化场景,收费站,车道线,白色实线,漏检,检出距离不足,采集直道变化场景-收费站场景视频，运行算法，人工核查车道线白色实线是否存在检出距离不足，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0226,直道场景,新旧车道线,路沿,水马,误检,位置错误,采集直道场景-新旧车道线场景视频，运行算法，人工核查路沿水马是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0227,路口场景,多叉路口,车道线,白色实线,系统问题,系统性问题,采集路口场景-多叉路口场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0228,路口场景,左转待转区,离散目标,driveline,误检,实例断联,采集路口场景-左转待转区场景视频，运行算法，人工核查离散目标driveline是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0229,道路属性,高速,离散目标,地面箭头,误检,多线重合,采集道路属性-高速场景视频，运行算法，人工核查离散目标地面箭头是否存在多线重合，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0230,道路属性,施工,车道线,黄色虚线,系统问题,系统性问题,采集道路属性-施工场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0231,直道变化场景,高速反光柱,离散目标,斑马线,误检,实例弯折,采集直道变化场景-高速反光柱场景视频，运行算法，人工核查离散目标斑马线是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0232,车辆行为,路口直行,车道线,白色虚线,类型错误,类型整体错误,采集车辆行为-路口直行场景视频，运行算法，人工核查车道线白色虚线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0233,直道变化场景,导流区,路沿,护栏,漏检,漏检护栏,采集直道变化场景-导流区场景视频，运行算法，人工核查路沿护栏是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0234,直道变化场景,多分少,路沿,路沿线,误检,实例弯折,采集直道变化场景-多分少场景视频，运行算法，人工核查路沿路沿线是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0235,路口场景,三岔路口,离散目标,禁停区,漏检,漏检护栏,采集路口场景-三岔路口场景视频，运行算法，人工核查离散目标禁停区是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0236,道路属性,城区,离散目标,禁停区,漏检,漏检禁停区,采集道路属性-城区场景视频，运行算法，人工核查离散目标禁停区是否存在漏检禁停区，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0237,直道变化场景,联排水马,车道线,左实右虚双白线,漏检,漏检水马,采集直道变化场景-联排水马场景视频，运行算法，人工核查车道线左实右虚双白线是否存在漏检水马，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0238,直道场景,隧道,路沿,路沿线,误检,多线重合,采集直道场景-隧道场景视频，运行算法，人工核查路沿路沿线是否存在多线重合，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0239,直道场景,鱼骨线,路沿,锥桶,误检,实例断联,采集直道场景-鱼骨线场景视频，运行算法，人工核查路沿锥桶是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0240,直道场景,高架,离散目标,地面箭头,漏检,漏检driveline,采集直道场景-高架场景视频，运行算法，人工核查离散目标地面箭头是否存在漏检driveline，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0241,直道场景,导流区,车道线,黄色实线,类型错误,类型整体错误,采集直道场景-导流区场景视频，运行算法，人工核查车道线黄色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0242,路口场景,三岔路口,车道线,黄色实线,类型错误,类型部分错误,采集路口场景-三岔路口场景视频，运行算法，人工核查车道线黄色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0243,路口场景,十字路口,车道线,导流线,系统问题,系统性问题,采集路口场景-十字路口场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0244,道路属性,城区,车道线,白色实线,类型错误,类型整体错误,采集道路属性-城区场景视频，运行算法，人工核查车道线白色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0245,道路属性,施工,离散目标,斑马线,误检,实例断联,采集道路属性-施工场景视频，运行算法，人工核查离散目标斑马线是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0246,直道场景,导流区,车道线,白色虚线,漏检,漏检导流线,采集直道场景-导流区场景视频，运行算法，人工核查车道线白色虚线是否存在漏检导流线，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0247,直道场景,禁停区,车道线,黄色实线,类型错误,类型跳变错误,采集直道场景-禁停区场景视频，运行算法，人工核查车道线黄色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0248,直道变化场景,多分少,离散目标,禁停区,误检,多线重合,采集直道变化场景-多分少场景视频，运行算法，人工核查离散目标禁停区是否存在多线重合，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0249,直道变化场景,少分多,车道线,黄色实线,类型错误,类型部分错误,采集直道变化场景-少分多场景视频，运行算法，人工核查车道线黄色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0250,路口场景,十字路口,离散目标,禁停区,误检,实例断联,采集路口场景-十字路口场景视频，运行算法，人工核查离散目标禁停区是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0251,直道变化场景,少分多,车道线,左实右虚双白线,系统问题,可视化问题,采集直道变化场景-少分多场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0252,直道变化场景,多分少,车道线,白色虚线,类型错误,类型部分错误,采集直道变化场景-多分少场景视频，运行算法，人工核查车道线白色虚线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0253,路口场景,错位路口,路沿,锥桶,漏检,漏检路沿线,采集路口场景-错位路口场景视频，运行算法，人工核查路沿锥桶是否存在漏检路沿线，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0254,直道场景,超宽车道,路沿,水马,误检,多线重合,采集直道场景-超宽车道场景视频，运行算法，人工核查路沿水马是否存在多线重合，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0255,路口场景,左转待转区,车道线,左实右虚双白线,类型错误,类型跳变错误,采集路口场景-左转待转区场景视频，运行算法，人工核查车道线左实右虚双白线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0256,车辆行为,路口右转,车道线,双黄线,误检,实例断联,采集车辆行为-路口右转场景视频，运行算法，人工核查车道线双黄线是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0257,直道变化场景,联排锥桶,离散目标,禁停区,误检,实例乱线,采集直道变化场景-联排锥桶场景视频，运行算法，人工核查离散目标禁停区是否存在实例乱线，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0258,道路属性,高速,离散目标,停止线,误检,多线重合,采集道路属性-高速场景视频，运行算法，人工核查离散目标停止线是否存在多线重合，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0259,道路属性,城区,车道线,左虚右实双白线,漏检,漏检护栏,采集道路属性-城区场景视频，运行算法，人工核查车道线左虚右实双白线是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0260,车辆行为,路口左转,车道线,左虚右实双白线,漏检,漏检水马,采集车辆行为-路口左转场景视频，运行算法，人工核查车道线左虚右实双白线是否存在漏检水马，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0261,道路属性,高速,车道线,左实右虚双白线,类型错误,类型跳变错误,采集道路属性-高速场景视频，运行算法，人工核查车道线左实右虚双白线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0262,直道场景,可变车道,车道线,黄色实线,类型错误,类型跳变错误,采集直道场景-可变车道场景视频，运行算法，人工核查车道线黄色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0263,直道变化场景,大曲率弯道,离散目标,禁停区,误检,实例断联,采集直道变化场景-大曲率弯道场景视频，运行算法，人工核查离散目标禁停区是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0264,车辆行为,路口直行,路沿,路沿线,误检,曲率错误,采集车辆行为-路口直行场景视频，运行算法，人工核查路沿路沿线是否存在曲率错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0265,车辆行为,路口左转,路沿,水马,漏检,漏检护栏,采集车辆行为-路口左转场景视频，运行算法，人工核查路沿水马是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0266,直道变化场景,联排水马,路沿,锥桶,漏检,漏检护栏,采集直道变化场景-联排水马场景视频，运行算法，人工核查路沿锥桶是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0267,直道场景,高架,离散目标,禁停区,漏检,漏检锥桶,采集直道场景-高架场景视频，运行算法，人工核查离散目标禁停区是否存在漏检锥桶，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0268,道路属性,城区,车道线,黄色实线,类型错误,类型部分错误,采集道路属性-城区场景视频，运行算法，人工核查车道线黄色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0269,道路属性,施工,车道线,白色虚线,类型错误,类型整体错误,采集道路属性-施工场景视频，运行算法，人工核查车道线白色虚线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0270,车辆行为,路口掉头,离散目标,地面箭头,误检,曲率错误,采集车辆行为-路口掉头场景视频，运行算法，人工核查离散目标地面箭头是否存在曲率错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0271,直道变化场景,收费站,离散目标,禁停区,误检,位置错误,采集直道变化场景-收费站场景视频，运行算法，人工核查离散目标禁停区是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0272,直道场景,隧道,路沿,护栏,误检,实例多联,采集直道场景-隧道场景视频，运行算法，人工核查路沿护栏是否存在实例多联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0273,车辆行为,路口掉头,离散目标,停止线,漏检,漏检禁停区,采集车辆行为-路口掉头场景视频，运行算法，人工核查离散目标停止线是否存在漏检禁停区，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0274,路口场景,左转待转区,车道线,导流线,类型错误,类型跳变错误,采集路口场景-左转待转区场景视频，运行算法，人工核查车道线导流线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0275,直道场景,高架,路沿,锥桶,漏检,漏检水马,采集直道场景-高架场景视频，运行算法，人工核查路沿锥桶是否存在漏检水马，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0276,路口场景,三岔路口,车道线,左实右虚双白线,漏检,漏检锥桶,采集路口场景-三岔路口场景视频，运行算法，人工核查车道线左实右虚双白线是否存在漏检锥桶，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0277,直道场景,弯道,车道线,导流线,误检,位置错误,采集直道场景-弯道场景视频，运行算法，人工核查车道线导流线是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0278,车辆行为,路口直行,离散目标,禁停区,误检,实例弯折,采集车辆行为-路口直行场景视频，运行算法，人工核查离散目标禁停区是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0279,直道场景,常规车道,离散目标,斑马线,漏检,漏检禁停区,采集直道场景-常规车道场景视频，运行算法，人工核查离散目标斑马线是否存在漏检禁停区，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0280,直道场景,超宽车道,路沿,护栏,误检,实例乱线,采集直道场景-超宽车道场景视频，运行算法，人工核查路沿护栏是否存在实例乱线，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0281,车辆行为,路口掉头,车道线,黄色虚线,误检,位置错误,采集车辆行为-路口掉头场景视频，运行算法，人工核查车道线黄色虚线是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0282,直道场景,禁停区,车道线,左实右虚双黄线,类型错误,类型跳变错误,采集直道场景-禁停区场景视频，运行算法，人工核查车道线左实右虚双黄线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0283,直道场景,高架,离散目标,停止线,漏检,漏检护栏,采集直道场景-高架场景视频，运行算法，人工核查离散目标停止线是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0284,车辆行为,路口直行,车道线,黄色实线,类型错误,类型部分错误,采集车辆行为-路口直行场景视频，运行算法，人工核查车道线黄色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0285,直道场景,新旧车道线,路沿,护栏,漏检,漏检护栏,采集直道场景-新旧车道线场景视频，运行算法，人工核查路沿护栏是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0286,道路属性,施工,车道线,导流线,误检,实例多联,采集道路属性-施工场景视频，运行算法，人工核查车道线导流线是否存在实例多联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0287,车辆行为,路口直行,车道线,左实右虚双白线,误检,位置错误,采集车辆行为-路口直行场景视频，运行算法，人工核查车道线左实右虚双白线是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0288,道路属性,施工,车道线,左实右虚双白线,漏检,漏检护栏,采集道路属性-施工场景视频，运行算法，人工核查车道线左实右虚双白线是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0289,道路属性,城区,车道线,左虚右实双白线,系统问题,可视化问题,采集道路属性-城区场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0290,道路属性,城区,离散目标,地面箭头,误检,实例断联,采集道路属性-城区场景视频，运行算法，人工核查离散目标地面箭头是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0291,路口场景,超大路口,离散目标,driveline,误检,位置错误,采集路口场景-超大路口场景视频，运行算法，人工核查离散目标driveline是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0292,车辆行为,路口直行,离散目标,停止线,误检,位置错误,采集车辆行为-路口直行场景视频，运行算法，人工核查离散目标停止线是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0293,直道场景,潮汐车道,离散目标,地面箭头,误检,曲率错误,采集直道场景-潮汐车道场景视频，运行算法，人工核查离散目标地面箭头是否存在曲率错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0294,直道场景,应急车道,离散目标,禁停区,误检,曲率错误,采集直道场景-应急车道场景视频，运行算法，人工核查离散目标禁停区是否存在曲率错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0295,直道变化场景,多分少,车道线,左实右虚双白线,误检,实例断联,采集直道变化场景-多分少场景视频，运行算法，人工核查车道线左实右虚双白线是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0296,车辆行为,路口掉头,车道线,黄色虚线,系统问题,可视化问题,采集车辆行为-路口掉头场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0297,道路属性,城区,车道线,导流线,类型错误,类型跳变错误,采集道路属性-城区场景视频，运行算法，人工核查车道线导流线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0298,道路属性,施工,车道线,左虚右实双黄线,漏检,漏检水马,采集道路属性-施工场景视频，运行算法，人工核查车道线左虚右实双黄线是否存在漏检水马，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0299,直道场景,超窄车道,车道线,黄色虚线,系统问题,可视化问题,采集直道场景-超窄车道场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0300,直道场景,超窄车道,离散目标,斑马线,漏检,漏检地面箭头,采集直道场景-超窄车道场景视频，运行算法，人工核查离散目标斑马线是否存在漏检地面箭头，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0301,直道变化场景,联排水马,路沿,锥桶,误检,曲率错误,采集直道变化场景-联排水马场景视频，运行算法，人工核查路沿锥桶是否存在曲率错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0302,直道变化场景,弯道,车道线,左实右虚双黄线,漏检,漏检导流线,采集直道变化场景-弯道场景视频，运行算法，人工核查车道线左实右虚双黄线是否存在漏检导流线，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0303,车辆行为,路口直行,车道线,白色虚线,类型错误,类型部分错误,采集车辆行为-路口直行场景视频，运行算法，人工核查车道线白色虚线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0304,直道场景,鱼骨线,离散目标,driveline,误检,多线重合,采集直道场景-鱼骨线场景视频，运行算法，人工核查离散目标driveline是否存在多线重合，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0305,车辆行为,路口掉头,车道线,白色虚线,漏检,漏检护栏,采集车辆行为-路口掉头场景视频，运行算法，人工核查车道线白色虚线是否存在漏检护栏，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0306,道路属性,城区,车道线,左虚右实双白线,误检,实例多联,采集道路属性-城区场景视频，运行算法，人工核查车道线左虚右实双白线是否存在实例多联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0307,直道场景,常规车道,离散目标,停止线,误检,实例断联,采集直道场景-常规车道场景视频，运行算法，人工核查离散目标停止线是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0308,直道变化场景,联排水马,离散目标,driveline,误检,实例弯折,采集直道变化场景-联排水马场景视频，运行算法，人工核查离散目标driveline是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0309,直道变化场景,少分多,车道线,左虚右实双白线,系统问题,系统性问题,采集直道变化场景-少分多场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0310,直道场景,隧道,离散目标,禁停区,误检,位置错误,采集直道场景-隧道场景视频，运行算法，人工核查离散目标禁停区是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0311,道路属性,施工,路沿,路沿线,误检,实例多联,采集道路属性-施工场景视频，运行算法，人工核查路沿路沿线是否存在实例多联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0312,直道场景,公交车道,离散目标,driveline,漏检,漏检锥桶,采集直道场景-公交车道场景视频，运行算法，人工核查离散目标driveline是否存在漏检锥桶，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0313,直道变化场景,环岛,离散目标,停止线,误检,位置错误,采集直道变化场景-环岛场景视频，运行算法，人工核查离散目标停止线是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0314,车辆行为,路口掉头,路沿,水马,误检,实例弯折,采集车辆行为-路口掉头场景视频，运行算法，人工核查路沿水马是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0315,直道场景,超宽车道,车道线,左实右虚双黄线,系统问题,系统性问题,采集直道场景-超宽车道场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0316,道路属性,高速,离散目标,停止线,漏检,漏检锥桶,采集道路属性-高速场景视频，运行算法，人工核查离散目标停止线是否存在漏检锥桶，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0317,道路属性,城区,路沿,路沿线,误检,实例弯折,采集道路属性-城区场景视频，运行算法，人工核查路沿路沿线是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0318,路口场景,直行待行区,离散目标,停止线,漏检,漏检停止线,采集路口场景-直行待行区场景视频，运行算法，人工核查离散目标停止线是否存在漏检停止线，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0319,直道场景,停止线,车道线,白色实线,类型错误,类型跳变错误,采集直道场景-停止线场景视频，运行算法，人工核查车道线白色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0320,道路属性,施工,路沿,锥桶,误检,实例弯折,采集道路属性-施工场景视频，运行算法，人工核查路沿锥桶是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0321,车辆行为,路口掉头,车道线,左实右虚双白线,系统问题,可视化问题,采集车辆行为-路口掉头场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0322,道路属性,城区,车道线,白色实线,误检,多线重合,采集道路属性-城区场景视频，运行算法，人工核查车道线白色实线是否存在多线重合，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0323,路口场景,十字路口,车道线,黄色实线,类型错误,类型部分错误,采集路口场景-十字路口场景视频，运行算法，人工核查车道线黄色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0324,路口场景,多叉路口,车道线,双黄线,误检,曲率错误,采集路口场景-多叉路口场景视频，运行算法，人工核查车道线双黄线是否存在曲率错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0325,直道场景,新旧车道线,路沿,水马,误检,实例弯折,采集直道场景-新旧车道线场景视频，运行算法，人工核查路沿水马是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0326,道路属性,高速,离散目标,斑马线,漏检,检出距离不足,采集道路属性-高速场景视频，运行算法，人工核查离散目标斑马线是否存在检出距离不足，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0327,车辆行为,路口右转,车道线,黄色虚线,类型错误,类型部分错误,采集车辆行为-路口右转场景视频，运行算法，人工核查车道线黄色虚线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0328,车辆行为,路口右转,车道线,双黄线,系统问题,系统性问题,采集车辆行为-路口右转场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0329,直道变化场景,弯道,离散目标,禁停区,漏检,漏检地面箭头,采集直道变化场景-弯道场景视频，运行算法，人工核查离散目标禁停区是否存在漏检地面箭头，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0330,路口场景,十字路口,离散目标,driveline,漏检,检出距离不足,采集路口场景-十字路口场景视频，运行算法，人工核查离散目标driveline是否存在检出距离不足，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0331,道路属性,高速,离散目标,禁停区,漏检,漏检地面箭头,采集道路属性-高速场景视频，运行算法，人工核查离散目标禁停区是否存在漏检地面箭头，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0332,路口场景,十字路口,路沿,水马,漏检,检出距离不足,采集路口场景-十字路口场景视频，运行算法，人工核查路沿水马是否存在检出距离不足，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0333,道路属性,高速,路沿,路沿线,误检,实例乱线,采集道路属性-高速场景视频，运行算法，人工核查路沿路沿线是否存在实例乱线，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0334,直道变化场景,高速反光柱,离散目标,地面箭头,漏检,检出距离不足,采集直道变化场景-高速反光柱场景视频，运行算法，人工核查离散目标地面箭头是否存在检出距离不足，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0335,道路属性,施工,离散目标,斑马线,漏检,漏检地面箭头,采集道路属性-施工场景视频，运行算法，人工核查离散目标斑马线是否存在漏检地面箭头，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0336,车辆行为,路口左转,路沿,水马,误检,位置错误,采集车辆行为-路口左转场景视频，运行算法，人工核查路沿水马是否存在位置错误，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0337,道路属性,城区,路沿,水马,误检,实例断联,采集道路属性-城区场景视频，运行算法，人工核查路沿水马是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0338,车辆行为,路口直行,车道线,黄色虚线,误检,实例乱线,采集车辆行为-路口直行场景视频，运行算法，人工核查车道线黄色虚线是否存在实例乱线，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0339,直道变化场景,合流,路沿,路沿线,误检,实例乱线,采集直道变化场景-合流场景视频，运行算法，人工核查路沿路沿线是否存在实例乱线，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0340,车辆行为,路口右转,离散目标,地面箭头,误检,实例弯折,采集车辆行为-路口右转场景视频，运行算法，人工核查离散目标地面箭头是否存在实例弯折，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0341,直道变化场景,导流区,车道线,导流线,类型错误,类型整体错误,采集直道变化场景-导流区场景视频，运行算法，人工核查车道线导流线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0342,直道变化场景,分流,路沿,水马,误检,实例乱线,采集直道变化场景-分流场景视频，运行算法，人工核查路沿水马是否存在实例乱线，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0343,车辆行为,路口掉头,路沿,护栏,误检,实例多联,采集车辆行为-路口掉头场景视频，运行算法，人工核查路沿护栏是否存在实例多联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0344,道路属性,城区,车道线,左实右虚双黄线,系统问题,可视化问题,采集道路属性-城区场景视频，运行算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0345,道路属性,高速,路沿,锥桶,误检,实例乱线,采集道路属性-高速场景视频，运行算法，人工核查路沿锥桶是否存在实例乱线，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0346,车辆行为,路口右转,路沿,路沿线,漏检,漏检水马,采集车辆行为-路口右转场景视频，运行算法，人工核查路沿路沿线是否存在漏检水马，并记录漏检情况。,无漏检，所有目标均被检测,阈值：漏检率<0.1%
TC-0347,直道场景,斑马线,路沿,锥桶,误检,实例断联,采集直道场景-斑马线场景视频，运行算法，人工核查路沿锥桶是否存在实例断联，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0348,直道场景,鱼骨线,车道线,左实右虚双白线,类型错误,类型跳变错误,采集直道场景-鱼骨线场景视频，运行算法，人工核查车道线左实右虚双白线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0349,道路属性,施工,路沿,护栏,漏检,漏检锥桶,实车采集施工场景数据，自动化运行路沿检测算法，人工复核护栏是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0350,直道变化场景,大曲率弯道,车道线,左实右虚双白线,类型错误,类型跳变错误,实车采集大曲率弯道场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0351,路口场景,直行待行区,车道线,白色虚线,类型错误,类型部分错误,实车采集直行待行区场景数据，自动化运行车道线检测算法，人工复核白色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0352,道路属性,城区,离散目标,停止线,漏检,漏检driveline,实车采集城区场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在漏检driveline，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0353,直道场景,车道线不清晰,离散目标,driveline,漏检,漏检锥桶,实车采集车道线不清晰场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0354,路口场景,左转待转区,路沿,锥桶,误检,实例乱线,实车采集左转待转区场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0355,直道场景,斑马线,离散目标,地面箭头,误检,实例乱线,实车采集斑马线场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0356,车辆行为,路口左转,车道线,双黄线,系统问题,系统性问题,实车采集路口左转场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0357,直道场景,停止线,车道线,双黄线,类型错误,类型整体错误,实车采集停止线场景数据，自动化运行车道线检测算法，人工复核双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0358,路口场景,三岔路口,车道线,左虚右实双黄线,类型错误,类型跳变错误,实车采集三岔路口场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0359,直道变化场景,主路进入辅路,离散目标,停止线,误检,位置错误,实车采集主路进入辅路场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0360,车辆行为,路口左转,车道线,白色虚线,误检,实例乱线,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0361,直道场景,公交车道,车道线,白色虚线,类型错误,类型部分错误,实车采集公交车道场景数据，自动化运行车道线检测算法，人工复核白色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0362,直道变化场景,主路进入辅路,车道线,双黄线,误检,实例多联,实车采集主路进入辅路场景数据，自动化运行车道线检测算法，人工复核双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0363,直道变化场景,多分少,车道线,双黄线,误检,实例多联,实车采集多分少场景数据，自动化运行车道线检测算法，人工复核双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0364,车辆行为,路口直行,路沿,锥桶,漏检,漏检锥桶,实车采集路口直行场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在漏检，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0365,路口场景,十字路口,车道线,左虚右实双白线,类型错误,类型跳变错误,实车采集十字路口场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0366,直道场景,新旧车道线,车道线,左虚右实双白线,漏检,漏检导流线,实车采集新旧车道线场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在漏检导流线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0367,车辆行为,路口直行,离散目标,停止线,漏检,漏检地面箭头,实车采集路口直行场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在漏检地面箭头，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0368,车辆行为,路口左转,车道线,左实右虚双黄线,误检,实例多联,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0369,直道场景,常规车道,车道线,黄色虚线,漏检,漏检道路线,实车采集常规车道场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在漏检道路线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0370,车辆行为,路口左转,离散目标,斑马线,漏检,漏检水马,实车采集路口左转场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0371,直道场景,新旧车道线,车道线,左虚右实双白线,漏检,漏检护栏,实车采集新旧车道线场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0372,车辆行为,路口右转,车道线,导流线,误检,实例断联,实车采集路口右转场景数据，自动化运行车道线检测算法，人工复核导流线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0373,直道场景,斑马线,离散目标,斑马线,误检,多线重合,实车采集斑马线场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0374,直道变化场景,多分少,车道线,双黄线,误检,曲率错误,实车采集多分少场景数据，自动化运行车道线检测算法，人工复核双黄线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0375,路口场景,三岔路口,离散目标,地面箭头,误检,实例乱线,实车采集三岔路口场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0376,直道场景,禁停区,路沿,锥桶,误检,多线重合,实车采集禁停区场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0377,道路属性,施工,车道线,左实右虚双黄线,系统问题,系统性问题,实车采集施工场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0378,道路属性,高速,车道线,黄色实线,漏检,漏检道路线,实车采集高速场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在漏检道路线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0379,道路属性,施工,离散目标,禁停区,漏检,漏检停止线,实车采集施工场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在漏检停止线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0380,道路属性,城区,车道线,左虚右实双白线,误检,曲率错误,实车采集城区场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0381,车辆行为,路口直行,路沿,锥桶,误检,实例多联,实车采集路口直行场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0382,道路属性,高速,车道线,黄色实线,系统问题,可视化问题,实车采集高速场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0383,车辆行为,路口左转,车道线,黄色虚线,误检,位置错误,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0384,道路属性,施工,车道线,左虚右实双黄线,漏检,漏检锥桶,实车采集施工场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0385,车辆行为,路口直行,车道线,白色实线,漏检,漏检护栏,实车采集路口直行场景数据，自动化运行车道线检测算法，人工复核白色实线是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0386,车辆行为,路口直行,路沿,锥桶,误检,位置错误,实车采集路口直行场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0387,路口场景,三岔路口,车道线,导流线,类型错误,类型部分错误,实车采集三岔路口场景数据，自动化运行车道线检测算法，人工复核导流线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0388,直道场景,高架,路沿,护栏,误检,实例断联,实车采集高架场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0389,直道场景,潮汐车道,车道线,黄色实线,误检,实例乱线,实车采集潮汐车道场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0390,直道变化场景,分流,车道线,左虚右实双黄线,类型错误,类型跳变错误,实车采集分流场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0391,直道场景,车道线不清晰,车道线,黄色实线,误检,曲率错误,实车采集车道线不清晰场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0392,车辆行为,路口右转,车道线,白色虚线,误检,位置错误,实车采集路口右转场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0393,车辆行为,路口左转,车道线,左实右虚双白线,系统问题,系统性问题,实车采集路口左转场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0394,直道变化场景,联排锥桶,车道线,黄色虚线,类型错误,类型部分错误,实车采集联排锥桶场景数据，自动化运行车道线检测算法，人工复核黄色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0395,直道变化场景,少分多,离散目标,斑马线,漏检,检出距离不足,实车采集少分多场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0396,路口场景,左转待转区,路沿,水马,误检,位置错误,实车采集左转待转区场景数据，自动化运行路沿检测算法，人工复核水马是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0397,路口场景,错位路口,车道线,左虚右实双黄线,漏检,检出距离不足,实车采集错位路口场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0398,道路属性,高速,离散目标,斑马线,误检,实例断联,实车采集高速场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0399,路口场景,丁字路口,车道线,黄色虚线,误检,实例乱线,实车采集丁字路口场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0400,直道变化场景,高速反光柱,车道线,黄色虚线,误检,实例乱线,实车采集高速反光柱场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0401,直道变化场景,收费站,路沿,锥桶,漏检,漏检护栏,实车采集收费站场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0402,直道变化场景,收费站,路沿,锥桶,误检,实例弯折,实车采集收费站场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0403,直道变化场景,辅路进入主路,车道线,白色实线,误检,曲率错误,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，人工复核白色实线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0404,道路属性,高速,车道线,左虚右实双白线,系统问题,可视化问题,实车采集高速场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0405,道路属性,城区,车道线,白色虚线,系统问题,可视化问题,实车采集城区场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0406,道路属性,施工,离散目标,地面箭头,漏检,检出距离不足,实车采集施工场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0407,直道变化场景,收费站,路沿,护栏,误检,实例断联,实车采集收费站场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0408,直道变化场景,导流区,离散目标,停止线,漏检,漏检停止线,实车采集导流区场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在漏检，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0409,直道场景,车道线不清晰,车道线,导流线,误检,多线重合,实车采集车道线不清晰场景数据，自动化运行车道线检测算法，人工复核导流线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0410,路口场景,十字路口,离散目标,停止线,误检,实例断联,实车采集十字路口场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0411,车辆行为,路口掉头,车道线,左虚右实双黄线,系统问题,系统性问题,实车采集路口掉头场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0412,直道变化场景,辅路进入主路,车道线,黄色实线,类型错误,类型整体错误,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，人工复核黄色实线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0413,路口场景,多叉路口,车道线,白色实线,类型错误,类型整体错误,实车采集多叉路口场景数据，自动化运行车道线检测算法，人工复核白色实线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0414,车辆行为,路口右转,离散目标,停止线,误检,多线重合,实车采集路口右转场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0415,道路属性,城区,车道线,黄色实线,误检,多线重合,实车采集城区场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0416,直道场景,导流区,车道线,左虚右实双黄线,误检,多线重合,实车采集导流区场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0417,道路属性,城区,车道线,白色实线,误检,曲率错误,实车采集城区场景数据，自动化运行车道线检测算法，人工复核白色实线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0418,道路属性,城区,车道线,白色虚线,系统问题,系统性问题,实车采集城区场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0419,直道变化场景,辅路进入主路,车道线,黄色虚线,系统问题,系统性问题,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0420,车辆行为,路口直行,车道线,左虚右实双黄线,误检,实例多联,实车采集路口直行场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0421,车辆行为,路口右转,车道线,导流线,漏检,漏检护栏,实车采集路口右转场景数据，自动化运行车道线检测算法，人工复核导流线是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0422,直道场景,停止线,车道线,左虚右实双黄线,漏检,检出距离不足,实车采集停止线场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0423,道路属性,城区,车道线,左实右虚双白线,系统问题,可视化问题,实车采集城区场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0424,车辆行为,路口右转,车道线,左实右虚双白线,误检,位置错误,实车采集路口右转场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0425,车辆行为,路口直行,离散目标,driveline,误检,实例弯折,实车采集路口直行场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0426,路口场景,多叉路口,路沿,路沿线,漏检,漏检护栏,实车采集多叉路口场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0427,直道变化场景,辅路进入主路,车道线,左虚右实双黄线,系统问题,系统性问题,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0428,直道场景,弯道,车道线,白色实线,类型错误,类型跳变错误,实车采集弯道场景数据，自动化运行车道线检测算法，人工复核白色实线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0429,直道变化场景,联排锥桶,车道线,黄色实线,漏检,漏检道路线,实车采集联排锥桶场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在漏检道路线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0430,车辆行为,路口掉头,离散目标,停止线,误检,曲率错误,实车采集路口掉头场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0431,路口场景,左转待转区,离散目标,禁停区,漏检,漏检地面箭头,实车采集左转待转区场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在漏检地面箭头，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0432,道路属性,施工,路沿,锥桶,漏检,漏检水马,实车采集施工场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0433,直道场景,新旧车道线,车道线,左虚右实双黄线,误检,曲率错误,实车采集新旧车道线场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0434,道路属性,高速,车道线,导流线,漏检,漏检水马,实车采集高速场景数据，自动化运行车道线检测算法，人工复核导流线是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0435,直道变化场景,收费站,车道线,导流线,类型错误,类型跳变错误,实车采集收费站场景数据，自动化运行车道线检测算法，人工复核导流线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0436,直道场景,隧道,车道线,白色虚线,类型错误,类型跳变错误,实车采集隧道场景数据，自动化运行车道线检测算法，人工复核白色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0437,路口场景,多叉路口,路沿,锥桶,漏检,漏检护栏,实车采集多叉路口场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0438,道路属性,高速,车道线,左实右虚双白线,系统问题,系统性问题,实车采集高速场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0439,直道变化场景,弯道,车道线,左实右虚双黄线,类型错误,类型部分错误,实车采集弯道场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0440,路口场景,三岔路口,离散目标,斑马线,漏检,漏检水马,实车采集三岔路口场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0441,直道场景,隧道,车道线,黄色虚线,漏检,检出距离不足,实车采集隧道场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0442,路口场景,十字路口,车道线,白色虚线,漏检,漏检导流线,实车采集十字路口场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在漏检导流线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0443,路口场景,左转待转区,车道线,左实右虚双黄线,误检,实例乱线,实车采集左转待转区场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0444,直道场景,潮汐车道,车道线,导流线,类型错误,类型部分错误,实车采集潮汐车道场景数据，自动化运行车道线检测算法，人工复核导流线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0445,直道场景,可变车道,路沿,锥桶,漏检,漏检护栏,实车采集可变车道场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0446,路口场景,直行待行区,车道线,黄色实线,误检,实例弯折,实车采集直行待行区场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0447,直道变化场景,联排锥桶,路沿,路沿线,漏检,漏检锥桶,实车采集联排锥桶场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0448,路口场景,超大路口,路沿,锥桶,误检,实例多联,实车采集超大路口场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0449,直道场景,公交车道,离散目标,停止线,误检,实例弯折,实车采集公交车道场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0450,直道场景,停止线,路沿,水马,误检,实例多联,实车采集停止线场景数据，自动化运行路沿检测算法，人工复核水马是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0451,道路属性,施工,车道线,左虚右实双黄线,误检,实例多联,实车采集施工场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0452,道路属性,高速,路沿,路沿线,误检,实例断联,实车采集高速场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0453,车辆行为,路口直行,车道线,黄色实线,系统问题,可视化问题,实车采集路口直行场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0454,车辆行为,路口直行,车道线,黄色实线,误检,实例弯折,实车采集路口直行场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0455,直道场景,超宽车道,车道线,黄色虚线,类型错误,类型跳变错误,实车采集超宽车道场景数据，自动化运行车道线检测算法，人工复核黄色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0456,路口场景,错位路口,离散目标,停止线,误检,实例乱线,实车采集错位路口场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0457,直道场景,可变车道,车道线,双黄线,类型错误,类型部分错误,实车采集可变车道场景数据，自动化运行车道线检测算法，人工复核双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0458,车辆行为,路口掉头,路沿,护栏,误检,曲率错误,实车采集路口掉头场景数据，自动化运行路沿检测算法，人工复核护栏是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0459,直道场景,超宽车道,车道线,左虚右实双黄线,系统问题,系统性问题,实车采集超宽车道场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0460,直道变化场景,收费站,路沿,锥桶,误检,实例乱线,实车采集收费站场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0461,直道场景,常规车道,路沿,锥桶,误检,实例弯折,实车采集常规车道场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0462,道路属性,施工,离散目标,斑马线,漏检,检出距离不足,实车采集施工场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0463,路口场景,直行待行区,离散目标,斑马线,误检,实例断联,实车采集直行待行区场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0464,道路属性,高速,车道线,双黄线,系统问题,系统性问题,实车采集高速场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0465,直道场景,高架,路沿,路沿线,误检,实例乱线,实车采集高架场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0466,直道场景,新旧车道线,离散目标,斑马线,漏检,检出距离不足,实车采集新旧车道线场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0467,车辆行为,路口左转,车道线,导流线,误检,多线重合,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核导流线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0468,直道场景,应急车道,车道线,左实右虚双黄线,漏检,漏检导流线,实车采集应急车道场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在漏检导流线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0469,车辆行为,路口左转,路沿,护栏,漏检,漏检路沿线,实车采集路口左转场景数据，自动化运行路沿检测算法，人工复核护栏是否存在漏检路沿线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0470,车辆行为,路口左转,离散目标,斑马线,误检,实例乱线,实车采集路口左转场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0471,路口场景,左转待转区,车道线,双黄线,系统问题,系统性问题,实车采集左转待转区场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0472,道路属性,城区,离散目标,斑马线,漏检,漏检锥桶,实车采集城区场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0473,车辆行为,路口左转,离散目标,停止线,误检,实例断联,实车采集路口左转场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0474,直道变化场景,少分多,离散目标,停止线,漏检,漏检水马,实车采集少分多场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0475,车辆行为,路口直行,离散目标,停止线,误检,实例弯折,实车采集路口直行场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0476,路口场景,丁字路口,离散目标,禁停区,漏检,检出距离不足,实车采集丁字路口场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0477,直道变化场景,环岛,路沿,护栏,误检,曲率错误,实车采集环岛场景数据，自动化运行路沿检测算法，人工复核护栏是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0478,车辆行为,路口直行,离散目标,地面箭头,漏检,漏检护栏,实车采集路口直行场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0479,直道变化场景,分流,离散目标,停止线,漏检,漏检driveline,实车采集分流场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在漏检driveline，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0480,路口场景,错位路口,车道线,左虚右实双黄线,类型错误,类型跳变错误,实车采集错位路口场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0481,直道场景,公交车道,车道线,左虚右实双白线,误检,实例弯折,实车采集公交车道场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0482,直道变化场景,匝道,离散目标,禁停区,误检,实例弯折,实车采集匝道场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0483,直道变化场景,少分多,路沿,锥桶,误检,位置错误,实车采集少分多场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0484,直道场景,应急车道,车道线,黄色虚线,误检,实例多联,实车采集应急车道场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0485,道路属性,高速,离散目标,斑马线,误检,实例乱线,实车采集高速场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0486,直道场景,新旧车道线,离散目标,地面箭头,误检,多线重合,实车采集新旧车道线场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0487,道路属性,高速,车道线,左虚右实双黄线,类型错误,类型跳变错误,实车采集高速场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0488,直道变化场景,环岛,离散目标,地面箭头,误检,实例多联,实车采集环岛场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0489,道路属性,高速,车道线,黄色实线,误检,实例多联,实车采集高速场景数据，自动化运行车道线检测算法，人工复核黄色实线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0490,道路属性,城区,离散目标,斑马线,误检,多线重合,实车采集城区场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0491,直道变化场景,弯道,离散目标,地面箭头,漏检,漏检driveline,实车采集弯道场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在漏检driveline，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0492,路口场景,丁字路口,车道线,双黄线,误检,实例乱线,实车采集丁字路口场景数据，自动化运行车道线检测算法，人工复核双黄线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0493,直道场景,车道线不清晰,车道线,导流线,系统问题,系统性问题,实车采集车道线不清晰场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0494,路口场景,三岔路口,离散目标,driveline,误检,多线重合,实车采集三岔路口场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0495,道路属性,高速,车道线,白色虚线,系统问题,系统性问题,实车采集高速场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0496,路口场景,多叉路口,离散目标,斑马线,漏检,漏检斑马线,实车采集多叉路口场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检斑马线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0497,路口场景,丁字路口,车道线,左虚右实双黄线,类型错误,类型部分错误,实车采集丁字路口场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0498,车辆行为,路口直行,路沿,水马,误检,曲率错误,实车采集路口直行场景数据，自动化运行路沿检测算法，人工复核水马是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0499,道路属性,高速,路沿,护栏,漏检,漏检水马,实车采集高速场景数据，自动化运行路沿检测算法，人工复核护栏是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0500,直道场景,常规车道,车道线,左虚右实双白线,误检,实例乱线,实车采集常规车道场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0501,道路属性,施工,路沿,护栏,误检,实例多联,实车采集施工场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0502,路口场景,直行待行区,离散目标,禁停区,漏检,漏检停止线,实车采集直行待行区场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在漏检停止线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0503,车辆行为,路口右转,离散目标,地面箭头,漏检,漏检地面箭头,实车采集路口右转场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在漏检，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0504,道路属性,施工,车道线,左虚右实双黄线,类型错误,类型跳变错误,实车采集施工场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0505,直道场景,鱼骨线,离散目标,driveline,漏检,漏检地面箭头,实车采集鱼骨线场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在漏检地面箭头，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0506,车辆行为,路口直行,车道线,导流线,系统问题,可视化问题,实车采集路口直行场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0507,直道场景,车道线不清晰,路沿,护栏,误检,实例弯折,实车采集车道线不清晰场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0508,直道场景,应急车道,离散目标,地面箭头,误检,实例弯折,实车采集应急车道场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0509,直道变化场景,多分少,离散目标,地面箭头,漏检,漏检护栏,实车采集多分少场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0510,道路属性,高速,离散目标,禁停区,误检,曲率错误,实车采集高速场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0511,直道场景,潮汐车道,离散目标,斑马线,漏检,漏检护栏,实车采集潮汐车道场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0512,道路属性,高速,离散目标,driveline,误检,曲率错误,实车采集高速场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0513,路口场景,三岔路口,离散目标,禁停区,误检,实例弯折,实车采集三岔路口场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0514,路口场景,十字路口,车道线,左实右虚双黄线,系统问题,系统性问题,实车采集十字路口场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0515,直道变化场景,主路进入辅路,路沿,护栏,误检,位置错误,实车采集主路进入辅路场景数据，自动化运行路沿检测算法，人工复核护栏是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0516,直道变化场景,匝道,离散目标,禁停区,漏检,漏检斑马线,实车采集匝道场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在漏检斑马线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0517,直道场景,超宽车道,路沿,路沿线,误检,位置错误,实车采集超宽车道场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0518,车辆行为,路口右转,离散目标,斑马线,误检,实例乱线,实车采集路口右转场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0519,路口场景,左转待转区,车道线,白色实线,类型错误,类型跳变错误,实车采集左转待转区场景数据，自动化运行车道线检测算法，人工复核白色实线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0520,道路属性,城区,路沿,护栏,误检,实例弯折,实车采集城区场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0521,直道场景,弯道,车道线,左虚右实双白线,误检,实例断联,实车采集弯道场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0522,路口场景,错位路口,离散目标,driveline,误检,实例弯折,实车采集错位路口场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0523,直道变化场景,辅路进入主路,离散目标,driveline,误检,曲率错误,实车采集辅路进入主路场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0524,直道场景,常规车道,车道线,左实右虚双黄线,系统问题,可视化问题,实车采集常规车道场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0525,直道场景,车道线不清晰,车道线,白色虚线,误检,实例多联,实车采集车道线不清晰场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0526,直道场景,高架,路沿,锥桶,误检,实例乱线,实车采集高架场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0527,直道变化场景,高速反光柱,路沿,水马,误检,曲率错误,实车采集高速反光柱场景数据，自动化运行路沿检测算法，人工复核水马是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0528,直道变化场景,高速反光柱,车道线,白色虚线,漏检,检出距离不足,实车采集高速反光柱场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0529,直道场景,超宽车道,车道线,黄色虚线,系统问题,系统性问题,实车采集超宽车道场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0530,车辆行为,路口掉头,离散目标,driveline,误检,实例弯折,实车采集路口掉头场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0531,车辆行为,路口掉头,离散目标,禁停区,漏检,漏检停止线,实车采集路口掉头场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在漏检停止线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0532,车辆行为,路口直行,离散目标,斑马线,误检,实例弯折,实车采集路口直行场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0533,道路属性,城区,车道线,导流线,系统问题,可视化问题,实车采集城区场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0534,直道变化场景,弯道,车道线,黄色虚线,漏检,漏检锥桶,实车采集弯道场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0535,直道变化场景,联排水马,车道线,左实右虚双白线,类型错误,类型整体错误,实车采集联排水马场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0536,直道场景,公交车道,车道线,左实右虚双黄线,误检,实例乱线,实车采集公交车道场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0537,直道变化场景,大曲率弯道,离散目标,driveline,误检,实例多联,实车采集大曲率弯道场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0538,直道变化场景,辅路进入主路,车道线,左虚右实双白线,系统问题,系统性问题,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0539,路口场景,右转专用道,离散目标,地面箭头,漏检,漏检禁停区,实车采集右转专用道场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在漏检禁停区，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0540,直道变化场景,收费站,离散目标,driveline,误检,实例弯折,实车采集收费站场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0541,路口场景,多叉路口,离散目标,斑马线,误检,实例断联,实车采集多叉路口场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0542,直道场景,超宽车道,车道线,导流线,类型错误,类型部分错误,实车采集超宽车道场景数据，自动化运行车道线检测算法，人工复核导流线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0543,车辆行为,路口左转,路沿,护栏,漏检,漏检水马,实车采集路口左转场景数据，自动化运行路沿检测算法，人工复核护栏是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0544,路口场景,直行待行区,路沿,水马,漏检,漏检护栏,实车采集直行待行区场景数据，自动化运行路沿检测算法，人工复核水马是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0545,直道场景,超窄车道,车道线,左虚右实双黄线,误检,位置错误,实车采集超窄车道场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0546,直道场景,隧道,离散目标,停止线,误检,实例乱线,实车采集隧道场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0547,车辆行为,路口直行,车道线,导流线,类型错误,类型部分错误,实车采集路口直行场景数据，自动化运行车道线检测算法，人工复核导流线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0548,直道变化场景,分流,离散目标,driveline,误检,位置错误,实车采集分流场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0549,车辆行为,路口右转,离散目标,斑马线,漏检,漏检斑马线,实车采集路口右转场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检斑马线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0550,路口场景,右转专用道,路沿,锥桶,漏检,漏检水马,实车采集右转专用道场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0551,直道场景,停止线,路沿,护栏,误检,实例多联,实车采集停止线场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0552,道路属性,城区,路沿,护栏,误检,曲率错误,实车采集城区场景数据，自动化运行路沿检测算法，人工复核护栏是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0553,直道变化场景,环岛,车道线,左实右虚双黄线,系统问题,系统性问题,实车采集环岛场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0554,道路属性,城区,车道线,白色虚线,误检,曲率错误,实车采集城区场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0555,直道场景,可变车道,车道线,白色实线,类型错误,类型部分错误,实车采集可变车道场景数据，自动化运行车道线检测算法，人工复核白色实线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0556,直道变化场景,弯道,路沿,路沿线,漏检,漏检路沿线,实车采集弯道场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在漏检，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0557,直道场景,潮汐车道,车道线,黄色虚线,系统问题,可视化问题,实车采集潮汐车道场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0558,道路属性,城区,车道线,导流线,类型错误,类型部分错误,实车采集城区场景数据，自动化运行车道线检测算法，人工复核导流线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0559,直道变化场景,弯道,车道线,导流线,系统问题,可视化问题,实车采集弯道场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0560,路口场景,三岔路口,离散目标,停止线,误检,多线重合,实车采集三岔路口场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0561,车辆行为,路口直行,车道线,白色虚线,误检,实例乱线,实车采集路口直行场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0562,直道变化场景,多分少,车道线,左虚右实双黄线,类型错误,类型整体错误,实车采集多分少场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0563,直道场景,应急车道,离散目标,禁停区,漏检,漏检停止线,实车采集应急车道场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在漏检停止线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0564,直道场景,鱼骨线,车道线,左实右虚双黄线,误检,实例弯折,实车采集鱼骨线场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0565,道路属性,城区,离散目标,停止线,误检,多线重合,实车采集城区场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0566,直道变化场景,弯道,离散目标,停止线,漏检,漏检地面箭头,实车采集弯道场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在漏检地面箭头，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0567,路口场景,左转待转区,车道线,左虚右实双白线,系统问题,可视化问题,实车采集左转待转区场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0568,直道场景,高架,车道线,左虚右实双白线,系统问题,可视化问题,实车采集高架场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0569,直道场景,弯道,离散目标,停止线,误检,实例乱线,实车采集弯道场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0570,直道变化场景,分流,车道线,导流线,误检,实例断联,实车采集分流场景数据，自动化运行车道线检测算法，人工复核导流线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0571,直道变化场景,多分少,车道线,左实右虚双白线,系统问题,系统性问题,实车采集多分少场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0572,路口场景,丁字路口,离散目标,停止线,误检,曲率错误,实车采集丁字路口场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0573,直道变化场景,匝道,离散目标,driveline,漏检,漏检禁停区,实车采集匝道场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在漏检禁停区，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0574,路口场景,丁字路口,离散目标,斑马线,漏检,漏检driveline,实车采集丁字路口场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检driveline，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0575,直道场景,弯道,车道线,黄色虚线,误检,多线重合,实车采集弯道场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0576,直道场景,斑马线,路沿,护栏,漏检,漏检水马,实车采集斑马线场景数据，自动化运行路沿检测算法，人工复核护栏是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0577,路口场景,丁字路口,车道线,黄色虚线,类型错误,类型整体错误,实车采集丁字路口场景数据，自动化运行车道线检测算法，人工复核黄色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0578,道路属性,城区,离散目标,driveline,误检,实例弯折,实车采集城区场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0579,路口场景,左转待转区,车道线,左实右虚双黄线,误检,实例多联,实车采集左转待转区场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0580,直道变化场景,弯道,离散目标,斑马线,误检,实例弯折,实车采集弯道场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0581,车辆行为,路口掉头,离散目标,斑马线,误检,曲率错误,实车采集路口掉头场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0582,直道场景,高架,离散目标,禁停区,漏检,漏检护栏,实车采集高架场景数据，自动化运行离散目标检测算法，人工复核禁停区是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0583,路口场景,错位路口,路沿,路沿线,误检,实例弯折,实车采集错位路口场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0584,车辆行为,路口右转,路沿,路沿线,漏检,漏检锥桶,实车采集路口右转场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0585,路口场景,三岔路口,离散目标,停止线,误检,实例弯折,实车采集三岔路口场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0586,路口场景,丁字路口,离散目标,斑马线,漏检,漏检锥桶,实车采集丁字路口场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0587,直道场景,高架,离散目标,斑马线,误检,多线重合,实车采集高架场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0588,车辆行为,路口左转,车道线,左虚右实双黄线,类型错误,类型跳变错误,实车采集路口左转场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0589,路口场景,十字路口,车道线,黄色虚线,系统问题,可视化问题,实车采集十字路口场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0590,车辆行为,路口左转,路沿,水马,误检,曲率错误,实车采集路口左转场景数据，自动化运行路沿检测算法，人工复核水马是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0591,直道变化场景,匝道,路沿,路沿线,误检,曲率错误,实车采集匝道场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0592,路口场景,丁字路口,离散目标,地面箭头,误检,实例弯折,实车采集丁字路口场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0593,路口场景,错位路口,车道线,左虚右实双白线,类型错误,类型整体错误,实车采集错位路口场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0594,直道变化场景,导流区,路沿,护栏,误检,实例弯折,实车采集导流区场景数据，自动化运行路沿检测算法，人工复核护栏是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0595,路口场景,右转专用道,路沿,护栏,漏检,检出距离不足,实车采集右转专用道场景数据，自动化运行路沿检测算法，人工复核护栏是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0596,直道变化场景,收费站,离散目标,driveline,漏检,漏检水马,实车采集收费站场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0597,直道场景,公交车道,车道线,白色虚线,漏检,漏检导流线,实车采集公交车道场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在漏检导流线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0598,路口场景,三岔路口,车道线,白色实线,漏检,漏检护栏,实车采集三岔路口场景数据，自动化运行车道线检测算法，人工复核白色实线是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0599,直道场景,车道线不清晰,离散目标,斑马线,漏检,漏检斑马线,实车采集车道线不清晰场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检斑马线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0600,直道变化场景,合流,车道线,黄色实线,系统问题,可视化问题,实车采集合流场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0601,道路属性,城区,离散目标,停止线,漏检,漏检水马,实车采集城区场景数据，自动化运行离散目标检测算法，人工复核停止线是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0602,车辆行为,路口左转,离散目标,斑马线,误检,曲率错误,实车采集路口左转场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0603,路口场景,左转待转区,车道线,双黄线,系统问题,可视化问题,实车采集左转待转区场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0604,直道场景,常规车道,车道线,左实右虚双黄线,类型错误,类型整体错误,实车采集常规车道场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0605,直道场景,超宽车道,路沿,护栏,漏检,漏检护栏,实车采集超宽车道场景数据，自动化运行路沿检测算法，人工复核护栏是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0606,路口场景,左转待转区,路沿,护栏,误检,曲率错误,实车采集左转待转区场景数据，自动化运行路沿检测算法，人工复核护栏是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0607,直道变化场景,少分多,离散目标,地面箭头,误检,实例断联,实车采集少分多场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0608,直道变化场景,分流,车道线,白色虚线,误检,曲率错误,实车采集分流场景数据，自动化运行车道线检测算法，人工复核白色虚线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0609,直道场景,新旧车道线,路沿,路沿线,误检,位置错误,实车采集新旧车道线场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0610,道路属性,城区,车道线,白色实线,类型错误,类型部分错误,采集道路属性-城区场景视频，运行算法，人工核查车道线白色实线类型识别是否准确，记录类型识别错误。,类型识别准确，无类型错误,类型识别准确率>99%
TC-0611,路口场景,错位路口,车道线,左虚右实双黄线,误检,多线重合,采集路口场景-错位路口场景视频，运行算法，人工核查车道线左虚右实双黄线是否存在多线重合，并记录误检情况。,无误检，所有目标均正确识别,阈值：误检率<0.1%
TC-0612,车辆行为,路口右转,车道线,黄色虚线,类型错误,类型跳变错误,实车采集路口右转场景数据，自动化运行车道线检测算法，人工复核黄色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0613,车辆行为,路口右转,离散目标,driveline,误检,位置错误,实车采集路口右转场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0614,道路属性,施工,离散目标,地面箭头,漏检,漏检斑马线,实车采集施工场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在漏检斑马线，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0615,车辆行为,路口掉头,车道线,白色虚线,类型错误,类型跳变错误,实车采集路口掉头场景数据，自动化运行车道线检测算法，人工复核白色虚线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0616,直道场景,新旧车道线,路沿,锥桶,误检,位置错误,实车采集新旧车道线场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0617,路口场景,右转专用道,路沿,路沿线,漏检,漏检锥桶,实车采集右转专用道场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0618,直道变化场景,辅路进入主路,车道线,左实右虚双黄线,类型错误,类型部分错误,实车采集辅路进入主路场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0619,道路属性,高速,车道线,左实右虚双白线,类型错误,类型部分错误,实车采集高速场景数据，自动化运行车道线检测算法，人工复核左实右虚双白线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0620,道路属性,高速,路沿,锥桶,误检,多线重合,实车采集高速场景数据，自动化运行路沿检测算法，人工复核锥桶是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0621,路口场景,超大路口,车道线,白色实线,系统问题,可视化问题,实车采集超大路口场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0622,车辆行为,路口右转,车道线,白色虚线,系统问题,系统性问题,实车采集路口右转场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0623,路口场景,三岔路口,车道线,左虚右实双白线,漏检,漏检锥桶,实车采集三岔路口场景数据，自动化运行车道线检测算法，人工复核左虚右实双白线是否存在漏检锥桶，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0624,直道场景,斑马线,路沿,路沿线,误检,多线重合,实车采集斑马线场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0625,直道场景,常规车道,路沿,水马,误检,位置错误,实车采集常规车道场景数据，自动化运行路沿检测算法，人工复核水马是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0626,道路属性,施工,离散目标,地面箭头,误检,实例多联,实车采集施工场景数据，自动化运行离散目标检测算法，人工复核地面箭头是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0627,路口场景,丁字路口,路沿,护栏,漏检,漏检水马,实车采集丁字路口场景数据，自动化运行路沿检测算法，人工复核护栏是否存在漏检水马，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0628,路口场景,直行待行区,路沿,水马,误检,多线重合,实车采集直行待行区场景数据，自动化运行路沿检测算法，人工复核水马是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0629,直道变化场景,分流,路沿,路沿线,误检,实例多联,实车采集分流场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0630,道路属性,施工,车道线,导流线,类型错误,类型跳变错误,实车采集施工场景数据，自动化运行车道线检测算法，人工复核导流线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0631,路口场景,多叉路口,车道线,黄色虚线,漏检,漏检护栏,实车采集多叉路口场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0632,直道变化场景,少分多,路沿,路沿线,误检,实例断联,实车采集少分多场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0633,路口场景,丁字路口,离散目标,斑马线,误检,实例乱线,实车采集丁字路口场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在实例乱线，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0634,车辆行为,路口右转,车道线,左实右虚双黄线,误检,实例断联,实车采集路口右转场景数据，自动化运行车道线检测算法，人工复核左实右虚双黄线是否存在实例断联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0635,路口场景,左转待转区,路沿,护栏,漏检,漏检护栏,实车采集左转待转区场景数据，自动化运行路沿检测算法，人工复核护栏是否存在漏检护栏，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0636,路口场景,右转专用道,路沿,水马,误检,位置错误,实车采集右转专用道场景数据，自动化运行路沿检测算法，人工复核水马是否存在位置错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0637,路口场景,右转专用道,离散目标,斑马线,漏检,漏检禁停区,实车采集右转专用道场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在漏检禁停区，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0638,道路属性,城区,离散目标,driveline,漏检,检出距离不足,实车采集城区场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在检出距离不足，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0639,直道场景,隧道,车道线,左实右虚双白线,系统问题,系统性问题,实车采集隧道场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,系统性问题
TC-0640,车辆行为,路口掉头,车道线,导流线,类型错误,类型部分错误,实车采集路口掉头场景数据，自动化运行车道线检测算法，人工复核导流线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0641,路口场景,超大路口,路沿,路沿线,误检,实例弯折,实车采集超大路口场景数据，自动化运行路沿检测算法，人工复核路沿线是否存在实例弯折，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0642,道路属性,城区,车道线,黄色虚线,误检,曲率错误,实车采集城区场景数据，自动化运行车道线检测算法，人工复核黄色虚线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0643,车辆行为,路口掉头,离散目标,driveline,漏检,漏检地面箭头,实车采集路口掉头场景数据，自动化运行离散目标检测算法，人工复核driveline是否存在漏检地面箭头，记录漏检数量和比例。,无漏检，所有目标均被检测，漏检率<0.1%,阈值：漏检率<0.1%
TC-0644,直道场景,可变车道,车道线,左实右虚双黄线,系统问题,可视化问题,实车采集可变车道场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0645,路口场景,丁字路口,车道线,左虚右实双黄线,误检,实例多联,实车采集丁字路口场景数据，自动化运行车道线检测算法，人工复核左虚右实双黄线是否存在实例多联，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0646,车辆行为,路口直行,车道线,左实右虚双白线,系统问题,可视化问题,实车采集路口直行场景数据，自动化运行车道线检测算法，监控系统稳定性及可视化表现，记录系统异常。,系统稳定，无异常，无可视化问题,可视化问题
TC-0647,道路属性,高速,离散目标,斑马线,误检,曲率错误,实车采集高速场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在曲率错误，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%
TC-0648,路口场景,多叉路口,车道线,黄色实线,类型错误,类型跳变错误,实车采集多叉路口场景数据，自动化运行车道线检测算法，人工复核黄色实线类型识别准确性，记录类型识别错误数量和比例。,类型识别准确，无类型错误，类型识别准确率>99%,类型识别准确率>99%
TC-0649,直道场景,新旧车道线,离散目标,斑马线,误检,多线重合,实车采集新旧车道线场景数据，自动化运行离散目标检测算法，人工复核斑马线是否存在多线重合，记录误检数量和比例。,无误检，所有目标均正确识别，误检率<0.1%,阈值：误检率<0.1%