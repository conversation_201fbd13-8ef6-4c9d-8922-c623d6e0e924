#!/usr/bin/env python3
"""
统一的导入管理器
解决相对导入和绝对导入混乱问题
"""

import sys
import importlib
from pathlib import Path
from typing import Any, Dict, List, Optional


class ImportManager:
    """统一的导入管理器"""
    
    def __init__(self, package_name: str = "mcap_autodrive_sdk"):
        self.package_name = package_name
        self._cache: Dict[str, Any] = {}
        self._setup_paths()
    
    def _setup_paths(self):
        """设置导入路径"""
        # 添加项目根目录到路径
        project_root = Path(__file__).parent.parent
        if str(project_root) not in sys.path:
            sys.path.insert(0, str(project_root))
        
        # 添加src目录到路径
        src_path = project_root / "src"
        if src_path.exists() and str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
    
    def safe_import(self, module_name: str, class_name: Optional[str] = None) -> Any:
        """安全导入模块或类"""
        cache_key = f"{module_name}.{class_name}" if class_name else module_name
        
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        try:
            module = importlib.import_module(module_name)
            result = getattr(module, class_name) if class_name else module
            self._cache[cache_key] = result
            return result
        except ImportError as e:
            raise ImportError(f"无法导入 {cache_key}: {e}")
    
    def check_optional_dependency(self, module_name: str) -> bool:
        """检查可选依赖是否可用"""
        try:
            importlib.import_module(module_name)
            return True
        except ImportError:
            return False


# 全局导入管理器实例
import_manager = ImportManager()

# 便利函数
def safe_import(module_name: str, class_name: Optional[str] = None) -> Any:
    """安全导入的便利函数"""
    return import_manager.safe_import(module_name, class_name)

def check_dependency(module_name: str) -> bool:
    """检查依赖的便利函数"""
    return import_manager.check_optional_dependency(module_name)


# 预定义的核心导入
try:
    MessageData = safe_import("mcap_data_classes", "MessageData")
    TopicInfo = safe_import("mcap_data_classes", "TopicInfo")
    TimeRange = safe_import("mcap_data_classes", "TimeRange")
    ProcessingStats = safe_import("mcap_data_classes", "ProcessingStats")
    AnalysisResult = safe_import("mcap_data_classes", "AnalysisResult")
    StreamConfig = safe_import("mcap_data_classes", "StreamConfig")
    MessageTypeRegistry = safe_import("mcap_message_registry", "MessageTypeRegistry")
    FastMcapParser = safe_import("mcap_fast_parser", "FastMcapParser")
    
    CORE_IMPORTS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  核心模块导入失败: {e}")
    CORE_IMPORTS_AVAILABLE = False

# 检查可选依赖
MCAP_AVAILABLE = check_dependency("mcap_ros2.reader")
NUMPY_AVAILABLE = check_dependency("numpy")
PANDAS_AVAILABLE = check_dependency("pandas")
MATPLOTLIB_AVAILABLE = check_dependency("matplotlib")

if __name__ == "__main__":
    print("🔍 导入管理器状态:")
    print(f"   核心模块: {'✅' if CORE_IMPORTS_AVAILABLE else '❌'}")
    print(f"   MCAP支持: {'✅' if MCAP_AVAILABLE else '❌'}")
    print(f"   NumPy: {'✅' if NUMPY_AVAILABLE else '❌'}")
    print(f"   Pandas: {'✅' if PANDAS_AVAILABLE else '❌'}")
    print(f"   Matplotlib: {'✅' if MATPLOTLIB_AVAILABLE else '❌'}")
