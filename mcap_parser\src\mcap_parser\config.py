#!/usr/bin/env python3
"""
配置管理模块
提供MCAP解析器的配置管理功能
"""

import json
import logging
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Optional, Any, Union


@dataclass
class PerformanceConfig:
    """性能配置"""
    enable_fast_mode: bool = True
    batch_size: int = 10000
    max_workers: int = 4
    memory_limit_mb: int = 1024
    cache_size: int = 1000


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    enable_file_logging: bool = True
    log_dir: str = "logs"
    max_log_files: int = 10
    max_file_size_mb: int = 100
    backup_count: int = 5


@dataclass
class ParsingConfig:
    """解析配置"""
    strict_mode: bool = False
    enable_validation: bool = True
    enable_structured_data: bool = False
    default_message_types: List[str] = None
    time_tolerance_ms: float = 1.0
    coordinate_precision: int = 3
    max_message_count: Optional[int] = None
    enable_error_recovery: bool = True
    
    def __post_init__(self):
        if self.default_message_types is None:
            self.default_message_types = [
                "deva_perception_msgs/msg/LaneArrayv2",
                "deva_perception_msgs/msg/ObjectArray",
                "sensor_msgs/msg/PointCloud2",
                "sensor_msgs/msg/Image"
            ]


@dataclass
class ValidationConfig:
    """验证配置"""
    enable_schema_validation: bool = True
    enable_data_validation: bool = True
    strict_type_checking: bool = False
    allow_unknown_fields: bool = True
    max_coordinate_value: float = 1e6
    min_confidence_threshold: float = 0.0
    max_waypoints_per_lane: int = 1000


@dataclass
class McapSDKConfig:
    """MCAP SDK主配置类"""
    version: str = "3.1.0"
    performance: PerformanceConfig = None
    logging: LoggingConfig = None
    parsing: ParsingConfig = None
    validation: ValidationConfig = None

    def __post_init__(self):
        """初始化后处理"""
        if self.performance is None:
            self.performance = PerformanceConfig()
        if self.logging is None:
            self.logging = LoggingConfig()
        if self.parsing is None:
            self.parsing = ParsingConfig()
        if self.validation is None:
            self.validation = ValidationConfig()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "version": self.version,
            "performance": asdict(self.performance),
            "logging": asdict(self.logging),
            "parsing": asdict(self.parsing),
            "validation": asdict(self.validation)
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'McapSDKConfig':
        """从字典创建配置"""
        config = cls()
        config.version = data.get("version", "3.1.0")
        
        if "performance" in data:
            config.performance = PerformanceConfig(**data["performance"])
        if "logging" in data:
            config.logging = LoggingConfig(**data["logging"])
        if "parsing" in data:
            config.parsing = ParsingConfig(**data["parsing"])
        if "validation" in data:
            config.validation = ValidationConfig(**data["validation"])
            
        return config


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        """初始化配置管理器"""
        self.config_file = Path(config_file) if config_file else None
        self.config = McapSDKConfig()
        
        if self.config_file and self.config_file.exists():
            self.load_config()
    
    def load_config(self, config_file: Optional[Union[str, Path]] = None) -> None:
        """加载配置文件"""
        file_path = Path(config_file) if config_file else self.config_file
        
        if not file_path or not file_path.exists():
            logging.warning(f"配置文件不存在: {file_path}")
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.config = McapSDKConfig.from_dict(data)
            logging.info(f"成功加载配置文件: {file_path}")
        except (json.JSONDecodeError, Exception) as e:
            logging.error(f"加载配置文件失败: {e}")
            raise
    
    def save_config(self, config_file: Optional[Union[str, Path]] = None) -> None:
        """保存配置文件"""
        file_path = Path(config_file) if config_file else self.config_file
        
        if not file_path:
            raise ValueError("未指定配置文件路径")
            
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config.to_dict(), f, indent=2, ensure_ascii=False)
            logging.info(f"成功保存配置文件: {file_path}")
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
            raise
    
    def update_config(self, **kwargs) -> None:
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
            else:
                logging.warning(f"未知的配置项: {key}")
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config = McapSDKConfig()
        logging.info("配置已重置为默认值")
    
    def validate_config(self) -> List[str]:
        """验证配置，返回错误列表"""
        errors = []
        
        # 验证性能配置
        if self.config.performance.batch_size <= 0:
            errors.append("batch_size必须大于0")
        if self.config.performance.max_workers <= 0:
            errors.append("max_workers必须大于0")
        if self.config.performance.memory_limit_mb <= 0:
            errors.append("memory_limit_mb必须大于0")
        if self.config.performance.cache_size < 0:
            errors.append("cache_size不能为负数")
        
        # 验证日志配置
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.config.logging.level not in valid_levels:
            errors.append(f"level必须是: {valid_levels}")
        if self.config.logging.max_file_size_mb <= 0:
            errors.append("max_file_size_mb必须大于0")
        
        return errors
    
    def get_config(self) -> McapSDKConfig:
        """获取当前配置"""
        return self.config


# 全局配置管理器实例
_global_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigManager()
    return _global_config_manager


def get_config() -> McapSDKConfig:
    """获取当前配置"""
    return get_config_manager().get_config()


def update_config(**kwargs) -> None:
    """更新全局配置"""
    get_config_manager().update_config(**kwargs)


def save_config(config_file: Union[str, Path]) -> None:
    """保存全局配置"""
    get_config_manager().save_config(config_file)


def load_config(config_file: Union[str, Path]) -> None:
    """加载全局配置"""
    get_config_manager().load_config(config_file)