[Jira]
# Jira 服务器地址
url = https://your-jira-instance.com
# Jira 用户名
user = your-username
# Jira 密码或 Personal Access Token
password = your-password-or-token
# JQL 查询语句，用于筛选相关问题
jql = labels = YOUR_LABEL and issueFunction in hasComments()
# 请求超时时间（秒）
timeout = 20
# 最大结果数量
max_results = 100

[Feishu]
# 飞书应用 ID
app_id = your-feishu-app-id
# 飞书应用密钥
app_secret = your-feishu-app-secret
# 飞书文件夹 Token
folder_token = your-folder-token

[Analysis]
# 系统提示词（用于 AI 分析）
system_prompt = 测试报告生成配置
# 是否启用 AI 分析
enable_ai_analysis = false
# 报告模板
report_template = default

[Report]
# 报告输出目录
output_dir = ./reports
# 报告文件格式（md/html/pdf）
file_format = md
# 是否在文件名中包含时间戳
include_timestamp = true
# 是否自动清理旧报告
auto_cleanup = false

[Routes]
# 测试路线列表（JSON 格式）
# 每个路线包含：name（名称）、distance（公里数）、scenario（场景描述）
route_list = [
    {
        "name": "城市道路测试",
        "distance": 25.5,
        "scenario": "城市主干道、次干道、支路混合场景，包含红绿灯、路口、转弯等"
    },
    {
        "name": "高速公路测试",
        "distance": 120.0,
        "scenario": "高速公路、服务区、收费站场景，包含分流合流、隧道等"
    },
    {
        "name": "乡村道路测试",
        "distance": 45.8,
        "scenario": "乡村道路、县道、乡道混合场景，包含窄路、坡道等"
    }
] 