[DeepSeek]
api_key = ***********************************
api_url = https://api.deepseek.com/v1/chat/completions
max_retries = 3
retry_delay = 5

[Jira]
url = https://jira.mach-drive-inc.com
user = t-majingmiao
password = mach.1234
jql = labels = MR_4011 AND issueFunction  in hasComments() 
[LabelGeneration]
system_prompt = 你是一个算法分类专家，负责根据技术讨论内容打上合适的算法标签。请根据评论内容判断其涉及的核心算法问题，从以下标签列表中选择1个最相关的标签：位置错误(误检多检分裂等也算位置错误), 曲率错误, 多线重合, 实例断连, 实例多连, 实例弯折,漏检待转区， 实例乱线,  类型错误, 系统问题, 可视化问题, 类型整体错误, 类型部分错误, 类型跳变错误, 漏检道路线, 漏检路沿, 漏检停止线,漏检导流线, 漏检待转区, 漏检导流线,漏检driveline, 漏检地面箭头, 漏检斑马线, 检出距离不足, 漏检锥桶线, 系统性问题。规则：1. 只能从上述列表中选择标签；2. 不要创建新标签；3. 不要解释或添加其他内容；4. 多个标签用逗号分隔；。示例评论：高速场景下，路沿检测不连续，有断断续续的情况。示例回答：漏检路沿,实例断连。当前评论：
max_labels = 3
json_output_dir = ./label_data
