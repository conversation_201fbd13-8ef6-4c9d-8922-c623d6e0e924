import json
import os
import pandas as pd

map = {}


def handle_line(line, file_name):
    if "fps_monitor_manager.cpp" in line:
        arr = line.split(",")
        topic = arr[0].split("Topic:")[-1].strip()
        if f"{topic}\t{file_name.split("_2025")[0]}" not in map:
            print(f"{topic}\t{file_name.split("_2025")[0]}")

        map[f"{topic}\t{file_name.split("_2025")[0]}"] = ""


def read_all_lines_in_current_folder(current_dir):
    for root, dirs, files in os.walk(current_dir):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        handle_line(line, file)
            except UnicodeDecodeError:
                print(f"Error decoding file {file_path}. It may not be a text file.")
            except Exception as e:
                print(f"An error occurred while reading {file_path}: {e}")


def generate_log_parse():
    df = pd.read_excel('fps.xlsx', sheet_name='文件名&topic')
    file_name = ""
    file_topics = []
    file_topics_entity = []

    result_view = []
    result_parse = []
    before_row = None
    for index, row in df.iterrows():
        current_file_name = row["文件名"]
        if file_name != '' and file_name != current_file_name:
            # 如果两个file_name 不相同，那么需要处理file_topics
            chart_config = []
            base = {
                "name": f"{file_name} log parser",
                "chart_config": chart_config
            }
            view_config = []
            view_base = {
                "name": f"{file_name} file_fps viewer",
                "chart_config": view_config
            }
            for index in range(len(file_topics)):
                item = file_topics[index]
                entity = file_topics_entity[index]
                one_config = {
                    "name": entity,
                    "alias": f"Topic: {item}",
                    "filter": {
                        "type": "contains",
                        "pattern": f"Topic: {item}"
                    },
                    "xAxis": {
                        "operate": [{
                            "type": "split",
                            "value": ".",
                            "index": 0
                        }, {
                            "type": "replace",
                            "oldValue": "[",
                            "newValue": ""
                        }]
                    },
                    "yAxis": [{
                        "name": "fps",
                        "comment": "FPS",
                        "group_idx": 1,
                        "operate": [{
                            "type": "split",
                            "value": " ",
                            "index": 8
                        }]
                    }, {
                        "name": "delay",
                        "comment": "Delay",
                        "group_idx": 1,
                        "operate": [{
                            "type": "split",
                            "value": " ",
                            "index": 12
                        }, {
                            "type": "replace",
                            "oldValue": "s,",
                            "newValue": ""
                        }]
                    }, {
                        "name": "maxdelay",
                        "comment": "MaxDelay",
                        "operate": [{
                            "type": "split",
                            "value": " ",
                            "index": 15
                        }, {
                            "type": "replace",
                            "oldValue": "s",
                            "newValue": ""
                        }]
                    }]
                }
                one_view = {
                    "name": entity,
                    "alias": f"Topic: {item}",
                    "yAxis": [{
                        "name": "fps",
                        "comment": "FPS",
                    }, {
                        "name": "delay",
                        "comment": "Delay(s)",
                    }, {
                        "name": "maxdelay",
                        "comment": "MaxDelay(s)",
                    }]
                }
                chart_config.append(one_config)
                view_config.append(one_view)

            sql = f""" insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_parse:common:comm_{file_name}', '{json.dumps(base)}', '文件日志解析', 'sys', now(), 'sys', now()); """
            result_parse.append(sql)
            sql = f""" insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:file_fps:{file_name}', '{json.dumps(view_base)}', '文件日志解析', 'sys', now(), 'sys', now()); """
            result_view.append(sql)
            file_topics = []
            file_topics_entity = []

        file_name = current_file_name
        before_row = row
        file_topics.append(row['topic'])
        file_topics_entity.append(row['类名'])

    for item in result_parse:
        print(item)

    for item in result_view:
        print(item)

if __name__ == "__main__":
    # path = r"D:\temp\aaaaaaa"
    # read_all_lines_in_current_folder(path)
    generate_log_parse()
