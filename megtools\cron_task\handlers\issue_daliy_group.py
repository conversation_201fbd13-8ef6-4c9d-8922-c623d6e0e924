import datetime
import logging
import time
import urllib
from threading import Thread

from basic.third_apis.dingding_api import generate_signature, dinglink_message
from basic.third_apis.feishu_api import FeishuApi
from basic.utils import parse_crontab, <PERSON>ra<PERSON>perate, get_col_name


class DailyJiraIssueTask(Thread):
    def __init__(self):
        super().__init__(name="DailyJiraIssueTask")
        self.next_execute = parse_crontab('0 18 * * *')
        logging.info(f"DailyJiraIssueTask next execute: {self.next_execute}")

        self.label_info = {
            "异常减速": ["功能异常降级", "顿挫", "加减速", "左转顿挫", "右转顿挫", "绿灯刹停", "绿灯不起步", "闯红灯"],
            "不减速": ["减速不及时", "cutin不减速", "红灯不减速", "溜车"],
            "减速猛": ["cutin猛减速"],
            "换道问题": ["车道选择不合理", "实线变道", "变道失败", "变道能力不足", "无效变道", "危险变道", "变道异常打灯", "不按导航行驶"],
            "横向问题": ["画龙", "方向盘猛打", "车身摆动", "不居中", "左转失败", "右转失败", "左转画龙", "右转画龙", "左转弧度大", "上下匝道失败"],
            "绕行": ["绕行受迫", "绕行阻塞", "绕行碰撞"],
            "感知": ["红绿灯误检", "停止墙纵向跳变", "车道线混乱", "车道线识别错误", "车道线不识别", "交通牌识别错误", "安全岛识别不准确", "障碍物miss", "误检障碍物", "VRU识别错误", "未分类不识别", "行人识别错误", "目标车误检", "红灯识别错误", "绿灯识别错误", "黄灯识别错误", "绿灯漏检", "锥桶", "误生成停止墙"],
            "其他": ["功能异常退出", "跟车距离远", "二次起步", "localmap识别错误", "参考线异常", "规划线弯曲", "不礼让行人VRU", "抢黄灯失败", "直行失败", "主辅路切换失败","规划线异常","定位异常"]
        }
        self.second_info = {}
        # 二级放到一级，为后面取数据做准备
        for key, value in self.label_info.items():
            for item in value:
                self.second_info[item] = key
        self.send_message = False

        self.params = {
            "hd":{
                "excel": "Jy04wOe9WiPUEgkyejnceJujnYf",
                "token": "ac9b69ee4639f446efd1e6b3b7d54384fcb6f2915da6a13364ab89ab9681daf8",
                "secret": "SEC0d76464ff403c814c915d011bb1a1b959bbdc401755d9a751183fafc634ceab5",
                "group_title": "新起点",
                "title": "有图",
                "atList": " @赵冲 @陈有品 "
            },
            "nothd": {
                "excel": "F5yEwU8qkiHvAkkTSmVcelRynwd",
                "token": "ac9b69ee4639f446efd1e6b3b7d54384fcb6f2915da6a13364ab89ab9681daf8",
                "secret": "SEC0d76464ff403c814c915d011bb1a1b959bbdc401755d9a751183fafc634ceab5",
                "group_title": "飞书消息测试",
                "title": "无图",
                "atList": " @温娟 @蔡岭 "
            }
        }

        self.match_rule = {
            "换道": ["变道", "换道"],
            "横向避让": ["横向", "拉偏"],
            "转弯": ["拐弯","左转","右转"],
            "跟车": ["异常减速", "闯灯", "跟停", "刹停", "直行", "画龙", "起步慢", "cutin", "Cutin", "cut in", "Cut in", "CutIn", "cutIn"],
        }

    def run(self) -> None:
        # 每60秒运行一次，看当前时间是否需要运行，
        while True:
            time.sleep(5)
            self.execute_once()

    def execute_once(self):
        self.send_message = False
        logging.info(f'"---->>>>>>" {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        self.execute_nohd()

        # 生成有图的
        self.execute_hd()
        logging.info(f'"---->>>>>>" {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

    def execute_hd(self):
        # 生成有图的
        logging.info(f'"---->>>>>>" {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        self.hd_pattern = ""
        self.apply()
        logging.info(f'"---->>>>>>" {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

    def execute_nohd(self):
        logging.info(f'"---->>>>>>" {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        # 生成无图的
        self.hd_pattern = "not"
        self.apply()
        logging.info(f'"---->>>>>>" {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

    def check_execute(self):
        # 校验今天运行的时间
        if self.next_execute < datetime.datetime.now():
            self.next_execute = parse_crontab('0 12-18 * * *')
            logging.info(f"DailyJiraIssueTask next execute: {self.next_execute}")
            return True
        return False


    def apply(self) -> None:
        # 1. 查询Jira 的数据
        current_day = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
        next_day = datetime.datetime.strftime(datetime.datetime.now() + datetime.timedelta(days=1), '%Y-%m-%d')

        jql = f'project = E2E平台项目 AND created >= {current_day} and created < {next_day} AND labels in ({",".join(list(self.second_info.keys()))}) AND summary ~“集成测试” and {self.hd_pattern} summary ~"hd" ORDER BY component ASC'
        fields = ["key", "summary", "created", "labels", "assignee", "components"]
        data = JiraOperate().query_jira(jql=jql, fields=fields)
        # 2. 解析Jira 的数据
        model_data, label_table, others_label = self.organize_data(data, current_day, next_day)

        # 3. 写入到飞书表格中！
        sheet_id = self.write_to_feishu(model_data, label_table, others_label)
        # 4. 发送消息到 钉钉
        self.send_dingtalk_message(model_data, sheet_id)

    def organize_data(self, data, current_day, next_day):
        model_data = []
        others_label = []
        first_label_issue_cnt = {}

        for jira_data in data:
            jira_key = jira_data.get('key')
            components = jira_data.get('fields', {}).get("components", [])
            components = " ".join([item["name"] for item in components])
            labels = jira_data.get('fields', {}).get("labels")
            summary = jira_data.get('fields', {}).get("summary")
            model_data.append([components,  {"text": jira_key, "link": f"https://jira.mach-drive-inc.com/browse/{jira_key}", "type": "url"},summary, " ".join(labels), "Summary必解bug" if "Summary必解bug" in labels else ""])

            # 计算每个一级标签下有多少个问题
            first_label = None
            for label in labels:
                # 如果标签在二级标签中，那么取出一级标签
                if label in self.second_info.keys():
                    first_label = self.second_info[label]
                    break
            if first_label is None:
                # 如果没有取到这个标签，放到其他里面！并且标注
                first_label = "其他"
                others_label.append(labels[-1])
            issue_cnt = first_label_issue_cnt.get(first_label, None)
            if issue_cnt is None:
                first_label_issue_cnt[first_label] = 1
            else:
                first_label_issue_cnt[first_label] = issue_cnt + 1

        label_table = []
        for key, value in self.label_info.items():
            jql = f'project = E2E平台项目 AND labels in ("{"\",\"".join(value)}") AND created >= {current_day} and created < {next_day}  AND summary ~“集成测试” and {self.hd_pattern} summary ~"hd"'
            url = f'https://jira.mach-drive-inc.com/issues/?jql={urllib.parse.quote(jql)}'
            issue_cnt = first_label_issue_cnt.get(key, 0)
            for item in value:
                label_table.append([{"text": key,"type": "text"}, {"text":item,"type": "text"}, {"text":jql,"type": "text"}, {"text": "点击跳转", "link": url, "type": "url"},
                                    issue_cnt])
        # 将两个表格的数据进行返回
        return model_data, label_table, others_label

    def write_to_feishu(self, model_data, label_table, others_label):
        # 先写第一个表格
        feishu_api = FeishuApi()
        wiki_node = feishu_api.wiki_get_node(self.params.get(f"{self.hd_pattern}hd").get("excel"), "wiki")
        sheet_token = wiki_node.get("data", {}).get("node", {}).get("obj_token")
        # 获取sheet 中的sheet有哪些
        feishu_api.set_target_file_token(sheet_token)
        sheet_title = f"{datetime.datetime.strftime(datetime.datetime.now(), '%Y%m%d')}"
        sheet_id = self.check_sheet(sheet_title, feishu_api)

        # 读取旧的数据， 判断是否需要发送数据
        old_data = feishu_api.sheet_single_area_data(f"{sheet_id}!E2:E63")
        # 按照数据进行插入
        label_table.insert(0, ["一级标签","二级标签","筛选条件","链接","问题数量"])
        self.insert_issue_categrey(feishu_api, sheet_id, label_table)
        self.merge_cel(feishu_api, sheet_id)
        new_data = feishu_api.sheet_single_area_data(f"{sheet_id}!E2:E63")
        self.check_send_message(old_data, new_data)

        current_day = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
        next_day = datetime.datetime.strftime(datetime.datetime.now() + datetime.timedelta(days=1), '%Y-%m-%d')
        # 继续插入第二个表格
        jql = f'project = E2E平台项目 AND labels in ({",".join(list(self.second_info.keys()))}) AND created >= {current_day} AND created < {next_day} AND summary ~“集成测试” and {self.hd_pattern} summary ~"hd" ORDER BY component ASC'
        url = f"https://jira.mach-drive-inc.com/issues/?jql={urllib.parse.quote(jql)}"
        model_data.insert(0, [ {"text": "模块分布", "link": url, "type": "url"},"","","", ""])
        self.insert_issue_categrey(feishu_api, sheet_id, model_data, len(label_table) + 4)

        # 处理第三个，按照分类处理的表格
        # 3. 再按照分类进行一次表格处理
        data = self.organize_category(model_data[1:])
        insert_data = []
        for item in data.values():
            insert_data.extend(item)
        self.insert_issue_categrey(feishu_api, sheet_id, insert_data, len(label_table) + len(model_data) + 8)
        self.merge_category_cel(feishu_api, sheet_id, data, len(label_table) + len(model_data) + 8)

        return sheet_id

    def organize_category(self, model_data):
        result = {}
        for item in model_data:
            key = "其他"
            for category, val in self.match_rule.items():
                for keywords in val:
                    if keywords in item[2]:
                        key = category
                        break
                if key != '其他':
                    break
            if key not in result:
                result[key] = []
            item.insert(0, key)
            result[key].append(item)
        # 数据处理完成了， 那么向飞书表格里面进行写入
        return result


    def check_send_message(self, old_data, new_data):
        old_arr = [item[0] for item in old_data.get("data", {}).get("valueRange", {}).get("values", [])]
        new_arr = [item[0] for item in new_data.get("data", {}).get("valueRange", {}).get("values", [])]
        if len(old_arr) != len(new_arr):
            self.send_message = True
            return
        for i in range(len(old_arr)):
            if old_arr[i] != new_arr[i]:
                self.send_message = True
                return

    def merge_cel(self, feishu_api, sheet_id):
        # 根据 一级，二级标签进行单元格合并，
        row_start = 2
        for key, value in self.label_info.items():
            if len(value) <= 1:
                row_start += len(value)
                continue
            body = {
                "range": f"{sheet_id}!A{row_start}:A{row_start + len(value)-1}",
                "mergeType": "MERGE_COLUMNS"
            }
            feishu_api.merge_cells(body)
            body["range"] = f"{sheet_id}!C{row_start}:E{row_start + len(value) - 1}"
            feishu_api.merge_cells(body)
            row_start = row_start + len(value)


    def merge_category_cel(self, feishu_api, sheet_id, merge_info, row_start):
        for key, value in merge_info.items():
            if len(value) <= 1:
                row_start += len(value)
                continue
            body = {
                "range": f"{sheet_id}!A{row_start}:A{row_start + len(value)-1}",
                "mergeType": "MERGE_COLUMNS"
            }
            feishu_api.merge_cells(body)
            row_start = row_start + len(value)



    def insert_issue_categrey(self, feishu_api, sheet_id, table_data, start_row = 1):
        body = {
            "valueRange":{
                "range": f"{sheet_id}!A{start_row}:{get_col_name(len(table_data[0]))}{start_row + len(table_data)-1}",
                "values": table_data
            }
        }
        feishu_api.sheet_write_cells(body)
        body = {
            "appendStyle": {
                "range": f"{sheet_id}!A{start_row}:{get_col_name(len(table_data[0]))}{start_row + len(table_data)-1}",
                "style": {
                    "borderType": "FULL_BORDER",
                    "hAlign": 0 ,
                    "vAlign": 1,
                }
            }
        }
        feishu_api.set_cell_style(body)



    def check_sheet(self, sheet_name, feishu_api):
        sheets_info = feishu_api.get_sheets()
        # 看 sheet 的列表
        for sheet in sheets_info.get('data', {}).get("sheets", []):
            if sheet.get("title") == sheet_name:
                # 如果有一样的，那么先删除
                feishu_api.delete_sheet(sheet.get("sheet_id"))
        # 如果发现没有这个title，那么创建一个sheet
        return feishu_api.add_sheet(sheet_name)



    def send_dingtalk_message(self, model_data, sheet_id):
        if len(model_data) < 2 or not self.send_message:
            return

        secret = self.params.get(f"{self.hd_pattern}hd").get("secret")
        token = self.params.get(f"{self.hd_pattern}hd").get("token")
        title = self.params.get(f"{self.hd_pattern}hd").get("title")
        excel = self.params.get(f"{self.hd_pattern}hd").get("excel")
        atList = self.params.get(f"{self.hd_pattern}hd").get("atList")
        sign = generate_signature(secret)
        url = f"https://oapi.dingtalk.com/robot/send?access_token={token}{sign}"


        message = {
            "markdown": {
                "title": "JiraIssue变更提醒",
                "text": f'【{title}】问题列表已发生变更请,请关注! {atList} \n [点击跳转](https://yuanlijuhe.feishu.cn/wiki/{excel}?sheet={sheet_id})'
            },
            "msgtype": "markdown"
        }
        dinglink_message(url, message)
