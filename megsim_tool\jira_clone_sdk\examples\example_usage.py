#!/usr/bin/env python3
"""Jira Clone SDK 使用示例"""

from jira_clone_sdk import JiraCloneSDK, CloneConfig, FieldConfig, FieldAction


def example_basic_clone():
    """基础克隆示例"""
    print("=== 基础克隆示例 ===")
    
    # 初始化SDK
    sdk = JiraCloneSDK(
        base_url="https://your-domain.atlassian.net",
        bearer_token="your-bearer-token"
    )
    
    # 测试连接
    if not sdk.test_connection():
        print("连接失败")
        return
    
    # 创建基础配置
    config = CloneConfig(
        clone_prefix="CLONE - ",
        create_clone_link=True
    )
    
    try:
        # 克隆问题
        cloned_key = sdk.clone_issue("PROJ-123", config)
        print(f"克隆成功: PROJ-123 -> {cloned_key}")
    except Exception as e:
        print(f"克隆失败: {e}")


def example_advanced_clone():
    """高级克隆示例"""
    print("\n=== 高级克隆示例 ===")
    
    sdk = JiraCloneSDK(
        base_url="https://your-domain.atlassian.net",
        bearer_token="your-bearer-token"
    )
    
    if not sdk.test_connection():
        print("连接失败")
        return
    
    # 创建高级配置
    config = CloneConfig(
        clone_prefix="TEST - ",
        max_retries=5,
        copy_comments=True,
        copy_worklogs=True,
        create_clone_link=True
    )
    
    # 配置字段处理
    config.add_field_config("assignee", FieldConfig(action=FieldAction.KEEP))
    config.add_field_config("priority", FieldConfig(action=FieldAction.SET, value={"id": "1"}))
    config.add_field_config("customfield_10001", FieldConfig(action=FieldAction.SET, value="新值"))
    
    # 排除某些字段
    config.exclude_field("created")
    config.exclude_field("updated")
    
    try:
        cloned_key = sdk.clone_issue("PROJ-456", config)
        print(f"高级克隆成功: PROJ-456 -> {cloned_key}")
    except Exception as e:
        print(f"高级克隆失败: {e}")


def example_batch_clone():
    """批量克隆示例"""
    print("\n=== 批量克隆示例 ===")
    
    sdk = JiraCloneSDK(
        base_url="https://your-domain.atlassian.net",
        bearer_token="your-bearer-token"
    )
    
    if not sdk.test_connection():
        print("连接失败")
        return
    
    config = CloneConfig(clone_prefix="BATCH - ")
    
    # 批量克隆指定问题
    issue_keys = ["PROJ-100", "PROJ-101", "PROJ-102"]
    
    try:
        successful, failed = sdk.clone_issues_batch(issue_keys, config)
        print(f"批量克隆完成: 成功 {len(successful)}, 失败 {len(failed)}")
        
        if successful:
            print(f"成功: {', '.join(successful)}")
        if failed:
            print(f"失败: {', '.join(failed)}")
    except Exception as e:
        print(f"批量克隆失败: {e}")


def example_jql_clone():
    """JQL克隆示例"""
    print("\n=== JQL克隆示例 ===")
    
    sdk = JiraCloneSDK(
        base_url="https://your-domain.atlassian.net",
        bearer_token="your-bearer-token"
    )
    
    if not sdk.test_connection():
        print("连接失败")
        return
    
    config = CloneConfig(clone_prefix="JQL - ")
    
    # 通过JQL搜索并克隆
    jql = "project = PROJ AND status = 'To Do' AND assignee = currentUser()"
    
    try:
        successful, failed = sdk.clone_issues_by_jql(jql, config, max_results=10)
        print(f"JQL克隆完成: 成功 {len(successful)}, 失败 {len(failed)}")
    except Exception as e:
        print(f"JQL克隆失败: {e}")


def example_edit_cloned_data():
    """编辑克隆数据示例"""
    print("\n=== 编辑克隆数据示例 ===")
    
    sdk = JiraCloneSDK(
        base_url="https://your-domain.atlassian.net",
        bearer_token="your-bearer-token"
    )
    
    if not sdk.test_connection():
        print("连接失败")
        return
    
    # 编辑数据
    edit_data = {
        "fields": {
            "summary": "编辑后的标题",
            "description": "编辑后的描述",
            "assignee": {"accountId": "新经办人ID"},
            "priority": {"id": "2"},
            "labels": ["编辑标签1", "编辑标签2"]
        }
    }
    
    try:
        success = sdk.edit_cloned_data("PROJ-789", edit_data)
        if success:
            print("编辑成功: PROJ-789")
        else:
            print("编辑失败: PROJ-789")
    except Exception as e:
        print(f"编辑失败: {e}")


def example_custom_processor():
    """自定义处理器示例"""
    print("\n=== 自定义处理器示例 ===")
    
    from jira_clone_sdk.processors import FieldProcessor
    
    class MyCustomProcessor(FieldProcessor):
        """自定义字段处理器"""
        
        def can_handle(self, field_name: str) -> bool:
            return field_name.startswith("myfield_")
        
        def process(self, field_name: str, field_value, context: dict) -> any:
            # 自定义处理逻辑
            if field_value:
                return f"处理后的值: {field_value}"
            return field_value
    
    sdk = JiraCloneSDK(
        base_url="https://your-domain.atlassian.net",
        bearer_token="your-bearer-token"
    )
    
    # 添加自定义处理器
    sdk.add_field_processor(MyCustomProcessor())
    
    config = CloneConfig(clone_prefix="CUSTOM - ")
    
    try:
        cloned_key = sdk.clone_issue("PROJ-999", config)
        print(f"使用自定义处理器克隆成功: PROJ-999 -> {cloned_key}")
    except Exception as e:
        print(f"自定义处理器克隆失败: {e}")


if __name__ == "__main__":
    # 运行所有示例
    print("Jira Clone SDK 使用示例")
    print("注意: 请先修改 base_url 和 bearer_token")
    
    # 取消注释以运行示例
    # example_basic_clone()
    # example_advanced_clone()
    # example_batch_clone()
    # example_jql_clone()
    # example_edit_cloned_data()
    # example_custom_processor()
    
    print("\n所有示例已准备就绪，请修改配置后运行")