# MegSim API 统计文档

## API端点概览

### 1. 事件记录搜索 API
- **URL:** `GET /api/event/records/multisearch/`
- **状态:** ✅ 正常
- **数据量:** 42,341条记录
- **功能:** 多条件搜索事件记录

### 2. 批量Jira API
- **URL:** `POST /api/event/batch/jira`
- **状态:** ✅ 正常
- **功能:** 批量创建Jira任务

## 测试结果统计

| API端点 | 请求方法 | 测试状态 | 响应时间 | 数据量 |
|---------|----------|----------|----------|--------|
| /api/event/records/multisearch/ | GET | ✅ 成功 | <1s | 42,341条 |
| /api/event/batch/jira | POST | ✅ 成功 | <1s | - |

## 关键发现

### 搜索API
- 支持按车辆ID、时间范围、路线搜索
- 支持分页查询（limit/skip）
- 无需认证，公开访问
- 返回格式：`{records: [], total: number, skip: number, limit: number}`

### 批量Jira API
- 支持批量事件处理
- 需要事件列表、用户列表、标签等参数
- 返回格式：`{code: 0, error_message: null, data: null}`

## 数据样本

### 事件记录字段
- id, start, end, operator, driver
- vehicle_id, vehicle_name, route, route_name
- event_num, mileage, takeover_count
- mpi, system_crash_count

### 测试用例
- 车辆473：1,584条记录
- 最近7天：734条记录
- 特定组合查询：0条记录

## 使用建议

1. **搜索API**：先用无条件查询了解数据概况
2. **批量API**：注意参数验证，事件列表不宜过长
3. **分页处理**：大数据量查询使用limit/skip
4. **时间格式**：统一使用YYYY-MM-DD格式

---
*文档生成时间：2025-08-06*