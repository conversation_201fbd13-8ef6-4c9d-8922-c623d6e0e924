import datetime
import re

from basic.utils import SqlUtil
from report.feishu.data_source.image.line_chart_image import Line<PERSON>hartImage
from report.feishu.generator.impl.text_handler import TextHandler, BACK_COLOR


class UrlPath(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.handle_conditions(variables, params)

    def apply(self):
        TextHandler(self.condition["url_name"], self.block,self.feishu_api).create_link(self.condition.get("params"), self.block.get("index", 0)+1)
        return

    def handle_conditions(self, variables, params):
        params = params.split(",")
        # 如果没有指定VIN，直接生成图片
        self.condition["vin"] = variables.get('vin', '')
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.condition["url_name"] = params[0]
        params = params[1]
        regexp_str = '\\[[a-z0-9A-Z,_.()=/: %\\-]+\\]'
        args_pattern = re.compile(regexp_str)
        result = args_pattern.findall(params)
        for key in result:
            unpack_key = key[1:-1].strip()
            field_value = self.condition.get(unpack_key, "")
            if ":" in unpack_key:
                field_name = unpack_key.split(":")[0]
                format = unpack_key.split(":")[1]
                field_value = self.condition.get(field_name, "")
                field_value = datetime.datetime.strptime(field_value, "%Y-%m-%d %H:%M:%S").strftime(format)
            params = params.replace(key, field_value)
        self.condition["params"] = params
