import json
import time

from django.utils.deprecation import MiddlewareMixin
from django.db import DatabaseError
from basic.middle.message.result import R
from basic.middle.message.enums import StatusCodeEnum
from basic.middle.exceptions import BusinessException
import logging
from django.shortcuts import HttpResponse
import traceback
from django.conf import settings
from django.core.cache import cache

from basic.utils import generate_md5


class MessageMiddleware(MiddlewareMixin):
    """
    对接收信息，返回信息进行统一处理的中间件
    """

    def process_request(self, request):
        """
        校验当前请求是否已经登录、是否有权限访问。
        :param request: 请求对象
        :return:
        """
        if request.path in settings.EXCLUDE_URL and 'Authorization' not in request.headers:
            return
        if request.path.startswith("/api/basic/transOss"):
            return
        r = None
        # 校验Token 是否到期
        if 'Authorization' not in request.headers or not self.valid_token(request):
            # 如果校验不通过，则直接返回错误
            r = R.set_result(StatusCodeEnum.TOKEN_ERR)
        # # 校验权限是否通过
        # elif not valid_permission(request):
        #     r = R.set_result(StatusCodeEnum.AUTH_ERR)
        if r is not None:
            return HttpResponse(json.dumps(r.data()), content_type='application/json')


    def process_response(self, request, response):
        """
        统一返回结果处理
        :param request: 请求对象
        :param response: 返回对象
        :return:
        """
        if request.path.startswith('/api/basic/transOss'):
            response['Access-Control-Allow-Origin'] = "*"
            response['Content-type'] = "application/json"
            response['Content-Encoding'] = "gzip"
            return response
        result = {}
        try:
            result = json.loads(response.content)
        except Exception:
            result = response.content
        # self.save_log(result, request)
        # 如果已经对结果进行封装，则这里不再进行封装
        if isinstance(result, dict) and "code" in result:
            return response

        response.content = json.dumps({"code": 0,
                                       "data": result,
                                       "msg": "成功"})
        return response


    def process_exception(self, request, exception):
        """
        统一异常处理
        :param request: 请求对象
        :param exception: 异常对象
        :return:
        """
        r = None
        logging.error(traceback.format_exc())

        if isinstance(exception, BusinessException):
            # 业务异常处理
            r = R.set_result(exception.enum_cls)
        elif isinstance(exception, DatabaseError):
            # 数据库异常
            r = R.set_result(StatusCodeEnum.DB_ERR)
        else:
            # 服务器异常处理
            r = R.server_error()

        logging.error(f"error info: {r.data()}")
        return HttpResponse(json.dumps(r.data()), content_type='application/json')

    def valid_token(self, request):
        """
        校验当前请求 Token 是否合法
        :param token: 请求的Token信息
        """
        token = request.headers['Authorization'][7:]
        token_key = f'valid_token_origin:{generate_md5(token)}'
        redis_value = cache.get(token_key)
        if redis_value:
            request.user = redis_value
            return True
        return False
