"""飞书服务"""

import asyncio
from typing import List, Dict, Any, Optional
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *

from .base import BaseService
from ..models.feishu import FeishuRecord, UpdateFields
from ..config import Settings


class FeishuService(BaseService):
    """飞书服务"""
    
    def __init__(self, settings: Settings):
        super().__init__(settings)
        self.client: Optional[lark.Client] = None
        self._last_processed_version: Optional[str] = None
    
    async def initialize(self) -> None:
        """初始化飞书客户端"""
        try:
            self.client = lark.Client.builder() \
                .app_id(self.settings.feishu.app_id) \
                .app_secret(self.settings.feishu.app_secret) \
                .enable_set_token(True) \
                .log_level(lark.LogLevel.INFO) \
                .build()
            
            self._log_info("飞书客户端初始化成功")
        except Exception as e:
            self._log_error("飞书客户端初始化失败", e)
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        self.client = None
        self._log_info("飞书服务资源已清理")
    
    async def get_records(self, page_size: int = 100) -> List[FeishuRecord]:
        """获取表格记录"""
        if not self.client:
            raise RuntimeError("飞书客户端未初始化")
        
        try:
            req = ListAppTableRecordRequest.builder() \
                .app_token(self.settings.feishu.app_token) \
                .table_id(self.settings.feishu.table_id) \
                .page_size(page_size) \
                .build()
            
            # 在异步环境中运行同步代码
            resp = await asyncio.get_event_loop().run_in_executor(
                None, self.client.bitable.v1.app_table_record.list, req
            )
            
            if not resp.success():
                self._log_error(f"获取表格记录失败: {resp.code}, {resp.msg}")
                return []
            
            if not resp.data or not resp.data.items:
                self._log_info("没有获取到表格记录")
                return []
            
            records = []
            for record in resp.data.items:
                record_version = f"{record.record_id}-{record.record_id}"
                
                # 只处理新记录或更新记录
                if not self._last_processed_version or record_version != self._last_processed_version:
                    records.append(FeishuRecord(
                        id=record.record_id,
                        fields=record.fields or {},
                        version=record_version
                    ))
            
            # 更新最后处理的记录版本
            if records:
                self._last_processed_version = records[-1].version
            
            self._log_info(f"获取到 {len(records)} 条记录")
            return records
            
        except Exception as e:
            self._log_error("获取表格记录异常", e)
            return []
    
    async def update_record(self, record_id: str, fields: Dict[str, str]) -> bool:
        """更新表格记录"""
        if not self.client:
            raise RuntimeError("飞书客户端未初始化")
        
        try:
            req = UpdateAppTableRecordRequest.builder() \
                .app_token(self.settings.feishu.app_token) \
                .table_id(self.settings.feishu.table_id) \
                .record_id(record_id) \
                .request_body(AppTableRecord.builder()
                    .fields(fields)
                    .build()) \
                .build()
            
            # 在异步环境中运行同步代码
            resp = await asyncio.get_event_loop().run_in_executor(
                None, self.client.bitable.v1.app_table_record.update, req
            )
            
            if not resp.success():
                self._log_error(f"更新记录失败: {record_id}, 错误码: {resp.code}, 错误信息: {resp.msg}")
                return False
            
            self._log_info(f"更新记录成功: {record_id}")
            return True
            
        except Exception as e:
            self._log_error(f"更新记录异常: {record_id}", e)
            return False
    
    async def batch_update_records(self, updates: List[tuple[str, Dict[str, str]]]) -> int:
        """批量更新记录"""
        success_count = 0
        
        # 使用信号量限制并发数
        semaphore = asyncio.Semaphore(5)
        
        async def update_single(record_id: str, fields: Dict[str, str]) -> bool:
            async with semaphore:
                return await self.update_record(record_id, fields)
        
        # 并发执行更新
        tasks = [update_single(record_id, fields) for record_id, fields in updates]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, bool) and result:
                success_count += 1
            elif isinstance(result, Exception):
                self._log_error("批量更新中的异常", result)
        
        self._log_info(f"批量更新完成: 成功 {success_count}/{len(updates)} 条记录")
        return success_count
