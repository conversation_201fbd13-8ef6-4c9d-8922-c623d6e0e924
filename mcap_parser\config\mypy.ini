[mypy]
# MyPy类型检查配置

# 基本设置
python_version = 3.8
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True

# 严格模式（逐步启用）
# strict = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

# 导入处理
ignore_missing_imports = True
follow_imports = silent

# 输出格式
show_error_codes = True
show_column_numbers = True
pretty = True
color_output = True
error_summary = True

# 缓存
cache_dir = .mypy_cache
sqlite_cache = True

# 特定模块配置
[mypy-tests.*]
ignore_errors = True

[mypy-setup]
ignore_errors = True

[mypy-mcap.*]
ignore_missing_imports = True

[mypy-psutil.*]
ignore_missing_imports = True

[mypy-numpy.*]
ignore_missing_imports = True

[mypy-pandas.*]
ignore_missing_imports = True

[mypy-matplotlib.*]
ignore_missing_imports = True

[mypy-plotly.*]
ignore_missing_imports = True

[mypy-dash.*]
ignore_missing_imports = True