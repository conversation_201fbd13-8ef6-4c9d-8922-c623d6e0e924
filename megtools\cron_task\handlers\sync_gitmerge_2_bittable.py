import copy
import json
import logging
import re
from datetime import datetime
from urllib.parse import urlparse, parse_qs

from basic.third_apis.feishu_api import FeishuApi
from basic.third_apis.gitlab_api import GitlabApi
from basic.utils import SqlUtil, JsonUtils


def apply(task_config):
    SyncGitLabMergeRequest(task_config).apply()

class SyncGitLabMergeRequest:
    def __init__(self, task_config):
        self.task_config = task_config
        self.gitlab_api = GitlabApi()
        self.cache = []
        self.project_id = self.task_config.get('project_id')
        self.feishu_url = task_config.get("feishu_url", "")
        self.feishuApi = FeishuApi()
        self.bitable_token = ""
        self.table_id = ""
        self.trans_config = task_config.get("trans_config", {})
        self.bitable_fields = [item['bitable_key'] for item in self.trans_config]

    def apply(self):
        # 查询飞书数据
        feishu_data = self.query_feishu(self.feishu_url)
        # 查询gitlab中的数据
        gitlab_data = self.query_gitlab()
        # compare
        result = self.compare_and_save(feishu_data, gitlab_data)
        logging.info(result)

    def compare_and_save(self, feishu_data, gitlab_data):
        result = []
        # 对比飞书的数据，和jira 的数据，应该删除的加删除标记， 如果需要添加的插入，如果修改的进行修改
        # 1. feishu_data 里面有，jira_data 里面没有
        feishu_need_del = set(feishu_data.keys()) - set(gitlab_data.keys())
        self.handle_feishu_del(feishu_need_del, feishu_data, result)
        # 2. feishu_data 里面没有， jira_data 里面有，直接插入数据
        feishu_need_insert = set(gitlab_data.keys()) - set(feishu_data.keys())
        self.handle_feishu_append(feishu_need_insert, gitlab_data, result)
        # 3. 两个里面如果都一样，那么需要对比进行更新
        feishu_need_compare = set(gitlab_data.keys()) & set(feishu_data.keys())
        self.handle_compare_update(feishu_need_compare, feishu_data, gitlab_data, result)
        return result

    def handle_compare_update(self, feishu_need_compare, feishu_data, git_data, result):
        if len(feishu_need_compare) == 0:
            return
        update_keys = []
        update_entity = []
        for item in feishu_need_compare:
            feishu_entity = feishu_data.get(item)
            git_entity = git_data.get(item)
            # 获取到每个元素进行对比
            change_fields = []
            for field_name in self.bitable_fields:
                feishu_text = self.get_feishu_text(field_name, feishu_entity.get("fields"))
                jira_text = self.get_feishu_text(field_name, git_entity)
                if feishu_text != jira_text:
                    change_fields.append(f"{feishu_text} -> {jira_text}")
            if len(change_fields) > 0:
                update_keys.append(f"-->>{item}: {','.join(change_fields)}")
                update_entity.append({"record_id": feishu_entity.get("record_id"), "fields": git_entity})
        if len(update_keys) == 0:
            return
        result.append("修改的DPMS数据：")
        result.extend(update_keys)
        self.feishuApi.bitable_batch_update_all(self.bitable_token, self.table_id, update_entity)

    def get_feishu_text(self, field_name, feishu_entity):
        value = feishu_entity.get(field_name, "")
        if isinstance(value, dict):
            return value.get("text", "")
        elif isinstance(value, list):
            temp = ""
            for item in value:
                if isinstance(item, dict):
                    temp = f"{temp}{item.get('text', '')}"
                else:
                    temp = f"{temp}{item}"
            return temp
        elif value is None:
            value = ""
        else:
            value = f"{value}"
        return value

    def handle_feishu_append(self, need_insert, gitlab_data, result):
        if need_insert is None or len(need_insert) == 0:
            return
        # 向飞书内插入数据
        rows = []
        for item in list(need_insert):
            rows.append({"fields":gitlab_data.get(item)})
        self.feishuApi.bitable_batch_insert_all(self.bitable_token, self.table_id, rows, index=4)
        result.append(f"新增GIT数据：{','.join(need_insert)}")


    def handle_feishu_del(self, feishu_need_del, feishu_data, result):
        if feishu_need_del is None or len(feishu_need_del) == 0:
            return
        del_record_id = []
        for item in list(feishu_need_del):
            del_record_id.append(feishu_data.get(item).get('record_id'))
        self.feishuApi.bitable_batch_delete_all(self.bitable_token, self.table_id, del_record_id)
        result.append(f"删除GIT数据：{','.join(feishu_need_del)}")


    def query_gitlab(self):
        page = 1
        result = {}
        while True:
            merge_list = self.gitlab_api.merge_requests(self.project_id, {"page": page, "per_page": 100 })
            if len(merge_list) == 0:
                break
            page = page + 1
            for item in merge_list:
                # 这里需要提取， key 是 commit_id ， value 是merge 信息,
                single_merge_info = self.reform_git_data(item)
                for single_info in single_merge_info:
                    commit_id = single_info.get("commitid", None)
                    if commit_id is None:
                        commit_id = ""
                    key = f"{commit_id}_{single_info.get('MergeID').get("text")}"
                    result[key] = single_info
        return result

    def reform_git_data(self, origin_data):
        result = []
        for item in self.trans_config:
            bitable_field = item.get("bitable_key", "")
            git_value_path = item.get("git_value_path", "")
            value = self.get_value_by_path(git_value_path, origin_data, item)
            # 如果Value 返回两个值， 说明JSON里面有两个。那么需要处理
            if len(result) == 0:
                result.append({})
            if isinstance(value, list):
                # 如果是数组，那么需要分别给数组赋值，
                for idx in range(0, len(value) - len(result)):
                    result.append(copy.deepcopy(result[0]))
                for idx in range(0, len(value)):
                    result[idx][bitable_field] = self.reform_bitable_type(origin_data, value[idx], item)
            else:
                value = self.reform_bitable_type(origin_data, value, item)
                for item in result:
                    item[bitable_field] = value
        return result

    def get_value_by_path(self, git_value_path, origin_data, field_config):
        path_arr = git_value_path.split(".")
        result = origin_data
        for item in path_arr:
            if result is None or result == "":
                result = ""
                break
            else:
                result = result.get(item, "")
        if "sub_str" in field_config:
            start = field_config.get("sub_str", {}).get("start","")
            end = field_config.get("sub_str", {}).get("end","")
            if start not in result or end not in result:
                return ""
            result = result[result.index(start) + len(start):result.index(end)].strip()
        if "json_extra" in field_config:
            # 有的需要提取json， 然后再reform
            result = self.extra_json(result, field_config)
        return result

    def extract_json(self, text):
        stack = []
        in_quote = False
        escaped = False
        start_idx = -1

        for i, char in enumerate(text):
            # 处理引号和转义字符
            if char == '"' and not escaped:
                in_quote = not in_quote
            escaped = char == '\\' and not escaped

            # 非引号内的括号才计入栈
            if not in_quote:
                if char == '{':
                    if not stack:
                        start_idx = i
                    stack.append(i)
                elif char == '}':
                    if stack:
                        stack.pop()
                        if not stack:
                            return text[start_idx:i + 1]
        return None

    def extra_json(self, value, field_config):
        # 要取出 result 里面的json ，然后再取出数据
        json_value = self.extract_json(value)
        if not JsonUtils.is_valid_json(json_value):
            return ""
        json_path = field_config.get("json_extra", "")
        json_entity = json.loads(json_value)
        commitid = list(json_entity.keys())
        if field_config.get("bitable_key", "")  == "commitid":
            return commitid
        if field_config.get("bitable_key", "")  == "模块":
            result = []
            for item in commitid:
                result.extend(list(json_entity.get(item).keys()))
            return result
        result = []
        try:
            for item in commitid:
                model_name = list(json_entity.get(item).keys())[0]
                entity = json_entity.get(item).get(model_name)
                value = self.get_value_by_path(json_path, entity, {})
                if isinstance(value, list):
                    result.extend(value)
                else:
                    result.append(value)
        except Exception as e:
            return "error! please check!"
        return result

    def reform_bitable_type(self, origin_data, value, field_config):
        bitable_type = field_config.get("bitable_type", "文本")
        if bitable_type == "超链接":
            if value:
                link_prefix = field_config.get("link_prefix", "")
                return {"text": str(value), "link": f"{link_prefix}{value}"}
            else:
                return {"text": " ", "link": ""}
        elif bitable_type == "日期":
            format_type = field_config.get("format_type", "%Y-%m-%d")
            if value is None:
                return None
            date_obj = datetime.strptime(value, format_type)
            value = int(date_obj.timestamp() * 1000)
            return value
        elif bitable_type == "多选":
            if isinstance(value, list):
                return value
            else:
                return [value]
        else:
            return value

    def query_feishu(self, url):
        parsed_url = urlparse(url)
        get_params = parse_qs(parsed_url.query)
        path = parsed_url.path
        self.wiki_token = path.split("/")[-1]
        self.table_id = get_params["table"][0]
        view_id = get_params["view"][0]

        bi_table_info = self.feishuApi.wiki_get_node(self.wiki_token, 'wiki')
        if bi_table_info.get("code", -1) != 0:
            # 没有找到飞书表格直接返回
            return bi_table_info.get("msg", "")
        # 开始查询bit_table 中的数据
        self.bitable_token = bi_table_info.get("data", {}).get("node", {}).get("obj_token", "")
        body = {"view_id": view_id, "field_names": self.bitable_fields}
        bitable_data = self.feishuApi.bitable_records_all(self.bitable_token, self.table_id, body)
        result = {}
        for item in bitable_data:
            commit_id_info = item.get("fields", {}).get("commitid", {})
            commit_id_str = ""
            if isinstance(commit_id_info, list):
                for commit_info in commit_id_info:
                    commit_id_str = f"{commit_id_str}{commit_info.get('text')}"
            else:
                commit_id_str = commit_id_info.get("text", "")
            key = f'{commit_id_str}_{item.get("fields", {}).get("MergeID", {}).get("text", "")}'
            result[key] = item
        return result

