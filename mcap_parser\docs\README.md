# 🚗 MCAP自动驾驶数据解析器

高性能的MCAP文件解析工具，专为自动驾驶数据处理设计。

## ✨ 特性

- ⚡ **高速解析**: 基于索引优化的批量处理
- 🎯 **43种消息类型**: 完整的自动驾驶数据支持
- 🔧 **模块化架构**: 清晰的代码组织，易于维护
- 🚀 **随机访问**: 直接跳转到指定时间点
- 📊 **详细统计**: 完整的性能监控

## 🚀 快速开始

### 检查依赖

```bash
# 自动检查所有依赖项
python check_dependencies.py
```

### 安装依赖

```bash
pip install -r requirements.txt
# 或手动安装核心依赖
pip install mcap-ros2-support psutil
```

### 基本使用

```python
# 推荐使用统一入口
import src
from src import create_sdk, fast_stream

# 创建SDK实例
sdk = create_sdk(verbose=True)

# 分析MCAP文件
analysis = sdk.analyze_mcap_file("data.mcap")
print(f"总消息数: {analysis.total_messages}")

# 高速流式处理
for msg in fast_stream("data.mcap", ["LaneArrayv2"]):
    print(f"车道线数据: {msg.timestamp}")
```

### 命令行使用

```bash
# 分析MCAP文件
python mcap_parser_cli.py analyze data.mcap

# 解析特定消息类型
python mcap_parser_cli.py parse data.mcap --types LaneArrayv2 --max 100
```

## 📁 项目结构

```
mcap_parser/                    # 主包目录
├── core/                       # 核心模块
├── parsers/                    # 解析器模块
├── utils/                      # 工具模块
└── cli/                        # 命令行接口

src/                            # 兼容性模块
├── mcap_core_sdk.py           # 核心SDK
├── mcap_autodrive_sdk.py      # 兼容性包装器
├── mcap_fast_parser.py        # 高速解析器
└── ...

examples/                       # 使用示例
├── basic_usage.py             # 基本使用示例
└── lane_data_parser.py        # 车道线数据解析示例
```

## 🎯 支持的消息类型

支持43种自动驾驶消息类型，包括：

- **感知数据**: PerceptionResult, RadarObjectArray, PointCloud2
- **车道线**: LaneArrayv2, EnvLaneArray, ReferenceLines
- **传感器**: RawImu, CorrectedImu, GnssBestPose
- **规划控制**: PlanningResult, ControlResult
- **定位**: LocalizationEstimate, Ins
- 更多...

## 📖 文档

- [快速开始指南](docs/QUICK_START.md)
- [高速解析器特性](FAST_PARSER_FEATURES.md)

## 🔧 开发

### 运行示例

```bash
# 基本使用示例
python examples/basic_usage.py

# 车道线数据解析示例
python examples/lane_data_parser.py
```

### 性能优化

- 使用高速解析器进行大文件处理
- 利用索引优化进行复杂过滤
- 批量处理减少I/O开销

## 📊 性能

- **解析速度**: 10,000+ 消息/秒
- **内存优化**: 智能缓存管理
- **随机访问**: 毫秒级时间点定位

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**高效、整洁、可维护** - 为自动驾驶数据处理提供坚实基础 🚗✨
