import datetime
import gzip
import json
import logging
import os
import threading
import time
import traceback
from threading import Thread

import jwt
import requests
from django.core.cache import cache
from django.conf import settings
from django.db.models import Q

from basic.models import BasicKeyValue, UserInfo, BasicAlarmGroup, BasicFeishuMessageInfo
from basic.third_apis.feishu_api import FeishuApi
from basic.utils import generate_md5, OssUtils


def get_global_kv(key):
    global_key = "basic:get_global_kv:" + key
    redis_value = cache.get(global_key)
    if redis_value is not None:
        logging.debug(f"get globalKV : {key} {redis_value}")
        return redis_value
    value = BasicKeyValue.objects.filter(is_delete=0, name=key).first()
    if value is not None:
        cache.set(global_key, value.value, timeout=60 * 30)
        return value.value
    return None


def set_global_kv(key, value):
    global_key = "tool_space:scatter:get_global_kv:" + key
    cache.set(global_key, value, timeout=60 * 30)
    return None

def cas_check(req):
    """
    登录传入ticket ， 校验ticket 是否正确
    """
    base_url = settings.LOGIN_CONFIG["base_url"]
    service = req.get("service", "")
    body = {
        "service": service,
        "ticket": req.get("ticket", ""),
        "format": "JSON",
    }
    resp = requests.get(f"{base_url}/p3/serviceValidate", params=body)
    logging.info(f"req: {body}, resp: {resp.text}")
    resp_json = resp.json()
    login_status = False
    login_name = ""
    attributes = {}
    accessToken = ""
    if "authenticationSuccess" in resp_json.get("serviceResponse", {}):
        login_status = True
        attributes = resp_json.get("serviceResponse", {}).get("authenticationSuccess", {}).get("attributes", {})
        login_name = resp_json.get("serviceResponse", {}).get("authenticationSuccess", {}).get("user", "")
    result = {
        "auth_status": login_status,
        "display_name": attributes.get("displayName", ""),
        "login_name": login_name,
        "uid": attributes.get("uid", ""),
        "email": attributes.get("email", ""),
    }

    if login_status:
        accessToken = jwt.encode(result, settings.LOGIN_CONFIG.get("JWT_SECRET"), algorithm='HS256')
        token_key = f'valid_token_origin:{generate_md5(accessToken)}'
        cache.set(token_key, result, timeout=60*60*14)
        userCount = UserInfo.objects.filter(username=login_name).count()
        if userCount > 0:
            UserInfo.objects.filter(username=login_name).update(update_time=datetime.datetime.now())
        else:
            UserInfo(username=login_name, display_name=attributes.get("displayName", ""),
                    email=attributes.get("email", ""), create_by='sys',
                    update_by='sys', update_time=datetime.datetime.now(), is_delete=0).save()

    return {"auth_status": login_status,"realName": attributes.get("diaplayName", ""), "accessToken": accessToken}

class SendMessageApi:
    def __init__(self, message):
        if isinstance(message, str):
            self.message = json.dumps({"template_id": "AAqBBFB02bCbg", "template_variable": {"content": message}})
        elif isinstance(message, dict):
            self.message = json.dumps(message)
        else:
            self.message = ""
        self.feishu_api = FeishuApi()

    def send_message(self, to):
        # 如果传入的不知道是 分组名称，还是用户名称，还是open_id, 那么需要分别查询后再考虑发送
        if to.startswith("oc_") or to.startswith("ou_"):
            self.send_message_by_open_id(to)
        elif self.send_message_by_user(to):
            logging.info("send message by user")
        elif self.send_message_by_group(to):
            logging.info("send message by group")
        else:
            logging.error(f"cannot send message!! {to}")
            return "cannot find target id!"


    def send_message_by_group(self, group_name):
        group_info = BasicAlarmGroup.objects.filter(group_name=group_name).first()
        if group_info is None:
            return False
        user_list = UserInfo.objects.filter(username__in=group_info.user_list.split(",")).all()
        for item in user_list:
            self.send_message_by_open_id(item.open_id, f"{group_name}~{item.username}")
        return True

    def send_message_by_user(self, user_name):
        user_info = UserInfo.objects.filter(Q(username=user_name) | Q(email=user_name) | Q(phone=user_name)).first()
        if user_info is None:
            return False
        else:
            self.send_message_by_open_id(user_info.open_id, f"{user_name}")
        return True

    def send_message_by_open_id(self, open_id, display_name=''):
        entity = {
            "open_id": open_id,
            "display_name": display_name,
            "message": self.message,
            "create_by": 'sys',
            "update_by": 'sys',
        }
        if open_id and (open_id.startswith("oc_") or open_id.startswith("ou_")) and self.message:
            try:
                remark = self.feishu_api.send_message(open_id, self.message)
                remark = json.dumps(remark)
                entity['remark'] = remark
            except Exception as e:
                remark = f"send message error: {traceback.format_exc()}"
                entity['remark'] = remark
                logging.error(remark)
        else:
            entity['remark'] = "can't find any open_id or chat_id or message is empty!"
        BasicFeishuMessageInfo(**entity).save()

class TransAdsFile:
    def __init__(self, bucket, path, cam_path):
        self.bucket = bucket
        self.path = path
        self.cam_path = cam_path
        self.client = OssUtils(bucket)

    def apply(self):
        file_name = generate_md5(self.path)
        wait_cnt = 0
        while True:
            wait_cnt = wait_cnt + 1
            if os.path.exists(os.path.join("oss_ads_file", file_name)):
                with open(os.path.join("oss_ads_file", file_name), "rb") as f:
                    return f.read()
            time.sleep(1)
            if wait_cnt > 100:
                break
        return None

class FileDownloadThread(threading.Thread):
    def __init__(self, bucket, path, cam_path):
        threading.Thread.__init__(self)
        self.bucket = bucket
        self.path = path
        self.cam_path = cam_path
        self.client = OssUtils(bucket)

    def run(self):
        first_frame = self.client.get_object(f"{self.path}0-frame.json")
        first_entity = json.loads(first_frame.decode('utf-8'))
        self.write_file(generate_md5(f"{self.path}0-frame.json"), first_frame.decode('utf-8'))
        for item in first_entity.get("timing"):
            frame_name = item[3]
            object_info = self.client.get_object(f"{self.path}{frame_name}.json")
            entity = json.loads(object_info.decode('utf-8'))
            entity_bak = json.loads(object_info.decode('utf-8'))
            for idx in range(0, len(entity.get("data", {}).get('updates', []))):
                item = entity.get("data", {}).get('updates', [])[idx]
                primitives = item.get("primitives", {})
                if not primitives:
                    continue
                for key in primitives:
                    if "/cam_" in key:
                        if key == self.cam_path:
                            continue
                        # 否则， 删除key
                        del entity_bak.get("data", {}).get('updates', [])[idx].get("primitives", {})[key]
            self.write_file(generate_md5(f"{self.path}{frame_name}"), json.dumps(entity_bak))
        self.del_old_files()

    def del_old_files(self):
        now = time.time()
        for filename in os.listdir("oss_ads_file"):
            file_path = os.path.join("oss_ads_file", filename)
            mtime = os.path.getmtime(file_path)
            age_hour = (now - mtime) / 3600
            if age_hour > 24:
                os.remove(file_path)

    def write_file(self, file_name, content):
        if not os.path.exists("oss_ads_file"):
            os.mkdir("oss_ads_file")
        with open(os.path.join("oss_ads_file", file_name), 'wb') as f:
            gzipped_content = gzip.compress(content.encode('utf-8'))
            f.write(gzipped_content)