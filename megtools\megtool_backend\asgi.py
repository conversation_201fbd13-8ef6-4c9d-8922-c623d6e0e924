"""
ASGI config for megtool_backend project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/howto/deployment/asgi/
"""

import os

from django.core.asgi import get_asgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'megtool_backend.settings.local')
# os.environ.setdefault('DJANGO_ALLOW_ASYNC_UNSAFE', 'True')

application = get_asgi_application()

# 这里是注册socket才使用的，socket是异步，需要使用async
# app = get_asgi_application()
# sio = socketio.AsyncServer(async_mode='asgi', logger=True, engineio_logger=True, cors_allowed_origins='*')
# application = socketio.ASGIApp(sio, other_asgi_app=app)
#
# from common.socket import init_socket
# init_socket(sio)
