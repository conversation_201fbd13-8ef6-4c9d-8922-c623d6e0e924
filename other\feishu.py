import time
import logging
import json
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import ListAppTableRecordRequest

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('FeishuPoller')

# 飞书应用配置
APP_ID = "cli_a8d028a01ed8900b"  # 替换为您的应用ID
APP_SECRET = "fA8FpGvqdoZvxBe9tTqiDbp6KkRGWmEF"  # 替换为您的应用密钥
APP_TOKEN = "G4wJbjmW4aIiu2sm0AOchaLcnfb"  # 替换为您的表格APP_TOKEN
TABLE_ID = "tblqZHDCYOF39QVG"  # 替换为您的表格ID

# 创建客户端 - 让SDK自动管理令牌
client = lark.Client.builder() \
    .app_id(APP_ID) \
    .app_secret(APP_SECRET) \
    .enable_set_token(True) \
    .log_level(lark.LogLevel.INFO) \
    .build()

def print_table_data(records):
    """格式化打印表格数据"""
    if not records:
        print("表格中没有记录")
        return
    
    # 获取表头（字段名）
    if records:
        first_record = records[0]
        headers = list(first_record.fields.keys())
        
        # 打印表头
        header_line = " | ".join(headers)
        print("\n" + "-" * 80)
        print(f"表格记录 ({len(records)} 条):")
        print("-" * 80)
        print(header_line)
        print("-" * len(header_line))
    
    # 打印每条记录
    for i, record in enumerate(records, 1):
        values = []
        for header in headers:
            value = record.fields.get(header, "")
            
            # 处理不同类型的数据
            if isinstance(value, dict):
                # 处理多行文本、人员选择等复杂类型
                if "text" in value:
                    value = value["text"]
                elif "name" in value:
                    value = value["name"]
                elif "start" in value and "end" in value:
                    value = f"{value['start']} 至 {value['end']}"
                elif "link" in value:
                    value = value["link"]
                else:
                    # 转换为JSON字符串
                    value = json.dumps(value, ensure_ascii=False)
            
            # 限制字符串长度
            if isinstance(value, str) and len(value) > 50:
                value = value[:47] + "..."
                
            values.append(str(value))
        
        # 打印数据行
        print(" | ".join(values))
    
    print("-" * 80 + "\n")

def poll_feishu_table():
    """轮询飞书表格数据并打印"""
    try:
        # 构建请求
        req = ListAppTableRecordRequest.builder() \
            .app_token(APP_TOKEN) \
            .table_id(TABLE_ID) \
            .build()
        
        # 发起请求 - SDK会自动处理令牌
        resp = client.bitable.v1.app_table_record.list(req)
        
        # 处理响应
        if not resp.success():
            logger.error(f"查询表格失败: code={resp.code}, msg={resp.msg}, request_id={resp.get_request_id()}")
            return
        
        # 获取记录数据
        records = resp.data.items
        if records:
            logger.info(f"成功获取 {len(records)} 条记录")
            print_table_data(records)
        else:
            logger.info("表格中没有记录")
            print("表格中没有记录")
            
    except Exception as e:
        logger.error(f"查询表格异常: {str(e)}")
        print(f"错误: {str(e)}")

def main():
    logger.info("启动飞书表格轮询服务...")
    print("飞书表格数据监控服务已启动 (每5分钟更新一次)")
    print("按 Ctrl+C 停止程序")
    
    # 立即执行一次
    poll_feishu_table()
    
    # 设置每5分钟轮询一次
    while True:
        try:
            time.sleep(300)  # 5分钟 = 300秒
            poll_feishu_table()
        except KeyboardInterrupt:
            logger.info("服务已停止")
            print("\n服务已停止")
            break
        except Exception as e:
            logger.error(f"轮询过程中发生异常: {str(e)}")
            print(f"错误: {str(e)}")
            # 遇到异常后等待1分钟再重试
            time.sleep(60)

if __name__ == "__main__":
    main()