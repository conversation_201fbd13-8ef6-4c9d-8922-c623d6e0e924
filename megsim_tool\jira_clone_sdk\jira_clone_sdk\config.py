"""配置管理模块"""

from typing import Dict, List, Optional, Any, Callable
from pydantic import BaseModel, Field, validator
from enum import Enum


class FieldAction(str, Enum):
    """字段处理动作"""
    KEEP = "keep"  # 保持原值
    SKIP = "skip"  # 跳过字段
    TRANSFORM = "transform"  # 转换字段
    REPLACE = "replace"  # 替换字段


class FieldConfig(BaseModel):
    """字段配置"""
    action: FieldAction = FieldAction.KEEP
    value: Optional[Any] = None
    transformer: Optional[Callable] = None
    
    class Config:
        arbitrary_types_allowed = True


class CloneConfig(BaseModel):
    """克隆配置"""
    
    # 基础配置
    clone_prefix: str = Field(default="CLONE - ", description="克隆前缀")
    max_retries: int = Field(default=3, ge=1, le=10, description="最大重试次数")
    
    # 功能开关
    copy_comments: bool = Field(default=False, description="是否复制评论")
    copy_worklogs: bool = Field(default=False, description="是否复制工作日志")
    create_clone_link: bool = Field(default=True, description="是否创建克隆链接")
    
    # 字段配置
    field_configs: Dict[str, FieldConfig] = Field(default_factory=dict, description="字段配置")
    safe_fields: List[str] = Field(
        default_factory=lambda: [
            'project', 'summary', 'issuetype', 'description', 'assignee',
            'priority', 'labels', 'environment', 'duedate', 'components'
        ],
        description="安全字段列表"
    )
    
    # 高级配置
    custom_field_mapping: Dict[str, str] = Field(default_factory=dict, description="自定义字段映射")
    exclude_fields: List[str] = Field(default_factory=list, description="排除字段列表")
    
    @validator('clone_prefix')
    def validate_clone_prefix(cls, v):
        if not isinstance(v, str):
            raise ValueError('clone_prefix必须是字符串')
        return v
    
    def add_field_config(self, field_name: str, config: FieldConfig) -> None:
        """添加字段配置"""
        self.field_configs[field_name] = config
    
    def get_field_config(self, field_name: str) -> FieldConfig:
        """获取字段配置"""
        return self.field_configs.get(field_name, FieldConfig())
    
    def exclude_field(self, field_name: str) -> None:
        """排除字段"""
        if field_name not in self.exclude_fields:
            self.exclude_fields.append(field_name)
    
    def include_field(self, field_name: str) -> None:
        """包含字段"""
        if field_name in self.exclude_fields:
            self.exclude_fields.remove(field_name)


class SDKConfig(BaseModel):
    """SDK配置"""
    
    # 连接配置
    base_url: str = Field(..., description="Jira基础URL")
    bearer_token: str = Field(..., description="Bearer Token")
    timeout: int = Field(default=30, ge=1, le=300, description="请求超时时间(秒)")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    
    # 性能配置
    batch_size: int = Field(default=10, ge=1, le=100, description="批处理大小")
    concurrent_requests: int = Field(default=5, ge=1, le=20, description="并发请求数")
    
    @validator('base_url')
    def validate_base_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('base_url必须以http://或https://开头')
        return v.rstrip('/')