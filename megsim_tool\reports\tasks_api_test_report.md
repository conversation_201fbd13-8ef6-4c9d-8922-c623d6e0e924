# MegSim 任务API测试报告

## 测试概览

本次测试验证了MegSim任务管理API的两个核心功能：
- **创建任务** (POST `/api/tasks/`)
- **查询任务列表** (GET `/api/tasks/`)

## 1. 创建任务API测试

### 请求信息
- **URL**: `https://megsim.mc.machdrive.cn/api/tasks/`
- **方法**: POST
- **Content-Type**: application/json

### 请求参数
```json
{
  "priority": 4,
  "run_metric": true,
  "run_test": true,
  "bp_delay_enable": false,
  "end_early_enable": false,
  "name": "test",
  "project": 4,
  "car_type": "Z10",
  "reinjection_scene_type": null,
  "reinjection_task_type": 150291,
  "tag_ids": "[7736]",
  "sync_jira_status": true,
  "type": 20,
  "case_status": "",
  "case_time": "",
  "scene_set_enable": false,
  "ego_cruise_speed": null,
  "evaluation_checkers": null
}
```

### 测试结果
- **状态码**: 201 Created
- **结果**: ✅ 成功创建任务
- **返回数据**: 包含完整的任务信息，包括自动生成的任务ID、创建时间等

## 2. 查询任务列表API测试

### 请求信息
- **URL**: `https://megsim.mc.machdrive.cn/api/tasks/`
- **方法**: GET

### 查询参数
- `order`: `-create_at` (按创建时间倒序)
- `limit`: `10` (限制返回10条记录)
- `skip`: `0` (跳过0条记录)
- `search`: `{}` (空搜索条件)

### 测试结果
- **状态码**: 200 OK
- **结果**: ✅ 成功获取任务列表
- **返回数据**: 包含10条任务记录，每条记录包含详细的任务信息

### 返回数据结构分析
每个任务记录包含以下关键字段：
- `id`: 任务唯一标识
- `name`: 任务名称
- `priority`: 优先级
- `create_by`: 创建者
- `create_at`: 创建时间
- `project`: 项目ID
- `car_type`: 车型
- `tags`: 标签列表
- `scenes`: 场景列表
- `events`: 事件列表
- `run_metric`: 是否运行指标
- `run_test`: 是否运行测试

## 3. 测试总结

### 成功验证的功能
1. **任务创建**: API能够成功接收POST请求并创建新任务
2. **任务查询**: API支持分页查询和排序功能
3. **数据完整性**: 返回的数据结构完整，包含所有必要字段
4. **响应格式**: 所有响应均为标准JSON格式

### API特性
- 支持复杂的任务配置参数
- 支持标签系统和项目关联
- 支持多种车型配置
- 提供完整的审计信息（创建者、创建时间等）
- 支持分页和排序查询

### 测试状态
- **创建任务API**: ✅ 测试通过
- **查询任务API**: ✅ 测试通过
- **整体评估**: 🎯 API功能完整，响应正常

---

**测试文件**: `test_tasks_api.py`  
**测试时间**: 2025年测试  
**API版本**: MegSim v1