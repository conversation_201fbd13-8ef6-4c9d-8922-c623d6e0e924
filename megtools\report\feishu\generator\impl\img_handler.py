import logging
import time

from report.feishu.generator.handler import Handler


class ImgHandler(Handler):
    def __init__(self, data, block, feishu_api, image_size=None):
        super().__init__(data, block, feishu_api)
        self.image_size = {"width": 1600, "height": 700}
        if image_size != None:
            self.image_size = image_size

    def apply(self):
        # 1. 创建图片块
        block_id = self.create_img_block()
        # 2. 上传文件
        img_token = self.upload_image(block_id)
        # 3. 替换文件
        self.replace_img(block_id, img_token)

    def create_img_block(self):
        parent_id = self.block.get("parent_id")
        new_block = self.feishu_api.create_document_block(parent_id, {
            'index': self.block.get("index", 0) + 1,
            'children': [{
                "block_type": 27,
                "image": {
                    "align": 2,
                }
            }]
        })
        logging.info(f"create_img_block {new_block}")
        return new_block['data']['children'][0]['block_id']

    def upload_image(self, block_id):
        return self.feishu_api.upload_document_img(self.data, f"{time.time_ns()}.png", block_id)

    def replace_img(self, block_id, img_token):
        self.feishu_api.update_document_block(block_id, {
            "replace_image": {
                "height": 700,
                "token": img_token,
                "width": 1600
            }
        })


