# Jira Clone SDK Makefile

.PHONY: help install install-dev test test-cov lint format clean build upload docs

# 默认目标
help:
	@echo "可用命令:"
	@echo "  install     - 安装包"
	@echo "  install-dev - 安装开发依赖"
	@echo "  test        - 运行测试"
	@echo "  test-cov    - 运行测试并生成覆盖率报告"
	@echo "  lint        - 代码检查"
	@echo "  format      - 代码格式化"
	@echo "  clean       - 清理构建文件"
	@echo "  build       - 构建包"
	@echo "  upload      - 上传到PyPI"
	@echo "  docs        - 生成文档"

# 安装包
install:
	pip install -e .

# 安装开发依赖
install-dev:
	pip install -e ".[dev,test]"

# 运行测试
test:
	python -m pytest tests/ -v

# 运行测试并生成覆盖率报告
test-cov:
	python -m pytest tests/ --cov=jira_clone_sdk --cov-report=html --cov-report=term

# 代码检查
lint:
	flake8 jira_clone_sdk/ tests/
	mypy jira_clone_sdk/

# 代码格式化
format:
	black jira_clone_sdk/ tests/ examples/

# 清理构建文件
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# 构建包
build: clean
	python setup.py sdist bdist_wheel

# 上传到PyPI
upload: build
	twine upload dist/*

# 生成文档
docs:
	@echo "文档生成功能待实现"

# 运行示例
example:
	python examples/example_usage.py

# 运行启动器帮助
launcher-help:
	python launcher.py --help