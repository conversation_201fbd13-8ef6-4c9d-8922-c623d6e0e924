"""
解析器管理器
统一管理所有解析器，提供高级解析功能

功能:
- 解析器生命周期管理
- 批量解析
- 异步解析支持
- 错误恢复
- 性能监控
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Callable, Iterator
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

from .sdk import McapAutoDriveSDK
from .message_router import MessageRouter
from ..parsers.base import BaseParser, ParseResult
from ..parsers.generic import GenericParser
from ..data_structures import MessageData
from ..utils.logger import get_logger
from ..utils.performance import PerformanceMonitor

logger = get_logger(__name__)


class ParserManager:
    """
    解析器管理器
    提供高级的解析管理功能
    """
    
    def __init__(self, 
                 max_workers: int = 4,
                 enable_async: bool = False,
                 enable_error_recovery: bool = True):
        """
        初始化解析器管理器
        
        Args:
            max_workers: 最大工作线程数
            enable_async: 是否启用异步处理
            enable_error_recovery: 是否启用错误恢复
        """
        self.max_workers = max_workers
        self.enable_async = enable_async
        self.enable_error_recovery = enable_error_recovery
        
        # 初始化组件
        self.sdk = McapAutoDriveSDK()
        self.message_router = MessageRouter()
        self.generic_parser = GenericParser()
        self.performance_monitor = PerformanceMonitor()
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers) if max_workers > 1 else None
        
        # 统计信息
        self.stats = {
            'total_parsed': 0,
            'successful_parsed': 0,
            'failed_parsed': 0,
            'total_time': 0.0,
            'parser_stats': {},
            'error_stats': {}
        }
        
        logger.info(f"解析器管理器初始化完成 (工作线程: {max_workers}, 异步: {enable_async})")
    
    def parse_file(self, 
                   mcap_file: Path,
                   message_types: Optional[List[str]] = None,
                   max_messages: Optional[int] = None,
                   batch_size: int = 100) -> Iterator[ParseResult]:
        """
        解析MCAP文件
        
        Args:
            mcap_file: MCAP文件路径
            message_types: 要解析的消息类型
            max_messages: 最大消息数量
            batch_size: 批处理大小
            
        Yields:
            ParseResult: 解析结果
        """
        logger.info(f"开始解析文件: {mcap_file.name}")
        
        self.performance_monitor.start_operation("parse_file")
        
        try:
            if self.enable_async and self.thread_pool:
                yield from self._parse_file_async(mcap_file, message_types, max_messages, batch_size)
            else:
                yield from self._parse_file_sync(mcap_file, message_types, max_messages)
        
        except Exception as e:
            logger.error(f"解析文件失败: {e}")
            if not self.enable_error_recovery:
                raise
        
        finally:
            self.performance_monitor.end_operation("parse_file")
            logger.info(f"文件解析完成: {mcap_file.name}")
    
    def _parse_file_sync(self, 
                        mcap_file: Path,
                        message_types: Optional[List[str]],
                        max_messages: Optional[int]) -> Iterator[ParseResult]:
        """同步解析文件"""
        for message in self.sdk.stream_data(mcap_file, message_types, max_messages=max_messages):
            result = self._parse_single_message(message)
            if result:
                yield result
    
    def _parse_file_async(self, 
                         mcap_file: Path,
                         message_types: Optional[List[str]],
                         max_messages: Optional[int],
                         batch_size: int) -> Iterator[ParseResult]:
        """异步批量解析文件"""
        messages = []
        
        for message in self.sdk.stream_data(mcap_file, message_types, max_messages=max_messages):
            messages.append(message)
            
            if len(messages) >= batch_size:
                # 批量处理
                yield from self._process_message_batch(messages)
                messages = []
        
        # 处理剩余消息
        if messages:
            yield from self._process_message_batch(messages)
    
    def _process_message_batch(self, messages: List[MessageData]) -> Iterator[ParseResult]:
        """处理消息批次"""
        if not self.thread_pool:
            # 单线程处理
            for message in messages:
                result = self._parse_single_message(message)
                if result:
                    yield result
            return
        
        # 多线程处理
        future_to_message = {
            self.thread_pool.submit(self._parse_single_message, message): message
            for message in messages
        }
        
        for future in as_completed(future_to_message):
            try:
                result = future.result()
                if result:
                    yield result
            except Exception as e:
                message = future_to_message[future]
                logger.error(f"批处理消息解析失败: {message.message_type} - {e}")
                self._record_error(message.message_type, str(e))
    
    def _parse_single_message(self, message: MessageData) -> Optional[ParseResult]:
        """解析单条消息"""
        start_time = time.time()
        
        try:
            # 路由到合适的解析器
            parser_func = self.message_router.route_message(message.message_type)
            
            if parser_func:
                # 使用专用解析器
                success = parser_func(message.data, message.timestamp, message.topic)
                parser_type = "specialized"
            else:
                # 使用通用解析器
                success = self.generic_parser.parse(message.data, message.timestamp, message.topic, message.message_type)
                parser_type = "generic"
            
            processing_time = time.time() - start_time
            message.mark_processed(processing_time)
            
            # 更新统计
            self._update_stats(message.message_type, parser_type, success, processing_time)
            
            return ParseResult(
                message_type=message.message_type,
                topic=message.topic,
                timestamp=message.timestamp,
                success=success,
                parser_type=parser_type,
                processing_time=processing_time,
                data=message.data if success else None,
                error=None if success else "解析失败"
            )
        
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            
            logger.error(f"消息解析异常: {message.message_type} - {error_msg}")
            self._record_error(message.message_type, error_msg)
            
            if not self.enable_error_recovery:
                raise
            
            return ParseResult(
                message_type=message.message_type,
                topic=message.topic,
                timestamp=message.timestamp,
                success=False,
                parser_type="error",
                processing_time=processing_time,
                data=None,
                error=error_msg
            )
    
    def _update_stats(self, message_type: str, parser_type: str, success: bool, processing_time: float):
        """更新统计信息"""
        self.stats['total_parsed'] += 1
        self.stats['total_time'] += processing_time
        
        if success:
            self.stats['successful_parsed'] += 1
        else:
            self.stats['failed_parsed'] += 1
        
        # 解析器统计
        if message_type not in self.stats['parser_stats']:
            self.stats['parser_stats'][message_type] = {
                'count': 0,
                'success_count': 0,
                'total_time': 0.0,
                'parser_type': parser_type
            }
        
        parser_stat = self.stats['parser_stats'][message_type]
        parser_stat['count'] += 1
        parser_stat['total_time'] += processing_time
        
        if success:
            parser_stat['success_count'] += 1
    
    def _record_error(self, message_type: str, error_msg: str):
        """记录错误"""
        if message_type not in self.stats['error_stats']:
            self.stats['error_stats'][message_type] = {}
        
        if error_msg not in self.stats['error_stats'][message_type]:
            self.stats['error_stats'][message_type][error_msg] = 0
        
        self.stats['error_stats'][message_type][error_msg] += 1
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 添加性能监控统计
        perf_stats = self.performance_monitor.get_statistics()
        stats.update(perf_stats)
        
        # 添加路由统计
        routing_stats = self.message_router.get_routing_statistics()
        stats['routing'] = routing_stats
        
        # 计算平均处理时间
        if stats['total_parsed'] > 0:
            stats['avg_processing_time'] = stats['total_time'] / stats['total_parsed']
            stats['success_rate'] = stats['successful_parsed'] / stats['total_parsed']
        else:
            stats['avg_processing_time'] = 0.0
            stats['success_rate'] = 0.0
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_parsed': 0,
            'successful_parsed': 0,
            'failed_parsed': 0,
            'total_time': 0.0,
            'parser_stats': {},
            'error_stats': {}
        }
        
        self.performance_monitor.reset()
        logger.info("统计信息已重置")
    
    def register_parser(self, message_type: str, parser: Callable):
        """注册解析器"""
        self.message_router.register_parser(message_type, parser)
    
    def get_supported_types(self) -> List[str]:
        """获取支持的消息类型"""
        return self.message_router.get_supported_types()
    
    def optimize_performance(self):
        """优化性能"""
        # 优化路由
        self.message_router.optimize_routing()
        
        # 根据统计信息调整线程池大小
        if self.thread_pool and self.stats['total_parsed'] > 100:
            avg_time = self.stats['total_time'] / self.stats['total_parsed']
            if avg_time > 0.01:  # 如果平均处理时间超过10ms，增加线程
                new_max_workers = min(self.max_workers + 1, 8)
                if new_max_workers != self.max_workers:
                    self.thread_pool.shutdown(wait=False)
                    self.thread_pool = ThreadPoolExecutor(max_workers=new_max_workers)
                    self.max_workers = new_max_workers
                    logger.info(f"线程池大小调整为: {new_max_workers}")
        
        logger.info("性能优化完成")
    
    def cleanup(self):
        """清理资源"""
        if self.thread_pool:
            self.thread_pool.shutdown(wait=True)
        
        self.performance_monitor.cleanup()
        logger.info("解析器管理器资源已清理")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()
