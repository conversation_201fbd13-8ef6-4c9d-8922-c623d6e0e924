# from django.conf import settings
import logging

import requests
from django.conf import settings


class GitlabApi:
    def __init__(self):
        self.headers = {
            'PRIVATE-TOKEN': settings.GITLAB_CONFIG.get('PRIVATE-TOKEN'),
        }
        self.base_url = settings.GITLAB_CONFIG.get('GIT-URL')

    def merge_requests(self, project_id, params={}):
        uri = f"{self.base_url}projects/{project_id}/merge_requests"
        return self.request(uri, params=params)

    def merge_request_notes(self, project_id,merge_request_id):
        uri = f"{self.base_url}projects/{project_id}/merge_requests/{merge_request_id}/notes"
        return self.request(uri)

    def request(self, url, params={}):
        if params:
            resp = requests.get(url, params=params, headers=self.headers)
        else:
            resp = requests.get(url, headers=self.headers)
        logging.info(resp.text)
        return resp.json()
