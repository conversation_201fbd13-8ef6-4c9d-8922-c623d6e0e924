from basic.utils import SqlUtil
from report.feishu.data_source.image.line_chart_image import LineChartImage
from report.feishu.generator.impl.text_handler import TextHandler, BACK_COLOR


class FpsAvg(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.handle_conditions(variables, params)

    def apply(self):
        result = self.query_data()
        if len(result) > 0:
            result = result[0]
        # 如果阈值超过了最大最小值， 那么需要放入颜色
        if "min" in self.condition and "max" in self.condition and result.get('fps', ''):
            fps = float(result.get('fps', 0))
            params = []
            if fps and (fps < self.condition["min"] or fps > self.condition["max"]):
                params.append({"content": f"{fps}", "back_color": BACK_COLOR.LightRed.code})
            else:
                params.append({"content": f"{fps}"})
            TextHandler(f"", self.block, self.feishu_api).create_block(self.block.get("index", 0) + 1, params)
            return None
        return f"{result.get('fps', '')}"

    def query_data(self):
        sql = f""" select ROUND(avg(fps),2) fps from {self.condition['table_name']} 
        where vin = %s and record_time >= %s and record_time <= %s"""
        result = SqlUtil.query_all_dict(sql, (self.condition['vin'],
                                            self.condition['start_time'],
                                            self.condition['end_time'],))
        return result


    def handle_conditions(self, variables, params):
        # 如果没有指定VIN，直接生成图片
        params = params.split(",")
        self.condition["vin"] = variables.get('vin', '')
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.condition['table_name'] = params[0]
        if len(params) == 3:
            self.condition['min'] = float(params[1])
            self.condition['max'] = float(params[2])
