import numpy as np

from basic.utils import SqlUtil
from report.feishu.data_source.image.line_chart_image import LineChartImage


class FpsDelayP99(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.handle_conditions(variables, params)

    def apply(self):
        result = self.query_data()
        # 如果阈值超过了最大最小值， 那么需要放入颜色
        delay_arr = [item['delay'] for item in result]
        maxdelay_arr = [item['maxdelay'] for item in result]
        delay_np = np.array(delay_arr, dtype=np.float64)
        maxdelay_np = np.array(maxdelay_arr, dtype=np.float64)
        if len(self.condition["field_name"]) == 1:
            if self.condition["field_name"][0] == "delay":
                return f"{np.nanpercentile(delay_np, 99, interpolation='nearest')}"
            if self.condition["field_name"][0] == "maxdelay":
                return f"{np.nanpercentile(maxdelay_np, 99, interpolation='nearest')}"
        return f"{np.nanpercentile(delay_np, 99, interpolation='nearest')}~{np.nanpercentile(maxdelay_np, 99, interpolation='nearest')}"

    def query_data(self):
        sql = f""" select ROUND(delay * 1000 ,2) delay, ROUND(maxdelay * 1000,2) maxdelay from {self.condition['table_name']} 
        where delay < 10 and maxdelay < 10 and vin = %s and record_time >= %s and record_time <= %s"""
        result = SqlUtil.query_all_dict(sql, (self.condition['vin'],
                                              self.condition['start_time'],
                                              self.condition['end_time'],))
        return result

    def handle_conditions(self, variables, params):
        params = params.split(",")
        # 如果没有指定VIN，直接生成图片
        self.condition["vin"] = variables.get('vin', '')
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.condition['table_name'] = params[0]
        if len(params) > 1:
            self.condition['field_name'] = params[1:]
