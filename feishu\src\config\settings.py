"""
配置管理系统
支持环境变量、配置文件和默认值的层级配置
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass, field
from functools import lru_cache
import json
try:
    import yaml
except ImportError:
    yaml = None


@dataclass
class FeishuConfig:
    """飞书配置"""
    app_id: str
    app_secret: str
    app_token: str
    table_id: str


@dataclass
class GitLabConfig:
    """GitLab配置"""
    url: str
    token: str
    timeout: int = 10


@dataclass
class PollingConfig:
    """轮询配置"""
    interval: int = 60
    max_retries: int = 5
    retry_delay: int = 30
    batch_size: int = 100


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class Settings:
    """应用程序设置"""
    feishu: FeishuConfig
    gitlab: GitLabConfig
    polling: PollingConfig = field(default_factory=PollingConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    debug: bool = False
    
    @classmethod
    def from_env(cls) -> "Settings":
        """从环境变量创建配置"""
        return cls(
            feishu=FeishuConfig(
                app_id=os.getenv("FEISHU_APP_ID", ""),
                app_secret=os.getenv("FEISHU_APP_SECRET", ""),
                app_token=os.getenv("FEISHU_APP_TOKEN", ""),
                table_id=os.getenv("FEISHU_TABLE_ID", "")
            ),
            gitlab=GitLabConfig(
                url=os.getenv("GITLAB_URL", ""),
                token=os.getenv("GITLAB_TOKEN", ""),
                timeout=int(os.getenv("GITLAB_TIMEOUT", "10"))
            ),
            polling=PollingConfig(
                interval=int(os.getenv("POLL_INTERVAL", "60")),
                max_retries=int(os.getenv("MAX_RETRIES", "5")),
                retry_delay=int(os.getenv("RETRY_DELAY", "30")),
                batch_size=int(os.getenv("BATCH_SIZE", "100"))
            ),
            logging=LoggingConfig(
                level=os.getenv("LOG_LEVEL", "INFO"),
                format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
                file_path=os.getenv("LOG_FILE_PATH"),
                max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", str(10 * 1024 * 1024))),
                backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
            ),
            debug=os.getenv("DEBUG", "false").lower() == "true"
        )
    
    @classmethod
    def from_file(cls, config_path: Path) -> "Settings":
        """从配置文件创建配置"""
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.suffix.lower() == '.json':
                data = json.load(f)
            elif config_path.suffix.lower() in ['.yml', '.yaml']:
                if yaml is None:
                    raise ImportError("需要安装PyYAML来支持YAML配置文件: pip install PyYAML")
                data = yaml.safe_load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
        
        return cls._from_dict(data)
    
    @classmethod
    def _from_dict(cls, data: Dict[str, Any]) -> "Settings":
        """从字典创建配置"""
        feishu_data = data.get("feishu", {})
        gitlab_data = data.get("gitlab", {})
        polling_data = data.get("polling", {})
        logging_data = data.get("logging", {})
        
        return cls(
            feishu=FeishuConfig(**feishu_data),
            gitlab=GitLabConfig(**gitlab_data),
            polling=PollingConfig(**polling_data),
            logging=LoggingConfig(**logging_data),
            debug=data.get("debug", False)
        )
    
    def validate(self) -> None:
        """验证配置的有效性"""
        errors = []
        
        if not self.feishu.app_id:
            errors.append("飞书 APP_ID 不能为空")
        if not self.feishu.app_secret:
            errors.append("飞书 APP_SECRET 不能为空")
        if not self.feishu.app_token:
            errors.append("飞书 APP_TOKEN 不能为空")
        if not self.feishu.table_id:
            errors.append("飞书 TABLE_ID 不能为空")
        
        if not self.gitlab.url:
            errors.append("GitLab URL 不能为空")
        if not self.gitlab.token:
            errors.append("GitLab TOKEN 不能为空")
        
        if self.polling.interval <= 0:
            errors.append("轮询间隔必须大于0")
        if self.polling.max_retries < 0:
            errors.append("最大重试次数不能小于0")
        if self.polling.retry_delay <= 0:
            errors.append("重试延迟必须大于0")
        
        if errors:
            raise ValueError("配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))


@lru_cache()
def get_settings() -> Settings:
    """获取应用程序设置（单例模式）"""
    # 优先级：配置文件 > 环境变量 > 默认值
    config_file = Path("config.yml")
    if config_file.exists():
        settings = Settings.from_file(config_file)
    else:
        settings = Settings.from_env()
    
    # 如果环境变量中没有配置，使用原有的硬编码配置作为默认值
    if not settings.feishu.app_id:
        settings.feishu.app_id = "cli_a8d028a01ed8900b"
        settings.feishu.app_secret = "fA8FpGvqdoZvxBe9tTqiDbp6KkRGWmEF"
        settings.feishu.app_token = "MudQbRICta0KjusHgXWcpCCJnyb"
        settings.feishu.table_id = "tbli7WPGf9S1xCF0"
    
    if not settings.gitlab.url:
        settings.gitlab.url = "https://git-core.megvii-inc.com"
        settings.gitlab.token = "********************"
    
    settings.validate()
    return settings
