import logging
import os
import time
import traceback

from cron_task.models import OssFileList


class Parser:
    def __init__(self):
        self.flush_status = True
        self.error_info = ""
        self.min_time = None
        self.max_time = None

    def parse(self, id, db_entity, log_path):
        try:
            # 如果日志不存在，那么说明需要重新去下载
            if not os.path.exists(log_path):
                OssFileList.objects.filter(id=id).update(current_status=0)

            self.parse_file(id, db_entity, log_path)

            os.remove(log_path)
            os.remove(log_path[0:-4])
            current_status = 4 if self.flush_status else 7
            current_status = 6 if current_status == 4 and self.error_info else current_status
            if self.min_time is None or self.max_time is None:
                OssFileList.objects.filter(id=id).update(current_status=current_status, remark=self.error_info[0:200])
            else:
                OssFileList.objects.filter(id=id).update(current_status=current_status, file_update_time=self.min_time, update_time=self.max_time, remark=self.error_info[0:200])
        except FileNotFoundError:
            logging.error(f"{traceback.format_exc()}")
            OssFileList.objects.filter(id=id).update(current_status=0, remark=f"{traceback.format_exc()}"[0:200])
        except Exception:
            logging.error(f"{traceback.format_exc()}")
            OssFileList.objects.filter(id=id).update(current_status=6, remark=f"{traceback.format_exc()}"[0:200])

    def parse_file(self, id, db_entity, log_path):
        pass

    # 提取公共方法进行刷缓存
    def flush_cache(self, db_model, cache):
        if len(cache) == 0:
            return
        idx = 0
        while True:
            if idx >= 3:
                break
            idx = idx + 1
            try:
                db_model.objects.bulk_create(cache)
                cache.clear()
                return
            except Exception:
                self.error_info = traceback.format_exc()
                logging.error(f"insert error {db_model} \t {self.error_info}")
                time.sleep(10)
        cache.clear()
        self.flush_status = False

    def update_time(self, record_time):
        if record_time is None or record_time == "":
            return
        if self.min_time is None:
            self.min_time = record_time
        self.max_time = record_time