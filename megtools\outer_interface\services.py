import datetime

from basic.utils import OssUtils
from cron_task.models import OssFileList


class OssFileFilter:
    def __init__(self, oss_path, prefix):
        self.bucket = None
        self.oss_path = None
        self.file_prefix = prefix
        self.parse_oss_path(oss_path)
        self.ossUtil = OssUtils(self.bucket)

    def parse_oss_path(self, oss_path):
        path_arr = oss_path.split("/")
        if oss_path.startswith('s3://'):
            # s3://megsim-test/task-plan/180351/153975/car_z18__e2e_percetptor__20250311__164954__E2E-10797__622851/test_result/log/soc_logs/log/performance_logs/2025-04-21
            self.bucket = path_arr[2]
            self.oss_path = "/".join(path_arr[3:])
        elif oss_path.startswith('http'):
            # https://oss.iap.qy.machdrive.cn/megsim-test/task-plan/180351/153975/car_z18__e2e_percetptor__20250311__164954__E2E-10797__622851/test_result/log/soc_logs/log/performance_logs/2025-04-18/
            self.bucket = path_arr[3]
            self.oss_path = "/".join(path_arr[4:])

    def parse_file_time(self, file_list):
        day = file_list.split('/')[-2]
        time = file_list.split('/')[-1].split('_')[-1].split('.')[0]
        return f"{day} {time[0]}{time[1]}:{time[2]}{time[3]}:{time[4]}{time[5]}"

    def apply(self):
        # 检索文件夹下的所有文件，不解析文件夹。
        file_list, dir_list = self.ossUtil.ls_dir(self.oss_path, )
        oss_path_list = {}
        parse_group = {}
        vin = ""
        for item in file_list:
            parse_type = self.checkObject(item.get("Key"))
            if not parse_type:
                continue
            # 这样能够找到时间的最大值和最小值， 后面返回时， 将时间向后平移两个小时，加上VIN
            file_time = self.parse_file_time(item.get("Key"))
            if parse_type not in parse_group:
                parse_group[parse_type] = {"min_time": file_time, "max_time": file_time}
            else:
                if file_time < parse_group[parse_type]["min_time"]:
                    parse_group[parse_type]["min_time"] = file_time
                if file_time > parse_group[parse_type]["max_time"]:
                    parse_group[parse_type]["max_time"] = file_time
            if not vin:
                vin = self.parse_vin(item.get("Key"))
            entity = {
                "bucket_name": self.bucket,
                "oss_path": item.get("Key"),
                "file_size": item.get("Size"),
                "file_update_time": item.get("LastModified"),
                "vin": vin,
                "parse_type": parse_type,
                "current_status": '0',
                "create_time": datetime.datetime.now(),
                "update_time": datetime.datetime.now(),
            }
            oss_path_list[item.get("Key")] = OssFileList(**entity)
        # 校验是否存在，如果存在需要删除掉
        db_result = OssFileList.objects.filter(oss_path__in=list(oss_path_list.keys())).all()
        db_oss_path = [item.oss_path for item in db_result]
        insert_array = []
        for key, value in oss_path_list.items():
            if key in db_oss_path:
                continue
            insert_array.append(value)
        OssFileList.objects.bulk_create(insert_array)
        return vin, parse_group

    def parse_vin(self, key):
        result = key.split("/")
        return f"{result[1]}_{result[2]}"

    def checkObject(self, key):
        file_name = key[key.rindex("/")+1:]
        if self.file_prefix and not file_name.startswith(self.file_prefix):
            return False
        # 找到 file_name
        if file_name.startswith('cpu_temperature_'):
            return "comm_cpu_temperature"
        elif file_name.startswith('gpu_nvdia_smi'):
            return "comm_gpu_nvidia_smi"
        elif file_name.startswith('host_process_usage_'):
            return "host_process"
        return False
