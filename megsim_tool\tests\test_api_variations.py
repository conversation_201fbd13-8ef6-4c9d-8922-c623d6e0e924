import requests
import json
from datetime import datetime, timed<PERSON><PERSON>

def test_api_endpoint(search_params=None, order="-id", limit=10, skip=0):
    """
    测试API端点的不同参数组合
    """
    base_url = "https://megsim.mc.machdrive.cn/api/event/records/multisearch/"
    
    params = {
        "order": order,
        "limit": limit,
        "skip": skip
    }
    
    if search_params:
        params["search"] = json.dumps(search_params, separators=(',', ':'))
    
    try:
        response = requests.get(base_url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

def main():
    print("=== MegSim API 多种参数测试 ===")
    print()
    
    # 测试1: 无搜索参数，获取最新的10条记录
    print("1. 测试无搜索参数（获取最新记录）:")
    result = test_api_endpoint()
    if result:
        print(f"   总记录数: {result.get('total', 0)}")
        print(f"   返回记录数: {len(result.get('records', []))}")
        if result.get('records'):
            print(f"   第一条记录示例: {list(result['records'][0].keys()) if result['records'] else 'N/A'}")
    print()
    
    # 测试2: 只搜索特定车辆ID
    print("2. 测试只搜索车辆ID 473:")
    result = test_api_endpoint({"vehicle_id": [473]})
    if result:
        print(f"   总记录数: {result.get('total', 0)}")
        print(f"   返回记录数: {len(result.get('records', []))}")
    print()
    
    # 测试3: 只搜索特定路线
    print("3. 测试只搜索路线 LD3994:")
    result = test_api_endpoint({"route": "LD3994"})
    if result:
        print(f"   总记录数: {result.get('total', 0)}")
        print(f"   返回记录数: {len(result.get('records', []))}")
    print()
    
    # 测试4: 搜索今天的数据
    today = datetime.now().strftime("%Y-%m-%d")
    print(f"4. 测试搜索今天的数据 ({today}):")
    result = test_api_endpoint({"time": [today, today]})
    if result:
        print(f"   总记录数: {result.get('total', 0)}")
        print(f"   返回记录数: {len(result.get('records', []))}")
    print()
    
    # 测试5: 搜索最近7天的数据
    week_ago = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    print(f"5. 测试搜索最近7天的数据 ({week_ago} 到 {today}):")
    result = test_api_endpoint({"time": [week_ago, today]})
    if result:
        print(f"   总记录数: {result.get('total', 0)}")
        print(f"   返回记录数: {len(result.get('records', []))}")
    print()
    
    # 测试6: 尝试不同的车辆ID
    print("6. 测试不同的车辆ID:")
    for vehicle_id in [1, 100, 200, 300, 400, 500]:
        result = test_api_endpoint({"vehicle_id": [vehicle_id]}, limit=1)
        if result and result.get('total', 0) > 0:
            print(f"   车辆ID {vehicle_id}: 找到 {result.get('total', 0)} 条记录")
    print()
    
    # 测试7: 原始请求验证
    print("7. 验证原始请求:")
    original_params = {
        "vehicle_id": [473],
        "time": ["2025-08-06", "2025-08-06"],
        "route": "LD3994"
    }
    result = test_api_endpoint(original_params)
    if result:
        print(f"   原始请求结果: 总记录数 {result.get('total', 0)}, 返回记录数 {len(result.get('records', []))}")
        print(f"   这与之前的结果一致，说明API调用正确")
    
    print()
    print("=== 测试完成 ===")
    print("注意: 如果所有测试都返回0条记录，可能是因为:")
    print("1. 数据库中确实没有匹配的数据")
    print("2. 需要认证才能访问数据")
    print("3. 搜索参数格式不正确")
    print("4. API端点需要特定的权限")

if __name__ == "__main__":
    main()