"""
基础解析器类
定义解析器的基础接口和通用功能
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Optional, Dict, List
import time

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ParseResult:
    """解析结果"""
    message_type: str
    topic: str
    timestamp: float
    success: bool
    parser_type: str  # "specialized", "generic", "error"
    processing_time: float
    data: Optional[Any] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """后处理初始化"""
        if self.metadata is None:
            self.metadata = {}


class BaseParser(ABC):
    """
    基础解析器抽象类
    所有专用解析器都应该继承此类
    """
    
    def __init__(self, name: str, supported_types: List[str]):
        """
        初始化解析器
        
        Args:
            name: 解析器名称
            supported_types: 支持的消息类型列表
        """
        self.name = name
        self.supported_types = supported_types
        self.parse_count = 0
        self.success_count = 0
        self.total_processing_time = 0.0
        self.last_error = None
        
        logger.info(f"解析器初始化: {name} (支持类型: {len(supported_types)})")
    
    @abstractmethod
    def parse_message(self, ros_msg: Any, timestamp: float, topic: str, message_type: str) -> bool:
        """
        解析消息的抽象方法
        
        Args:
            ros_msg: ROS消息对象
            timestamp: 时间戳
            topic: 话题名称
            message_type: 消息类型
            
        Returns:
            bool: 解析是否成功
        """
        pass
    
    def parse(self, ros_msg: Any, timestamp: float, topic: str, message_type: str) -> ParseResult:
        """
        解析消息的公共接口
        
        Args:
            ros_msg: ROS消息对象
            timestamp: 时间戳
            topic: 话题名称
            message_type: 消息类型
            
        Returns:
            ParseResult: 解析结果
        """
        start_time = time.time()
        
        try:
            # 检查是否支持此消息类型
            if not self.supports_type(message_type):
                raise ValueError(f"不支持的消息类型: {message_type}")
            
            # 执行解析
            success = self.parse_message(ros_msg, timestamp, topic, message_type)
            processing_time = time.time() - start_time
            
            # 更新统计
            self.parse_count += 1
            self.total_processing_time += processing_time
            
            if success:
                self.success_count += 1
                self.last_error = None
            
            return ParseResult(
                message_type=message_type,
                topic=topic,
                timestamp=timestamp,
                success=success,
                parser_type="specialized",
                processing_time=processing_time,
                data=ros_msg if success else None,
                error=None if success else "解析失败"
            )
        
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            self.last_error = error_msg
            
            logger.error(f"解析器 {self.name} 解析失败: {error_msg}")
            
            return ParseResult(
                message_type=message_type,
                topic=topic,
                timestamp=timestamp,
                success=False,
                parser_type="specialized",
                processing_time=processing_time,
                data=None,
                error=error_msg
            )
    
    def supports_type(self, message_type: str) -> bool:
        """
        检查是否支持指定的消息类型
        
        Args:
            message_type: 消息类型
            
        Returns:
            bool: 是否支持
        """
        # 直接匹配
        if message_type in self.supported_types:
            return True
        
        # 简化名称匹配
        simple_type = message_type.split('.')[-1]
        if simple_type in self.supported_types:
            return True
        
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取解析器统计信息"""
        return {
            'name': self.name,
            'supported_types': self.supported_types,
            'parse_count': self.parse_count,
            'success_count': self.success_count,
            'success_rate': self.success_count / self.parse_count if self.parse_count > 0 else 0.0,
            'total_processing_time': self.total_processing_time,
            'avg_processing_time': self.total_processing_time / self.parse_count if self.parse_count > 0 else 0.0,
            'last_error': self.last_error
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.parse_count = 0
        self.success_count = 0
        self.total_processing_time = 0.0
        self.last_error = None
        
        logger.info(f"解析器 {self.name} 统计信息已重置")
    
    def validate_message(self, ros_msg: Any) -> bool:
        """
        验证消息的有效性
        子类可以重写此方法来实现自定义验证
        
        Args:
            ros_msg: ROS消息对象
            
        Returns:
            bool: 消息是否有效
        """
        return ros_msg is not None
    
    def preprocess_message(self, ros_msg: Any) -> Any:
        """
        预处理消息
        子类可以重写此方法来实现自定义预处理
        
        Args:
            ros_msg: ROS消息对象
            
        Returns:
            Any: 预处理后的消息对象
        """
        return ros_msg
    
    def postprocess_result(self, result: bool, ros_msg: Any) -> bool:
        """
        后处理结果
        子类可以重写此方法来实现自定义后处理
        
        Args:
            result: 解析结果
            ros_msg: ROS消息对象
            
        Returns:
            bool: 后处理后的结果
        """
        return result
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.name}Parser(支持类型: {len(self.supported_types)}, 解析次数: {self.parse_count})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"{self.__class__.__name__}(name='{self.name}', "
                f"supported_types={self.supported_types}, "
                f"parse_count={self.parse_count}, "
                f"success_rate={self.success_count/self.parse_count if self.parse_count > 0 else 0:.2%})")


class SimpleParser(BaseParser):
    """
    简单解析器
    用于快速创建基于函数的解析器
    """
    
    def __init__(self, name: str, supported_types: List[str], parse_func: callable):
        """
        初始化简单解析器
        
        Args:
            name: 解析器名称
            supported_types: 支持的消息类型列表
            parse_func: 解析函数
        """
        super().__init__(name, supported_types)
        self.parse_func = parse_func
    
    def parse_message(self, ros_msg: Any, timestamp: float, topic: str, message_type: str) -> bool:
        """解析消息"""
        if not self.validate_message(ros_msg):
            return False
        
        preprocessed_msg = self.preprocess_message(ros_msg)
        
        try:
            result = self.parse_func(preprocessed_msg, timestamp, topic)
            return self.postprocess_result(result, ros_msg)
        except Exception as e:
            logger.error(f"简单解析器 {self.name} 执行失败: {e}")
            return False
