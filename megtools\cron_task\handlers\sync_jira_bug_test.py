import datetime
import json
import logging
import re

from basic.utils import Jira<PERSON><PERSON>ate


def apply(task_config):
    JiraBug2Test(task_config).apply()

# 这里存储的是不变的数据， 变化的数据不存，因此存储的东西有限
class JiraBug2Test:
    def __init__(self, task_config):
        self.task_config = task_config
        self.jql = task_config.get("jql", "")
        self.trans_config = task_config.get("trans_config", {})
        # 对其去重
        self.jira_fields = ["issuelinks"]
        self.__init_jira_fields()
        self.jira_value_path = [item['jira_value_path'] for item in self.trans_config]

    def __init_jira_fields(self):
        for item in self.trans_config:
            field = item['jira_field']
            if isinstance(field, list):
                self.jira_fields.extend(field)
            else:
                self.jira_fields.append(field)
        self.jira_fields = list(dict.fromkeys(self.jira_fields))

    def apply(self):
        self.check()
        # 1. 查询MYSQL的最后更新时间，按照更新时间去查询上次更新之后的问题
        jira_data = self.query_jira(self.task_config.get("before_execute", None))
        for jira_info in jira_data:
            # 获取到jira 之后，再找到link 的issue：
            issue_links = jira_info.get("fields", {}).get("issuelinks", [])
            for issue_link in issue_links:
                issue_type_id = issue_link.get("inwardIssue", {}).get("fields", {}).get("issuetype", {}).get("id", 0)
                summary = issue_link.get("inwardIssue", {}).get("fields", {}).get("summary", "")
                if not (issue_type_id == "10002") or not summary.startswith("CLONE - "):
                    continue
                # 否则认为是 Clone 的issue
                jira_key = issue_link.get("inwardIssue", {}).get("key", "")
                jql = f"key = {jira_key}"
                cloned_issue = JiraOperate().query_jira(jql=jql, fields=self.jira_fields)
                compare_result = self.compare_cloned(jira_info, cloned_issue[0])
                if compare_result:
                    self.update_jira(cloned_issue[0].get("key"), compare_result)
                self.sync_issue_comment(jira_info.get("key", ""), jira_key)
                break

    def sync_issue_comment(self, source_key, clone_key):
        source_comments = JiraOperate().query_comment(source_key)
        if len(source_comments.get('comments', [])) == 0:
            return
        clone_comments = JiraOperate().query_comment(clone_key)
        clone_body_arr = [item.get("body", "") for item in clone_comments.get("comments", [])]
        for comment in source_comments.get('comments', []):
            if comment.get("body", "") in clone_body_arr:
                continue
            resp = JiraOperate().add_comment(clone_key, comment.get("body", "") )
            logging.info(resp)

    def update_jira(self, clone_key, compare_result):
        JiraOperate().modify_values(clone_key, compare_result)



    def compare_cloned(self, jira_info, cloned_issue):
        # 对比两个数据，然后clone
        result = {}
        for item in self.trans_config:
            if item['jira_field'] == "key":
                continue
            current_issue_value = self.get_value_by_path(item.get("jira_value_path"), jira_info)
            cloned_issue_value = self.get_value_by_path(item.get("jira_value_path"), cloned_issue)
            if str(current_issue_value) != str(cloned_issue_value):
                modify_value = self.handle_value(current_issue_value, cloned_issue_value,cloned_issue, item.get("type", ""))
                if modify_value:
                    result[item.get("jira_field")] = modify_value
        return result

    def handle_value(self, current_value, cloned_value, cloned_issue, type):
        if type == "status":
            # 如果是状态需要换种方式更新
            # 1. 查询状态需要修改的状态 id
            transition_list = JiraOperate().get_transitions(cloned_issue.get("key", ""))
            transition_id = None
            for transition in transition_list.get("transitions", []):
                if transition.get("name", "-1") == current_value.get("statusCategory", {}).get("name", "-2"):
                    # 如果类型相同，那么返回 transition 的id
                    transition_id = transition.get("id", None)
                    break
            # 2. 修改
            if transition_id:
                JiraOperate().post_transitions(cloned_issue.get("key", ""), transition_id)
            return None
        elif type == "union":
            if isinstance(current_value, list) and isinstance(cloned_value, list):
                return list(set(current_value).union(set(cloned_value)))
        return current_value

    def query_jira(self, last_execute):
        last_update_time = '2021-01-01 00:00'
        if last_execute:
            last_update_time = last_execute.strftime("%Y-%m-%d %H:%M")
        # 添加SQL的片段
        self.jql = self.jql.replace("{{updated}}", last_update_time)
        jira_entities = JiraOperate().query_jira(self.jql, self.jira_fields)
        return jira_entities

    # 通过配置的路径
    def get_value_by_path(self, jira_path, jira_entity):
        path_arr = jira_path.split(".")
        result = jira_entity
        for item in path_arr:
            if result is None or result == "":
                result = ""
                break
            result = result.get(item, "")
        return result

    def check(self):
        if not self.jql:
            raise Exception("请配置jql参数")


