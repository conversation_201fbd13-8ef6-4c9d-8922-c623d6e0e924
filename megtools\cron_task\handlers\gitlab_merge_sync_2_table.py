import json
import logging
from datetime import datetime
from urllib.parse import urlparse, parse_qs

from basic.services import Send<PERSON><PERSON>age<PERSON>pi
from basic.third_apis.feishu_api import FeishuApi
from basic.third_apis.gitlab_api import GitlabApi
from basic.utils import SqlUtil
from cron_task.models import GitlabMergeRequests


def apply(task_config):
    SyncGitLabMergeRequest(task_config).apply()

class SyncGitLabMergeRequest:
    def __init__(self, task_config):
        self.task_config = task_config
        self.gitlab_api = GitlabApi()
        self.cache = []
        self.project_id = self.task_config.get('project_id')
        current_time =  datetime.now()
        self.flush_batch = current_time.strftime('%Y%m%d_%H%M%S')
        self.message_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.trans_config = self.task_config.get('trans_config')
        self.feishuApi = FeishuApi()
        self.bitable_token = ""
        self.table_id = ""

    def apply(self):
        # 查询gitlab中的数据
        merge_list = self.gitlab_api.merge_requests(self.project_id, {"state": 'merged',"per_page": 100})
        # 查找去重
        mergeRequest = GitlabMergeRequests.objects.filter(project_id=self.project_id).order_by('-merged_at').first()
        # 落库
        self.save_db(mergeRequest, merge_list)
        # 落入 bi table
        db_entity_list = self.save_bitable()
        # 全部操作完成之后， 同步消息至群里或者用户
        self.send_message(db_entity_list)

    def send_message(self, db_entity_list):
        if not self.task_config.get("alarm", None) or len(db_entity_list) == 0:
            return
        merge_info = []
        for item in db_entity_list:
            merge_at = item.get('merged_at', "")
            if merge_at:
                merge_at = merge_at.strftime('%Y-%m-%d %H:%M:%S')
            title = item.get('title', "")
            if len(title) > 70:
                title = f"{title[0:69]}..."
            merge_info.append({
                "title": title,
                "author": item.get('author',""),
                "merge_time": merge_at,
                "pass_by": item.get('test_author', ""),
            })

        message_entity = {
            "merge_info": merge_info,
            "batch_info": self.message_time
        }
        message = {"template_id": "AAqBboH4Hn0oj", "template_variable": message_entity}
        # 如果存在alarm 的信息，那么进行消息发送
        for item in self.task_config.get("alarm", "").split(";"):
            SendMessageApi(message).send_message(item)

    def save_bitable(self):
        sql = f""" select merge_id, title, source_branch, target_branch, merged_at, author, merged_by, test_author, pass_time, flush_batch 
        from cron_gitlab_merge_requests where flush_batch = '{self.flush_batch}' """
        db_entity_list = SqlUtil.query_all_dict(sql)
        # 查出数据，然后落库！！！
        if not db_entity_list or len(db_entity_list) <= 0:
            return
        # 如果不为空，那么向bitable中插入数据
        bitable_entity_list = []
        for item in db_entity_list:
            bitable_entity_list.append({"fields": self.trans_data2bitable(item)})
        self.parse_wiki_node()
        self.feishuApi.bitable_batch_insert_all(self.bitable_token, self.table_id, bitable_entity_list)
        return db_entity_list

    def parse_wiki_node(self):
        parsed_url = urlparse(self.task_config.get("feishu_url"))
        get_params = parse_qs(parsed_url.query)
        path = parsed_url.path
        wiki_token = path.split("/")[-1]
        self.table_id = get_params["table"][0]
        bi_table_info = self.feishuApi.wiki_get_node(wiki_token, 'wiki')
        if bi_table_info.get("code", -1) != 0:
            # 没有找到飞书表格直接返回
            return bi_table_info.get("msg", "")
        # 开始查询bit_table 中的数据
        self.bitable_token = bi_table_info.get("data", {}).get("node", {}).get("obj_token", "")

    def trans_data2bitable(self, entity):
        result = {}
        for item in self.trans_config:
            bitable_field = item.get("bitable_key", "")
            table_field = item.get("table_field", "")
            value = None
            if table_field:
                # 如果jira_path 存在，通过jira_path 进行取值
                value = entity[table_field]
            if value:
                result[bitable_field] = self.reform_bitable_type(f"{value}", item)
        return result

    def reform_bitable_type(self, value, field_config):
        bitable_type = field_config.get("bitable_type", "文本")
        if bitable_type == "超链接":
            if value and value.startswith("[") and value.endswith("]") and len(value.split("|")) == 2:
                jira_link = value.split("|")
                return {"text": jira_link[0][1:], "link": jira_link[1][:-1]}
            elif value:
                link_prefix = field_config.get("link_prefix", "")
                return {"text": value, "link": f"{link_prefix}{value}"}
            else:
                return {"text": " ", "link": ""}
        elif bitable_type == "多选":
            if isinstance(value, list):
                return value
            elif value is None:
                return []
            else:
                return [value]
        elif bitable_type == "数字":
            if isinstance(value, int) or isinstance(value, float):
                return value
            else:
                return float(value)
        elif bitable_type == "过滤单选":
            match_arr = field_config.get("match_arr", [])
            if isinstance(value, list):
                for item in value:
                    if item in match_arr:
                        return item
            else:
                if value in match_arr:
                    return value
        elif bitable_type == "日期":
            format_type = field_config.get("format_type", "%Y-%m-%d")
            if value is None:
                return None
            date_obj = datetime.strptime(value, format_type)
            value = int(date_obj.timestamp() * 1000)
            return value
        else:
            return value

    def save_db(self, mergeRequest, merge_list):
        count = 0
        for item in merge_list:
            merged_time = datetime.strptime(item.get('merged_at'),'%Y-%m-%dT%H:%M:%S.%f%z')
            if mergeRequest and merged_time <= mergeRequest.merged_at:
                continue
            merge_id = item.get('iid')
            project_id = item.get('project_id')
            notes_list = self.gitlab_api.merge_request_notes(project_id, merge_id)
            test_author = ""
            pass_time = None
            for note in notes_list:
                if "测试通过" in note.get("body", ""):
                    test_author = note.get("author", {}).get("name", "")
                    pass_time = note.get('updated_at')
                    break
            entity = {
                "merge_id": merge_id,
                "project_id": project_id,
                "title": item.get('title'),
                "state": item.get('state'),
                "merged_by": item.get('merged_by',{}).get("name", ""),
                "merged_at": item.get('merged_at'),
                "source_branch": item.get('source_branch'),
                "target_branch": item.get('target_branch'),
                "reviewers": ",".join([item.get("name", "") for item in item.get('reviewers')]),
                "author": item.get('author', {}).get("name"),
                "sha": item.get('sha'),
                "web_url": item.get('web_url'),
                "test_author": test_author,
                "pass_time": pass_time,
                "created_at": item.get('created_at'),
                "updated_at": item.get('updated_at'),
                "flush_batch": self.flush_batch,
            }
            logging.info(entity)
            self.cache.append(GitlabMergeRequests(**entity))
            count+=1
            self.flush_cache()
        self.flush_cache(True)
        return count

    def flush_cache(self, is_force=False):
        if len(self.cache) == 0:
            return
        if is_force or len(self.cache) > 1000:
            GitlabMergeRequests.objects.bulk_create(self.cache)
            self.cache = []


