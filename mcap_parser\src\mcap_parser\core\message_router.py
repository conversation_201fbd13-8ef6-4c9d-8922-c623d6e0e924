"""
消息路由器
智能路由消息到合适的解析器

功能:
- 智能解析器选择
- 消息类型映射
- 路由缓存
- 性能优化
"""

import re
from typing import Dict, List, Optional, Callable, Any
from collections import defaultdict

from ..utils.logger import get_logger
from ..parsers.registry import ParserRegistry

logger = get_logger(__name__)


class MessageRouter:
    """
    消息路由器
    负责将消息路由到合适的解析器
    """
    
    def __init__(self):
        self.parser_registry = ParserRegistry()
        self.routing_cache = {}  # 路由缓存
        self.routing_stats = defaultdict(int)  # 路由统计
        
        # 消息类型映射规则
        self.type_mapping_rules = [
            # 完整类型名称到简化名称的映射
            (r'mcap_ros2\._dynamic\.(.+)', r'\1'),
            # 其他可能的映射规则
            (r'(.+)_msgs\.msg\.(.+)', r'\2'),
            (r'(.+)\.(.+)', r'\2'),
        ]
        
        logger.info("消息路由器初始化完成")
    
    def route_message(self, message_type: str) -> Optional[Callable]:
        """
        路由消息到合适的解析器
        
        Args:
            message_type: 消息类型
            
        Returns:
            Optional[Callable]: 解析器函数，如果没有找到返回None
        """
        # 检查缓存
        if message_type in self.routing_cache:
            parser = self.routing_cache[message_type]
            self.routing_stats[f"cache_hit_{message_type}"] += 1
            return parser
        
        # 尝试直接匹配
        parser = self.parser_registry.get_parser(message_type)
        if parser:
            self.routing_cache[message_type] = parser
            self.routing_stats[f"direct_match_{message_type}"] += 1
            return parser
        
        # 尝试类型映射
        for pattern, replacement in self.type_mapping_rules:
            match = re.match(pattern, message_type)
            if match:
                simplified_type = re.sub(pattern, replacement, message_type)
                parser = self.parser_registry.get_parser(simplified_type)
                if parser:
                    self.routing_cache[message_type] = parser
                    self.routing_stats[f"mapped_match_{simplified_type}"] += 1
                    logger.debug(f"映射路由: {message_type} -> {simplified_type}")
                    return parser
        
        # 没有找到专用解析器
        self.routing_cache[message_type] = None
        self.routing_stats[f"no_parser_{message_type}"] += 1
        logger.debug(f"未找到专用解析器: {message_type}")
        return None
    
    def register_parser(self, message_type: str, parser: Callable):
        """
        注册解析器
        
        Args:
            message_type: 消息类型
            parser: 解析器函数
        """
        self.parser_registry.register(message_type, parser)
        # 清除相关缓存
        self._clear_cache_for_type(message_type)
        logger.info(f"注册解析器: {message_type}")
    
    def unregister_parser(self, message_type: str):
        """
        注销解析器
        
        Args:
            message_type: 消息类型
        """
        self.parser_registry.unregister(message_type)
        self._clear_cache_for_type(message_type)
        logger.info(f"注销解析器: {message_type}")
    
    def get_supported_types(self) -> List[str]:
        """获取支持的消息类型列表"""
        return self.parser_registry.get_supported_types()
    
    def get_routing_statistics(self) -> Dict[str, Any]:
        """获取路由统计信息"""
        return {
            'cache_size': len(self.routing_cache),
            'routing_stats': dict(self.routing_stats),
            'supported_types_count': len(self.get_supported_types()),
            'cache_hit_rate': self._calculate_cache_hit_rate()
        }
    
    def clear_cache(self):
        """清除路由缓存"""
        self.routing_cache.clear()
        logger.info("路由缓存已清除")
    
    def _clear_cache_for_type(self, message_type: str):
        """清除特定类型的缓存"""
        # 清除直接匹配的缓存
        if message_type in self.routing_cache:
            del self.routing_cache[message_type]
        
        # 清除可能映射到此类型的缓存
        to_remove = []
        for cached_type, cached_parser in self.routing_cache.items():
            if cached_parser and self.parser_registry.get_parser(message_type) == cached_parser:
                to_remove.append(cached_type)
        
        for cached_type in to_remove:
            del self.routing_cache[cached_type]
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        cache_hits = sum(count for key, count in self.routing_stats.items() if key.startswith('cache_hit_'))
        total_requests = sum(self.routing_stats.values())
        
        if total_requests == 0:
            return 0.0
        
        return cache_hits / total_requests
    
    def add_type_mapping_rule(self, pattern: str, replacement: str):
        """
        添加类型映射规则
        
        Args:
            pattern: 正则表达式模式
            replacement: 替换模式
        """
        self.type_mapping_rules.append((pattern, replacement))
        self.clear_cache()  # 清除缓存以应用新规则
        logger.info(f"添加类型映射规则: {pattern} -> {replacement}")
    
    def remove_type_mapping_rule(self, pattern: str):
        """
        移除类型映射规则
        
        Args:
            pattern: 要移除的正则表达式模式
        """
        self.type_mapping_rules = [
            (p, r) for p, r in self.type_mapping_rules if p != pattern
        ]
        self.clear_cache()  # 清除缓存以应用更改
        logger.info(f"移除类型映射规则: {pattern}")
    
    def optimize_routing(self):
        """优化路由性能"""
        # 根据使用频率重新排序映射规则
        rule_usage = defaultdict(int)
        
        for stat_key in self.routing_stats:
            if stat_key.startswith('mapped_match_'):
                rule_type = stat_key.replace('mapped_match_', '')
                for pattern, replacement in self.type_mapping_rules:
                    if re.match(pattern, rule_type):
                        rule_usage[pattern] += self.routing_stats[stat_key]
                        break
        
        # 按使用频率排序规则
        self.type_mapping_rules.sort(key=lambda rule: rule_usage[rule[0]], reverse=True)
        
        logger.info("路由规则已优化")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"MessageRouter(支持类型: {len(self.get_supported_types())}, 缓存大小: {len(self.routing_cache)})"
