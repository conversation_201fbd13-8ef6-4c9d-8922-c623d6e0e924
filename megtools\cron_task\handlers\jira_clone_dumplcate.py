import requests


JIRA_CONFIG = {
    # 查询jira链接
    'base_url': 'https://jira.mach-drive-inc.com/rest/',
    'url': "https://jira.mach-drive-inc.com/rest/api/2/search",
    'headers': {
        "Authorization": "Bearer MzgzNjAyMzk0NTc4Og458SHa5P6oI6VZLBHDLQ17/GP/",
        "Content-Type": "application/json"
    }
}

class JiraOperate:

    def query_jira(self, jql, fields=None):
        """
        直接在Jira 中查询出来数据，返回body就好
        这里记得配置 jql 的时候需要验证下，不能查出来的数据太多！！！！！
        """
        if fields is None:
            fields = ["key", "summary"]
        page = 0
        jira_entities = []
        while True:
            jira_entity = self.page_jira(page, jql, fields)
            if len(jira_entity) == 0:
                break
            page += 1
            jira_entities.extend(jira_entity)
        return jira_entities

    def page_jira(self, page, jql, fields, count=400):
        jira_entity = []
        start = page * count
        body = {
            "maxResults": count,
            "startAt": start,
            "fields": fields,
            "jql": jql
        }

        resp = requests.post(JIRA_CONFIG["url"], headers=JIRA_CONFIG["headers"], json=body)
        data = resp.json()
        # 循环所有的问题列表
        for item in data.get('issues', []):
            if "fields" not in item:
                continue
            jira_entity.append(item)
        return jira_entity

    def query_comment(self, jira_key):
        url = f'{JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}/comment'
        resp = requests.get(url, headers=JIRA_CONFIG["headers"])
        return resp.json()

    def add_comment(self, jira_key, comment):
        url = f'{JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}/comment'
        body = { "body": comment }
        resp = requests.post(url, headers=JIRA_CONFIG["headers"], json=body)
        return resp.json()

    def modify_assignee(self, jira_key, assignee):
        self.modify_values(jira_key, {"assignee":  {"name": assignee}})

    def modify_values(self, jira_key, fields):
        url = f'{JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}'
        body = {
            "fields": fields
        }
        resp = requests.put(url, headers=JIRA_CONFIG["headers"], json=body)

    def get_transitions(self, jira_key):
        url = f'{JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}/transitions'
        resp = requests.get(url, headers=JIRA_CONFIG["headers"])
        return resp.json()

    def post_transitions(self, jira_key, id):
        url = f'{JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}/transitions'
        resp = requests.post(url, headers=JIRA_CONFIG["headers"], json={
            "transition": {"id": id}
        })

    def delete_jira(self, jira_key):
        url = f'{JIRA_CONFIG["base_url"]}api/2/issue/{jira_key}'
        resp = requests.delete(url, headers=JIRA_CONFIG["headers"])




def handle_one_jira(jira_info):
    issue_links = jira_info.get("fields", {}).get("issuelinks", [])
    if len(issue_links) < 2:
        return

    max_jira = issue_links[0].get("inwardIssue", {}).get("key", "E2E-10000")
    jira_keys = []
    for item in issue_links:
        current_link_key = item.get("inwardIssue", {}).get("key", "E2E-10000")
        current_type = item.get("inwardIssue", {}).get("fields", {}).get("issuetype", {}).get("id")
        if current_link_key == 'E2E-10000' or current_type != "10002":
            continue
        jira_keys.append(current_link_key)
        if int(max_jira[4:]) < int(current_link_key[4:]):
            max_jira = current_link_key
    if len(jira_keys) < 2:
        return

    # 查询过滤出来的 jira_key reporter 需要是megtool
    jql = f""" key in ({','.join(jira_keys)}) and reporter = megtool """
    result = JiraOperate().query_jira(jql, fields=["issuelinks"])
    filter_key = [item.get("key") for item in result]
    if len(filter_key) < 2:
        return
    max_jira = filter_key[0]
    for item in filter_key:
        if int(max_jira[4:]) < int(item[4:]):
            max_jira = item

    print(f"deleted: {max_jira}\t {jira_keys}")
    JiraOperate().delete_jira(max_jira)








if __name__ == '__main__':
    # project = E2E平台项目 AND summary ~ "数据采集" AND created >= 2025-05-01 AND summary !~ CLONE and labels = 场景库
    # jql = """ project = E2E平台项目 AND summary ~ "数据采集" AND created >= 2025-05-01 AND summary !~ CLONE and labels = 场景库  """
    # jql = """ project = E2E平台项目 and type = 故障 and labels = 场景库 order by key """
    # result = JiraOperate().query_jira(jql, fields=["issuelinks"])
    # for item in result:
    #     handle_one_jira(item)

    jql = """ project = E2E平台项目 AND summary ~ "数据采集" AND created >= 2025-05-01 AND type = Test and log链接 is EMPTY  order by key """
    result = JiraOperate().query_jira(jql, fields=["issuelinks"])
    for item in result:
        out_key = item.get("fields", {}).get("issuelinks")[0].get("outwardIssue").get("key")
        bug_field = JiraOperate().query_jira(f"key = {out_key}", fields=["customfield_10211", "customfield_10600", "customfield_13202", "customfield_13201", "customfield_13200", "customfield_13101", "customfield_13100"])
        update_fields = bug_field[0].get("fields")
        JiraOperate().modify_values(item.get("key"), update_fields)
        print(f"updated: {item.get('key')}")

    # jql = "reporter = megtool and type = 故障"
    # result = JiraOperate().query_jira(jql, fields=["issuelinks"])
    # for item in result:
    #     print(item.get("key"))
    #     JiraOperate().delete_jira(item.get("key"))
    #     print(item.get("key"))

