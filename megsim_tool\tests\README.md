
# 测试脚本

本目录包含所有 MegSim API 的测试脚本，用于验证API功能和性能。

## 📋 测试脚本列表

| 测试脚本 | 测试目标 | 功能描述 |
|---------|----------|----------|
| `test_api_variations.py` | 多搜索API | 测试不同搜索参数组合 |
| `test_batch_jira_api.py` | 批量Jira API | 测试批量任务创建 |
| `test_tasks_api.py` | 任务管理API | 测试任务CRUD操作 |
| `test_task_plans_api.py` | 任务计划API | 测试任务计划创建 |

## 🚀 运行测试

### 单个测试
```bash
# 测试多搜索API
python test_api_variations.py

# 测试任务管理API
python test_tasks_api.py

# 测试任务计划API
python test_task_plans_api.py

# 测试批量Jira API
python test_batch_jira_api.py
```

### 批量测试
```bash
# 运行所有测试
for script in test_*.py; do python "$script"; done
```

## 📊 测试结果

测试完成后，详细的测试报告会保存在 `../reports/` 目录中：

- `complete_api_statistics.md` - 完整的API统计报告
- `tasks_api_test_report.md` - 任务API详细测试报告
- `task_plans_api_test_report.md` - 任务计划API测试报告

## ⚙️ 测试配置

所有测试脚本都使用以下默认配置：
- **基础URL**: `https://megsim.mc.machdrive.cn`
- **超时时间**: 30秒
- **请求头**: `Content-Type: application/json`

## 🔍 测试覆盖范围

### 多搜索API测试
- ✅ 无参数查询（获取总记录数）
- ✅ 按车辆ID搜索
- ✅ 按时间范围搜索
- ✅ 按路线搜索
- ✅ 复杂组合查询
- ✅ 分页功能测试

### 任务管理API测试
- ✅ 创建任务（POST）
- ✅ 查询任务列表（GET）
- ✅ 分页和排序
- ✅ 搜索过滤

### 任务计划API测试
- ✅ 创建任务计划（POST）
- ✅ 参数验证
- ✅ 错误处理
- ✅ 重复数据检查

### 批量Jira API测试
- ✅ 批量任务创建（POST）
- ✅ 事件列表处理
- ✅ 用户和标签配置

## 📝 注意事项

1. 测试脚本会向真实的API端点发送请求
2. 某些测试可能会创建实际的数据记录
3. 建议在测试环境中运行，避免影响生产数据
4. 如果遇到网络问题，可以调整超时时间
