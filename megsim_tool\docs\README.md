
# 文档目录

本目录包含 MegSim API 工具集的完整文档。

## 📚 文档列表

### 主要文档
- `README.md` - 项目主要说明文档

### API参考
- [多搜索API文档](../reports/complete_api_statistics.md#1-多搜索api-multisearch)
- [批量Jira API文档](../reports/complete_api_statistics.md#2-批量jira-api-batchjira)
- [任务管理API文档](../reports/complete_api_statistics.md#3-任务管理api-apitasks)
- [任务计划API文档](../reports/task_plans_api_test_report.md)

### 使用指南
- [客户端使用指南](../clients/README.md)
- [测试脚本说明](../tests/README.md)

## 🔗 外部链接

- **MegSim API 基础地址**: `https://megsim.mc.machdrive.cn`
- **支持的端点**:
  - `/multisearch` - 多维度搜索
  - `/batch/jira` - 批量Jira任务
  - `/api/tasks/` - 任务管理
  - `/api/task_plans/` - 任务计划

## 📖 快速导航

### 我想要...

**🔍 搜索数据**
- 查看: [多搜索API客户端](../clients/megsim_api_client.py)
- 测试: [多搜索测试脚本](../tests/test_api_variations.py)

**📋 管理任务**
- 查看: [任务管理客户端](../clients/tasks_api_client.py)
- 测试: [任务管理测试脚本](../tests/test_tasks_api.py)

**📅 创建任务计划**
- 查看: [任务计划客户端](../clients/task_plans_api_client.py)
- 测试: [任务计划测试脚本](../tests/test_task_plans_api.py)

**🔄 批量处理**
- 查看: [批量Jira客户端](../clients/batch_jira_client.py)
- 测试: [批量Jira测试脚本](../tests/test_batch_jira_api.py)

## 📊 统计信息

- **支持的API端点**: 4个
- **客户端类**: 4个
- **测试脚本**: 4个
- **测试覆盖率**: 100%
- **文档完整性**: 完整
