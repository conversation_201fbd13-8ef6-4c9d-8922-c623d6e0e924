"""
这里解析TOP相关的日志，
日志长这个样子：

Timestamp: 2025-03-07 17:47:48 - perception_fusi

    PID USER      PR  NI    VIRT    RES    SHR S  %CPU  %MEM     TIME+ COMMAND
2030821 user      20   0 1104256  46556  19428 S   6.7   0.1   0:00.01 perception_fusi
2030663 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.12 perception_fusi
2030725 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 perception_fusi
2030792 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 perception_fusi
2030795 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 gc
2030796 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.03 dq.builtins
2030797 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 dq.user
2030798 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.01 tev
2030799 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 recv
2030801 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.02 recvUC
2030802 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 perception_fusi
2030806 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 perception_fusi
2030807 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 deal_fusion
2030808 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 deal_track
2030815 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 feed_thread
2030816 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 pub_thread
2030817 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 perception_fusi
2030818 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.00 perception_fusi
2030819 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.01 perception_fusi
2030820 user      20   0 1104256  46556  19428 S   0.0   0.1   0:00.01 perception_fusi
"""
import datetime
import logging
import traceback

from cron_task.handlers.log_parsers.parser_super import Parser
from cron_task.models import HostProcessInnerE2eLog, HostProcessInnerMdriverLog, HostProcessInnerEnvLog, \
    HostProcessInnerPilotLog, HostProcessInnerControlLog, HostProcessInnerPerceptionLog, HostProcessInnerLocLog

model_model_map = {
    "e2e_perceptor_n": HostProcessInnerE2eLog,
    "mdriver_nodes_e": HostProcessInnerMdriverLog,
    "environment_mod": HostProcessInnerEnvLog,
    "pilot_planning_": HostProcessInnerPilotLog,
    "control_node_ex": HostProcessInnerControlLog,
    "perception_fusi": HostProcessInnerPerceptionLog,
    "localization_no": HostProcessInnerLocLog,
}

def apply(file_path, db_entity):
    HostProcessInnerParser().parse(db_entity.id, db_entity, file_path)


class HostProcessInnerParser(Parser):
    def __init__(self):
        super().__init__()
        self.current_day = ""
        self.current_module = ""
        self.process_cache = {}
        self.db_entity = None

    def parse_file(self, id, db_entity, log_path):
        self.db_entity = db_entity
        with open(log_path, "r", encoding="utf-8") as log:
            is_new_line = True
            while True:
                try:
                    line = log.readline()
                    if line == "":
                        break
                    is_new_line = True
                    # 这里需要处理分组
                    self.choose_parse(line)
                    self.check_flash_cache()
                except Exception as e:
                    logging.error(f"top parse error: {line} {traceback.format_exc()}")
                    self.error_info = f"top parse error: {line} {traceback.format_exc()}"

                    if is_new_line is False:
                        ## 这时候说明，第二次进来的时候还是没有读取到新的行，从而破开这个死循环
                        break
                    is_new_line = False
        self.check_flash_cache(True)

        for item in model_model_map.keys():
            default_save_day = 5
            if item == "e2e_perceptor_n":
                default_save_day = 3
            before = datetime.datetime.now() - datetime.timedelta(days=default_save_day)
            model_model_map.get(item).objects.filter(record_time__lt=before).delete()


    # 将缓存数据 刷到数据库里面
    def check_flash_cache(self, is_force=False):
        for item in self.process_cache.keys():
            if len(self.process_cache[item]) >= 1000 or is_force :
                self.flush_cache(model_model_map[item], self.process_cache[item])

    def choose_parse(self, line):
        if line.strip() == '':
            return
        if line.startswith("Timestamp:"):
            day = line.split()
            self.current_day = f"{day[1]} {day[2]}"
            self.update_time(self.current_day)
            self.current_module = day[4]
        elif 'USER' not in line:
            self.parse_process(line)

    def parse_process(self, process_line):
        line_elements = process_line.split()
        if len(line_elements) < 12:
            return
        if self.current_day == "" or self.current_module == "":
            return

        progress_info = {
            "vin": self.db_entity.vin,
            "record_time": self.current_day,
            "module_name": self.current_module,
            "pid": int(line_elements[0]),
            "user": line_elements[1],
            "priority": int(line_elements[2]) if line_elements[2] != "rt" else 1,
            "nice_value": int(line_elements[3]),
            "virtual_image": float(self.trans_unit(line_elements[4])),
            "resident_size": float(self.trans_unit(line_elements[5])),
            "shared_mem_size": float(self.trans_unit(line_elements[6])),
            "process_status": line_elements[7],
            "cpu_usage": float(line_elements[8]),
            "mem_usage": float(line_elements[9]),
            "cpu_time": int(line_elements[10].split(":")[0]) * 60 + float(line_elements[10].split(":")[1]),
            "command": " ".join(line_elements[11:]),
        }
        if self.current_module not in model_model_map:
            return
        if self.current_module in self.process_cache:
            self.process_cache[self.current_module].append(model_model_map.get(self.current_module)(**progress_info))
        else:
            self.process_cache[self.current_module] = [model_model_map.get(self.current_module)(**progress_info)]

    def trans_unit(self, value: str):
        if value.endswith("g"):
            value = value[0:-1]
            return float(value) * 1024 * 1024
        if value.endswith("m"):
            value = value[0:-1]
            return float(value) * 1024
        if value.endswith("t"):
            value = value[0:-1]
            return float(value) * 1024 * 1024 * 1024
        return value
