import os
from atlassian import Jira
from dotenv import load_dotenv
import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_jira_client() -> Jira:
    """获取Jira客户端实例"""
    load_dotenv()
    
    jira_url = os.getenv('JIRA_URL')
    username = os.getenv('JIRA_USERNAME')
    api_token = os.getenv('JIRA_API_TOKEN')
    
    if not all([jira_url, username, api_token]):
        logger.error("请设置JIRA_URL, JIRA_USERNAME和JIRA_API_TOKEN环境变量")
        raise ValueError("缺少必要的Jira配置信息")
    
    try:
        jira = Jira(
            url=jira_url,
            username=username,
            password=api_token
        )
        
        # 替换为正确的连接验证方式
        jira.get_myself()  # 尝试获取当前用户信息来验证连接
        logger.info(f"成功连接到Jira: {jira_url}")
        return jira
    except Exception as e:
        logger.error(f"连接Jira失败: {str(e)}")
        raise

def search_issues_with_label(jira: Jira, label: str, project_key: str, max_results: int = 100) -> List[Dict[str, Any]]:
    """搜索包含特定标签的问题"""
    jql = f'project = {project_key} AND labels = {label}'
    try:
        issues = jira.jql(jql, fields='id,key,labels', max_results=max_results)
        logger.info(f"找到 {len(issues.get('issues', []))} 个包含标签 '{label}' 的问题")
        return issues.get('issues', [])
    except Exception as e:
        logger.error(f"搜索问题失败: {str(e)}")
        return []

def update_issue_labels(jira: Jira, issue_key: str, new_labels: List[str]) -> bool:
    """更新问题的标签"""
    try:
        jira.update_issue_field(issue_key, fields={"labels": new_labels})
        logger.info(f"成功更新问题 {issue_key} 的标签")
        return True
    except Exception as e:
        logger.error(f"更新问题 {issue_key} 标签失败: {str(e)}")
        return False

def bulk_replace_label(jira: Jira, project_key: str, old_label: str, new_label: str) -> None:
    """批量替换项目中所有问题的标签"""
    issues = search_issues_with_label(jira, old_label, project_key)
    
    if not issues:
        logger.info(f"没有找到包含标签 '{old_label}' 的问题")
        return
    
    success_count = 0
    failure_count = 0
    
    for issue in issues:
        issue_key = issue['key']
        current_labels = issue['fields']['labels']
        
        # 替换标签
        updated_labels = [new_label if label == old_label else label for label in current_labels]
        
        # 如果标签不存在则添加
        if new_label not in updated_labels:
            updated_labels.append(new_label)
        
        if update_issue_labels(jira, issue_key, updated_labels):
            success_count += 1
        else:
            failure_count += 1
    
    logger.info(f"批量替换标签完成: 成功={success_count}, 失败={failure_count}")

if __name__ == "__main__":
    try:
        jira = get_jira_client()
        
        # 配置参数
        project_key = input("请输入项目Key: ")
        old_label = input("请输入要替换的旧标签: ")
        new_label = input("请输入新标签: ")
        
        # 执行批量替换
        bulk_replace_label(jira, project_key, old_label, new_label)
        
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")    