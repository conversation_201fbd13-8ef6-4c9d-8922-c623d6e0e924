import requests


class JiraOperate:

    def query_jira(self, jql, fields=None):
        """
        直接在Jira 中查询出来数据，返回body就好
        这里记得配置 jql 的时候需要验证下，不能查出来的数据太多！！！！！
        """
        if fields is None:
            fields = ["key", "summary"]
        page = 0
        jira_entities = []
        while True:
            jira_entity = self.page_jira(page, jql, fields)
            if len(jira_entity) == 0:
                break
            page += 1
            jira_entities.extend(jira_entity)
        return jira_entities

    def page_jira(self, page, jql, fields, count=100):
        jira_entity = []
        start = page * count
        body = {
            "maxResults": count,
            "startAt": start,
            "fields": fields,
            "jql": jql
        }

        resp = requests.post("https://jira.mach-drive-inc.com/rest/api/2/search", headers={
                    "Authorization": "Bearer ODgyNjA1MzkyMzI0Oh9nupQ7Z7gUCmG6kf2zrkoJkQOd",
                    "Content-Type": "application/json"
                }, json=body)
        data = resp.json()
        # 循环所有的问题列表
        for item in data.get('issues', []):
            if "fields" not in item:
                continue
            jira_entity.append(item)
        return jira_entity

def check_clone_issue(jira_issue):
    # 1. 已经clone的issue 没有 comment
    issue_links = jira_issue.get("fields", {}).get("issuelinks", [])
    clone_key = ""
    for link in reversed(issue_links):
        # 已经clone的信息
        if not (link.get('type',{}).get("id") == '10001'):
            continue
        if not (link.get('inwardIssue',{}).get("fields", {}).get("issuetype", {}).get('id') == '10002'):
            continue
        clone_key = link.get('inwardIssue', {}).get('key', "")
        if clone_key:
            break
    if not clone_key:
        return None
    # 判断clone_key 下面没有comments
    url = f'https://jira.mach-drive-inc.com/rest/api/2/issue/{clone_key}/comment'
    resp = requests.get(url, headers={
                    "Authorization": "Bearer ODgyNjA1MzkyMzI0Oh9nupQ7Z7gUCmG6kf2zrkoJkQOd",
                    "Content-Type": "application/json"
                })
    resp = resp.json()
    total = resp.get("total", 0)
    if total == 0:
        return clone_key
    return None

if __name__ == '__main__':
    # 查询需要clone 的issue,
    jql = """ project = E2E平台项目 and issueLinkType = "is cloned by" order by key desc"""
    jql = " key = E2E-9241"
    result = JiraOperate().query_jira(jql, ['key','issuelinks'])
    # 判断是否有 comment，
    for item in result:
        clone_key = check_clone_issue(item)
        if not clone_key:
            print(f"cannot find clone_key: {item.get('key')}, {clone_key}")
            continue
        print(f"clone_key: {clone_key}")
        # 获取到所有的comments
        url = f'https://jira.mach-drive-inc.com/rest/api/2/issue/{item.get("key")}/comment'
        resp = requests.get(url, headers={
            "Authorization": "Bearer ODgyNjA1MzkyMzI0Oh9nupQ7Z7gUCmG6kf2zrkoJkQOd",
            "Content-Type": "application/json"
        })
        resp = resp.json()
        for comment in resp.get('comments', []):
            clone_comment = f'https://jira.mach-drive-inc.com/rest/api/2/issue/{clone_key}/comment'
            resp = requests.post(clone_comment, headers={
                "Authorization": "Bearer ODgyNjA1MzkyMzI0Oh9nupQ7Z7gUCmG6kf2zrkoJkQOd",
                "Content-Type": "application/json"
            }, json={"body": comment.get("body", "")})
            # print(resp.text)

    # 如果没有comment， 则进行clone