# 定时运行的任务
import datetime
import json
import logging
import time
import traceback
from urllib.parse import urlencode

import requests
from django.core.cache import cache

from basic.services import SendMessageApi
from basic.third_apis.feishu_api import FeishuApi
from basic.utils import parse_crontab, DistributedLockThread, <PERSON><PERSON><PERSON><PERSON>ate

from cron_task.models import CronTaskInfo
from importlib import import_module
from cron_task import handlers
from report.feishu.generator.impl.table_handler import TableHandler


class CronTaskThread(DistributedLockThread):
    def __init__(self):
        super().__init__("CronTaskThread", name="CronTaskThread", switchName="cron_task_switch")
        self.handler_cache = {}

    def apply(self):
        # 查询表中需要运行的Task
        task = self.query_need_execute_task()
        if task is None:
            return
        self.release_lock()
        self.need_release = False
        # 运行任务
        # 暂时先不加线程， 一个一个的Handler去运行
        try:
            start = time.time_ns() / 1000000000
            # 如果抛出异常，则记录异常信息
            self.handle_task(task)
            end = time.time_ns() / 1000000000
            CronTaskInfo.objects.filter(id=task.id).update(speed_time=(end-start))
        except Exception as e:
            logging.error(traceback.format_exc())
            CronTaskInfo.objects.filter(id=task.id).update(speed_time=-1, remark=f"{traceback.format_exc()}"[-200:])
            SendMessageApi(f"定时任务运行异常！！{traceback.format_exc()}").send_message("系统异常告警分组")
        finally:
            cache.delete(f"cron_task:services:CronTaskThread:{task.id}")

    def handle_task(self, task):
        task_config = json.loads(task.task_config)
        # 向前提2个小时避免数据没有刷新
        task_config["before_execute"] = task.next_exec_time - datetime.timedelta(hours=4)
        method = task_config.get("method", "")
        handler = import_module(f".{method}", handlers.__name__)
        self.handler_cache[method] = handler
        handler.apply(task_config)

    def query_need_execute_task(self):
        """
        转成ORM查询的方式，
        查询出需要执行的任务，即下次运行时间小于当前时间的任务就是应该运行的任务。
        select * from alarm_jira_task where is_delete = 0 and next_execute_time < current_time

        查询出来的数据，需要及时更新，更新的字段为： next_exec_time ， croniter 使用方法在 tests.py 中有

        返回任务列表
        """
        task = CronTaskInfo.objects.filter(is_delete=0, is_run=1, next_exec_time__lt=datetime.datetime.now()).order_by("next_exec_time").first()
        if task is None:
            return None
        # 判断是否获取到任务的锁， 如果没有获取到，返回为 None

        if cache.set(f"cron_task:services:CronTaskThread:{task.id}", 1, nx=True, timeout=1800):
            next_exec_time = parse_crontab(task.cron_exp, None)
            CronTaskInfo.objects.filter(id=task.id).update(next_exec_time=next_exec_time, update_time=datetime.datetime.now())
            return task
        return None


# clone jira issue and change to test
class CloneIssue:
    def __init__(self, task_info):
        self.task_info = task_info
        self.cookie = {}
        self.atl_token = ""
        self.headers = {"Content-Type": "application/x-www-form-urlencoded"}

    def login_jira(self):
        username = self.task_info.get("username")
        password = self.task_info.get("password")

        url = "https://jira.mach-drive-inc.com/rest/gadget/1.0/login"
        body = {
            "os_username": username,
            "os_password": password,
        }
        # 这个地方不打印任何日志！！！！  防止账号被窃取！！！！！
        result = requests.post(f"{url}", data=body, headers={"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"})
        if result.status_code != 200:
            return False
        for key in result.headers:
            if key != "Set-Cookie":
                continue
            value = result.headers.get(key)
            cookies = value.split(",")
            for item in cookies:
                cookie_item = item.split(";")[0]
                cookie_key_value = cookie_item.split("=")
                self.cookie[cookie_key_value[0].strip()] = cookie_key_value[1].strip()
        if "atlassian.xsrf.token" not in self.cookie:
            return False
        self.atl_token = self.cookie.get("atlassian.xsrf.token")
        return True

    def apply(self):
        # 进行登录
        result = self.login_jira()
        if not result:
            return  ["登录失败，请检查！", ""]
        # 1. 查询需要clone 的issue
        result = {}
        clone_issues = self.query_need_clone_issue(result)
        for item in clone_issues:
            # 2. 拷贝issue
            clone_id, clone_key = self.clone_issue(item, result)
            if not clone_id or not clone_key:
                result[item.get("key")][1] = "未找到Clone 的id，请检查！"
                continue
            # 3. clone 完成之后，更改type
            change_status = self.change_issue_type(clone_id)
            if change_status:
                result[item.get("key")][1] = f"Clone key: {clone_key}"
            else:
                result[item.get("key")][1] = f"Clone key: {clone_key}，修改Type 失败，请检查"
            # 4. 再次校验是否修改成功
            cloned_summary = f'CLONE - {item.get("fields", {}).get("summary")}'
            cloned_issue = self.query_cloned_issue(item.get('key'), cloned_summary)
            if cloned_issue[2] == item.get("fields", {}).get("issuetype", {}).get("id", ""):
                # 如果相等， 那么认为修改失败了
                result[item.get("key")][1] = f"Clone key: {clone_key}，修改Type 失败，请检查"
            # 5. 拷贝comment
            self.sync_issue_comment(item.get('key'), clone_key)
            # 6. 修改assign!!!
            self.modify_assignee(clone_key)

        return list(result.values())

    def modify_assignee(self, clone_key):
        JiraOperate().modify_assignee(clone_key,self.task_info.get("username"))

    def sync_issue_comment(self, source_key, clone_key):
        comments = JiraOperate().query_comment(source_key)
        for comment in comments.get('comments', []):
            resp = JiraOperate().add_comment(clone_key, comment.get("body", "") )
            logging.info(resp)

    def change_issue_type(self, clone_id):
        step1, step2, step3, step4, step5 = 0, 0, 0, 0, 0
        step1 = self.open_modify_type(clone_id)
        if step1 == 200:
            step2 = self.modify_type(clone_id)
        if step2 == 200:
            step3 = self.modify_type_flow(clone_id)
        if step3 == 200:
            step4 = self.modify_type_fields(clone_id)
        if step4 == 200:
            step5 = self.modify_confirm(clone_id)
        if step5 == 200:
            return True
        return False

    def open_modify_type(self, choned_id):
        url = f"https://jira.mach-drive-inc.com/secure/MoveIssue!default.jspa?id={choned_id}"
        result = requests.get(url, headers=self.headers, cookies=self.cookie)
        logging.info(f"{choned_id} modify 0 result: {result.text.replace('\n', '')[0:200]}")
        return result.status_code

    def modify_type(self, cloned_id):
        url = "https://jira.mach-drive-inc.com/secure/MoveIssue.jspa"
        body = {
            "pid": 10500,
            "id": int(cloned_id),
            "issuetype": 10002,
            "Next >>": "Next >>",
            "atl_token": self.atl_token
        }
        result = requests.post(f"{url}?{urlencode(body)}", headers=self.headers, cookies=self.cookie)
        logging.info(f"{cloned_id} modify 1 result: {result.text.replace('\n', '')[0:200]}")
        return result.status_code

    def modify_type_flow(self, cloned_id):
        url = "https://jira.mach-drive-inc.com/secure/MoveIssueUpdateWorkflow.jspa"
        body = {
            "beanTargetStatusId": 10000,
            "id": int(cloned_id),
            "Next >>": "Next >>",
            "atl_token": self.atl_token
        }
        result = requests.post(f"{url}?{urlencode(body)}", headers=self.headers, cookies=self.cookie)
        logging.info(f"{cloned_id}  modify 2 result: {result.text.replace('\n', '')[0:200]}")
        return result.status_code

    def modify_type_fields(self, cloned_id):
        url = "https://jira.mach-drive-inc.com/secure/MoveIssueUpdateFields.jspa"
        body = {
            "customfield_10114": 10000,
            "customfield_10120": "Manual",
            "customfield_10121": "RAVEN_TEST_KEYS",
            "customfield_10117": """{"testId":-1,"fieldValuesBeans":[]}""",
            "customfield_10119": "RAVEN_TEST_SET_KEYS:",
            "raven-test-type-cf-id-manual-test-edit": "customfield_10114",
            "id": int(cloned_id),
            "Next >>": "Next >>",
            "atl_token": self.atl_token
        }

        result = requests.post(f"{url}?{urlencode(body)}", headers=self.headers, cookies=self.cookie)
        logging.info(f"{cloned_id} modify 3 result: {result.text.replace('\n', '')[0:200]}")
        return result.status_code

    def modify_confirm(self, cloned_id):
        url = "https://jira.mach-drive-inc.com/secure/MoveIssueConfirm.jspa"
        body = {
            "confirm": True,
            "id": int(cloned_id),
            "Move": "Move",
            "atl_token": self.atl_token
        }
        result = requests.post(f"{url}?{urlencode(body)}", headers=self.headers, cookies=self.cookie)
        logging.info(f"{cloned_id} modify 4 result: {result.text.replace('\n', '')[0:200]}")
        return result.status_code

    def clone_issue(self, jira_info, log_info):
        clone_id = None
        clone_key = None
        title = jira_info.get("fields", {}).get("summary")
        cloned_summary = f"CLONE - {title}"
        url = "https://jira.mach-drive-inc.com/secure/CloneIssueDetails.jspa"
        body = {
            "inline": True,
            "decorator": "dialog",
            "id": jira_info.get("id"),
            "summary": cloned_summary,
            "cloneAttachments": True,
            "atl_token": self.atl_token
        }
        result = requests.post(url, params=body, headers=self.headers, cookies=self.cookie)
        if result.status_code != 200:
            log_info[jira_info.get("key")][1] = "Clone 失败！"
            return clone_id, clone_key
        # Clone 完成之后，查一下 刚Clone 的id 和 key
        clone_id, clone_key, clone_type = self.query_cloned_issue(jira_info.get("key", ""), cloned_summary)
        if clone_type == jira_info.get("fields", {}).get("issuetype", {}).get("id", ""):
            return clone_id, clone_key
        return None, None


    def query_cloned_issue(self, jira_key, summary):
        jql = f"key = {jira_key}"
        data = JiraOperate().query_jira(jql=jql, fields=["key", "issuelinks"])
        issue_link = data[0].get("fields", {}).get("issuelinks", [])
        if issue_link is None or len(issue_link) == 0:
            return None, None, None

        # 取key 最大的那个。刚copy 完成，那么最大的那个一定是！
        link = None
        max_key = None
        for item in issue_link:
            current_key = item.get("inwardIssue", {}).get("key", "")
            if current_key == "":
                continue
            if item.get("type", {}).get("id") == "10001":
                if max_key is None:
                    link = item
                    max_key = current_key
                    continue
                if int(current_key.split("-")[1]) > int(max_key.split("-")[1]):
                    max_key = current_key
                    link = item

        if link is None:
            return None, None, None

        if (link.get("type", {}).get("id") == "10001" and
                link.get("inwardIssue", {}).get("fields", {}).get("summary", "") == summary):
            cloned_type = link.get("inwardIssue", {}).get("fields", {}).get("issuetype", {}).get("id", "")
            return link.get("inwardIssue", {}).get("id"), link.get("inwardIssue", {}).get("key"), cloned_type
        return None, None, None

    def query_need_clone_issue(self, log_info):
        # 查询需要clone 的issue
        jql = self.task_info.get("jql")
        if not jql:
            return []
        clone_issues = JiraOperate().query_jira(jql=jql, fields=["key", "summary", "issuelinks", "issuetype"])
        result = []
        for item in clone_issues:
            log_info[item.get("key")] = [item.get("key"), ""]
            if self.check_need_clone(item):
                result.append(item)
            else:
                log_info[item.get("key")][1] = "Clone 条件不满足，可能已经Clone，请检查!"
        return result

    def check_need_clone(self, issue):
        summary = issue.get("fields", {}).get("summary", "")
        if summary.startswith("CLONE -"):
            return False
        issue_links = issue.get("fields", {}).get("issuelinks", [])
        for item in issue_links:
            # 如果已经存在一个类型为 Test 的Clone issue， 那么认为已经Clone过了
            if item.get("type", {}).get("id") == "10001" and item.get("inwardIssue", {}).get("fields", {}).get(
                    "issuetype", {}).get("id", "") == "10002":
                return False
        return True


class BatchTagIssue:
    def __init__(self, task_info):
        self.task_info = task_info
        self.insert_labels = set(self.task_info.get("labels", []))

    def apply(self):
        # 查询 Jira 的相关数据
        result = []
        jira_operate = JiraOperate()
        jira_list = jira_operate.query_jira(jql=self.task_info.get("jql"), fields=["key", "labels"])
        for item in jira_list:
            labels = item.get("fields").get("labels")
            labels_set = set(item.get("fields").get("labels"))
            need_add = list(self.insert_labels - labels_set)
            if len(need_add) == 0:
                continue
            need_add.sort()
            labels.extend(need_add)
            jira_operate.modify_values(item.get("key"), {"labels": labels})
            result.append({"key": item.get("key"), "labels": need_add})
        return result

class Jira2FeishuDoc:
    def __init__(self, task_info):
        self.task_info = task_info
        self.feishu_url = task_info.get("feishuUrl")
        self.jql = task_info.get("jql", "")
        self.feishu_api = FeishuApi()

    def apply(self):
        # 查询 Jira 的相关数据
        jira_operate = JiraOperate()
        jira_list = jira_operate.query_jira(jql=self.task_info.get("jql"), fields=["key", "summary"])
        data = [[{"content": item.get("key"), "url": f"https://jira.mach-drive-inc.com/browse/{item.get('key')}"}, item.get("fields", "").get("summary"), "", ""] for item in jira_list]
        self.write_feishu(data)

    def write_feishu(self, data):
        doc_token = self.getObjToken()
        if not doc_token:
            return False
        page_block = self.feishu_api.get_document_blocks({"page_size": 1}).get("data", {}).get("items", [])
        block_info = {"parent_id": page_block[0].get("block_id"), "index": len(page_block[0].get("children")) - 1}
        table_header = ["问题", "描述", "分析", "结论"]
        column_width = [100, 300, 400, 200]
        TableHandler(data, block_info, self.feishu_api,column_width=column_width, table_header=table_header).apply()

    def getObjToken(self):
        url_arr = self.feishu_url.split("/")
        token = url_arr[-1]
        obj_type = url_arr[-2]
        if obj_type == "docx":
            self.feishu_api.set_target_file_token(token)
            return token
        if obj_type == "wiki":
            wiki_node_info = self.feishu_api.wiki_get_node(token, 'wiki')
            if wiki_node_info.get("code", -1) != 0:
                # 没有找到飞书表格直接返回
                return wiki_node_info.get("msg", "")
            # 开始查询bit_table 中的数据
            if  wiki_node_info.get("data", {}).get("node", {}).get("obj_type", "") != "docx":
                return "can't find docx type object!"
            token = wiki_node_info.get("data", {}).get("node", {}).get("obj_token", "")
            self.feishu_api.set_target_file_token(token)
            return token
        return ""
