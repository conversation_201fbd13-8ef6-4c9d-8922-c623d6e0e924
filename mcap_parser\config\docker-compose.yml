version: '3.8'

services:
  # MCAP解析器主服务
  mcap-parser:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: mcap-parser:latest
    container_name: mcap-parser-main
    volumes:
      - ./data:/app/data:ro  # 只读挂载数据目录
      - ./output:/app/output  # 输出目录
      - ./logs:/app/logs  # 日志目录
    environment:
      - MCAP_PARSER_LOG_LEVEL=INFO
      - MCAP_PARSER_OUTPUT_DIR=/app/output
      - MCAP_PARSER_MAX_MEMORY=2048
    networks:
      - mcap-network
    restart: unless-stopped
    command: ["--help"]
    
  # 开发环境服务
  mcap-parser-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    image: mcap-parser:dev
    container_name: mcap-parser-dev
    volumes:
      - .:/app  # 挂载整个项目目录用于开发
      - ./data:/app/data
      - ./output:/app/output
    environment:
      - MCAP_PARSER_LOG_LEVEL=DEBUG
      - PYTHONPATH=/app
    networks:
      - mcap-network
    working_dir: /app
    command: ["/bin/bash"]
    stdin_open: true
    tty: true
    profiles:
      - dev
    
  # 性能监控服务（可选）
  monitoring:
    image: prom/prometheus:latest
    container_name: mcap-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - mcap-network
    restart: unless-stopped
    profiles:
      - monitoring
    
  # Grafana仪表板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: mcap-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - mcap-network
    restart: unless-stopped
    profiles:
      - monitoring
    depends_on:
      - monitoring
    
  # Redis缓存服务（用于大文件处理缓存）
  redis:
    image: redis:7-alpine
    container_name: mcap-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    networks:
      - mcap-network
    restart: unless-stopped
    profiles:
      - cache
    
  # 文件服务器（用于共享MCAP文件）
  file-server:
    image: nginx:alpine
    container_name: mcap-file-server
    ports:
      - "8080:80"
    volumes:
      - ./data:/usr/share/nginx/html/data:ro
      - ./output:/usr/share/nginx/html/output:ro
      - ./monitoring/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - mcap-network
    restart: unless-stopped
    profiles:
      - fileserver
    
  # 测试服务
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    image: mcap-parser:test
    container_name: mcap-test-runner
    volumes:
      - .:/app
      - ./test-results:/app/test-results
    environment:
      - PYTHONPATH=/app
      - PYTEST_ARGS=-v --cov=mcap_parser --cov-report=html:/app/test-results/coverage
    networks:
      - mcap-network
    working_dir: /app
    command: [
      "sh", "-c", 
      "pip install pytest pytest-cov pytest-xdist && pytest tests/ $$PYTEST_ARGS"
    ]
    profiles:
      - test

networks:
  mcap-network:
    driver: bridge
    name: mcap-parser-network

volumes:
  prometheus-data:
    name: mcap-prometheus-data
  grafana-data:
    name: mcap-grafana-data
  redis-data:
    name: mcap-redis-data

# 使用示例:
# 
# 1. 基本使用:
#    docker-compose up mcap-parser
# 
# 2. 开发环境:
#    docker-compose --profile dev up mcap-parser-dev
# 
# 3. 运行测试:
#    docker-compose --profile test up test-runner
# 
# 4. 启动监控:
#    docker-compose --profile monitoring up
# 
# 5. 启动文件服务器:
#    docker-compose --profile fileserver up file-server
# 
# 6. 完整环境:
#    docker-compose --profile dev --profile monitoring --profile fileserver up
# 
# 7. 解析MCAP文件:
#    docker-compose run --rm mcap-parser parse /app/data/your-file.mcap
# 
# 8. 分析MCAP文件:
#    docker-compose run --rm mcap-parser analyze /app/data/your-file.mcap --output /app/output/analysis.json
# 
# 9. 列出支持的消息类型:
#    docker-compose run --rm mcap-parser list