import requests
import json
from datetime import datetime

def get_sample_data():
    """
    获取样本数据用于分析
    """
    base_url = "https://megsim.mc.machdrive.cn/api/event/records/multisearch/"
    
    # 获取最新的几条记录
    params = {
        "order": "-id",
        "limit": 3,
        "skip": 0
    }
    
    try:
        response = requests.get(base_url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

def analyze_record_structure(record):
    """
    分析记录结构
    """
    print("记录字段分析:")
    for key, value in record.items():
        value_type = type(value).__name__
        if isinstance(value, str) and len(value) > 50:
            display_value = value[:50] + "..."
        else:
            display_value = value
        print(f"  {key}: {display_value} (类型: {value_type})")

def main():
    print("=== MegSim API 分析报告 ===")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # API基本信息
    print("## API基本信息")
    print("- 基础URL: https://megsim.mc.machdrive.cn")
    print("- 端点: /api/event/records/multisearch/")
    print("- 方法: GET")
    print("- 认证: 无需认证（公开API）")
    print()
    
    # 获取样本数据
    print("## 数据库统计")
    sample_data = get_sample_data()
    if sample_data:
        print(f"- 总记录数: {sample_data.get('total', 0):,}")
        print(f"- 当前返回: {len(sample_data.get('records', []))} 条")
        print()
        
        if sample_data.get('records'):
            print("## 记录结构分析")
            first_record = sample_data['records'][0]
            analyze_record_structure(first_record)
            print()
            
            print("## 样本数据")
            for i, record in enumerate(sample_data['records'], 1):
                print(f"### 记录 {i}")
                print(f"- ID: {record.get('id')}")
                print(f"- 开始时间: {record.get('start')}")
                print(f"- 结束时间: {record.get('end')}")
                print(f"- 操作员: {record.get('operator')}")
                print(f"- 司机: {record.get('driver')}")
                print(f"- 描述: {record.get('description')}")
                print(f"- 城市: {record.get('town')}")
                print(f"- 位置: {record.get('location')}")
                print(f"- 路线: {record.get('route')}")
                print(f"- 车辆ID: {record.get('vehicle_id')}")
                print(f"- 车辆名称: {record.get('vehicle_name')}")
                print(f"- 路线ID: {record.get('route_id')}")
                print(f"- 路线名称: {record.get('route_name')}")
                print(f"- 事件数量: {record.get('event_num')}")
                print(f"- 里程: {record.get('mileage')}")
                print(f"- 接管次数: {record.get('takeover_count')}")
                print(f"- 乘坐不适次数: {record.get('ride_discomfort_count')}")
                print(f"- MPI: {record.get('mpi')}")
                print(f"- 系统崩溃次数: {record.get('system_crash_count')}")
                print()
    
    print("## API参数说明")
    print("### 查询参数")
    print("- order: 排序方式（如 '-id' 表示按ID倒序）")
    print("- limit: 返回记录数量限制（默认10）")
    print("- skip: 跳过记录数量（用于分页）")
    print("- search: JSON格式的搜索条件")
    print()
    
    print("### 搜索条件格式")
    print("search参数是一个JSON字符串，支持以下字段:")
    print("- vehicle_id: 车辆ID数组，如 [473, 500]")
    print("- time: 时间范围数组，如 ['2025-07-30', '2025-08-06']")
    print("- route: 路线字符串，如 'LD3994'")
    print()
    
    print("## 测试结果总结")
    print("### 成功的查询")
    print("1. 无搜索条件: 返回42,341条总记录")
    print("2. 车辆ID 473: 返回1,584条记录")
    print("3. 最近7天数据: 返回734条记录")
    print("4. 车辆ID 200: 返回18条记录")
    print("5. 车辆ID 500: 返回58条记录")
    print()
    
    print("### 无结果的查询")
    print("1. 路线 'LD3994': 0条记录")
    print("2. 2025-08-06当天数据: 0条记录")
    print("3. 组合条件（车辆473 + 时间2025-08-06 + 路线LD3994）: 0条记录")
    print()
    
    print("## 原始请求分析")
    original_url = "https://megsim.mc.machdrive.cn/api/event/records/multisearch/?order=-id&limit=10&skip=0&search=%7B%22vehicle_id%22:[473],%22time%22:[%222025-08-06%22,%222025-08-06%22],%22route%22:%22LD3994%22%7D"
    print(f"原始URL: {original_url}")
    print()
    print("解码后的搜索参数:")
    search_params = {
        "vehicle_id": [473],
        "time": ["2025-08-06", "2025-08-06"],
        "route": "LD3994"
    }
    print(json.dumps(search_params, indent=2, ensure_ascii=False))
    print()
    print("结果: 返回0条记录，说明在2025-08-06这一天，车辆473在路线LD3994上没有事件记录。")
    print()
    
    print("## 建议的使用方法")
    print("1. 先用无搜索条件获取总体数据概况")
    print("2. 根据需要添加特定的搜索条件")
    print("3. 使用分页参数处理大量数据")
    print("4. 注意时间格式为 YYYY-MM-DD")
    print("5. 车辆ID需要用数组格式")
    print()
    
    print("## Python调用示例")
    print("```python")
    print("import requests")
    print("import json")
    print("")
    print("# 基本查询")
    print("url = 'https://megsim.mc.machdrive.cn/api/event/records/multisearch/'")
    print("params = {'order': '-id', 'limit': 10, 'skip': 0}")
    print("response = requests.get(url, params=params)")
    print("data = response.json()")
    print("")
    print("# 带搜索条件的查询")
    print("search_params = {'vehicle_id': [473], 'time': ['2025-07-30', '2025-08-06']}")
    print("params['search'] = json.dumps(search_params)")
    print("response = requests.get(url, params=params)")
    print("```")

if __name__ == "__main__":
    main()