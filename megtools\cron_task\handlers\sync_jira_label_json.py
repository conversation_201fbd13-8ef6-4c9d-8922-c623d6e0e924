import json

from basic.utils import <PERSON>ra<PERSON><PERSON>ate, JsonUtils


def apply(task_config):
    JiraLabel2ChangJingJson(task_config).apply()

#
class JiraLabel2ChangJingJson:
    def __init__(self, task_config):
        self.task_config = task_config
        self.jql = task_config.get("jql", "")
        self.extra_template = task_config.get("extra_template", {})
        # 13901 场景标签，   13805 是JSON
        self.jira_fields = ["customfield_13901", "customfield_13805"]

    def apply(self):
        self.check()
        # 1. 查询MYSQL的最后更新时间，按照更新时间去查询上次更新之后的问题
        jira_data = self.query_jira(self.task_config.get("before_execute", None))
        for jira_info in jira_data:
            # 获取到jira 之后，再找到link 的issue：
            json_str = jira_info.get("fields", {}).get("customfield_13805", "")
            new_value = self.extra_value(jira_info)
            modify_value = str(json.loads(json.dumps(new_value))).replace("'", '"')
            if len(new_value) > 0 and modify_value != json_str:
                self.update_jira(jira_info.get("key"),{
                    "customfield_13805":modify_value
                })

    def handle_union_value(self, new_value, old_value):
        result = {}
        is_modify = False
        for item in self.extra_template.keys():
            new_arr = new_value.get(item, [])
            old_arr = old_value.get(item, [])
            union = list(set(new_arr) | set(old_arr))
            if len(union) > 0:
                result[item] = union
            if len(union) == len(old_arr):
                continue
            is_modify = True
        if is_modify:
            return result
        return {}

    def extra_value(self, jira_info):
        """
        提取必要的信息
        :param jira_info:
        :return:
        """
        labels = jira_info.get("fields", {}).get("customfield_13901", None)
        result = {}
        for item in self.extra_template.keys():
            current_arr = self.extra_template[item]
            if labels is None:
                intersection = []
            else:
                intersection = list(set(current_arr) & set(labels))
            intersection.sort()
            if len(intersection) > 0:
                result[item] = intersection
        return result


    def update_jira(self, clone_key, compare_result):
        JiraOperate().modify_values(clone_key, compare_result)

    def query_jira(self, last_execute):
        last_update_time = '2021-01-01 00:00'
        if last_execute:
            last_update_time = last_execute.strftime("%Y-%m-%d %H:%M")
        # 添加SQL的片段
        self.jql = self.jql.replace("{{updated}}", last_update_time)
        jira_entities = JiraOperate().query_jira(self.jql, self.jira_fields)
        return jira_entities

    def check(self):
        if not self.jql:
            raise Exception("请配置jql参数")


