import logging

from django.apps import AppConfig


class ReportConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'report'

    def ready(self):
        logging.info("execute once! 22")
        if not self.check_exec_once():
            return
        from report.services import FeishuReportGenerateThread
        FeishuReportGenerateThread().start()


    def check_exec_once(self):
        import os
        run_once = os.environ.get('ReportConfig_RUN_ONCE')
        if run_once is not None:
            return False
        os.environ['ReportConfig_RUN_ONCE'] = 'True'
        return True
