import requests
import json

def test_batch_jira_api():
    """
    测试MegSim批量Jira API
    """
    url = "https://megsim.mc.machdrive.cn/api/event/batch/jira"
    
    # 请求参数
    payload = {
        "event_list": [956580, 956582, 1062195, 956587, 1032283, 1062202, 956592, 956593, 956594, 956595, 956603],
        "user_list": ["nan.xu"],
        "tags": ["TEST"],
        "task_type": ["MR测试"],
        "problem_discovery_stage": "MR合入测试"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print("=== MegSim 批量Jira API 测试 ===")
    print(f"请求URL: {url}")
    print(f"请求方法: POST")
    print("请求头:")
    for key, value in headers.items():
        print(f"  {key}: {value}")
    print()
    print("请求参数:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    try:
        print("发送请求...")
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        print()
        
        # 尝试解析JSON响应
        try:
            response_data = response.json()
            print("响应内容 (JSON):")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            print("响应内容 (文本):")
            print(response.text)
        
        print()
        
        # 分析响应
        if response.status_code == 200:
            print("✅ 请求成功")
        elif response.status_code == 201:
            print("✅ 资源创建成功")
        elif response.status_code == 400:
            print("❌ 请求参数错误")
        elif response.status_code == 401:
            print("❌ 未授权，可能需要登录")
        elif response.status_code == 403:
            print("❌ 权限不足")
        elif response.status_code == 404:
            print("❌ API端点不存在")
        elif response.status_code == 500:
            print("❌ 服务器内部错误")
        else:
            print(f"⚠️ 未知状态码: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    print()
    print("=== 参数分析 ===")
    print(f"事件列表数量: {len(payload['event_list'])}")
    print(f"事件ID范围: {min(payload['event_list'])} - {max(payload['event_list'])}")
    print(f"用户列表: {', '.join(payload['user_list'])}")
    print(f"标签: {', '.join(payload['tags'])}")
    print(f"任务类型: {', '.join(payload['task_type'])}")
    print(f"问题发现阶段: {payload['problem_discovery_stage']}")
    
    print()
    print("=== cURL 命令等效 ===")
    curl_command = f"""curl -X POST "{url}" \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d '{json.dumps(payload, separators=(',', ':'))}'"""    
    print(curl_command)

if __name__ == "__main__":
    test_batch_jira_api()