from basic.services import SendMessageApi
from basic.utils import Jira<PERSON><PERSON>ate, DateUtil


# 根据条件自动对labels 增加标签
# 1. 如果人工改过了， 那不再次打签， 解决方案， 记录最后一个修改的Jira创建时间。
# 2. 根据条件判断需要打什么标签
def apply(task_config):
    AutoJiraLabels(task_config).apply()

class AutoJiraLabels:
    def __init__(self, task_config):
        self.task_config = task_config
        self.jql = task_config.get("jql", "")
        self.trans_config = task_config.get("trans_config", {})
        self.jira_fields = ["labels"]
        self.__init_jira_fields()
        self.jira_value_path = [item['jira_value_path'] for item in self.trans_config]

    def __init_jira_fields(self):
        for item in self.trans_config:
            field = item['jira_field']
            if isinstance(field, list):
                self.jira_fields.extend(field)
            else:
                self.jira_fields.append(field)
        self.jira_fields = list(dict.fromkeys(self.jira_fields))

    def apply(self):
        self.check()
        jira_data = self.query_jira(self.task_config.get("before_execute", None))
        # 判断是否符合条件，
        # 修改Labels
        result = self.auto_labels(jira_data)
        self.send_message(result)

    def auto_labels(self, jira_data):
        result = []
        for item in jira_data:
            update_labels = self.handle_modify_value(item)
            labels = self.get_value_by_path("fields.labels", item)
            minus = set(update_labels) - set(labels)
            labels.extend(list(minus))
            if len(minus) > 0:
                self.update_labels(item.get("key", ""), {"labels": labels})
                result.append(f'{item.get("key")}: {minus}')
        return result

    def update_labels(self, jira_key, update_value):
        JiraOperate().modify_values(jira_key, update_value)

    def handle_modify_value(self, jira_info):
        modify_labels = []
        for config in self.trans_config:
            value = self.get_value_by_path(config.get("jira_value_path"), jira_info)
            if isinstance(value, list):
                # 后面遇到了再写吧
                pass
            elif isinstance(value, str):
                for item in config.get("label_arr", []):
                    if item in value:
                        modify_labels.append(item)
        return modify_labels

    def get_value_by_path(self, jira_path, jira_entity):
        path_arr = jira_path.split(".")
        result = jira_entity
        for item in path_arr:
            if result is None or result == "":
                result = ""
                break
            if item == "[*]" and isinstance(result, list):
                temp = []
                for item in result:
                    temp.append(self.get_value_by_path(jira_path[jira_path.index("[*]")+4:], item, {}))
                result = temp
                break
            elif item.startswith("["):
                # 如果当前条目是数组下标则按照下标进行取值, 下标越界啥的都娶不到。
                if isinstance(result, list) and int(item[1:-1]) < len(result):
                    result = result[int(item[1:-1])]
                else:
                    result = ""
            else:
                result = result.get(item, "")
        return result

    def send_message(self, result):
        open_ids = self.task_config.get("alarm", "")
        if not open_ids or len(result) == 0:
            return
        for item in open_ids.split(";"):
            SendMessageApi(f""" **{self.task_config.get("task_name", "")}**\n {"\n".join(result)} """).send_message(item)

    def query_jira(self, last_execute):
        last_update_time = '2021-01-01 00:00'
        if last_execute:
            last_update_time = last_execute.strftime("%Y-%m-%d %H:%M")
        # 添加SQL的片段
        self.jql = self.jql.replace("{{updated}}", last_update_time)
        jira_entities = JiraOperate().query_jira(self.jql, self.jira_fields)
        return jira_entities

    def check(self):
        if not self.jql:
            raise Exception("请配置jql参数")


