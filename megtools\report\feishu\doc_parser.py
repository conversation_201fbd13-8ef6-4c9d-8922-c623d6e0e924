import logging
import re
import traceback

from basic.third_apis.feishu_api import <PERSON><PERSON>uA<PERSON>
from report.feishu.data_source.image.common_img import CommonImg
from report.feishu.data_source.image.cpu_temperature import CpuTemperatureImg
from report.feishu.data_source.image.fps_delay_img import FpsDelayImg
from report.feishu.data_source.image.fps_monitor_img import FpsMonitorImg
from report.feishu.data_source.image.gpu_info import GpuNvidiaSmi
from report.feishu.data_source.image.gpu_tegrastats import GpuTegrastats
from report.feishu.data_source.image.one_frame_delay_img import OneFrameDelayImg
from report.feishu.data_source.image.top_cpu import TopCpu
from report.feishu.data_source.image.top_memory import TopMemory
from report.feishu.data_source.image.top_process import TopProgress
from report.feishu.data_source.text.fps_avg import FpsAvg
from report.feishu.data_source.text.fps_delay import FpsDelay
from report.feishu.data_source.text.fps_delay_p99 import FpsDelayP99
from report.feishu.data_source.text.fps_p99 import FpsP99
from report.feishu.data_source.text.git_merge_list import GitMergeList
from report.feishu.data_source.text.gpu_info_agg import GpuNvidiaSmiAgg
from report.feishu.data_source.text.gpu_tegrastats_agg import GpuTegrastatsAgg
from report.feishu.data_source.text.one_frame_delay import OneFrameDelay
from report.feishu.data_source.text.report_message import ReportMessage
from report.feishu.data_source.text.top_cpu_agg import TopCpuAgg
from report.feishu.data_source.text.top_memory_agg import TopMemoryAgg
from report.feishu.data_source.text.top_process_agg import TopProgressAgg
from report.feishu.data_source.text.url_path import UrlPath
from report.feishu.data_source.text.vehicle_software_version import VehicleSoftware
from report.feishu.feishu_parser.placeholder_parser import Parser

regexp_str = '\\{\\{[a-z0-9A-Z,_.()=/:%&#? \\[\\]\\-\u4e00-\u9fa5]+\\}\\}'
args_pattern = re.compile(regexp_str)

class FeishuDocParser:
    def __init__(self, doc_id, variables, exec_method=None):
        if exec_method is None:
            exec_method = []
        self.lock_name = f"feishu_doc_parser:{doc_id}"
        self.feishu_api = FeishuApi(doc_id)
        self.replace_block = []
        self.variables = variables
        self.exec_method = exec_method

    def execute(self):
        try:
            # 1. 读取飞书文档的内容
            blocks = self.feishu_api.get_document_all_blocks()
            # 2. 获取到所有需要替换的Block
            self.parse_placeholder(blocks)
            # 3. 根据可替换变量中的信息进行数据替换&生成
            self.rewrite_block()
        except:
            logging.error(f"{traceback.format_exc()}")

    def parse_placeholder(self, blocks):
        block_map = {}
        for item in blocks:
            block_map[item.get("block_id")] = item
            need_replace = Parser(item).apply()
            if need_replace is None:
                continue
            parent_id = need_replace.get("parent_id", "")
            if parent_id and (parent_id in block_map):
                child_arr = block_map.get(parent_id).get("children", [])
                # 放置下标，好删除
                need_replace["index"] = child_arr.index(need_replace.get("block_id"))
                need_replace['children'] = child_arr
            self.replace_block.append(need_replace)

    def rewrite_block(self):
        # 重新生成 block
        for block in self.replace_block:
            # 找到所有的替换变量
            content = block.get("content")
            result = args_pattern.findall(content)
            is_replace = True
            for key in result:
                try:
                    unpack_key = key[2:-2].strip()
                    if len(self.exec_method) > 0 and f"{unpack_key.split('(')[0]}(" not in self.exec_method:
                        is_replace = False
                        break
                    value = self.handle_method(key, block)
                except:
                    value = f"{traceback.format_exc()}"
                    logging.error(f"doc parser error: {traceback.format_exc()}")
                if value is not None:
                    content = content.replace(key, value)
            if is_replace is False:
                # 控制报告部分生成！
                continue
            if content.strip():
                # 如果替换完成文本中还有，则说明需要更新
                body = {"update_text_elements": {"elements": [{"text_run": {"content": content.strip()}}]}}
                self.feishu_api.update_document_block(block.get("block_id"), body)
            else:
                # 如果替换完成之后没有了，则说明需要删除
                self.feishu_api.delete_document_block(block.get("parent_id", ""), {'start_index': block["index"],
                    'end_index': block["index"] + 1})

    def handle_method(self, key, block):
        unpack_key = key[2:-2].strip()
        if unpack_key.endswith(")"):
            # 这里都是创建一个新的block， 然后进行存储即可
            method = self.choose_method(unpack_key, block)
            if method:
                key = method.apply()
        elif unpack_key in self.variables:
            key = self.variables[unpack_key]
        return key if key is not None else ""

    def choose_method(self, key: str, block):
        if key.startswith("top_cpu("):
            return TopCpu(block, key[8:-1], self.variables, self.feishu_api)
        if key.startswith("top_memory("):
            return TopMemory(block, key[11:-1], self.variables, self.feishu_api)
        if key.startswith("top_process("):
            return TopProgress(block, key[12:-1], self.variables, self.feishu_api)
        if key.startswith("gpu_nvidia_smi("):
            return GpuNvidiaSmi(block, key[15:-1], self.variables, self.feishu_api)
        if key.startswith("gpu_tegrastats("):
            return GpuTegrastats(block, key[15:-1], self.variables, self.feishu_api)
        if key.startswith("fps_avg("):
            return FpsAvg(block, key[8:-1], self.variables, self.feishu_api)
        if key.startswith("fps_p99("):
            return FpsP99(block, key[8:-1], self.variables, self.feishu_api)
        if key.startswith("fps_delay("):
            return FpsDelay(block, key[10:-1], self.variables, self.feishu_api)
        if key.startswith("fps_delay_p99("):
            return FpsDelayP99(block, key[14:-1], self.variables, self.feishu_api)
        if key.startswith("fps_monitor("):
            return FpsMonitorImg(block, key[12:-1], self.variables, self.feishu_api)
        if key.startswith("fps_delay_img("):
            return FpsDelayImg(block, key[14:-1], self.variables, self.feishu_api)
        if key.startswith("send_message("):
            return ReportMessage(block, key[13:-1], self.variables, self.feishu_api)
        if key.startswith("one_frame_delay("):
            return OneFrameDelay(block, key[16:-1], self.variables, self.feishu_api)
        if key.startswith("one_frame_delay_img("):
            return OneFrameDelayImg(block, key[20:-1], self.variables, self.feishu_api)
        if key.startswith("cpu_temperature("):
            return CpuTemperatureImg(block, key[16:-1], self.variables, self.feishu_api)
        if key.startswith("vehicle_software("):
            return VehicleSoftware(block, key[17:-1], self.variables, self.feishu_api)
        if key.startswith("common_img("):
            return CommonImg(block, key[11:-1], self.variables, self.feishu_api)
        if key.startswith("top_cpu_agg("):
            return TopCpuAgg(block, key[12:-1], self.variables, self.feishu_api)
        if key.startswith("top_memory_agg("):
            return TopMemoryAgg(block, key[15:-1], self.variables, self.feishu_api)
        if key.startswith("top_process_agg("):
            return TopProgressAgg(block, key[16:-1], self.variables, self.feishu_api)
        if key.startswith("gpu_nvidia_smi_agg("):
            return GpuNvidiaSmiAgg(block, key[19:-1], self.variables, self.feishu_api)
        if key.startswith("gpu_tegrastats_agg("):
            return GpuTegrastatsAgg(block, key[19:-1], self.variables, self.feishu_api)
        if key.startswith("merge_list("):
            return GitMergeList(block, key[11:-1], self.variables, self.feishu_api)
        if key.startswith("url_path("):
            return UrlPath(block, key[9:-1], self.variables, self.feishu_api)
