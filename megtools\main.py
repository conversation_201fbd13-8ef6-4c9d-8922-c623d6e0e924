# import os
#
# env = os.getenv("ENVIRONMENT") if "app" in os.getcwd() else "base"
#
# print(env)
#
# os.system(f"python3 manage.py runserver --insecure 0.0.0.0:8080 --settings=tool_space.settings.{env}")
# # 用runserver得加上 --insecure
#
# # uwsgi --init uwsgi_{env}
#

import subprocess
from os.path import join, abspath, dirname
import os
from loguru import logger

ROOT_DIR_PATH = abspath(dirname(__file__))
env = os.getenv("ENVIRONMENT") if "app" in os.getcwd() else "test"

class CMD:
    def __init__(self, cmd) -> None:
        self.cmd = cmd

    def append(self, cmd):
        self.cmd = f"{self.cmd} && {cmd}"

    def run(self):
        try:
            ret = subprocess.check_output(self.cmd, shell=True).decode()
            logger.debug(f"{self.cmd}: {ret}")
        except Exception as e:
            logger.exception(e)


if __name__ == "__main__":
    print(env)
    cmd = CMD(f"cd {ROOT_DIR_PATH}")
    cmd.append(
        f"export DJANGO_SETTINGS_MODULE=megtool_backend.settings.dev"
    )
    cmd.append(
        f"cp basic/resource/SimHei.ttf /usr/share/fonts/truetype/dejavu/"
    )
    cmd.append(
        f"cp basic/resource/SimHei.ttf /usr/local/lib/python3.12/site-packages/matplotlib/mpl-data/fonts/ttf/"
    )
    # cmd.append(
    #     f"export DJANGO_ALLOW_ASYNC_UNSAFE=True"
    # )
    cmd.append(
        "python3 manage.py runserver 0.0.0.0:8000 --noreload"
    )
    cmd.run()
