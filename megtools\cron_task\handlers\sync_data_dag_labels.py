import logging

import requests

from basic.utils import JiraOperate


def apply(task_config):
    SyncDataDagLabels(task_config).apply()

#
class SyncDataDagLabels(object):
    def __init__(self, task_config):
        self.task_config = task_config
        self.jql = task_config.get("jql", "")
        self.data_dag_url = task_config.get("data_dag_url", "")
        self.jira_fields = ["key", "customfield_13901"]

    def apply(self):
        self.check()
        # 1. 查询MYSQL的最后更新时间，按照更新时间去查询上次更新之后的问题
        jira_data = self.query_jira(self.task_config.get("before_execute", None))
        # 查询jira中对应的标签！！
        data_dag_labels = self.query_data_dag(jira_data)
        for item in jira_data:
            jira_key = item.get("key")
            query_labels = data_dag_labels.get(jira_key)
            if query_labels is None:
                continue
            if isinstance(query_labels, list) and len(query_labels) > 0 and isinstance(query_labels[0], list):
                query_labels = query_labels[0]
            suanfa_labels = item.get("fields", {}).get("customfield_13901", [])

            if suanfa_labels is None:
                suanfa_labels = []
            # 两个作差别， 然后放到
            diff_labels = set(query_labels) - set(suanfa_labels)
            suanfa_labels.extend(list(diff_labels))
            if len(diff_labels) > 0:
                suanfa_labels = [item.replace(" ","") for item in suanfa_labels]
                JiraOperate().modify_values(jira_key, {"customfield_13901": suanfa_labels})


    def query_data_dag(self, jira_data):
        result = {}
        jira_keys = [item.get("key") for item in jira_data]
        batch_size = 100
        current_start = 0
        while True:
            query_keys = jira_keys[current_start: current_start + batch_size]
            current_start = current_start + batch_size
            if len(query_keys) == 0:
                break
            req = {
                "user_name": "megtool",
                "user_id": "megtool",
                "session_id": "megtool",
                "business_type": 2,
                "jira_ids": query_keys,
                "clip_ids": [""],
                "merger_tags_dimensions": 2
            }
            resp = requests.post(self.data_dag_url, json=req, headers={'Content-Type': 'application/json'})
            logging.info(f"data dag: {req} \n{resp.text}")
            resp = resp.json()
            result.update(resp.get("clip_merged_tags", {}))
        return result


    def query_jira(self, last_execute):
        last_update_time = '2021-01-01 00:00'
        if last_execute:
            last_update_time = last_execute.strftime("%Y-%m-%d %H:%M")
        # 添加SQL的片段
        self.jql = self.jql.replace("{{updated}}", last_update_time)
        jira_entities = JiraOperate().query_jira(self.jql, self.jira_fields)
        return jira_entities

    def check(self):
        if not self.jql:
            raise Exception("请配置jql参数")


