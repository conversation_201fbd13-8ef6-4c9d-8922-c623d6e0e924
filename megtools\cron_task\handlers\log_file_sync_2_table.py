import datetime

from basic.utils import OssUtils
from cron_task.models import OssFileList

from django.core.cache import cache

def apply(task_config):
    SyncLogInfo(task_config).apply()

class SyncLogInfo:
    def __init__(self, task_config):
        self.task_config = task_config
        # 需要记录今天和昨天处理的最后一条数据
        self.bucket_name = self.task_config.get("bucket_name")
        self.ossUtil = OssUtils(self.bucket_name)
        self.monitor_config = self.task_config.get("monitor_config")
        self.car_list = self.task_config.get("car_list", [])

    def apply(self):
        today = datetime.datetime.now()
        self.list_objects(today)
        yesterday = today - datetime.timedelta(days=1)
        self.list_objects(yesterday)
        self.ossUtil.close()

    def list_objects(self, time):
        prefix = datetime.datetime.strftime(time, '%Y%m%d')
        objects, marker = self.ossUtil.ls(prefix)

        if len(objects) == 0:
            return
        oss_path_list = {}
        for item in objects:
            parse_type = self.checkObject(item.get("Key"))
            vin = self.getVin(item.get("Key"))
            if not parse_type or not vin:
                continue
            entity = {
                "bucket_name": self.bucket_name,
                "oss_path": item.get("Key"),
                "file_size": item.get("Size"),
                "file_update_time": item.get("LastModified"),
                "vin": vin,
                "parse_type": parse_type[1],
                "current_status": '0',
                "create_time": datetime.datetime.now(),
                "update_time": datetime.datetime.now(),
            }
            oss_path_list[item.get("Key")] = OssFileList(**entity)
        # 校验是否存在，如果存在需要删除掉
        db_result = OssFileList.objects.filter(oss_path__in=list(oss_path_list.keys())).all()
        db_oss_path = [item.oss_path for item in db_result]
        insert_array = []
        for key, value in oss_path_list.items():
            if key in db_oss_path:
                continue
            insert_array.append(value)
        OssFileList.objects.bulk_create(insert_array)

    def checkObject(self, oss_path):
        file_list = oss_path.split("/")
        file_name = file_list[-1]
        for item in self.monitor_config:
            if file_name.startswith(item.get("prefix")):
                return item.get("prefix"), item.get("parse_type")
        return None

    def getVin(self, file_path):
        result = ""
        for item in self.car_list:
            if f"/{item}/" in file_path:
                result = item
                break
        if result.startswith("p") and "/pc/" in file_path:
            # 如果开始是p， 则说明是p177 的项目，需要重新赋值结果
            result = f"{result}-pc"
        return result
