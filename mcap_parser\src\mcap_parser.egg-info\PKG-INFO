Metadata-Version: 2.4
Name: mcap-parser
Version: 3.1.0
Summary: 现代化的MCAP自动驾驶数据解析工具包
Author-email: MCAP SDK Team <<EMAIL>>
Maintainer-email: MCAP SDK Team <<EMAIL>>
License: MIT License
        
        Copyright (c) 2025 车道线离线测试 Team
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.
        
Project-URL: Homepage, https://github.com/mcap-sdk/mcap-parser
Project-URL: Documentation, https://mcap-parser.readthedocs.io
Project-URL: Repository, https://github.com/mcap-sdk/mcap-parser.git
Project-URL: Bug Tracker, https://github.com/mcap-sdk/mcap-parser/issues
Project-URL: Changelog, https://github.com/mcap-sdk/mcap-parser/blob/main/CHANGELOG.md
Keywords: mcap,ros2,autonomous-driving,data-parser,robotics
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: System :: Hardware :: Hardware Drivers
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: mcap-ros2-support>=0.5.0
Requires-Dist: psutil>=5.8.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=22.0.0; extra == "dev"
Requires-Dist: isort>=5.10.0; extra == "dev"
Requires-Dist: flake8>=5.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Provides-Extra: analysis
Requires-Dist: numpy>=1.19.0; extra == "analysis"
Requires-Dist: pandas>=1.3.0; extra == "analysis"
Requires-Dist: matplotlib>=3.5.0; extra == "analysis"
Provides-Extra: visualization
Requires-Dist: plotly>=5.0.0; extra == "visualization"
Requires-Dist: dash>=2.0.0; extra == "visualization"
Provides-Extra: all
Requires-Dist: mcap-parser[analysis,dev,visualization]; extra == "all"
Dynamic: license-file

# MCAP Parser - 现代化自动驾驶数据解析工具包

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Tests](https://github.com/your-org/mcap-parser/workflows/Tests/badge.svg)](https://github.com/your-org/mcap-parser/actions)

一个高性能、现代化的MCAP（Message Capture and Playback）文件解析工具包，专为自动驾驶数据分析而设计。

## 🚀 特性

- **高性能解析**: 优化的MCAP文件读取和处理性能
- **灵活的数据结构**: 支持多种消息类型和数据格式
- **现代化架构**: 基于Python 3.8+的模块化设计
- **丰富的工具集**: 命令行工具和编程接口
- **完整的测试覆盖**: 单元测试、集成测试和性能测试
- **详细的文档**: API文档和使用指南

## 📦 安装

```bash
# 从源码安装
git clone https://github.com/your-org/mcap-parser.git
cd mcap-parser
pip install -e .

# 或使用pip安装（发布后）
pip install mcap-parser
```

## 🔧 快速开始

### 命令行使用

```bash
# 解析MCAP文件
mcap-parser your_file.mcap

# 分析数据
mcap-analyze your_file.mcap --output analysis.json

# 使用工具集
mcap-tools --help
```

### 编程接口

```python
from mcap_parser import McapAutoDriveSDK, quick_analyze

# 快速分析
result = quick_analyze('your_file.mcap')
print(f"处理了 {result.message_count} 条消息")

# 使用SDK
sdk = McapAutoDriveSDK()
with sdk.open_file('your_file.mcap') as reader:
    for message in reader.iter_messages():
        # 处理消息
        pass
```

## 📁 项目结构

```
mcap-parser/
├── src/mcap_parser/          # 主要源代码
│   ├── cli/                  # 命令行接口
│   ├── core/                 # 核心功能
│   ├── parsers/              # 解析器模块
│   ├── tools/                # 工具模块
│   └── utils/                # 工具函数
├── tests/                    # 测试文件
├── docs/                     # 文档
├── examples/                 # 示例代码
├── tools/                    # 独立工具脚本
├── config/                   # 配置文件
└── scripts/                  # 构建和部署脚本
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_core.py

# 生成覆盖率报告
pytest --cov=mcap_parser --cov-report=html
```

## 📚 文档

- [API文档](docs/api/)
- [使用指南](docs/guides/)
- [示例代码](docs/examples/)
- [快速开始](docs/QUICK_START.md)

## 🤝 贡献

欢迎贡献代码！请查看我们的[贡献指南](CONTRIBUTING.md)。

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

## 🔗 相关链接

- [MCAP格式规范](https://mcap.dev/)
- [ROS2文档](https://docs.ros.org/)
- [项目主页](https://github.com/your-org/mcap-parser)

## 📞 支持

如有问题或建议，请[提交Issue](https://github.com/your-org/mcap-parser/issues)。
