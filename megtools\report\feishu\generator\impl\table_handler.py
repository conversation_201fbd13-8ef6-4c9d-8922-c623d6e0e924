import logging
from urllib.parse import quote
import time

from report.feishu.generator.handler import Handler


class TableHandler(Handler):

    def __init__(self, data, block, feishu_api, column_width=None, table_header=None, merge_cell_col=None, merge_cell_row=None):
        super().__init__(data, block, feishu_api)
        if column_width is None:
            column_width = []
        if table_header is None:
            table_header = []
        self.update_cache = []
        self.column_width = column_width
        self.table_header = table_header
        self.header_row = 1 if len(self.table_header) > 0 else 0
        self.merge_cell_col = [] if merge_cell_col is None else merge_cell_col
        self.merge_cell_row = [] if merge_cell_row is None else merge_cell_row

    def apply(self):
        table_cells, block_id = self.create_table()
        # 此时拿到所有的表格的block ids，对其进行数据填充
        for idx in range(0, len(self.table_header)):
            self.update_cache.append({
                "block_id": table_cells[idx].get("children")[0],
                "update_text_elements": {"elements": [{"text_run": {"content": self.table_header[idx]}}]}
            })
        for idx in range(len(self.table_header), len(table_cells)):
            row = idx // len(self.data[0])
            col = idx % len(self.data[0])
            val = self.data[row - self.header_row][col]
            if col in self.merge_cell_col:
                col_arr = [row[col] for row in self.data]
                ranges = self.find_consecutive_ranges(col_arr)
                for range_idx in ranges:
                    if (row - self.header_row) == range_idx[0]:
                        break
                    if (row - self.header_row) > range_idx[0] and (row - self.header_row) <= range_idx[1]:
                        val = ""
            if isinstance(val, dict) and 'content' in val and 'url' in val:
                content = {"elements": [{"text_run": {
                            "content": val['content'],
                            "text_element_style": {
                                "link": {
                                    "url": quote(val['url'], safe='')
                                }
                            }
                }}]}
            elif isinstance(val, dict) and 'content' in val and 'style' in val:

                content = {"elements": [{"text_run": {
                    "content": val['content'],
                    "text_element_style": val['style']
                }}]}
            elif isinstance(val,list):
                content_data = []
                for obj in val:
                    content_data.append({"text_run": {
                            "content": obj.get('content',''),
                            "text_element_style": {
                                "link": {
                                    "url": quote(obj.get('url',''), safe='')
                                }
                            }
                    }})
                    content_data.append({"text_run": {"content": "  "}})
                if len(content_data) == 0:
                    continue
                content = {"elements": content_data}
            else:
                content = {"elements": [{"text_run": {"content": val}}]}
            self.update_cache.append({
                "block_id": table_cells[idx].get("children")[0],
                "update_text_elements": content})
            if len(self.update_cache) >= 150:
                self.flush_cache()
        self.flush_cache()
        # 合并单元格
        self.merge_cell_by_col(block_id)
        return block_id

    def flush_cache(self):
        if len(self.update_cache) == 0:
            return

        self.feishu_api.bulk_update_document_block({"requests": self.update_cache})
        self.update_cache.clear()
        time.sleep(0.33)

    def create_table(self):
        """
        生成一个空的表格，准备数据填充！
        """
        rows = len(self.data) + self.header_row
        cols = len(self.data[0])
        gen_rows = rows if rows <= 9 else 9
        gen_cols = cols if cols <= 9 else 9
        block = [
            {
                "block_type": 31,
                "table": {
                    "property": {
                        "row_size": gen_rows,
                        "column_size": gen_cols,
                        "header_row": self.header_row > 0
                    }
                }
            }
        ]
        if len(self.column_width) > 0:
            block[0]["table"]["property"]["column_width"] = self.column_width

        parent_id = self.block.get("parent_id")
        data = self.feishu_api.create_document_block(parent_id, {
            'index': self.block.get("index", 0) + 1,
            'children': block
        })
        block_id = data.get('data', {}).get("children")[0].get('block_id', "")
        self.append_row_col("insert_table_row", "row_index", rows - gen_rows, block_id)
        self.append_row_col("insert_table_column", "column_index", cols - gen_cols, block_id)

        # # 获取parent_id 下所有的子块
        return self.feishu_api.get_block_all_blocks(block_id), block_id

    def append_row_col(self, type, index_type, size, block_id):
        """
        生成行列，  行：row_index  列：col_index
        """
        if size <= 0:
            return
        data = None
        for i in range(0, size):
            body = {type: {index_type: -1}}
            data = self.feishu_api.update_document_block(block_id, body)
        return data

    def merge_cell_by_col(self, block_id):
        for col_idx in self.merge_cell_col:
            # 获取指定列的数据
            col_arr = [row[col_idx] for row in self.data]
            ranges = self.find_consecutive_ranges(col_arr)
            for range in ranges:
                if range[0] == range[1]:
                    continue
                self.feishu_api.update_document_block(block_id, {
                    'merge_table_cells': {
                        'row_start_index': range[0]+1,
                        'row_end_index': range[1] + 2,
                        'column_start_index': col_idx,
                        'column_end_index': col_idx + 1
                    }
                })

    def find_consecutive_ranges(self,arr):
        if not arr:
            return []

        ranges = []
        start_index = 0
        for i in range(1, len(arr)):
            if arr[i] != arr[i - 1]:
                if i - start_index > 1:
                    ranges.append((start_index, i - 1))
                start_index = i
        if len(arr) - start_index > 1:
            ranges.append((start_index, len(arr) - 1))
        return ranges

