#!/usr/bin/env python3
"""
直接使用SDK的车道线解析示例
最简洁的车道线数据解析方法
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

# 使用统一的SDK入口
try:
    import src
    from src import create_sdk, MessageData
    from src.utils.sdk_logger import log_error, log_info, log_success
    
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保在项目根目录运行此脚本")
    print("或尝试使用命令行工具: python mcap_parser_cli.py parse data.mcap --types LaneArrayv2")
    sys.exit(1)


def parse_lane_data(mcap_file: str, max_messages: int = 5):
    """解析车道线数据"""
    log_info(f"🚗 开始解析车道线数据: {mcap_file}")
    
    try:
        # 创建SDK实例，禁用快速模式避免创建索引
        sdk = create_sdk(verbose=False)
        # 确保不使用快速模式
        sdk.enable_fast_mode = False
        
        # 解析车道线数据
        count = 0
        for msg in sdk.stream_data(mcap_file, ["LaneArrayv2"], max_messages=max_messages):
            count += 1
            log_info(f"📍 车道线消息 {count}:")
            log_info(f"   时间: {msg.timestamp:.3f}s")
            log_info(f"   话题: {msg.topic}")
            log_info(f"   类型: {msg.message_type}")
            
            # 解析车道线数据
            if hasattr(msg.data, 'lane_array'):
                lanes = msg.data.lane_array
                log_info(f"   🛣️  检测到 {len(lanes)} 条车道线")
                
                # 显示前2条车道线
                for lane in lanes[:2]:
                    if hasattr(lane, 'waypoints') and lane.waypoints:
                        log_info(f"      车道{lane.lane_id}: {len(lane.waypoints)}点, 置信度{lane.confidence:.2f}")
                        
                        # 显示起点和终点坐标
                        if lane.waypoints:
                            start = lane.waypoints[0]
                            end = lane.waypoints[-1]
                            log_info(f"        起点: ({start.x:.1f}, {start.y:.1f}, {start.z:.1f})")
                            log_info(f"        终点: ({end.x:.1f}, {end.y:.1f}, {end.z:.1f})")
                            
                        # 显示车道属性
                        if hasattr(lane, 'lane_property'):
                            log_info(f"        属性: {lane.lane_property}")
            else:
                log_info("   ⚠️  无法解析车道线数据结构")
        
        log_success(f" 解析完成，共处理 {count} 条车道线消息")
        
    except Exception as e:
        log_error(f" 解析失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    if len(sys.argv) < 2:
        log_info("🚗 车道线数据解析示例")
        log_info(f"用法:")
        log_info("   python examples/direct_lane_parser.py <mcap_file> [max_messages]")
        log_info(f"示例:")
        log_info("   python examples/direct_lane_parser.py data.mcap 10")
        log_info(f"说明:")
        log_info("   - 解析MCAP文件中的LaneArrayv2消息")
        log_info("   - 显示车道线的关键信息")
        log_info("   - 包括坐标点、置信度、属性等")
        return
    
    mcap_file = sys.argv[1]
    max_messages = int(sys.argv[2]) if len(sys.argv) > 2 else 5
    
    # 检查文件
    if not os.path.exists(mcap_file):
        log_error(f" 文件不存在: {mcap_file}")
        return
    
    # 解析数据
    parse_lane_data(mcap_file, max_messages)


if __name__ == "__main__":
    main()
