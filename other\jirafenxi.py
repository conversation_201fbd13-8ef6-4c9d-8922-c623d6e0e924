import os
import requests
import json
import re
from collections import Counter
from datetime import datetime
from jira import JIRA

# ===== DeepSeek API 配置 =====
DEEPSEEK_API_KEY = "***********************************"  # 请替换为您的实际API密钥
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"
# =============================

# ===== Jira 配置 =====
JIRA_URL = "https://jira.mach-drive-inc.com"
JIRA_USER = "t-majingmiao"
JIRA_PASSWORD = "mach.1234"
# =====================

# 目标 JQL 查询
JQL = (
    'labels = MR_3291 '
    'and local-ads-no-lidar is not empty '
    'and labels != "非问题" '
    'and issueFunction in hasComments() '
    'and labels != "非车道线问题"'
)

def get_jira_issues():
    """从 Jira 获取所有匹配的问题（包含key和summary）"""
    try:
        # 连接 Jira
        jira = JIRA(
            server=JIRA_URL,
            basic_auth=(JIRA_USER, JIRA_PASSWORD),
            options={'verify': True},
            timeout=20
        )
        
        print(f"正在执行 JQL 查询: {JQL}")
        
        # 获取所有匹配的问题
        all_issues = []
        start_at = 0
        max_results = 100
        total = None
        
        while total is None or start_at < total:
            issues = jira.search_issues(
                JQL,
                startAt=start_at,
                maxResults=max_results,
                fields="summary"  # 只获取 summary 字段
            )
            all_issues.extend(issues)
            if total is None:
                total = issues.total
            start_at += len(issues)
            print(f"已获取 {len(all_issues)}/{total} 个问题")
        
        print(f"\n找到 {len(all_issues)} 个匹配问题")
        
        if len(all_issues) == 0:
            print("没有匹配的问题")
            return []
        
        # 提取所有 issue 对象（包含key和summary）
        return all_issues
        
    except Exception as e:
        print(f"获取问题失败: {str(e)}")
        return []

def print_issues(issues):
    """打印所有问题（包含链接）"""
    if not issues:
        print("没有可用的问题")
        return
    
    print("\n" + "="*80)
    print("所有问题列表".center(80))
    print("="*80)
    for i, issue in enumerate(issues, 1):
        issue_link = f"{JIRA_URL}/browse/{issue.key}"
        print(f"{i}. [{issue.key}] {issue.fields.summary}")
        print(f"   链接: {issue_link}")
    print("="*80)

def generate_jira_links_table(issues):
    """生成包含所有Jira链接的Markdown表格"""
    if not issues:
        return ""
    
    table = "## 相关Jira问题列表\n\n"
    table += "| 序号 | 问题Key | 摘要 | 链接 |\n"
    table += "|------|---------|------|------|\n"
    
    for i, issue in enumerate(issues, 1):
        issue_link = f"{JIRA_URL}/browse/{issue.key}"
        # 缩短摘要以避免表格过长
        summary = issue.fields.summary
        if len(summary) > 60:
            summary = summary[:57] + "..."
        
        table += f"| {i} | {issue.key} | {summary} | [查看]({issue_link}) |\n"
    
    return table

def deep_insight_analysis_with_links(issues):
    """使用 DeepSeek API 进行深度思考分析并包含Jira链接"""
    if not issues:
        print("没有可用数据进行分析")
        return
    
    # 提取摘要列表
    summaries = [issue.fields.summary for issue in issues]
    
    # 准备系统提示 - 专注于深度思考
    system_prompt = f"""
    你是一位自动测试专家，拥有15年行业经验。请对以下问题摘要进行思考分析，
    超越表面现象，挖掘根本原因和系统性问题。你的分析应包含：
    
    1. 统计问题数量：分析问题的数量和分布
    2. 系统关联性：揭示不同问题之间的内在联系和模式
    3.top问题总结
    4. 风险评估：预测未解决问题的潜在后果

    
    报告中提到的具体问题请使用以下格式引用：
    [问题序号] (例如: [1], [5], [12])
    
    以下是问题列表（每个问题前有序号）：
    """
    
    # 准备用户输入 - 包含问题序号
    user_input = ""
    for i, issue in enumerate(issues, 1):
        user_input += f"{i}. {issue.fields.summary}\n"
    
    user_input += "\n请进行深度技术思考分析，输出不少于1500字的专业报告，并在适当位置引用问题序号。"
    
    # 调用 DeepSeek API
    headers = {
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input}
        ],
        "temperature": 0.2,  # 降低随机性，提高专注度
        "max_tokens": 4000    # 增加输出长度
    }
    
    print("\n正在进行深度思考分析... (这可能需要一些时间)")
    
    try:
        response = requests.post(DEEPSEEK_API_URL, headers=headers, json=payload)
        response.raise_for_status()
        
        result = response.json()
        analysis = result["choices"][0]["message"]["content"]
        
        # 添加Jira链接表格
        links_table = generate_jira_links_table(issues)
        full_report = f"{analysis}\n\n{links_table}"
        
        # 打印分析报告
        print("\n" + "="*80)
        print("深度思考分析报告".center(80))
        print("="*80)
        print(full_report)
        print("="*80)
        
        # 保存报告到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"deep_insight_analysis_{timestamp}.md"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(f"# 自动驾驶问题深度思考分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**分析问题数量**: {len(issues)}\n\n")
            f.write(full_report)
        print(f"\n深度分析报告已保存到: {filename}")
        
        # 在报告中添加问题引用说明
        with open(filename, "a", encoding="utf-8") as f:
            f.write("\n\n> 注：报告中提到的 `[序号]` 对应上方表格中的问题序号，点击链接可查看详细问题描述")
        
    except Exception as e:
        print(f"DeepSeek API 调用失败: {str(e)}")
        print("无法进行深度分析")

def generate_local_report_with_links(issues):
    """本地生成的报告包含Jira链接"""
    if not issues:
        return
    
    print("\n" + "="*80)
    print("本地深度思考分析".center(80))
    print("="*80)
    
    # 生成问题链接表格
    print(generate_jira_links_table(issues))
    
    # 简单分析
    summaries = [issue.fields.summary for issue in issues]
    
    print("\n## 问题分类统计")
    categories = {
        "定位": ["定位", "位置", "坐标", "location", "position"],
        "感知": ["感知", "识别", "检测", "perception", "detection"],
        "规划": ["规划", "路径", "轨迹", "planning", "path"],
        "控制": ["控制", "转向", "刹车", "control", "steering"],
        "地图": ["地图", "车道线", "路标", "map", "lane"],
        "通信": ["通信", "连接", "延迟", "communication", "latency"],
        "数据": ["数据", "传输", "记录", "data", "logging"],
        "系统": ["系统", "崩溃", "重启", "system", "crash"]
    }
    
    category_counts = {category: 0 for category in categories}
    
    for summary in summaries:
        for category, keywords in categories.items():
            if any(keyword in summary for keyword in keywords):
                category_counts[category] += 1
                break
    
    for category, count in category_counts.items():
        if count > 0:
            percentage = count / len(summaries) * 100
            print(f"- {category}: {count} 个问题 ({percentage:.1f}%)")
    
    print("\n## 代表性问题")
    for i, issue in enumerate(issues[:3], 1):
        issue_link = f"{JIRA_URL}/browse/{issue.key}"
        print(f"{i}. [{issue.key}]({issue_link}) - {issue.fields.summary}")
    
    print("="*80)

if __name__ == "__main__":
    # 获取所有问题（包含key和summary）
    issues = get_jira_issues()
    
    # 打印所有问题（包含链接）
    print_issues(issues)
    
    if issues:
        # 使用 DeepSeek API 进行深度思考分析（包含链接）
        deep_insight_analysis_with_links(issues)
    else:
        print("没有可用数据进行分析")