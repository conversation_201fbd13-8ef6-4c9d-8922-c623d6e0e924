import os
import requests
import json
import configparser
from datetime import datetime
from jira import JIRA
import lark_oapi as lark
from lark_oapi.api.drive.v1 import *

# 读取配置文件
config = configparser.ConfigParser()
config.read('config.ini', encoding='utf-8')

# ===== 从配置文件读取设置 =====
DEEPSEEK_API_KEY = config.get('DeepSeek', 'api_key', fallback='')
DEEPSEEK_API_URL = config.get('DeepSeek', 'api_url', fallback='https://api.deepseek.com/v1/chat/completions')

JIRA_URL = config.get('Jira', 'url', fallback='')
JIRA_USER = config.get('Jira', 'user', fallback='')
JIRA_PASSWORD = config.get('Jira', 'password', fallback='')
BASE_JQL = config.get('Jira', 'jql', fallback='')

# 飞书相关配置
FEISHU_APP_ID = config.get('Feishu', 'app_id', fallback='')
FEISHU_APP_SECRET = config.get('Feishu', 'app_secret', fallback='')
FEISHU_FOLDER_TOKEN = config.get('Feishu', 'folder_token', fallback='')

# 从配置文件中读取系统提示
SYSTEM_PROMPT = config.get('Analysis', 'system_prompt', fallback="")
# =============================
def create_config_file():
    """创建默认配置文件"""
    config = configparser.ConfigParser()
    
    # DeepSeek 配置
    config['DeepSeek'] = {
        'api_key': 'your_deepseek_api_key_here',
        'api_url': 'https://api.deepseek.com/v1/chat/completions'
    }
    
    # Jira 配置
    config['Jira'] = {
        'url': 'https://your-jira-instance.com',
        'user': 'your_jira_username',
        'password': 'your_jira_password',
        'jql': 'project = YOURPROJECT AND status = "To Do"'
    }
    
    # Feishu 配置
    config['Feishu'] = {
        'app_id': 'your_feishu_app_id',
        'app_secret': 'your_feishu_app_secret',
        'folder_token': 'your_feishu_folder_token',
    }
    
    # Analysis 配置
    config['Analysis'] = {
        'system_prompt': """请基于以下要求进行深度分析：
1. 问题分类统计：统计各分类下的问题数量，计算占比（数量 / 总问题数），用数据呈现问题分布规律，对比不同分类的规模差异。并贴上对应的问题链接。
2. top问题总结：识别最常见（数量最多）、最关键（影响最大/关联核心流程）的问题
需基于具体问题集合展开，确保分类合理、统计准确、总结聚焦，输出清晰可落地的分析结论。"""
    }
    
    with open('config.ini', 'w', encoding='utf-8') as configfile:
        config.write(configfile)
    print("配置文件 config.ini 已创建，请修改其中的配置项")
def delete_feishu_file(client,file_token):

    # 构造请求对象
    request: DeleteFileRequest = DeleteFileRequest.builder() \
        .file_token(file_token) \
        .type("file") \
        .build()

    # 发起请求
    response: DeleteFileResponse = client.drive.v1.file.delete(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.drive.v1.file.delete failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return False

    # 处理业务结果
    lark.logger.info(lark.JSON.marshal(response.data, indent=4))
    return True

def upload_and_convert_to_feishu_doc(file_path):
    """
    上传Markdown文件到飞书并转换为云文档，转换成功后删除本地文件
    返回元组：(是否成功, 转换后的文档token)
    """
    if not FEISHU_APP_ID or not FEISHU_APP_SECRET or not FEISHU_FOLDER_TOKEN:
        print("飞书配置不完整，跳过上传和转换")
        return (False, None)
    
    try:
        # 创建client
        client = lark.Client.builder() \
            .app_id(FEISHU_APP_ID) \
            .app_secret(FEISHU_APP_SECRET) \
            .log_level(lark.LogLevel.INFO) \
            .build()
        
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        # 第一步：上传文件
        with open(file_path, "rb") as file:
            # 构造上传请求
            upload_request: UploadAllMediaRequest = UploadAllMediaRequest.builder() \
                .request_body(UploadAllMediaRequestBody.builder()
                    .file_name(file_name)
                    .parent_type("explorer")  # 云空间类型
                    .parent_node(FEISHU_FOLDER_TOKEN)  # 文件夹token
                    .size(str(file_size))
                    .extra(json.dumps({
                        "file_extension": "md",
                        "obj_type": "docx"  # 标记为文档类型
                    }))
                    .file(file)
                    .build()) \
                .build()
            
            print(f"正在上传文件到飞书: {file_name} ({file_size}字节)")
            
            # 发起上传请求
            upload_response: UploadAllMediaResponse = client.drive.v1.media.upload_all(upload_request)
            
            # 处理上传失败
            if not upload_response.success():
                print(f"文件上传失败: code={upload_response.code}, msg={upload_response.msg}, log_id={upload_response.get_log_id()}")
                return (False, None)
            
            # 获取上传文件的token
            file_token = upload_response.data.file_token
            print(f"文件上传成功! 文件token: {file_token}")
        
        # 第二步：创建转换任务
        # 从文件名中提取日期部分
        file_name = os.path.basename(file_path)
        date_str = file_name.split("_")[2]  # 提取日期部分
        doc_name = f"{date_str}_MR测试报告"
        
        # 构造转换请求
        convert_request: CreateImportTaskRequest = CreateImportTaskRequest.builder() \
            .request_body(ImportTask.builder()
                .file_extension("md")
                .file_token(file_token)
                .type("docx")  # 转换为飞书文档
                .file_name(doc_name)  # 转换后的文档名称
                .point(ImportTaskMountPoint.builder()
                    .mount_type(1)  # 挂载到云空间目录
                    .mount_key(FEISHU_FOLDER_TOKEN)  # 目标文件夹token
                    .build())
                .build()) \
            .build()
        
        print(f"正在创建转换任务: {doc_name}")
        
        # 发起转换请求
        convert_response: CreateImportTaskResponse = client.drive.v1.import_task.create(convert_request)
        
        # 处理转换失败
        if not convert_response.success():
            print(f"创建转换任务失败: code={convert_response.code}, msg={convert_response.msg}, log_id={convert_response.get_log_id()}")
            return (False, file_token)
        
        # 获取转换任务信息
        ticket = convert_response.data.ticket
        print(f"转换任务创建成功! 任务ID: {ticket}")
        # 第三步：删除本地文件
        try:
            os.remove(file_path)
            print(f"已成功删除本地文件: {file_path}")
        except Exception as e:
            print(f"删除本地文件失败: {str(e)}")
        
        # 第四步：删除飞书中的原始文件
        if file_token:
            delete_success = delete_feishu_file(client,file_token)
            if not delete_success:
                print("警告：飞书原始文件删除失败，请手动清理")
        
        return (True, file_token)
            
    except Exception as e:
        print(f"上传或转换文件时出错: {str(e)}")
        return (False, None)

def get_jira_issues():
    """从 Jira 获取所有匹配的问题（包含key和summary）"""
    try:
        # 连接 Jira
        jira = JIRA(
            server=JIRA_URL,
            basic_auth=(JIRA_USER, JIRA_PASSWORD),
            options={'verify': True},
            timeout=20
        )
        
        print(f"正在执行 JQL 查询: {BASE_JQL}")
        
        # 获取所有匹配的问题
        all_issues = []
        start_at = 0
        max_results = 100
        total = None
        
        while total is None or start_at < total:
            issues = jira.search_issues(
                BASE_JQL,
                startAt=start_at,
                maxResults=max_results,
                fields="summary,labels"  # 获取摘要和标签
            )
            all_issues.extend(issues)
            if total is None:
                total = issues.total
            start_at += len(issues)
            print(f"已获取 {len(all_issues)}/{total} 个问题")
        
        print(f"\n找到 {len(all_issues)} 个匹配问题")
        
        if len(all_issues) == 0:
            print("没有匹配的问题")
            return []
        
        return all_issues
        
    except Exception as e:
        print(f"获取问题失败: {str(e)}")
        return []

def print_issues(issues):
    """打印所有问题（包含链接）"""
    if not issues:
        print("没有可用的问题")
        return
    
    print("\n" + "="*80)
    print("所有问题列表".center(80))
    print("="*80)
    for i, issue in enumerate(issues, 1):
        issue_link = f"{JIRA_URL}/browse/{issue.key}"
        print(f"{i}. [{issue.key}] {issue.fields.summary}")
        print(f"   链接: {issue_link}")
    print("="*80)

def generate_category_report(issues):
    """生成分类报告"""
    if not issues:
        return "没有可用数据生成报告"
    
    report = "# 问题分类报告\n\n"
    report += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    report += f"**分析问题数量**: {len(issues)}\n\n"
    
    # 添加问题表格
    report += "| 序号 | 问题Key | 摘要 | 链接 |\n"
    report += "|------|---------|------|------|\n"
    
    for i, issue in enumerate(issues, 1):
        issue_link = f"{JIRA_URL}/browse/{issue.key}"
        # 缩短摘要
        summary = issue.fields.summary
        if len(summary) > 60:
            summary = summary[:57] + "..."
        
        report += f"| {i} | {issue.key} | {summary} | [查看]({issue_link}) |\n"
    
    return report

def generate_local_report(issues):
    """生成本地报告（当DeepSeek API失败时使用）"""
    if not issues:
        return "没有可用数据生成报告"
    
    report = "# 问题分析报告（本地生成）\n\n"
    report += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    report += f"**分析问题数量**: {len(issues)}\n\n"
    
    # 问题列表
    report += "## 所有问题列表\n\n"
    report += "| 序号 | 问题Key | 摘要 | 链接 |\n"
    report += "|------|---------|------|------|\n"
    
    for i, issue in enumerate(issues, 1):
        issue_link = f"{JIRA_URL}/browse/{issue.key}"
        # 缩短摘要
        summary = issue.fields.summary
        if len(summary) > 60:
            summary = summary[:57] + "..."
        
        report += f"| {i} | {issue.key} | {summary} | [查看]({issue_link}) |\n"
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"local_analysis_report_{timestamp}.md"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n本地报告已保存到: {filename}")
    return filename

def deep_insight_analysis(issues):
    """使用 DeepSeek API 生成完整的Markdown格式测试报告"""
    if not issues:
        print("没有可用数据进行分析")
        return generate_local_report(issues)
    
    # 提取摘要列表
    summaries = [issue.fields.summary for issue in issues]
    
    # 准备系统提示
    if not SYSTEM_PROMPT:
        # 默认系统提示
        system_prompt = """
        你是一位自动测试专家，拥有15年行业经验。请基于以下问题摘要生成一份完整的Markdown格式测试报告，包含以下内容：
        
        ## 1. 执行摘要
        - 简要概述分析结果
        - 主要发现和关键指标
        
        ## 2. 问题分类统计
        - 创建4-8个有意义的问题类别
        - 统计各类问题的数量和比例
        - 使用表格展示分类结果
        
        ## 3. 问题分布分析
        - 识别问题的集中领域和热点区域
        - 分析问题的时间分布趋势（如果可能）
        
        ## 4. 根本原因分析
        - 探究问题产生的深层次原因
        - 分析系统性和重复性问题模式
        
        ## 5. 风险评估
        - 评估各类问题的严重程度
        - 分析潜在影响和风险等级
        - 识别高风险问题
        
        ## 6. 改进建议
        - 针对不同类别的问题提出专业改进建议
        - 提出预防措施和优化方案
        - 建议优先级排序
        
        ## 7. 详细问题列表
        - 列出所有分析的问题（包含链接）
        
        报告应不少于1000字，使用专业的测试术语，并包含必要的表格和数据可视化建议。
        """
    else:
        system_prompt = SYSTEM_PROMPT
    
    # 准备用户输入
    user_input = "以下是问题摘要和链接列表：\n\n"
    for i, summary in enumerate(summaries, 1):
        user_input += f"{i}. {summary}\n"
        user_input += f"   链接: {JIRA_URL}/browse/{issues[i-1].key}\n"
    
    user_input += "\n请生成一份完整的测试报告。"
    
    # 调用 DeepSeek API
    headers = {
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input}
        ],
        "temperature": 0.2,
        "max_tokens": 4000,
        }
    
    print("\n正在生成测试报告... (这可能需要一些时间)")
    
    try:
        response = requests.post(DEEPSEEK_API_URL, headers=headers, json=payload)
        response.raise_for_status()
        
        result = response.json()
        report_content = result["choices"][0]["message"]["content"]
        
        # 添加问题列表到报告
        category_report = generate_category_report(issues)
        full_report = f"{report_content}\n\n{category_report}"
        
        # 保存报告到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_report_{timestamp}.md"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(full_report)
        
        # 打印报告摘要
        print("\n" + "="*80)
        print("测试报告已生成".center(80))
        print("="*80)
        print(f"报告已保存到: {filename}")
        print("="*80)
        
        return filename
        
    except requests.exceptions.HTTPError as e:
        print(f"DeepSeek API HTTP错误: {e.response.status_code}")
        if e.response.content:
            try:
                error_data = e.response.json()
                print(f"错误详情: {error_data.get('error', {}).get('message', '未知错误')}")
            except:
                print(f"原始错误响应: {e.response.text[:200]}")
        return generate_local_report(issues)
    
    except Exception as e:
        print(f"DeepSeek API 调用失败: {str(e)}")
        return generate_local_report(issues)

def main():
    print("\n" + "="*60)
    print(f"AutoTest Report Generator v1.0".center(60))
    print(f"{datetime.now().strftime('%Y-%m-%d')}".center(60))
    print("="*60 + "\n")
    if not os.path.exists('config.ini'):
        create_config_file()
        print("请先配置 config.ini 文件，然后重新运行程序")
        return    
    # 获取所有问题
    issues = get_jira_issues()
    
    # 打印所有问题（包含链接）
    if issues:
        print_issues(issues)
    
    # 生成测试报告
    report_file = deep_insight_analysis(issues)
    print(f"\n报告生成完成，请查看文件: {report_file}")
    
    # 上传报告到飞书云文档并转换为文档格式
    if FEISHU_APP_ID and FEISHU_APP_SECRET and FEISHU_FOLDER_TOKEN:
        print("\n尝试上传报告到飞书云文档并转换为文档格式...")
        success, file_token = upload_and_convert_to_feishu_doc(report_file)
        
        if success:
            print("报告已成功上传并转换")
            # 构造文档链接（需要根据实际环境调整）
            doc_link = f"https://{FEISHU_APP_ID}.feishu.cn/drive/folder/{FEISHU_FOLDER_TOKEN}"
            print(f"您可以在飞书云文档中查看转换后的文档: {doc_link}")
        else:
            print("报告上传或转换失败")
    else:
        print("\n飞书配置不完整，跳过上传和转换")

if __name__ == "__main__":
    main()