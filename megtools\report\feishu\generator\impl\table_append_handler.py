import json
from urllib.parse import quote
import time

from report.feishu.generator.handler import Handler


class TableAppendHandler(Handler):

    def __init__(self, data, block, feishu_api, table_block_id, table_col_nums):
        super().__init__(data, block, feishu_api)
        self.table_block_id = table_block_id
        self.table_col_nums = int(table_col_nums)
        self.update_cache = []

    def apply(self):
        # 表格追加列
        table_cells = self.append_table_column()
        # 此时拿到所有的表格的block ids，对其进行数据填充
        for idx in range(0, len(table_cells)):
            row = idx // (len(self.data) + self.table_col_nums)
            col = idx % (len(self.data) + self.table_col_nums)
            if col < self.table_col_nums:
                continue
            val = self.data[col - self.table_col_nums][row]
            if isinstance(val, dict) and 'content' in val and 'url' in val:
                content = {"elements": [{"text_run": {
                    "content": val['content'],
                    "text_element_style": {
                        "link": {
                            "url": quote(val['url'], safe='')
                        }
                    }
                }}]}
            elif isinstance(val, dict) and 'content' in val and 'style' in val:
                content = {"elements": [{"text_run": {
                    "content": val['content'],
                    "text_element_style": json.loads(val['style'])
                }}]}
            else:
                content = {"elements": [{"text_run": {"content": val}}]}
            self.update_cache.append({
                "block_id": table_cells[idx].get("children")[0],
                "update_text_elements": content})
            if len(self.update_cache) >= 150:
                self.flush_cache()
        self.flush_cache()

    def flush_cache(self):
        if len(self.update_cache) == 0:
            return

        self.feishu_api.bulk_update_document_block({"requests": self.update_cache})
        self.update_cache.clear()
        time.sleep(0.33)

    def append_table_column(self):
        """
        生成一个空的表格，准备数据填充！
        """

        # 生成表格
        for item in self.data:
            self.feishu_api.update_document_block(self.table_block_id, {"insert_table_column": {"column_index": -1}})
        # # 获取parent_id 下所有的子块
        return self.feishu_api.get_block_all_blocks(self.table_block_id)
