import logging

from django.apps import AppConfig



class CronTaskConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'cron_task'

    def ready(self):
        logging.info("execute cron_task")
        if not self.check_exec_once():
            return
        from .services import CronTaskThread
        from cron_task.handlers.log_file_download import LogFileDownloadThread
        from cron_task.handlers.log_file_parser import LogFileParserThread

        CronTaskThread().start()
        for idx in range(0, 1):
            # 下载的进程。
            LogFileDownloadThread().start()
        # 解析的线程
        for idx in range(0, 2):
            LogFileParserThread().start()




    def check_exec_once(self):
        import os
        run_once = os.environ.get('FILE_PARSER_RUN_ONCE')
        if run_once is not None:
            return False
        os.environ['FILE_PARSER_RUN_ONCE'] = 'True'
        return True
