[DeepSeek]
api_key = sk-a76b1e202a564234a1f876e44195fea
api_url = https://api.deepseek.com/v1/chat/completions

[Jira]
url = https://jira.mach-drive-inc.com
user = t-majingmiao
password = mach.1234
jql = labels = MR_3291_滨江
[Feishu]
app_id = cli_a8d028a01ed8900b
app_secret = fA8FpGvqdoZvxBe9tTqiDbp6KkRGWmEF
folder_token = Kf6xfRFZfld96AdEOYrcgJscnEb
user_access_token = u-fUiS7ZZUV2xUCVbNdhnB.jk01rXlgkyPUG00g4Mw0f1l
[Analysis]
system_prompt = 
    请基于以下要求进行深度分析：
    1. 问题分类统计：统计各分类下的问题数量，计算占比（数量 / 总问题数） ，用数据呈现问题分布规律，对比不同分类的规模差异。并贴上对应的问题链接。
    2. top问题总结：识别最常见（数量最多） 、最关键（影响最大 / 关联核心流程） 的问题
    需基于具体问题集合展开，确保分类合理、统计准确、总结聚焦，输出清晰可落地的分析结论 。
    