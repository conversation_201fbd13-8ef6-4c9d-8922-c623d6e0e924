#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jira问题克隆工具 - 精简版
高可扩展性和可维护性设计
"""

import requests
import json
from datetime import datetime
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class JiraApiException(Exception):
    """Jira API异常"""
    def __init__(self, message: str, status_code: int = None, response_text: str = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_text = response_text


@dataclass
class CloneConfig:
    """克隆配置"""
    clone_prefix: str = "CLONE - "
    max_retries: int = 3
    copy_comments: bool = False
    copy_worklogs: bool = False
    create_clone_link: bool = False


class JiraApiClient:
    """Jira API客户端"""
    
    def __init__(self, base_url: str, bearer_token: str):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {bearer_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发起HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        response = self.session.request(method, url, **kwargs)
        return response
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        try:
            response = self._make_request('GET', '/rest/api/2/myself')
            return response.json() if response.status_code == 200 else None
        except Exception as e:
            logger.warning(f"获取用户信息失败: {e}")
            return None
    
    def get_issue(self, issue_key: str) -> Optional[Dict[str, Any]]:
        """获取问题详情"""
        try:
            response = self._make_request('GET', f'/rest/api/2/issue/{issue_key}')
            return response.json() if response.status_code == 200 else None
        except Exception as e:
            logger.error(f"获取问题失败 {issue_key}: {e}")
            return None
    
    def create_issue(self, fields: Dict[str, Any]) -> Dict[str, Any]:
        """创建问题"""
        data = {'fields': fields}
        response = self._make_request('POST', '/rest/api/2/issue', json=data)
        
        if response.status_code == 201:
            return response.json()
        else:
            # 详细错误信息，显示具体错误内容
            try:
                error_data = response.json()
                errors = error_data.get('errors', {})
                error_messages = error_data.get('errorMessages', [])
                
                error_details = []
                if error_messages:
                    error_details.extend(error_messages)
                
                for field, message in errors.items():
                    error_details.append(f"{field}: {message}")
                
                error_msg = f"创建问题失败 - 状态码: {response.status_code}\n错误详情: {'; '.join(error_details)}"
            except:
                error_msg = f"创建问题失败 - 状态码: {response.status_code}\n响应内容: {response.text[:500]}"
            
            logger.error(error_msg)
            raise JiraApiException(error_msg, response.status_code, response.text)
    
    def update_issue(self, issue_key: str, fields: Dict[str, Any]) -> bool:
        """更新问题"""
        data = {'fields': fields}
        response = self._make_request('PUT', f'/rest/api/2/issue/{issue_key}', json=data)
        return response.status_code == 204
    
    def create_issue_link(self, source_key: str, target_key: str, link_type: str = "Cloners") -> bool:
        """创建问题链接"""
        endpoint = '/rest/api/2/issueLink'
        link_data = {
            "type": {
                "name": link_type
            },
            "inwardIssue": {
                "key": target_key
            },
            "outwardIssue": {
                "key": source_key
            }
        }
        try:
            response = self._make_request('POST', endpoint, json=link_data)
            return response.status_code == 201
        except Exception as e:
            logger.warning(f"创建Issue Link失败: {e}")
            return False
    
    def search_issues(self, jql: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """使用JQL搜索问题"""
        endpoint = '/rest/api/2/search'
        params = {
            'jql': jql,
            'maxResults': max_results,
            'fields': 'key,summary'
        }
        response = self._make_request('GET', endpoint, params=params)
        
        if response.status_code == 200:
            result = response.json()
            return result.get('issues', [])
        else:
            logger.error(f"JQL搜索失败 - 状态码: {response.status_code}")
            return []


class FieldProcessor(ABC):
    """字段处理器基类"""
    
    @abstractmethod
    def can_process(self, field_name: str, field_value: Any = None) -> bool:
        """判断是否可以处理该字段"""
        pass
    
    @abstractmethod
    def process(self, field_name: str, field_value: Any, context: Dict[str, Any]) -> Any:
        """处理字段值"""
        pass


class BasicFieldProcessor(FieldProcessor):
    """基础字段处理器"""
    
    BASIC_FIELDS = {'project', 'summary', 'issuetype', 'description', 'assignee'}
    
    def can_process(self, field_name: str, field_value: Any = None) -> bool:
        return field_name in self.BASIC_FIELDS
    
    def process(self, field_name: str, field_value: Any, context: Dict[str, Any]) -> Any:
        if field_name == 'assignee':
            # 保持原始assignee，不强制改为当前用户
            return field_value
        elif field_name == 'issuetype':
            # 保持原始问题类型
            return field_value
        elif field_name == 'summary':
            # 保持原始标题，不添加CLONE前缀
            return field_value
        elif field_name == 'description' and not field_value:
            return '克隆的问题，原问题无描述'
        return field_value


class SystemFieldProcessor(FieldProcessor):
    """系统字段处理器"""
    
    SYSTEM_FIELDS = {'priority', 'labels', 'environment', 'duedate', 'components'}
    
    def can_process(self, field_name: str, field_value: Any = None) -> bool:
        return field_name in self.SYSTEM_FIELDS
    
    def process(self, field_name: str, field_value: Any, context: Dict[str, Any]) -> Any:
        if field_name == 'labels':
            # 添加克隆标签
            original_labels = field_value or []
            current_date = datetime.now().strftime('%Y%m%d')
            clone_label = f"{current_date}_clone"
            return original_labels + [clone_label]
        elif field_name == 'components':
            # 保持原始components，不添加额外组件
            return field_value
        return field_value


class CustomFieldProcessor(FieldProcessor):
    """自定义字段处理器"""
    
    def can_process(self, field_name: str, field_value: Any = None) -> bool:
        return field_name.startswith('customfield_')
    
    def process(self, field_name: str, field_value: Any, context: Dict[str, Any]) -> Any:
        """处理自定义字段"""
        if field_value is None:
            return None
        
        # 处理对象类型字段
        if isinstance(field_value, dict):
            if 'id' in field_value:
                return {'id': field_value['id']}
            elif 'accountId' in field_value:
                return {'accountId': field_value['accountId']}
            return field_value
        
        # 处理数组类型字段
        elif isinstance(field_value, list) and field_value:
            processed_list = []
            for item in field_value:
                if isinstance(item, dict) and 'id' in item:
                    processed_list.append({'id': item['id']})
                elif isinstance(item, dict) and 'accountId' in item:
                    processed_list.append({'accountId': item['accountId']})
                else:
                    processed_list.append(item)
            return processed_list
        
        return field_value


class FieldFilterService:
    """字段过滤服务"""
    
    def __init__(self):
        self.processors = [
            BasicFieldProcessor(),
            SystemFieldProcessor(),
            CustomFieldProcessor()
        ]
        # 安全字段白名单
        self.safe_fields = {
            'project', 'summary', 'issuetype', 'description', 'assignee',
            'priority', 'labels', 'environment', 'duedate', 'components'
        }
    
    def filter_fields(self, source_fields: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """过滤并处理字段"""
        clone_fields = {}
        context = context or {}
        
        processed_count = 0
        skipped_count = 0
        
        for field_name, field_value in source_fields.items():
            if field_value is None:
                continue
            
            # 优先处理安全字段
            if field_name in self.safe_fields or field_name.startswith('customfield_'):
                processor = self._find_processor(field_name, field_value)
                if processor:
                    try:
                        processed_value = processor.process(field_name, field_value, context)
                        clone_fields[field_name] = processed_value
                        processed_count += 1
                        logger.debug(f"成功处理字段 {field_name}")
                    except Exception as e:
                        logger.warning(f"处理字段失败 {field_name}: {e}")
                        skipped_count += 1
                else:
                    logger.warning(f"未找到处理器，跳过字段 {field_name}")
                    skipped_count += 1
            else:
                skipped_count += 1
        
        logger.info(f"成功处理 {processed_count} 个字段，跳过 {skipped_count} 个字段")
        return clone_fields
    
    def _find_processor(self, field_name: str, field_value: Any = None) -> Optional[FieldProcessor]:
        """查找合适的字段处理器"""
        for processor in self.processors:
            if processor.can_process(field_name, field_value):
                return processor
        return None
    
    def add_processor(self, processor: FieldProcessor):
        """添加自定义处理器"""
        self.processors.append(processor)


class IssueCloneService:
    """问题克隆服务"""
    
    def __init__(self, api_client: JiraApiClient, field_filter: FieldFilterService):
        self.api_client = api_client
        self.field_filter = field_filter
    
    def clone_issue(self, issue_key: str, config: CloneConfig) -> str:
        """克隆单个问题"""
        logger.info(f"开始克隆问题: {issue_key}")
        
        # 获取当前用户信息
        current_user = self.api_client.get_current_user()
        context = {'current_user': current_user} if current_user else {}
        
        # 获取源问题
        source_issue = self.api_client.get_issue(issue_key)
        if not source_issue:
            raise JiraApiException(f"无法获取源问题: {issue_key}")
        
        source_fields = source_issue['fields']
        logger.info(f"原问题: {source_fields.get('summary', 'Unknown')}")
        
        # 处理字段
        clone_fields = self.field_filter.filter_fields(source_fields, context)
        
        # 修改标题
        if 'summary' in clone_fields:
            clone_fields['summary'] = f"{config.clone_prefix}{clone_fields['summary']}"
        
        # 创建问题
        new_issue = self._create_issue_with_retry(clone_fields, config.max_retries)
        new_issue_key = new_issue['key']
        
        # 创建克隆关系链接
        if config.create_clone_link:
            try:
                self.api_client.create_issue_link(issue_key, new_issue_key, "Cloners")
                logger.info(f"成功创建克隆关系链接: {issue_key} -> {new_issue_key}")
            except Exception as e:
                logger.warning(f"创建克隆关系链接失败: {e}")
        
        logger.info(f"成功创建克隆问题: {new_issue_key}")
        return new_issue_key
    
    def _create_issue_with_retry(self, fields: Dict[str, Any], max_retries: int) -> Dict[str, Any]:
        """带重试的创建问题"""
        try:
            return self.api_client.create_issue(fields)
        except JiraApiException as e:
            if e.status_code == 400 and max_retries > 0:
                logger.warning("创建失败，尝试分离字段后重试")
                
                # 分离基本字段和自定义字段
                basic_fields = {k: v for k, v in fields.items() if not k.startswith('customfield_')}
                custom_fields = {k: v for k, v in fields.items() if k.startswith('customfield_')}
                
                # 先创建基本问题
                issue = self.api_client.create_issue(basic_fields)
                issue_key = issue['key']
                
                # 逐个添加自定义字段
                successful = 0
                failed_fields = []
                for field_name, field_value in custom_fields.items():
                    try:
                        logger.debug(f"尝试设置字段 {field_name}: {field_value}")
                        self.api_client.update_issue(issue_key, {field_name: field_value})
                        successful += 1
                        logger.debug(f"成功设置字段 {field_name}")
                    except Exception as field_error:
                        failed_fields.append(field_name)
                        logger.debug(f"跳过字段 {field_name}: 不在适当的屏幕上或未知")
                
                logger.info(f"成功设置 {successful}/{len(custom_fields)} 个自定义字段")
                if failed_fields:
                    logger.warning(f"跳过 {len(failed_fields)} 个字段（不在适当的屏幕上或未知）")
                return issue
            else:
                raise


class JiraCloneManager:
    """Jira克隆管理器"""
    
    def __init__(self, base_url: str, bearer_token: str):
        self.api_client = JiraApiClient(base_url, bearer_token)
        self.field_filter = FieldFilterService()
        self.clone_service = IssueCloneService(self.api_client, self.field_filter)
    
    def test_connection(self) -> bool:
        """测试连接"""
        user = self.api_client.get_current_user()
        if user:
            logger.info(f"连接成功! 当前用户: {user.get('displayName', 'Unknown')}")
            return True
        else:
            logger.error("连接失败")
            return False
    
    def clone_issue(self, issue_key: str, config: CloneConfig = None) -> str:
        """克隆单个问题"""
        config = config or CloneConfig()
        return self.clone_service.clone_issue(issue_key, config)
    
    def clone_issues_batch(self, issue_keys: List[str], config: CloneConfig = None) -> tuple[List[str], List[str]]:
        """批量克隆问题"""
        config = config or CloneConfig()
        cloned = []
        failed = []
        
        for issue_key in issue_keys:
            try:
                clone_key = self.clone_issue(issue_key, config)
                cloned.append(clone_key)
                logger.info(f"克隆成功: {issue_key} -> {clone_key}")
            except Exception as e:
                failed.append(issue_key)
                logger.error(f"克隆失败: {issue_key} - {e}")
        
        return cloned, failed
    
    def add_field_processor(self, processor: FieldProcessor):
        """添加字段处理器"""
        self.field_filter.add_processor(processor)
    
    def clone_issues_by_jql(self, jql: str, config: CloneConfig = None, max_results: int = 50) -> tuple[List[str], List[str]]:
        """使用JQL搜索并克隆问题"""
        if config is None:
            config = CloneConfig()
        
        logger.info(f"使用JQL搜索问题: {jql}")
        
        # 搜索问题
        issues = self.api_client.search_issues(jql, max_results)
        if not issues:
            logger.warning("未找到匹配的问题")
            return [], []
        
        logger.info(f"找到 {len(issues)} 个问题，开始批量克隆")
        
        # 提取问题key列表
        issue_keys = [issue['key'] for issue in issues]
        
        # 显示要克隆的问题
        for issue in issues:
            logger.info(f"  - {issue['key']}: {issue['fields']['summary']}")
        
        # 批量克隆
        return self.clone_issues_batch(issue_keys, config)


def main():
    """主函数"""
    # 配置信息
    jira_url = 'https://jira.mach-drive-inc.com'
    bearer_token = "OTAxMzc2MDUxNDM4OuLU8ywhVqtKIJW2qlZUeQxOS1bv"
    
    logger.info("Jira克隆工具 - 精简版")
    
    try:
        # 创建管理器
        manager = JiraCloneManager(jira_url, bearer_token)
        
        # 测试连接
        if not manager.test_connection():
            return
        
        # 配置克隆选项
        config = CloneConfig(clone_prefix="CLONE - ", create_clone_link=True)
        
        # 使用JQL搜索并克隆问题
        jql = "labels = 车道线-0807 and summary !~ clone and component is  EMPTY"
        logger.info(f"使用JQL搜索并克隆问题")
        
        successful, failed = manager.clone_issues_by_jql(jql, config, max_results=200)
        
        if successful:
            logger.info(f"成功克隆 {len(successful)} 个问题: {', '.join(successful)}")
        if failed:
            logger.warning(f"克隆失败 {len(failed)} 个问题: {', '.join(failed)}")
        
        logger.info("克隆操作完成！")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")


if __name__ == "__main__":
    main()