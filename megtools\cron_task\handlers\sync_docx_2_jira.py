import logging
import re

from basic.third_apis.feishu_api import FeishuApi
from basic.utils import <PERSON><PERSON><PERSON><PERSON><PERSON>


def apply(task_config):
    Docx2J<PERSON>(task_config).apply()


# 提取飞书文档中人的信息，然后再同步到Jira 中
class Docx2Jira(object):
    def __init__(self, task_config):
        self.task_config = task_config
        self.read_doc_cnt = task_config.get('read_doc_cnt', 1)
        self.docx_url = task_config.get('feishu_url', "")
        self.jira_col = task_config.get('jira_col', 0)
        self.user_col = task_config.get('user_col', 0)
        self.feishu_api = FeishuApi()
        self.jira_api = JiraOperate()
        regexp_str = r'E2E-\d+'
        self.jira_pattern = re.compile(regexp_str)
        regexp_str = r'@\((.*?)\)'
        self.user_pattern = re.compile(regexp_str)
        self.user_cache = {}
        self.read_feishu_url = None

    def apply(self):
        # 查询飞书完成，获取到所有的表格中的数据
        for idx in range(0, self.read_doc_cnt):
            table_list = self.query_feishu(idx+1)
            for item in table_list:
                 self.parse_table(item)

    def parse_table(self, table_info):
        for row in table_info:
            if len(row[0]) == 0:
                continue
            jira_info = self.jira_api.query_jira_by_key(row[0],  ["key", "labels"])
            labels = jira_info.get("fields", {}).get("labels")
            modify_body = {
                "customfield_14301": self.read_feishu_url
            }
            if len(row[1]) > 0:
                modify_body["customfield_14300"] = [{"name": item} for item in row[1]]
            if labels and "重点跟进问题" not in labels:
                labels.append("重点跟进问题")
                modify_body["labels"] = labels
            self.jira_api.modify_values(row[0], modify_body)
            logging.info(f"modify: {row[0]}\t{row[1]}")

    def query_feishu(self, node_cnt=1):
        node_info = self.get_last_child_node(node_cnt)
        if len(node_info) == 0:
            return []
        # 获取文档里面所有的表格， 并提取相应的内容
        doc_blocks = self.feishu_api.get_document_all_blocks()
        # 转换成map，好取数据
        blocks_map = {item.get("block_id"): item for item in doc_blocks}
        # 继续，找到block_type 是表格的
        table_list = []
        for item in doc_blocks:
            if item.get("block_type") != 31:
                continue
            # 判断当前表格是几个列的
            col_size = item.get("table", {}).get("property", {}).get("column_size", 0)
            cells_ids = item.get("table", {}).get("cells", [])
            rows = []
            row_idx = -1
            col_idx = -1
            for cell_idx in range(0, len(cells_ids)):
                if cell_idx % col_size == 0:
                    rows.append([])
                    row_idx += 1
                    col_idx = -1
                col_idx += 1
                cell_id = cells_ids[cell_idx]
                # 提取url 和 人的数据
                if col_idx == self.jira_col:
                    # 提取url
                    rows[-1].append(self.extra_url(cell_id,blocks_map))
                if col_idx == self.user_col:
                    # 提取人
                    rows[-1].append(self.extra_user(cell_id,blocks_map))
            if len(rows) > 0:
                table_list.append(rows)
        return table_list

    def extra_url(self, cell_id, blocks_map):
        cell_info = blocks_map[cell_id]
        # 获取所有的子节点，如果还有子节点，那么继续获取子节点，然后获取所有的文本
        node_text = self.get_all_sub_nodes_text(cell_info.get('block_id'), blocks_map)
        jira_key = self.jira_pattern.findall(node_text)
        if len(jira_key) == 0:
            return ""
        return jira_key[0]

    def extra_user(self, cell_id, blocks_map):
        cell_info = blocks_map[cell_id]
        node_text = self.get_all_sub_nodes_text(cell_info.get('block_id'), blocks_map)
        jira_keys = self.user_pattern.findall(node_text)
        return jira_keys

    def get_all_sub_nodes_text(self, block_id, blocks_map, prefix=""):
        block_info = blocks_map.get(block_id, None)
        if block_info is None:
            return prefix
        if f',{block_info.get("block_type")},' in ",2,":
            return self.handle_text(block_info)
        if "children" in block_info:
            for item in block_info.get("children"):
                prefix = f"{prefix}{self.get_all_sub_nodes_text(item, blocks_map)}"
        return prefix

    def handle_text(self, text_block):
        if text_block.get("block_type") != 2:
            return ""
        result = ""
        for item in text_block.get("text", {}).get("elements", []):
            element_text = item.get("text_run", {}).get("content", "")
            if 'mention_user' in item:
                user_open_id = item.get('mention_user').get("user_id")
                element_text = f"@({self.get_user_name(user_open_id)})"
            result = f'{result}{element_text}'
        return result

    def get_user_name(self, open_id):
        if open_id in self.user_cache:
            return self.user_cache[open_id]
        user_info = self.feishu_api.get_user_by_open_id(open_id)
        if user_info is None:
            return ""
        user_name = user_info.get("email").split("@")[0]
        self.user_cache[open_id] = user_name
        return user_name


    def get_last_child_node(self, node_cnt=1):
        # 获取最下面的一个文档
        url_info = self.docx_url.split("/")
        obj_type = url_info[-2]
        token = url_info[-1]
        result = self.feishu_api.wiki_get_node(token, obj_type)
        space_id = result.get("data", {}).get("node", {}).get("space_id", "")
        if not space_id:
            return []
        node_list = self.feishu_api.wiki_sub_nodes(space_id, token)
        if node_list is None or len(node_list) == 0:
            return []
        node_info = node_list[-node_cnt]
        self.read_feishu_url = f"https://yuanlijuhe.feishu.cn/wiki/{node_info.get('node_token')}"
        self.feishu_api.set_target_file_token(node_info.get("obj_token"))

        return node_info
