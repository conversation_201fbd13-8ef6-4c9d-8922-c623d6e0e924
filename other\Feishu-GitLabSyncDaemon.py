import logging
import requests
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *
import re
import time
import signal
import sys
from functools import lru_cache
from typing import Optional, Tuple, Dict, Any, List
from dataclasses import dataclass
import os
import atexit

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('FeishuPoller')

# 配置常量
CONFIG = {
    "APP_ID": "cli_a8d028a01ed8900b",
    "APP_SECRET": "fA8FpGvqdoZvxBe9tTqiDbp6KkRGWmEF",
    "APP_TOKEN": "MudQbRICta0KjusHgXWcpCCJnyb",
    "TABLE_ID": "tbli7WPGf9S1xCF0",
    "GITLAB_URL": "https://git-core.megvii-inc.com",
    "GITLAB_TOKEN": "********************",
    "POLL_INTERVAL": 1,  # 5分钟轮询一次
    "MAX_RETRIES": 5,      # 最大重试次数
    "RETRY_DELAY": 30      # 重试延迟(秒)
}

# 创建飞书客户端
client = lark.Client.builder() \
    .app_id(CONFIG["APP_ID"]) \
    .app_secret(CONFIG["APP_SECRET"]) \
    .enable_set_token(True) \
    .log_level(lark.LogLevel.INFO) \
    .build()

# 预编译正则表达式
URL_PATTERNS = [
    re.compile(r"https?://[^/]+/([^/-]+/[^/-]+)/-/(merge_requests|merge_requests)/(\d+)"),
    re.compile(r"https?://[^/]+/([^/]+/[^/]+)/(merge_requests|merge_requests)/(\d+)"),
    re.compile(r"https?://[^/]+/([^/-]+/[^/-]+)/-/(merge_requests|merge_requests)/(\d+)/.*")
]

CONTENT_REGEX = re.compile(
    r'(?:##\s*)?更新内容【\*必填】\s*\n([\s\S]*?)(?=\n##|\Z)',
    re.DOTALL | re.IGNORECASE
)

METHOD_REGEX = re.compile(
    r'(?:##\s*)?测试方法【\*必填】\s*\n([\s\S]*?)(?=\n##|\Z)',
    re.DOTALL | re.IGNORECASE
)

# 数据结构
@dataclass
class GitLabMR:
    project_path: str
    mr_id: int

@dataclass
class FeishuRecord:
    id: str
    fields: Dict[str, Any]
    version: str  # 记录版本，用于检测变化

@dataclass
class UpdateFields:
    commit_id: Optional[str] = None
    update_content: Optional[str] = None
    test_method: Optional[str] = None
    
    def to_dict(self) -> Dict[str, str]:
        """将更新字段转换为字典格式"""
        fields = {}
        if self.commit_id:
            fields["Commit ID"] = self.commit_id
        if self.update_content:
            fields["更新内容"] = self.update_content
        if self.test_method:
            fields["验证要求"] = self.test_method
        return fields
    
    def has_updates(self) -> bool:
        """检查是否有需要更新的字段"""
        return bool(self.commit_id or self.update_content or self.test_method)

# 全局状态
class State:
    def __init__(self):
        self.last_processed_version = None  # 最后处理的记录版本
        self.running = True
        self.is_daemon = False
        
    def stop(self):
        self.running = False
    def daemonize(self):
            """将进程转换为守护进程"""
            try:
                pid = os.fork()
                if pid > 0:
                    sys.exit(0)  # 退出父进程
            except OSError as e:
                sys.stderr.write(f"Fork failed: {e}\n")
                sys.exit(1)
            
            os.chdir("/")
            os.setsid()
            os.umask(0)
            
            # 重定向标准文件描述符
            sys.stdout.flush()
            sys.stderr.flush()
            
            self.is_daemon = True
            logger.info("进程已转为守护进程")
            
            # 注册退出处理
            atexit.register(self.cleanup)
        
    def cleanup(self):
        """清理资源"""
        if self.is_daemon:
            logger.info("守护进程退出，清理资源")
        # 清理缓存
        get_first_commit_id.cache_clear()
        get_mr_description.cache_clear()
        extract_mr_params.cache_clear()
        

# 辅助函数
def is_field_empty(fields: Dict[str, Any], field_name: str) -> bool:
    """检查字段是否为空（处理各种空值情况）"""
    value = fields.get(field_name)
    if value is None:
        return True
    if isinstance(value, str) and value.strip() == "":
        return True
    if isinstance(value, list) and len(value) == 0:
        return True
    return False

@lru_cache(maxsize=128)
def extract_mr_params(url: str) -> Optional[GitLabMR]:
    """从GitLab合并请求URL中提取项目路径和合并请求ID"""
    try:
        for pattern in URL_PATTERNS:
            match = pattern.search(url)
            if match:
                return GitLabMR(project_path=match.group(1), mr_id=int(match.group(3)))
        
        logger.warning(f"无法识别的URL格式: {url}")
        return None
            
    except Exception as e:
        logger.error(f"URL解析失败: {url}, 错误: {str(e)}")
        return None

@lru_cache(maxsize=128)
def get_first_commit_id(project_path: str, mr_id: int) -> Optional[str]:
    """获取合并请求的第一个commit ID"""
    try:
        project_encoded = requests.utils.quote(project_path, safe='')
        api_url = f"{CONFIG['GITLAB_URL']}/api/v4/projects/{project_encoded}/merge_requests/{mr_id}/commits"
        
        response = requests.get(
            api_url,
            headers={"PRIVATE-TOKEN": CONFIG["GITLAB_TOKEN"]},
            params={"per_page": 1},
            timeout=10
        )
        
        if response.status_code != 200:
            logger.error(f"GitLab请求失败: {response.status_code}, 响应: {response.text}")
            return None
        
        commits = response.json()
        return commits[0]['id'] if commits else None
        
    except Exception as e:
        logger.error(f"获取commit ID失败: {str(e)}")
        return None

@lru_cache(maxsize=128)
def get_mr_description(project_path: str, mr_id: int) -> Optional[str]:
    """使用GitLab API获取合并请求的描述内容"""
    try:
        project_encoded = requests.utils.quote(project_path, safe='')
        api_url = f"{CONFIG['GITLAB_URL']}/api/v4/projects/{project_encoded}/merge_requests/{mr_id}"
        
        response = requests.get(
            api_url,
            headers={"PRIVATE-TOKEN": CONFIG["GITLAB_TOKEN"]},
            timeout=10
        )
        
        if response.status_code != 200:
            logger.error(f"GitLab API请求失败: {response.status_code}, 响应: {response.text}")
            return None
        
        return response.json().get('description', '')
        
    except Exception as e:
        logger.error(f"获取描述内容失败: {str(e)}")
        return None

def extract_section(text: str, pattern: re.Pattern) -> Optional[str]:
    """从描述文本中提取特定部分的内容"""
    if not text:
        return None
        
    match = pattern.search(text)
    if match:
        content = match.group(1).strip()
        # 清理多余空白字符
        return re.sub(r'\s+', ' ', content)
    return None

def process_mr_content(record: FeishuRecord, mr: GitLabMR) -> Tuple[Optional[str], Optional[str]]:
    """处理合并请求内容，提取更新内容和测试方法"""
    description = get_mr_description(mr.project_path, mr.mr_id)
    if not description:
        return None, None
    
    update_content = extract_section(description, CONTENT_REGEX)
    test_method = extract_section(description, METHOD_REGEX)
    
    if update_content:
        logger.debug(f"提取到更新内容: {update_content[:50]}...")
    if test_method:
        logger.debug(f"提取到测试方法: {test_method[:50]}...")
    
    return update_content, test_method

def get_table_records(state: State) -> List[FeishuRecord]:
    """获取飞书表格记录"""
    try:
        req = ListAppTableRecordRequest.builder() \
            .app_token(CONFIG["APP_TOKEN"]) \
            .table_id(CONFIG["TABLE_ID"]) \
            .page_size(100) \
            .build()
        
        resp = client.bitable.v1.app_table_record.list(req)
        
        if not resp.success():
            logger.error(f"获取表格记录失败: {resp.code}, {resp.msg}")
            return []
            
        if not resp.data or not resp.data.items:
            logger.info("没有获取到表格记录")
            return []
        
        records = []
        for record in resp.data.items:
            # 使用记录ID和版本号作为唯一标识
            record_version = f"{record.record_id}-{record.record_id}"
            # 只处理新记录或更新记录
            if not state.last_processed_version or record_version != state.last_processed_version:
                records.append(FeishuRecord(
                    id=record.record_id, 
                    fields=record.fields or {},
                    version=record_version
                ))
        
        # 更新最后处理的记录版本
        if records:
            state.last_processed_version = records[-1].version
        
        return records
        
    except Exception as e:
        logger.exception(f"获取表格记录异常: {str(e)}")
        return []

def update_feishu_record(record_id: str, fields_dict: Dict[str, str]) -> bool:
    """更新飞书表格记录"""
    try:
        req = UpdateAppTableRecordRequest.builder() \
            .app_token(CONFIG["APP_TOKEN"]) \
            .table_id(CONFIG["TABLE_ID"]) \
            .record_id(record_id) \
            .request_body(AppTableRecord.builder()
                .fields(fields_dict)
                .build()) \
            .build()
        
        resp = client.bitable.v1.app_table_record.update(req)
        
        if not resp.success():
            logger.error(f"更新记录失败: {record_id}, 错误码: {resp.code}, 错误信息: {resp.msg}")
            return False
            
        logger.info(f"更新记录成功: {record_id}")
        return True
        
    except Exception as e:
        logger.error(f"更新记录异常: {str(e)}")
        return False

def process_record(record: FeishuRecord) -> UpdateFields:
    """处理单条表格记录"""
    update_fields = UpdateFields()
    version_url = record.fields.get("版本地址", "")
    
    # 跳过没有版本地址的记录
    if not version_url or is_field_empty(record.fields, "版本地址"):
        logger.info(f"记录 {record.id} 缺少有效的版本地址")
        return update_fields
    
    # 提取MR参数
    mr = extract_mr_params(version_url)
    if not mr:
        logger.warning(f"记录 {record.id} 无法解析版本地址")
        return update_fields
    
    # 处理Commit ID
    if is_field_empty(record.fields, "Commit ID"):
        commit_id = get_first_commit_id(mr.project_path, mr.mr_id)
        if commit_id:
            update_fields.commit_id = commit_id
            logger.info(f"准备更新Commit ID: {record.id} -> {commit_id}")
    
    # 处理内容字段
    if is_field_empty(record.fields, "更新内容") or is_field_empty(record.fields, "验证要求"):
        update_content, test_method = process_mr_content(record, mr)
        
        if update_content and is_field_empty(record.fields, "更新内容"):
            update_fields.update_content = update_content
            logger.info(f"准备更新更新内容: {record.id}")
        
        if test_method and is_field_empty(record.fields, "验证要求"):
            update_fields.test_method = test_method
            logger.info(f"准备更新验证要求: {record.id}")
    
    return update_fields

def process_table(state: State) -> int:
    """处理表格数据并返回更新记录数"""
    records = get_table_records(state)
    if not records:
        logger.info("没有需要处理的新记录")
        return 0
    
    logger.info(f"获取到 {len(records)} 条需要处理的记录")
    updated_count = 0
    
    for record in records:
        try:
            update_fields = process_record(record)
            
            if update_fields.has_updates():
                logger.info(f"更新记录 {record.id}: {update_fields.to_dict().keys()}")
                if update_feishu_record(record.id, update_fields.to_dict()):
                    updated_count += 1
            else:
                logger.debug(f"记录 {record.id} 无需更新")
                
        except Exception as e:
            logger.exception(f"处理记录 {record.id} 时出错: {str(e)}")
    
    logger.info(f"处理完成: 共处理 {len(records)} 条记录, 更新 {updated_count} 条")
    return updated_count

def signal_handler(sig, frame, state: State):
    """处理终止信号"""
    logger.info("接收到终止信号，准备退出...")
    state.stop()
    sys.exit(0)

def run_daemon(state: State):
    """守护进程主循环"""
    logger.info("守护进程启动，开始监控飞书表格...")
    retry_count = 0
    
    while state.running:
        try:
            logger.info(f"开始轮询表格 (轮询间隔: {CONFIG['POLL_INTERVAL']}秒)")
            updated = process_table(state)
            
            if updated > 0:
                logger.info(f"本次轮询更新了 {updated} 条记录")
            else:
                logger.info("本次轮询未发现需要更新的记录")
            
            # 重置重试计数器
            retry_count = 0
            
        except Exception as e:
            logger.error(f"处理过程中发生错误: {str(e)}")
            retry_count += 1
            
            if retry_count >= CONFIG["MAX_RETRIES"]:
                logger.error(f"达到最大重试次数 ({CONFIG['MAX_RETRIES']})，程序将退出")
                state.stop()
                break
            
            logger.info(f"将在 {CONFIG['RETRY_DELAY']} 秒后重试 ({retry_count}/{CONFIG['MAX_RETRIES']})")
            time.sleep(CONFIG["RETRY_DELAY"])
            continue
        
        # 等待下一次轮询
        for _ in range(CONFIG["POLL_INTERVAL"]):
            if not state.running:
                break
            time.sleep(1)

def main():
    """主函数"""
    logger.info("启动飞书表格监控守护进程...")
    print("飞书表格监控守护进程已启动")
    
    # 初始化状态
    state = State()
    if len(sys.argv) > 1 and sys.argv[1] == "--daemon":
        state.daemonize()
    # 注册信号处理器
    signal.signal(signal.SIGINT, lambda sig, frame: signal_handler(sig, frame, state))
    signal.signal(signal.SIGTERM, lambda sig, frame: signal_handler(sig, frame, state))
    
    try:
        run_daemon(state)
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.exception("守护进程发生未捕获的异常")
    finally:
        logger.info("守护进程退出")
        print("守护进程已退出")

if __name__ == "__main__":
    main()