#!/usr/bin/env python3
"""
MCAP SDK专用日志系统
替换所有print语句，提供专业的日志功能
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Any
from enum import Enum


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class McapLogger:
    """MCAP SDK专用日志器"""
    
    def __init__(self, name: str = "mcap_sdk", level: LogLevel = LogLevel.INFO, 
                 enable_file_logging: bool = False, log_dir: str = "logs"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.value))
        
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 设置处理器
        self._setup_console_handler()
        
        if enable_file_logging:
            self._setup_file_handler(log_dir)
    
    def _setup_console_handler(self):
        """设置控制台处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 简洁的控制台格式
        console_formatter = logging.Formatter(
            '%(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
    
    def _setup_file_handler(self, log_dir: str):
        """设置文件处理器"""
        log_path = Path(log_dir)
        log_path.mkdir(exist_ok=True)
        
        log_file = log_path / f"mcap_sdk_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 详细的文件格式
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, extra=kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, extra=kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.logger.error(message, extra=kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, extra=kwargs)
    
    # 自定义日志方法，保持原有的emoji风格但使用日志系统
    def success(self, message: str, **kwargs):
        """成功日志"""
        self.logger.info(f"✅ {message}", extra=kwargs)
    
    def progress(self, message: str, current: int = None, total: int = None, **kwargs):
        """进度日志"""
        if current is not None and total is not None:
            percentage = (current / total) * 100 if total > 0 else 0
            self.logger.info(f"📊 {message} ({current}/{total}, {percentage:.1f}%)", extra=kwargs)
        else:
            self.logger.info(f"📊 {message}", extra=kwargs)
    
    def performance(self, operation: str, duration: float, **kwargs):
        """性能日志"""
        self.logger.info(f"⚡ {operation} 完成，耗时 {duration:.3f}s", extra=kwargs)
    
    def file_operation(self, operation: str, file_path: str, **kwargs):
        """文件操作日志"""
        self.logger.info(f"📁 {operation}: {file_path}", extra=kwargs)
    
    def data_info(self, message: str, **kwargs):
        """数据信息日志"""
        self.logger.info(f"📋 {message}", extra=kwargs)
    
    def parsing_info(self, message: str, **kwargs):
        """解析信息日志"""
        self.logger.info(f"🔍 {message}", extra=kwargs)
    
    def lane_info(self, message: str, **kwargs):
        """车道线信息日志"""
        self.logger.info(f"🛣️  {message}", extra=kwargs)


# 全局日志器实例
_global_logger: Optional[McapLogger] = None

def get_logger(name: str = "mcap_sdk", 
               level: LogLevel = LogLevel.INFO,
               enable_file_logging: bool = False) -> McapLogger:
    """获取全局日志器"""
    global _global_logger
    if _global_logger is None:
        _global_logger = McapLogger(name, level, enable_file_logging)
    return _global_logger

def set_log_level(level: LogLevel):
    """设置日志级别"""
    logger = get_logger()
    logger.logger.setLevel(getattr(logging, level.value))

def enable_file_logging(log_dir: str = "logs"):
    """启用文件日志"""
    logger = get_logger()
    logger._setup_file_handler(log_dir)

# 便利函数，保持与原有print风格类似的接口
def log_debug(message: str, **kwargs):
    get_logger().debug(message, **kwargs)

def log_info(message: str, **kwargs):
    get_logger().info(message, **kwargs)

def log_warning(message: str, **kwargs):
    get_logger().warning(message, **kwargs)

def log_error(message: str, **kwargs):
    get_logger().error(message, **kwargs)

def log_success(message: str, **kwargs):
    get_logger().success(message, **kwargs)

def log_progress(message: str, current: int = None, total: int = None, **kwargs):
    get_logger().progress(message, current, total, **kwargs)

def log_performance(operation: str, duration: float, **kwargs):
    get_logger().performance(operation, duration, **kwargs)

def log_file_operation(operation: str, file_path: str, **kwargs):
    get_logger().file_operation(operation, file_path, **kwargs)

def log_data_info(message: str, **kwargs):
    get_logger().data_info(message, **kwargs)

def log_parsing_info(message: str, **kwargs):
    get_logger().parsing_info(message, **kwargs)

def log_lane_info(message: str, **kwargs):
    get_logger().lane_info(message, **kwargs)


if __name__ == "__main__":
    # 测试日志系统
    logger = get_logger(level=LogLevel.DEBUG, enable_file_logging=True)
    
    logger.debug("这是调试信息")
    logger.info("这是普通信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    logger.success("这是成功信息")
    logger.progress("处理进度", 50, 100)
    logger.performance("数据解析", 1.234)
    logger.file_operation("读取文件", "test.mcap")
    logger.data_info("发现25条车道线")
    logger.parsing_info("开始解析MCAP文件")
    logger.lane_info("车道线解析完成")
