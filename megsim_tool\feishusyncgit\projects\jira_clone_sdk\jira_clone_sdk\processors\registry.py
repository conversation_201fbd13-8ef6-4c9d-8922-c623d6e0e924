"""处理器注册表"""

from typing import List, Optional, Any, Dict
from .base import FieldProcessor
from .basic import BasicFieldProcessor
from .system import SystemFieldProcessor
from .custom import CustomFieldProcessor
from ..exceptions import FieldProcessingException


class ProcessorRegistry:
    """字段处理器注册表"""
    
    def __init__(self):
        self.processors: List[FieldProcessor] = []
        self._register_default_processors()
    
    def _register_default_processors(self):
        """注册默认处理器"""
        self.register(BasicFieldProcessor())
        self.register(SystemFieldProcessor())
        self.register(CustomFieldProcessor())
    
    def register(self, processor: FieldProcessor):
        """注册处理器"""
        self.processors.append(processor)
        # 按优先级排序
        self.processors.sort()
    
    def unregister(self, processor_class: type):
        """注销处理器"""
        self.processors = [p for p in self.processors if not isinstance(p, processor_class)]
    
    def find_processor(self, field_name: str, field_value: Any = None) -> Optional[FieldProcessor]:
        """查找合适的处理器"""
        for processor in self.processors:
            if processor.can_process(field_name, field_value):
                return processor
        return None
    
    def process_field(self, field_name: str, field_value: Any, context: Dict[str, Any]) -> Any:
        """处理字段"""
        processor = self.find_processor(field_name, field_value)
        if processor:
            try:
                # 获取字段配置
                clone_config = context.get('clone_config')
                if clone_config:
                    field_config = clone_config.get_field_config(field_name)
                    return processor.transform_field(field_name, field_value, field_config, context)
                else:
                    return processor.process(field_name, field_value, context)
            except Exception as e:
                raise FieldProcessingException(field_name, str(e), field_value)
        else:
            # 没有找到处理器，返回原值
            return field_value
    
    def validate_field(self, field_name: str, field_value: Any) -> bool:
        """验证字段"""
        processor = self.find_processor(field_name, field_value)
        if processor:
            return processor.validate_field(field_name, field_value)
        return True
    
    def get_processors(self) -> List[FieldProcessor]:
        """获取所有处理器"""
        return self.processors.copy()
    
    def clear(self):
        """清空所有处理器"""
        self.processors.clear()
    
    def reset_to_default(self):
        """重置为默认处理器"""
        self.clear()
        self._register_default_processors()