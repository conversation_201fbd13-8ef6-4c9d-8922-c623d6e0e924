# API 接口清单

## 基础服务模块 (/api/basic/)

### 认证相关
- `POST /api/basic/auth/login` - 用户登录
- `GET /api/basic/user/info` - 获取用户信息
- `GET /api/basic/auth/codes` - 获取权限代码

### 配置管理
- `POST /api/basic/kvPage` - 分页查询配置
- `POST /api/basic/modifyKey` - 修改配置
- `POST /api/basic/delByKey` - 删除配置
- `POST /api/basic/queryKey` - 查询配置值

### 用户管理
- `POST /api/basic/userinfoPageApi` - 分页查询用户
- `POST /api/basic/modifyUserApi` - 修改用户
- `POST /api/basic/delUserinfoApi` - 删除用户
- `POST /api/basic/getUserNameListApi` - 获取用户名列表

### 告警分组
- `POST /api/basic/alarmGroupPageApi` - 分页查询告警分组
- `POST /api/basic/modifyAlarmGroupApi` - 修改告警分组
- `POST /api/basic/delAlarmGroupApi` - 删除告警分组

### 其他功能
- `POST /api/basic/queryJira` - 查询Jira数据
- `GET /api/basic/transOss/<path>` - OSS数据转换
- `POST /api/basic/send_feishu_message` - 发送飞书消息

## 数据视图模块 (/api/dataview/)

### 性能监控
- `POST /api/dataview/queryTopOverview` - 查询系统概览
- `POST /api/dataview/queryTopCpus` - 查询CPU数据
- `POST /api/dataview/queryTopProcess` - 查询进程数据
- `POST /api/dataview/queryCommonAnylizeApi` - 通用分析查询
- `POST /api/dataview/queryHostAllCpuMpstatUsageApi` - 查询主机CPU统计
- `POST /api/dataview/queryHostProcessInnerApi` - 查询主机进程内部数据

### 文件管理
- `POST /api/dataview/getLogFileListPageApi` - 查询日志文件列表
- `POST /api/dataview/insertOssFile` - 插入OSS文件记录
- `POST /api/dataview/rerunLogParseApi` - 重新解析日志

## 定时任务模块 (/api/cron/)

### 任务管理
- `POST /api/cron/cronTaskPage` - 分页查询定时任务
- `POST /api/cron/modifyCronTask` - 修改定时任务
- `POST /api/cron/delCronTask` - 删除定时任务
- `POST /api/cron/rerunTask` - 重新运行任务

### 专项功能
- `POST /api/cron/genDailyExcel` - 生成日报Excel
- `POST /api/cron/cloneJiraIssue` - 克隆Jira问题
- `POST /api/cron/batchTagValues` - 批量标记值
- `POST /api/cron/jira2Feishu` - Jira同步到飞书

## 报告模块 (/api/report/)

### 飞书报告管理
- `POST /api/report/feishuReportPage` - 分页查询飞书报告
- `POST /api/report/modifyFeishuReport` - 修改飞书报告
- `POST /api/report/delFeishuReport` - 删除飞书报告
- `POST /api/report/generateFeishuReportApi` - 生成飞书报告
- `POST /api/report/getFeishuReportLogPageApi` - 查询报告日志
- `POST /api/report/rerunFeishuReportApi` - 重新生成报告
- `POST /api/report/delFeishuReportLogApi` - 删除报告日志
- `POST /api/report/modifyReportRemark` - 修改报告备注

## 地图服务模块 (/api/amap/)

### 车辆和问题查询
- `POST /api/amap/queryCarSite` - 查询车辆位置
- `POST /api/amap/queryJiraIssues` - 查询Jira问题
- `POST /api/amap/queryJiraSite` - 查询Jira问题位置
- `POST /api/amap/queryJiraLine` - 查询Jira问题轨迹
- `POST /api/amap/queryJiraKeysByJql` - 通过JQL查询Jira Key
- `POST /api/amap/queryJiraByKey` - 根据Key查询Jira详情

## 外部接口模块 (/api/outer/)

### 性能分析
- `POST /api/outer/parsePerformance` - 解析性能数据

## 系统监控

### 健康检查
- `GET /misc/ping` - 系统Ping检查
- `GET /actuator/health` - 健康状态检查

---

## 接口统计

- **基础服务**: 13个接口
- **数据视图**: 9个接口
- **定时任务**: 8个接口
- **报告管理**: 8个接口
- **地图服务**: 6个接口
- **外部接口**: 1个接口
- **系统监控**: 2个接口

**总计**: 47个API接口

## 认证要求

- ✅ **需要认证**: 45个接口
- ❌ **无需认证**: 2个接口 (`/misc/ping`, `/actuator/health`)

## HTTP方法分布

- **POST**: 45个接口 (96%)
- **GET**: 2个接口 (4%)

## 高频使用接口

1. `POST /api/basic/auth/login` - 用户登录
2. `POST /api/dataview/queryTopOverview` - 系统性能监控
3. `POST /api/cron/cronTaskPage` - 定时任务查询
4. `POST /api/report/feishuReportPage` - 报告查询
5. `POST /api/amap/queryCarSite` - 车辆轨迹查询

## 安全等级分类

### 🔴 高风险接口
- `POST /api/basic/delByKey` - 删除配置
- `POST /api/basic/delUserinfoApi` - 删除用户
- `POST /api/cron/delCronTask` - 删除定时任务
- `POST /api/report/delFeishuReport` - 删除报告

### 🟡 中风险接口
- `POST /api/basic/modifyKey` - 修改配置
- `POST /api/basic/modifyUserApi` - 修改用户
- `POST /api/cron/modifyCronTask` - 修改定时任务
- `POST /api/report/modifyFeishuReport` - 修改报告

### 🟢 低风险接口
- 所有查询类接口 (query*, get*, Page等)
- 健康检查接口

---

**注意**: 使用高风险接口时请特别谨慎，建议实施额外的权限验证和操作日志记录。