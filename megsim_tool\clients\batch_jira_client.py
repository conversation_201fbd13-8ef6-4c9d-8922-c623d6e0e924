import requests
import json
from datetime import datetime
from typing import List, Optional, Dict, Any

class MegSimBatchJiraClient:
    """
    MegSim 批量Jira API 客户端
    """
    
    def __init__(self, base_url: str = "https://megsim.mc.machdrive.cn"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
    
    def create_batch_jira(self, 
                         event_list: List[int],
                         user_list: List[str],
                         tags: List[str],
                         task_type: List[str],
                         problem_discovery_stage: str,
                         timeout: int = 30) -> Dict[str, Any]:
        """
        创建批量Jira任务
        
        Args:
            event_list: 事件ID列表
            user_list: 用户列表
            tags: 标签列表
            task_type: 任务类型列表
            problem_discovery_stage: 问题发现阶段
            timeout: 请求超时时间（秒）
        
        Returns:
            API响应数据
        """
        url = f"{self.base_url}/api/event/batch/jira"
        
        payload = {
            "event_list": event_list,
            "user_list": user_list,
            "tags": tags,
            "task_type": task_type,
            "problem_discovery_stage": problem_discovery_stage
        }
        
        try:
            response = self.session.post(url, json=payload, timeout=timeout)
            response.raise_for_status()
            
            result = {
                "success": True,
                "status_code": response.status_code,
                "data": response.json(),
                "headers": dict(response.headers),
                "request_payload": payload,
                "timestamp": datetime.now().isoformat()
            }
            
            return result
            
        except requests.exceptions.Timeout:
            return {
                "success": False,
                "error": "请求超时",
                "error_type": "timeout",
                "request_payload": payload,
                "timestamp": datetime.now().isoformat()
            }
        except requests.exceptions.ConnectionError:
            return {
                "success": False,
                "error": "连接错误",
                "error_type": "connection",
                "request_payload": payload,
                "timestamp": datetime.now().isoformat()
            }
        except requests.exceptions.HTTPError as e:
            return {
                "success": False,
                "error": f"HTTP错误: {e}",
                "error_type": "http",
                "status_code": response.status_code,
                "response_text": response.text,
                "request_payload": payload,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"未知错误: {e}",
                "error_type": "unknown",
                "request_payload": payload,
                "timestamp": datetime.now().isoformat()
            }
    
    def validate_payload(self, 
                        event_list: List[int],
                        user_list: List[str],
                        tags: List[str],
                        task_type: List[str],
                        problem_discovery_stage: str) -> Dict[str, Any]:
        """
        验证请求参数
        
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        
        # 验证事件列表
        if not event_list:
            errors.append("事件列表不能为空")
        elif len(event_list) > 100:
            warnings.append(f"事件列表过长({len(event_list)}个)，可能影响性能")
        
        # 验证用户列表
        if not user_list:
            errors.append("用户列表不能为空")
        
        # 验证标签
        if not tags:
            warnings.append("标签列表为空")
        
        # 验证任务类型
        if not task_type:
            errors.append("任务类型不能为空")
        
        # 验证问题发现阶段
        if not problem_discovery_stage or not problem_discovery_stage.strip():
            errors.append("问题发现阶段不能为空")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def generate_curl_command(self, 
                             event_list: List[int],
                             user_list: List[str],
                             tags: List[str],
                             task_type: List[str],
                             problem_discovery_stage: str) -> str:
        """
        生成等效的cURL命令
        
        Returns:
            cURL命令字符串
        """
        url = f"{self.base_url}/api/event/batch/jira"
        payload = {
            "event_list": event_list,
            "user_list": user_list,
            "tags": tags,
            "task_type": task_type,
            "problem_discovery_stage": problem_discovery_stage
        }
        
        json_data = json.dumps(payload, separators=(',', ':'), ensure_ascii=False)
        
        curl_command = f"""curl -X POST "{url}" \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d '{json_data}'"""
        
        return curl_command

def main():
    """
    测试批量Jira API客户端
    """
    print("=== MegSim 批量Jira API 客户端测试 ===")
    print()
    
    # 创建客户端
    client = MegSimBatchJiraClient()
    
    # 测试参数
    event_list = [956580, 956582, 1062195, 956587, 1032283, 1062202, 956592, 956593, 956594, 956595, 956603]
    user_list = ["nan.xu"]
    tags = ["TEST"]
    task_type = ["MR测试"]
    problem_discovery_stage = "MR合入测试"
    
    # 验证参数
    print("1. 参数验证:")
    validation = client.validate_payload(event_list, user_list, tags, task_type, problem_discovery_stage)
    if validation["valid"]:
        print("   ✅ 参数验证通过")
        if validation["warnings"]:
            for warning in validation["warnings"]:
                print(f"   ⚠️ 警告: {warning}")
    else:
        print("   ❌ 参数验证失败:")
        for error in validation["errors"]:
            print(f"      - {error}")
        return
    
    print()
    
    # 发送请求
    print("2. 发送API请求:")
    result = client.create_batch_jira(event_list, user_list, tags, task_type, problem_discovery_stage)
    
    if result["success"]:
        print("   ✅ 请求成功")
        print(f"   状态码: {result['status_code']}")
        print("   响应数据:")
        print(f"   {json.dumps(result['data'], indent=4, ensure_ascii=False)}")
        
        # 分析响应
        response_data = result["data"]
        if response_data.get("code") == 0:
            print("   ✅ 业务逻辑执行成功")
        else:
            print(f"   ⚠️ 业务逻辑返回错误码: {response_data.get('code')}")
            if response_data.get("error_message"):
                print(f"   错误信息: {response_data.get('error_message')}")
    else:
        print("   ❌ 请求失败")
        print(f"   错误类型: {result['error_type']}")
        print(f"   错误信息: {result['error']}")
        if "status_code" in result:
            print(f"   状态码: {result['status_code']}")
    
    print()
    
    # 生成cURL命令
    print("3. 等效cURL命令:")
    curl_command = client.generate_curl_command(event_list, user_list, tags, task_type, problem_discovery_stage)
    print(curl_command)
    
    print()
    print("=== 测试完成 ===")

if __name__ == "__main__":
    main()