# 通用日志内容解析
"""
{
    "name": "******",
    "group_sign": "分组标识， 如果没有分组标识， 则认为一行执行一次数据清洗",
    "chart_config": [{
            "name": "Delay",
            "filter": {
                    "type": "contains",
                    "pattern": "delay = "
            },
            "xAxis": [{
                    "type": "split",
                    "value": " ",
                    "index": 0
            }],
            "yAxis": [{
                    "name": "delay",
                    "operate": [{
                            "type": "split",
                            "value": " ",
                            "index": 6
                    }]
            }]
    }, {
    # 如果是分组类型的日志解析， 可能需要按照当前分组的下标来进行解析
            "name": "Delay",
            "filter": {
                    "type": "contains",
                    "pattern": "delay = "
            },
            "xAxis": {
                "group_idx": 0,
                "operate": [{
                    "type": "split",
                    "value": " ",
                    "index": 0
                }]
            },
            "yAxis": [{
                    "name": "delay",
                    "group_idx": -5, 代表的是第5行记录以后的数据
                    "operate": [{
                            "type": "split",
                            "value": " ",
                            "index": 6
                    }]
            }]
    }]
}
"""
import datetime
import json
import logging
import traceback
from importlib import import_module

from basic.services import get_global_kv
from cron_task.handlers.log_parsers.parser_super import Parser
import cron_task


def apply(file_path, db_entity):
    CommonParser().parse(db_entity.id, db_entity, file_path)

class CommonParser(Parser):
    def __init__(self):
        super().__init__()
        # 如果是通用日志解析，先获取日志解析的配置
        self.line_group = []
        self.log_config = {}
        self.db_entity = None
        self.model_cache = {}
        self.entity_cache = {}

    def parse_file(self, id, db_entity, log_path):
        self.db_entity = db_entity
        log_config = get_global_kv(f"log_parse:common:{db_entity.parse_type}")
        if log_config is None:
            log_config = get_global_kv(f"log_parse:common:{db_entity.vin}:{db_entity.parse_type}")
        if log_config is None:
            raise Exception(f"cannot find chart_config: {db_entity.parse_type}")
        self.handle_log_config(log_config)
        parse_start_flag = False
        with open(log_path, "r", encoding="utf-8") as log:
            # 这是有意思的一个问题，
            # 当readline 报错， 此时，会跳入异常处理， 异常处理完成后，没有抛出异常，就进入了死循环，程序GG
            is_new_line = True
            while True:
                try:
                    line = log.readline()
                    if line == "":
                        break
                    is_new_line = True
                    if self.group_sign == '' or self.group_sign in line:
                        parse_start_flag = True
                    # 如果遇到了分组标识，那么进行日志解析
                    if self.check_group(line):
                        self.parse_lines()
                        self.line_group.clear()
                    if parse_start_flag:
                        self.line_group.append(line)
                except Exception as e:
                    self.error_info = f"log parse fail [{db_entity.parse_type}]: {log_path} {line} {traceback.format_exc()}"
                    logging.error(self.error_info)
                    self.line_group.clear()
                    if is_new_line is False:
                        ## 这时候说明，第二次进来的时候还是没有读取到新的行，从而破开这个死循环
                        break
                    is_new_line = False
            self.flush_common_cache(True)

    def handle_log_config(self, log_config):
        self.log_config = json.loads(log_config)
        self.group_sign = self.log_config.get("group_sign", "")
        self.chart_config = self.log_config.get("chart_config", "")

    def check_group(self, line):
        # 如果遇到了分组标识，或者没有遇到分组标识，那么就认为需要对cache 的数据进行解析
        if self.group_sign == '':
            return True
        if self.group_sign and self.group_sign not in line:
            return False
        return True

    def parse_lines(self):
        # 解析cache的里呢
        if len(self.line_group) == 0:
            return

        if self.group_sign:
            # 如果有分组，需要处理一次
            self.handle_chart_map("")
        else:
            for line in self.line_group:
                self.handle_chart_map(line)

        self.flush_common_cache()

    def handle_chart_map(self, line):
        for chart_config in self.chart_config:
            if not self.handle_filter(chart_config, line):
                continue
            entity_name = chart_config.get("name")
            entity = self.handle_entity(line, chart_config)
            # 获取到所有数据，存储到数据库里
            model_entity = self.get_model(entity_name)

            cache = self.entity_cache.get(entity_name, None)
            if cache is None:
                cache = []
                self.entity_cache[entity_name] = cache
            cache.append(model_entity(**entity))

    def flush_common_cache(self, is_flush=False):
        for key in self.entity_cache.keys():
            value = self.entity_cache.get(key)
            if len(value) >= 1000 or is_flush:
                model_entity = self.get_model(key)
                self.flush_cache(model_entity, value)

    def get_model(self, model_name):
        model_entity = self.model_cache.get(model_name, None)
        if model_entity is None:
            module = import_module(f".models", cron_task.__name__)
            model_entity = getattr(module, model_name)
            self.model_cache[model_name] = model_entity
        return model_entity


    def handle_entity(self, line, chart_config):
        if -1 < chart_config.get('xAxis', {}).get("group_idx", -1) < len(self.line_group):
            line = self.line_group[chart_config.get('xAxis', {}).get("group_idx")]
        x_value = self.handle_operate(line, chart_config.get('xAxis', {}).get("operate", []))
        entity = {'record_time': x_value, 'vin': self.db_entity.vin }
        self.update_time(x_value)
        for y_config in chart_config.get("yAxis", []):
            if -1 < y_config.get("group_idx", -1) < len(self.line_group):
                line = self.line_group[y_config.get("group_idx")]
            field_name = y_config.get("name", "")
            entity[field_name] = self.handle_operate(line, y_config.get("operate", []))
        return entity

    def handle_operate(self, line, operates):
        value = line
        for operate in operates:
            type = operate.get('type')
            if type == 'split':
                value = self.handle_split(operate, value)
            elif type == 'replace':
                value = self.handle_replace(operate, value)
            elif type == 'reform_time':
                value = self.reform_value(operate, value)
        return value.strip()

    def reform_value(self, operate, value):
        """
        由于日期类型会有好多种，因此需要对数据进行重新构造
        """
        old_format = operate.get("old_type", "")
        new_type = operate.get("new_type", "")
        old_time = datetime.datetime.strptime(value.strip(), old_format)
        return old_time.strftime(new_type)

    # 处理line的取值--------------------- start
    def handle_split(self, operate, value):
        split_sign = operate.get('value')
        index = operate.get('index')
        value = value.split(split_sign)[index]
        return value

    def handle_replace(self, operate, value):
        oldValue = operate.get('oldValue')
        newValue = operate.get('newValue')
        value = value.replace(oldValue, newValue)
        return value

    # 处理line的取值--------------------- end
    def handle_filter(self, chart_config, line):
        if "filter" not in chart_config or self.group_sign:
            return True
        filter = chart_config.get("filter", {})
        type = filter.get("type", "")
        if type == "contains":
            pattern = filter.get("pattern", "")
            if pattern == "":
                return False
            if pattern in line:
                return True
        elif type == "startswith":
            pattern = filter.get("pattern", "")
            if pattern == "":
                return False
            if line.startswith(pattern):
                return True
        return False
