"""
URL configuration for megtool_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path, include

from basic import views

urlpatterns = [
    path('api/basic/', include('basic.urls')),
    path('api/dataview/', include('dataview.urls')),

    path('misc/ping', views.ping),
    path('actuator/health', views.health),
    path('api/cron/',  include('cron_task.urls')),
    path('api/report/',  include('report.urls')),
    path('api/amap/',  include('gaode.urls')),
    path('api/outer/',  include('outer_interface.urls')),
]
