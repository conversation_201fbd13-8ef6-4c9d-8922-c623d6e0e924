#!/usr/bin/env python3
"""
MCAP Core SDK - 兼容性模块
为集成测试提供向后兼容的接口
"""

# 从主模块导入所有功能
from src.mcap_parser.main import (
    create_sdk,
    quick_analyze,
    quick_stream,
    McapAutoDriveSDK,
    AnalysisResult,
    MessageData,
    TopicInfo,
    TimeRange,
    ProcessingStats,
    StreamConfig,
    get_version,
    get_supported_message_types,
    is_message_type_supported
)

# 导出所有公共接口
__all__ = [
    'create_sdk',
    'quick_analyze', 
    'quick_stream',
    'McapAutoDriveSDK',
    'AnalysisResult',
    'MessageData',
    'TopicInfo',
    'TimeRange',
    'ProcessingStats',
    'StreamConfig',
    'get_version',
    'get_supported_message_types',
    'is_message_type_supported'
]

__version__ = get_version()