"""异常定义模块"""

from typing import Optional


class JiraCloneException(Exception):
    """Jira克隆基础异常"""
    
    def __init__(self, message: str, details: Optional[str] = None):
        self.message = message
        self.details = details
        super().__init__(message)


class JiraApiException(JiraCloneException):
    """Jira API异常"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_text: Optional[str] = None):
        self.status_code = status_code
        self.response_text = response_text
        super().__init__(message, response_text)


class FieldProcessingException(JiraCloneException):
    """字段处理异常"""
    
    def __init__(self, field_name: str, message: str, field_value=None):
        self.field_name = field_name
        self.field_value = field_value
        super().__init__(f"字段 '{field_name}' 处理失败: {message}")


class ConfigurationException(JiraCloneException):
    """配置异常"""
    pass