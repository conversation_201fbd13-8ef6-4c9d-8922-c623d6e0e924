import json
import sys
from collections import defaultdict
from typing import Dict, List, Any, Tu<PERSON>
from pathlib import Path

# 导入SDK
try:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
    from mcap_parser.core.sdk import McapAutoDriveSDK
    SDK_AVAILABLE = True
except ImportError:
    SDK_AVAILABLE = False
    # 回退到原始ROS2方法
    from mcap_ros2.reader import read_ros2_messages


def sanitize_filename(message_type: str) -> str:
    """清理文件名中的特殊字符"""
    return message_type.replace("/", "_")


def extract_message_types(stream_data) -> Dict[str, Any]:
    """从流数据中提取消息类型"""
    message_types = {}
    
    for msg in stream_data:
        # 获取消息类型
        if hasattr(msg, 'message_type'):
            message_type = msg.message_type
        else:
            message_type = "unknown"
            
        topic = getattr(msg, 'topic', '/unknown')
        timestamp = getattr(msg, 'timestamp', 0)
        data = getattr(msg, 'data', {})
        
        if message_type not in message_types:
            message_types[message_type] = {
                'count': 0,
                'samples': [],
                'topics': set(),
                'first_timestamp': timestamp,
                'last_timestamp': timestamp
            }
        
        # 更新统计信息
        message_types[message_type]['count'] += 1
        message_types[message_type]['topics'].add(topic)
        message_types[message_type]['last_timestamp'] = timestamp
        
        # 收集样本数据（最多5个）
        if len(message_types[message_type]['samples']) < 5:
            message_types[message_type]['samples'].append({
                'timestamp': timestamp,
                'topic': topic,
                'data': data
            })
    
    # 转换topics为列表
    for msg_type in message_types:
        message_types[msg_type]['topics'] = list(message_types[msg_type]['topics'])
    
    return message_types


def collect_sample_data(messages, message_type: str, limit: int = 5) -> List[Dict[str, Any]]:
    """收集指定消息类型的样本数据"""
    samples = []
    count = 0
    
    for msg in messages:
        if hasattr(msg, 'message_type') and msg.message_type == message_type:
            if count >= limit:
                break
            
            sample = {
                'timestamp': getattr(msg, 'timestamp', 0),
                'topic': getattr(msg, 'topic', '/unknown'),
                'data': getattr(msg, 'data', {})
            }
            samples.append(sample)
            count += 1
    
    return samples


def generate_json_files(
    message_types: Dict[str, Any], file_metadata: Dict[str, Any], output_dir: str = "."
) -> int:
    """生成JSON文件"""
    import os
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    success_count = 0

    for message_type, type_info in message_types.items():
        filename = f"{sanitize_filename(message_type)}.json"
        filepath = os.path.join(output_dir, filename)

        output_data = {
            "message_type": message_type,
            "count": type_info.get("count", 0),
            "samples": type_info.get("samples", []),
            "topics": type_info.get("topics", []),
            "first_timestamp": type_info.get("first_timestamp", 0),
            "last_timestamp": type_info.get("last_timestamp", 0),
            "file_metadata": file_metadata
        }

        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(
                    output_data, f, default=str, indent=2, ensure_ascii=False
                )
            success_count += 1
        except Exception as e:
            print(f"❌ 生成 {filename} 时出错: {e}")

    return success_count


def _extract_message_types_from_mcap(mcap_file: str) -> Tuple[Dict[str, Any], Dict[str, int]]:
    """从MCAP文件中提取消息类型（内部函数）"""
    import os
    
    # 检查文件是否存在
    if not os.path.exists(mcap_file):
        raise FileNotFoundError(f"MCAP文件不存在: {mcap_file}")
    
    message_types = {}
    message_counts = defaultdict(int)
    processed_count = 0

    try:
        if SDK_AVAILABLE:
            # 使用SDK处理，支持ROS1和ROS2
            sdk = McapAutoDriveSDK(verbose=False)
            for msg_data in sdk.stream_data(mcap_file):
                processed_count += 1
                message_type = msg_data.message_type
                topic = msg_data.topic
                timestamp = msg_data.timestamp
                data = msg_data.data
                
                message_counts[message_type] += 1
                
                # 存储第一个样本
                if message_type not in message_types:
                    message_types[message_type] = {
                        "message_type": message_type,
                        "sample_topic": topic,
                        "sample_timestamp": timestamp,
                        "sample_message": data,
                        "topics": set([topic]),
                        "debug_info": {}
                    }
                else:
                    message_types[message_type]["topics"].add(topic)
        else:
            # 回退到原始ROS2方法
            for msg in read_ros2_messages(mcap_file):
                processed_count += 1

                # 尝试多种方式获取真正的消息类型
                message_type = "unknown"

                # 首先尝试从ROS消息对象获取类型
                if hasattr(msg, 'ros_msg') and hasattr(msg.ros_msg, '__class__'):
                    class_name = msg.ros_msg.__class__.__name__
                    module_name = msg.ros_msg.__class__.__module__
                    if module_name and 'interfaces' in module_name:
                        # 提取ROS2消息类型，如 sensor_msgs.msg.Image -> sensor_msgs/Image
                        parts = module_name.split('.')
                        if len(parts) >= 2 and parts[-1] == 'msg':
                            package_name = parts[-2]
                            message_type = f"{package_name}/{class_name}"
                        else:
                            message_type = f"{module_name}.{class_name}"
                    else:
                        message_type = (
                            f"{module_name}.{class_name}" if module_name else class_name
                        )

                # 如果还是unknown，尝试其他方式
                if message_type == "unknown":
                    if hasattr(msg.channel, 'schema_name') and msg.channel.schema_name:
                        message_type = msg.channel.schema_name
                    elif (
                        hasattr(msg.channel, 'message_encoding')
                        and msg.channel.message_encoding
                    ):
                        message_type = msg.channel.message_encoding

                topic = msg.channel.topic
                message_counts[message_type] += 1

                # 存储第一个样本
                if message_type not in message_types:
                    debug_info = {
                        "channel_id": getattr(msg.channel, 'id', None),
                        "schema_name": getattr(msg.channel, 'schema_name', None),
                        "message_encoding": getattr(msg.channel, 'message_encoding', None),
                        "metadata": getattr(msg.channel, 'metadata', None),
                    }

                    message_types[message_type] = {
                        "message_type": message_type,
                        "sample_topic": topic,
                        "sample_timestamp": msg.log_time,
                        "sample_message": msg.ros_msg,
                        "topics": set([topic]),
                        "debug_info": debug_info
                    }
                else:
                    message_types[message_type]["topics"].add(topic)
    except Exception as e:
        raise Exception(f"读取MCAP文件时出错: {e}")

    return message_types, dict(message_counts)


def analyze_mcap_file(mcap_file: str) -> Dict[str, Any]:
    """分析MCAP文件"""
    message_types, message_counts = _extract_message_types_from_mcap(mcap_file)
    
    # 转换为测试期望的格式
    formatted_message_types = {}
    for msg_type, type_info in message_types.items():
        formatted_message_types[msg_type] = {
            'count': message_counts[msg_type],
            'samples': [{
                'timestamp': type_info['sample_timestamp'],
                'topic': type_info['sample_topic'],
                'data': type_info['sample_message']
            }],
            'topics': list(type_info['topics']),
            'first_timestamp': type_info['sample_timestamp'],
            'last_timestamp': type_info['sample_timestamp']
        }

    file_metadata = {
        'filename': mcap_file,
        'total_messages': sum(message_counts.values()),
        'unique_types': len(message_types)
    }

    return {
        "analysis": {
            "total_messages": sum(message_counts.values()),
            "unique_types": len(message_types)
        },
        "message_types": formatted_message_types,
        "file_metadata": file_metadata
    }


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print(f"Usage: python {sys.argv[0]} <mcap_file>\n")
        print("参数说明:")
        print("  <mcap_file>  要分析的MCAP文件路径")
        print("\n示例:")
        print(f"  python {sys.argv[0]} data.mcap")
        sys.exit(1)

    mcap_file = sys.argv[1]
    
    # 检查是否是帮助请求
    if mcap_file in ['--help', '-h', 'help']:
        print("MCAP文件分析工具")
        print(f"\n使用方法: python {sys.argv[0]} <mcap_file>")
        print("\n参数说明:")
        print("  <mcap_file>  要分析的MCAP文件路径")
        print("\n功能:")
        print("  - 分析MCAP文件中的消息类型")
        print("  - 统计各类型消息数量")
        print("  - 生成JSON格式的样本数据")
        print("\n示例:")
        print(f"  python {sys.argv[0]} data.mcap")
        sys.exit(0)

    print(f"🚗 正在分析自动驾驶感知数据: {mcap_file}")
    print("=" * 60)

    try:
        # 分析MCAP文件
        analysis_result = analyze_mcap_file(mcap_file)
        message_types = analysis_result["message_types"]
        file_metadata = analysis_result["file_metadata"]
        analysis = analysis_result["analysis"]
    except (FileNotFoundError, Exception) as e:
        print(f"❌ 错误: {e}")
        sys.exit(2)

    print("\n✅ 扫描完成！")
    print(f"📈 总消息数: {analysis['total_messages']}")
    print(f"🏷️  消息类型数: {analysis['unique_types']}")
    print("\n📋 消息类型统计:")
    print("-" * 80)

    # 按消息数量排序显示
    for msg_type, type_info in sorted(
        message_types.items(), key=lambda x: x[1]['count'], reverse=True
    ):
        count = type_info['count']
        topics_list = type_info['topics']
        topics_str = ", ".join(topics_list[:3])  # 只显示前3个topic
        if len(topics_list) > 3:
            topics_str += f" (共{len(topics_list)}个topic)"
        print(f"  {msg_type:<40} | {count:>6} 条 | {topics_str}")

    print("\n🔄 正在生成JSON文件...")
    print("=" * 60)

    # 生成JSON文件
    success_count = generate_json_files(message_types, file_metadata)

    for message_type, type_info in message_types.items():
        filename = f"{sanitize_filename(message_type)}.json"
        count = type_info['count']
        print(f"✅ {filename:<50} | {count:>6} 条消息")

    print("=" * 60)
    print("🎉 处理完成！")
    print(f"📁 成功生成 {success_count}/{len(message_types)} 个JSON文件")
    print(f"📊 总共发现 {len(message_types)} 种不同的消息类型")
    print("🚗 自动驾驶感知数据解析完成！")


if __name__ == '__main__':
    main()
