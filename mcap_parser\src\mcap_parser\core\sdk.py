#!/usr/bin/env python3

import os
import time
from pathlib import Path
from typing import Dict, Iterator, List, Optional, Union

# 导入日志系统
try:
    from ..utils.sdk_logger import get_logger, log_info, log_warning, log_error, log_success, log_progress, log_performance, log_file_operation, log_parsing_info, log_data_info, log_lane_info
except ImportError:
    try:
        from ..utils.logger import get_logger, log_info, log_warning, log_error, log_success, log_progress, log_performance, log_file_operation, log_parsing_info, log_data_info, log_lane_info
    except ImportError:
        # 回退到print（兼容性）
        def get_logger(): return None
        def log_info(msg, **kwargs): print(f"INFO - {msg}")
        def log_warning(msg, **kwargs): print(f"WARNING - {msg}")
        def log_error(msg, **kwargs): print(f"ERROR - {msg}")
        def log_success(msg, **kwargs): print(f"SUCCESS - {msg}")
        def log_progress(msg, current=None, total=None, **kwargs):
            if current and total:
                print(f"PROGRESS - {msg} ({current}/{total})")
            else:
                print(f"PROGRESS - {msg}")
        def log_performance(op, duration, **kwargs): print(f"PERFORMANCE - {op} 完成，耗时 {duration:.3f}s")
        def log_file_operation(op, path, **kwargs): print(f"FILE - {op}: {path}")
        def log_parsing_info(msg, **kwargs): print(f"PARSING - {msg}")
        def log_data_info(msg, **kwargs): print(f"DATA - {msg}")
        def log_lane_info(msg, **kwargs): print(f"LANE - {msg}")

# MCAP相关导入
try:
    from mcap_ros2.reader import read_ros2_messages
    MCAP_AVAILABLE = True
except ImportError:
    MCAP_AVAILABLE = False
    log_warning("MCAP ROS2支持不可用，请安装: pip install mcap-ros2-support")

# 导入数据类和其他模块
try:
    from ..data_structures import (
        MessageData, TopicInfo, TimeRange, ProcessingStats,
        AnalysisResult, StreamConfig
    )
    from ..parsers.message_registry import MessageTypeRegistry
    from ..parsers.fast_parser import FastMcapParser
except ImportError:
    try:
        from ..data_structures import (
            MessageData, TopicInfo, TimeRange, ProcessingStats,
            AnalysisResult, StreamConfig
        )
        from ..parsers.message_registry import MessageTypeRegistry
        from ..parsers.fast_parser import FastMcapParser
    except ImportError as e:
        log_error(f"导入错误: {e}")
        log_error("请确保所有依赖模块都在正确的路径中")
        raise

# 尝试导入结构化数据模块（可选）
try:
    from realtime_data_structures import *
    DATA_STRUCTURES_AVAILABLE = True
except ImportError:
    DATA_STRUCTURES_AVAILABLE = False
    # 这是可选模块，不影响核心功能


class McapAutoDriveSDK:
    """MCAP自动驾驶数据解析SDK - 高性能MCAP文件解析器"""

    def __init__(self,
                 enable_structured_data: bool = False,
                 verbose: bool = False,
                 enable_fast_mode: bool = True,
                 fast_batch_size: int = 10000,
                 fast_max_workers: int = 4):
        """初始化MCAP自动驾驶SDK"""
        self.enable_structured_data = enable_structured_data
        self.verbose = verbose
        self.enable_fast_mode = enable_fast_mode

        # 检查依赖
        if not MCAP_AVAILABLE:
            raise ImportError("MCAP ROS2支持不可用，请安装: pip install mcap-ros2-support")

        if enable_structured_data and not DATA_STRUCTURES_AVAILABLE:
            log_warning("结构化数据模块不可用，将禁用结构化数据转换")
            self.enable_structured_data = False

        # 初始化统计
        self.stats = ProcessingStats()

        # 初始化高速解析器
        if self.enable_fast_mode:
            try:
                self.fast_parser = FastMcapParser(
                    verbose=verbose,
                    batch_size=fast_batch_size,
                    max_workers=fast_max_workers
                )
            except Exception as e:
                if verbose:
                    log_warning(f"高速解析器初始化失败，将使用标准模式: {e}")
                self.enable_fast_mode = False
                self.fast_parser = None
        else:
            self.fast_parser = None

        if self.verbose:
            log_success("MCAP自动驾驶SDK初始化完成")
            log_info(f"支持的消息类型: {MessageTypeRegistry.get_supported_count()}")
            log_info(f"结构化数据: {'启用' if self.enable_structured_data else '禁用'}")
            log_info(f"高速解析模式: {'启用' if self.enable_fast_mode else '禁用'}")

    def analyze_mcap_file(self, mcap_file: Union[str, Path]) -> AnalysisResult:
        """分析MCAP文件，提取文件信息、话题统计、消息类型分布等"""
        mcap_file = Path(mcap_file)
        
        if not mcap_file.exists():
            raise FileNotFoundError(f"MCAP文件不存在: {mcap_file}")
        
        if self.verbose:
            log_parsing_info(f"开始分析MCAP文件: {mcap_file.name}")
        
        start_time = time.time()
        
        # 初始化分析结果
        result = AnalysisResult(
            file_path=str(mcap_file),
            file_size=mcap_file.stat().st_size
        )
        
        # 统计变量
        message_count = 0
        topics = {}
        message_types = {}
        first_timestamp = None
        last_timestamp = None
        
        try:
            # 读取并分析消息
            for ros_msg in read_ros2_messages(str(mcap_file)):
                message_count += 1

                # 更新时间范围 - 使用正确的MCAP ROS2属性
                timestamp = getattr(ros_msg, 'log_time_ns', getattr(ros_msg, 'publish_time_ns', 0)) / 1e9  # 纳秒转秒
                if first_timestamp is None:
                    first_timestamp = timestamp
                last_timestamp = timestamp
                
                # 统计话题信息 - 使用正确的MCAP ROS2属性
                topic = ros_msg.channel.topic if hasattr(ros_msg, 'channel') and hasattr(ros_msg.channel, 'topic') else 'unknown'
                msg_type = ros_msg.schema.name if hasattr(ros_msg, 'schema') and hasattr(ros_msg.schema, 'name') else 'unknown'

                if topic not in topics:
                    topics[topic] = TopicInfo(
                        topic_name=topic,
                        message_type=msg_type,
                        first_timestamp=timestamp
                    )

                topic_info = topics[topic]
                topic_info.message_count += 1
                topic_info.last_timestamp = timestamp

                # 统计消息类型
                message_types[msg_type] = message_types.get(msg_type, 0) + 1
                
                # 进度显示
                if self.verbose and message_count % 10000 == 0:
                    log_progress(f"已分析 {message_count:,} 条消息")
        
        except Exception as e:
            if self.verbose:
                log_error(f"分析过程中出错: {e}")
            raise
        
        # 计算话题持续时间
        for topic_info in topics.values():
            if topic_info.first_timestamp and topic_info.last_timestamp:
                topic_info.duration = topic_info.last_timestamp - topic_info.first_timestamp
        
        # 填充分析结果
        result.total_messages = message_count
        result.total_topics = len(topics)
        result.total_message_types = len(message_types)
        result.duration = (last_timestamp - first_timestamp) if first_timestamp and last_timestamp else 0.0
        result.topics = topics
        result.message_types = message_types
        result.processing_time = time.time() - start_time
        
        # 分类支持的和不支持的消息类型
        all_types = list(message_types.keys())
        validation = MessageTypeRegistry.validate_types(all_types)
        result.supported_types = validation["supported"]
        result.unsupported_types = validation["unsupported"]
        
        if self.verbose:
            log_performance("分析", result.processing_time)
            log_data_info(f"总消息数: {result.total_messages:,}")
            log_data_info(f"话题数: {result.total_topics}")
            log_data_info(f"消息类型数: {result.total_message_types}")
            log_data_info(f"支持的类型: {len(result.supported_types)}")
            log_data_info(f"不支持的类型: {len(result.unsupported_types)}")
        
        return result

    def stream_data(self,
                   mcap_file: Union[str, Path],
                   message_types: Optional[List[str]] = None,
                   topics: Optional[List[str]] = None,
                   time_range: Optional[TimeRange] = None,
                   max_messages: Optional[int] = None) -> Iterator[MessageData]:
        """标准流式处理MCAP数据"""

        if self.verbose:
            log_parsing_info(f"开始解析MCAP文件: {mcap_file}")
            log_info(f"目标消息类型: {message_types}")
            log_info(f"最大消息数: {max_messages}")
            log_info(f"快速模式: {self.enable_fast_mode}")

        # 如果启用快速模式，使用快速流式处理
        if self.enable_fast_mode and self.fast_parser:
            if self.verbose:
                log_info("使用快速模式")
            return self.fast_stream_data(mcap_file, message_types, topics, time_range, max_messages)

        # 标准流式处理
        if self.verbose:
            log_info("使用标准流式处理")

        return self._standard_stream_data(mcap_file, message_types, topics, time_range, max_messages)

    def _standard_stream_data(self,
                             mcap_file: Union[str, Path],
                             message_types: Optional[List[str]] = None,
                             topics: Optional[List[str]] = None,
                             time_range: Optional[TimeRange] = None,
                             max_messages: Optional[int] = None) -> Iterator[MessageData]:
        """标准流式处理实现"""

        mcap_path = Path(mcap_file)
        if not mcap_path.exists():
            raise FileNotFoundError(f"MCAP文件不存在: {mcap_path}")

        if self.verbose:
            log_info(f"📂 文件大小: {mcap_path.stat().st_size / 1024 / 1024:.1f} MB")

        message_count = 0
        found_types = set()

        try:
            # 使用ROS2解码器读取消息
            if MCAP_AVAILABLE:
                for ros_msg in read_ros2_messages(str(mcap_path)):
                    # 调试：打印消息对象的属性
                    if self.verbose and message_count == 0:
                        log_info(f"🔍 消息对象属性: {[attr for attr in dir(ros_msg) if not attr.startswith('_')]}")

                    # 提取消息信息 - 根据调试结果使用正确的属性名
                        try:
                            # 从channel对象获取topic
                            if hasattr(ros_msg, 'channel') and hasattr(ros_msg.channel, 'topic'):
                                topic = ros_msg.channel.topic
                            else:
                                topic = 'unknown'

                            # 获取时间戳
                            timestamp = ros_msg.log_time_ns / 1e9 if hasattr(ros_msg, 'log_time_ns') else 0

                            # 从schema获取消息类型
                            if hasattr(ros_msg, 'schema') and hasattr(ros_msg.schema, 'name'):
                                message_type = ros_msg.schema.name
                            else:
                                message_type = 'unknown'

                            # 获取消息数据
                            data = ros_msg.ros_msg if hasattr(ros_msg, 'ros_msg') else None

                            if self.verbose and message_count < 5:
                                log_info(f"📋 消息 {message_count + 1}:")
                                log_info(f"   话题: {topic}")
                                log_info(f"   类型: {message_type}")
                                log_info(f"   时间: {timestamp:.3f}s")

                        except Exception as attr_error:
                            if self.verbose:
                                log_warning(f"⚠️  提取消息属性失败: {attr_error}")
                            continue

                        found_types.add(message_type)

                        # 过滤消息类型
                        if message_types and message_type not in message_types:
                            continue

                        # 过滤话题
                        if topics and topic not in topics:
                            continue

                        # 时间范围过滤
                        if time_range:
                            if timestamp < time_range.start_time or timestamp > time_range.end_time:
                                continue

                        # 创建消息数据
                        msg_data = MessageData(
                            data=data,
                            timestamp=timestamp,
                            topic=topic,
                            message_type=message_type
                        )

                        yield msg_data
                        message_count += 1

                        if self.verbose and message_count <= 3:
                            log_success(f"✅ 找到消息 {message_count}: {message_type} @ {timestamp:.3f}s")

                        # 检查最大消息数限制
                        if max_messages and message_count >= max_messages:
                            break

        except Exception as e:
            if self.verbose:
                log_error(f"❌ 读取MCAP文件时出错: {e}")
            raise

        if self.verbose:
            log_success(f"📊 处理完成:")
            log_info(f"   总消息数: {message_count}")
            log_info(f"   发现的消息类型: {sorted(found_types)}")

    def fast_stream_data(self,
                        mcap_file: Union[str, Path],
                        message_types: Optional[List[str]] = None,
                        topics: Optional[List[str]] = None,
                        time_range: Optional[TimeRange] = None,
                        max_messages: Optional[int] = None) -> Iterator[MessageData]:
        """高速流式处理MCAP数据，使用批量读取和索引优化"""
        if not self.enable_fast_mode or not self.fast_parser:
            if self.verbose:
                log_warning("⚠️  高速模式不可用，回退到标准流式处理")
            return self._standard_stream_data(mcap_file, message_types, topics, time_range, max_messages)

        # 使用高速解析器
        for msg_data in self.fast_parser.fast_stream_data(
            mcap_file=mcap_file,
            message_types=message_types,
            topics=topics,
            time_range=time_range,
            max_messages=max_messages,
            enable_structured_data=self.enable_structured_data
        ):
            yield msg_data

        # 更新统计信息
        self.stats = self.fast_parser.get_statistics()



    def random_access(self, mcap_file: Union[str, Path], timestamp: float,
                     tolerance: float = 0.1) -> Optional[MessageData]:
        """随机访问指定时间点的消息"""
        if not self.enable_fast_mode or not self.fast_parser:
            if self.verbose:
                log_warning("⚠️  随机访问需要高速模式支持")
            return None

        return self.fast_parser.random_access(mcap_file, timestamp, tolerance)

    def get_statistics(self) -> ProcessingStats:
        """获取处理统计信息"""
        return self.stats

    def analyze_file(self, mcap_file: Union[str, Path]) -> AnalysisResult:
        """分析文件 (兼容性方法)"""
        return self.analyze_mcap_file(mcap_file)

    def stream_data(self, mcap_file: Union[str, Path], message_types: list = None, max_messages: int = None):
        """流式处理数据 (兼容性方法)"""
        # 调用主要的stream_data方法
        for msg in self._standard_stream_data(mcap_file, message_types=message_types, max_messages=max_messages):
            yield msg


