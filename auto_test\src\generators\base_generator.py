from abc import ABC, abstractmethod
from typing import List, Optional, Dict
from src.core.config_manager import ConfigManager
from src.clients.jira_client import JiraClient
from src.utils.logger import get_logger

logger = get_logger(__name__)


class BaseGenerator(ABC):
    """基础报告生成器抽象类"""
    
    def __init__(self, config: ConfigManager, jira_client: JiraClient):
        self.config = config
        self.jira_client = jira_client
    
    @abstractmethod
    def generate(self, issues: List, route_info: Optional[Dict] = None) -> Optional[str]:
        """生成报告"""
        pass
    
    def _generate_common_report_header(self, issues: List, route_info: Optional[Dict] = None) -> str:
        """生成报告通用头部"""
        from datetime import datetime
        report = f"# 问题分析报告\n\n"
        report += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 添加路线信息
        if route_info and route_info.get('total_distance', 0) > 0:
            report += f"**总测试里程**: {route_info['total_distance']:.1f} 公里\n\n"
        
        report += f"**分析问题数量**: {len(issues)}\n\n"
        return report
    
    def _format_issue_table(self, issues: List) -> str:
        """格式化问题表格"""
        if not issues:
            return ""
        
        report = "## 问题列表\n\n"
        report += "| 序号 | 问题Key | 摘要 | 链接 |\n"
        report += "|------|---------|------|------|\n"
        
        jira_url = self.config.get('Jira', 'url')
        for i, issue in enumerate(issues, 1):
            issue_link = f"{jira_url}/browse/{issue.key}"
            summary = issue.fields.summary
            if len(summary) > 60:
                summary = summary[:57] + "..."
            
            report += f"| {i} | {issue.key} | {summary} | [查看]({issue_link}) |\n"
        
        return report
    
    def get_config(self, section: str, option: str, default: str = '') -> str:
        """获取配置值"""
        return self.config.get(section, option, default)
    
    def get_config_int(self, section: str, option: str, default: int = 0) -> int:
        """获取整数配置值"""
        return self.config.get_int(section, option, default)
    
    def get_config_float(self, section: str, option: str, default: float = 0.0) -> float:
        """获取浮点数配置值"""
        return self.config.get_float(section, option, default)
    
    def get_config_boolean(self, section: str, option: str, default: bool = False) -> bool:
        """获取布尔配置值"""
        return self.config.get_boolean(section, option, default) 