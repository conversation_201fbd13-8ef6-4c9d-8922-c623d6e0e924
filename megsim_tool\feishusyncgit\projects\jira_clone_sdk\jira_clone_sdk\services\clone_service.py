"""Jira克隆服务"""

import logging
from typing import Dict, List, Tuple, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from ..client import JiraApiClient
from ..config import SDKConfig, CloneConfig
from ..exceptions import JiraCloneException, JiraApiException
from .field_service import FieldFilterService
from ..processors import ProcessorRegistry


class JiraCloneSDK:
    """Jira克隆SDK主类"""
    
    def __init__(self, base_url: str = None, bearer_token: str = None, config: SDKConfig = None):
        if config:
            self.config = config
        elif base_url and bearer_token:
            self.config = SDKConfig(base_url=base_url, bearer_token=bearer_token)
        else:
            raise ValueError("必须提供config或base_url和bearer_token")
        
        # 初始化组件
        self.client = JiraApiClient(self.config)
        self.field_service = FieldFilterService()
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format=self.config.log_format
        )
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            user = self.client.get_current_user()
            if user:
                self.logger.info(f"连接成功! 当前用户: {user.get('displayName', user.get('name', 'Unknown'))}")
                return True
            else:
                self.logger.error("连接失败: 无法获取用户信息")
                return False
        except Exception as e:
            self.logger.error(f"连接测试失败: {str(e)}")
            return False
    
    def clone_issue(self, issue_key: str, config: CloneConfig = None) -> str:
        """克隆单个问题"""
        if config is None:
            config = CloneConfig()
        
        try:
            self.logger.info(f"开始克隆问题: {issue_key}")
            
            # 获取原问题
            source_issue = self.client.get_issue(issue_key)
            if not source_issue:
                raise JiraCloneException(f"无法获取问题: {issue_key}")
            
            # 获取当前用户信息
            current_user = self.client.get_current_user()
            
            # 准备上下文
            context = {
                'source_issue': source_issue,
                'current_user': current_user,
                'issue_key': issue_key
            }
            
            # 过滤和处理字段
            source_fields = source_issue.get('fields', {})
            filtered_fields = self.field_service.filter_fields(source_fields, config, context)
            
            # 创建克隆问题
            cloned_issue = self._create_issue_with_retry(filtered_fields, config)
            cloned_key = cloned_issue['key']
            
            # 添加克隆前缀到标题
            if config.clone_prefix:
                summary = filtered_fields.get('summary', '')
                updated_summary = f"{config.clone_prefix}{summary}"
                self.client.update_issue(cloned_key, {'summary': updated_summary})
            
            # 创建克隆链接
            if config.create_clone_link:
                self.client.create_issue_link(issue_key, cloned_key, "Cloners")
            
            self.logger.info(f"克隆成功: {issue_key} -> {cloned_key}")
            return cloned_key
            
        except Exception as e:
            self.logger.error(f"克隆失败: {issue_key} - {str(e)}")
            raise JiraCloneException(f"克隆问题失败: {str(e)}")
    
    def clone_issues_batch(self, issue_keys: List[str], config: CloneConfig = None) -> Tuple[List[str], List[str]]:
        """批量克隆问题"""
        if config is None:
            config = CloneConfig()
        
        successful = []
        failed = []
        
        self.logger.info(f"开始批量克隆 {len(issue_keys)} 个问题")
        
        # 使用线程池进行并发处理
        with ThreadPoolExecutor(max_workers=self.config.concurrent_requests) as executor:
            # 分批处理
            for i in range(0, len(issue_keys), self.config.batch_size):
                batch = issue_keys[i:i + self.config.batch_size]
                
                # 提交任务
                future_to_key = {
                    executor.submit(self._clone_single_issue, key, config): key 
                    for key in batch
                }
                
                # 收集结果
                for future in as_completed(future_to_key):
                    issue_key = future_to_key[future]
                    try:
                        cloned_key = future.result()
                        successful.append(cloned_key)
                        self.logger.info(f"  ✓ {issue_key} -> {cloned_key}")
                    except Exception as e:
                        failed.append(issue_key)
                        self.logger.error(f"  ✗ {issue_key}: {str(e)}")
        
        self.logger.info(f"批量克隆完成: 成功 {len(successful)}, 失败 {len(failed)}")
        return successful, failed
    
    def clone_issues_by_jql(self, jql: str, config: CloneConfig = None, max_results: int = 50) -> Tuple[List[str], List[str]]:
        """通过JQL搜索并克隆问题"""
        if config is None:
            config = CloneConfig()
        
        self.logger.info(f"使用JQL搜索问题: {jql}")
        
        # 搜索问题
        issues = self.client.search_issues(jql, max_results)
        if not issues:
            self.logger.warning("未找到匹配的问题")
            return [], []
        
        issue_keys = [issue['key'] for issue in issues]
        self.logger.info(f"找到 {len(issue_keys)} 个问题，开始批量克隆")
        
        # 显示要克隆的问题
        for issue in issues:
            self.logger.info(f"  - {issue['key']}: {issue['fields']['summary']}")
        
        # 批量克隆
        return self.clone_issues_batch(issue_keys, config)
    
    def _clone_single_issue(self, issue_key: str, config: CloneConfig) -> str:
        """克隆单个问题（内部方法）"""
        return self.clone_issue(issue_key, config)
    
    def _create_issue_with_retry(self, fields: Dict[str, Any], config: CloneConfig) -> Dict[str, Any]:
        """创建问题并重试"""
        for attempt in range(config.max_retries):
            try:
                return self.client.create_issue(fields)
            except JiraApiException as e:
                if attempt == config.max_retries - 1:
                    raise
                
                if e.status_code == 400:
                    # 尝试分离基本字段和自定义字段
                    self.logger.warning(f"创建失败，尝试分离字段 (尝试 {attempt + 1}/{config.max_retries})")
                    
                    basic_fields = self._extract_basic_fields(fields)
                    custom_fields = self._extract_custom_fields(fields)
                    
                    try:
                        # 先创建基本问题
                        issue = self.client.create_issue(basic_fields)
                        
                        # 再更新自定义字段
                        if custom_fields:
                            self.client.update_issue(issue['key'], custom_fields)
                        
                        return issue
                    except Exception as retry_e:
                        self.logger.error(f"重试失败: {str(retry_e)}")
                        if attempt == config.max_retries - 1:
                            raise
                else:
                    raise
        
        raise JiraCloneException("创建问题失败，已达到最大重试次数")
    
    def _extract_basic_fields(self, fields: Dict[str, Any]) -> Dict[str, Any]:
        """提取基本字段"""
        basic_field_names = {'project', 'summary', 'issuetype', 'description', 'assignee'}
        return {k: v for k, v in fields.items() if k in basic_field_names}
    
    def _extract_custom_fields(self, fields: Dict[str, Any]) -> Dict[str, Any]:
        """提取自定义字段"""
        basic_field_names = {'project', 'summary', 'issuetype', 'description', 'assignee'}
        return {k: v for k, v in fields.items() if k not in basic_field_names}
    
    def add_field_processor(self, processor):
        """添加字段处理器"""
        self.field_service.add_processor(processor)
    
    def get_supported_fields(self) -> List[str]:
        """获取支持的字段列表"""
        return self.field_service.get_supported_fields()
    
    def edit_cloned_data(self, issue_key: str, field_updates: Dict[str, Any]) -> bool:
        """编辑克隆后的数据"""
        try:
            self.logger.info(f"编辑克隆数据: {issue_key}")
            return self.client.update_issue(issue_key, field_updates)
        except Exception as e:
            self.logger.error(f"编辑失败: {issue_key} - {str(e)}")
            return False