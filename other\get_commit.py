import requests

# 直接填写GitLab私有访问令牌
GITLAB_PRIVATE_TOKEN = "tx_Up49xbJfEDzC2JafF"  # <-- 这里填写你的token

if not GITLAB_PRIVATE_TOKEN:
    raise ValueError("请设置GITLAB_PRIVATE_TOKEN")

# 配置信息
GITLAB_URL = "https://git-core.megvii-inc.com"
PROJECT_PATH = "e2e/zeta"  # 项目路径
MERGE_REQUEST_IID = 3194  # 合并请求ID

# 构造API基础URL
project_encoded = requests.utils.quote(PROJECT_PATH, safe='')
api_url = f"{GITLAB_URL}/api/v4/projects/{project_encoded}/merge_requests/{MERGE_REQUEST_IID}/commits"

# 设置请求头
headers = {"PRIVATE-TOKEN": GITLAB_PRIVATE_TOKEN}

# 存储所有commit ID的列表
commit_ids = []
page = 1

while True:
    # 添加分页参数
    params = {"page": page, "per_page": 100}  # 每页最大100条
    
    # 发送API请求
    response = requests.get(api_url, headers=headers, params=params)
    
    # 检查响应状态
    if response.status_code != 200:
        print(f"请求失败，状态码: {response.status_code}")
        print(f"错误信息: {response.text}")
        break
    
    # 解析JSON数据
    commits = response.json()
    
    # 如果没有数据则退出循环
    if not commits:
        break
    
    # 提取当前页的所有commit ID
    for commit in commits:
        commit_ids.append(commit['id'])
    
    # 检查是否有下一页
    if "next" not in response.links:
        break
    
    page += 1

print(f"共获取到 {len(commit_ids)} 个commit ID：")
if commit_ids:
    print(f"第一个commit ID: {commit_ids[0]}")  # 单独输出第一个commit id
for i, commit_id in enumerate(commit_ids, 1):
    print(f"{i}. {commit_id}")
