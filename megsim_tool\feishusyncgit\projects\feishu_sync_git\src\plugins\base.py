"""插件系统基础类"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Type, Optional
from ..config import Settings


class BasePlugin(ABC):
    """插件基类"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(self.__class__.__name__)
        self.enabled = True
    
    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称"""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass
    
    @property
    def description(self) -> str:
        """插件描述"""
        return ""
    
    async def initialize(self) -> None:
        """初始化插件"""
        self.logger.info(f"插件 {self.name} v{self.version} 初始化")
    
    async def cleanup(self) -> None:
        """清理插件资源"""
        self.logger.info(f"插件 {self.name} 清理完成")
    
    def enable(self) -> None:
        """启用插件"""
        self.enabled = True
        self.logger.info(f"插件 {self.name} 已启用")
    
    def disable(self) -> None:
        """禁用插件"""
        self.enabled = False
        self.logger.info(f"插件 {self.name} 已禁用")


class PluginManager:
    """插件管理器"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(self.__class__.__name__)
        self._plugins: Dict[str, BasePlugin] = {}
        self._plugin_types: Dict[str, List[BasePlugin]] = {}
    
    async def initialize(self) -> None:
        """初始化所有插件"""
        for plugin in self._plugins.values():
            if plugin.enabled:
                await plugin.initialize()
        
        self.logger.info(f"插件管理器初始化完成，加载了 {len(self._plugins)} 个插件")
    
    async def cleanup(self) -> None:
        """清理所有插件"""
        for plugin in self._plugins.values():
            try:
                await plugin.cleanup()
            except Exception as e:
                self.logger.error(f"清理插件 {plugin.name} 时出错: {e}")
        
        self.logger.info("所有插件清理完成")
    
    def register_plugin(self, plugin: BasePlugin) -> None:
        """注册插件"""
        if plugin.name in self._plugins:
            self.logger.warning(f"插件 {plugin.name} 已存在，将被覆盖")
        
        self._plugins[plugin.name] = plugin
        
        # 按类型分组
        plugin_type = plugin.__class__.__name__
        if plugin_type not in self._plugin_types:
            self._plugin_types[plugin_type] = []
        self._plugin_types[plugin_type].append(plugin)
        
        self.logger.info(f"插件 {plugin.name} v{plugin.version} 注册成功")
    
    def get_plugin(self, name: str) -> Optional[BasePlugin]:
        """获取插件"""
        return self._plugins.get(name)
    
    def get_plugins_by_type(self, plugin_type: Type[BasePlugin]) -> List[BasePlugin]:
        """按类型获取插件"""
        type_name = plugin_type.__name__
        return [p for p in self._plugin_types.get(type_name, []) if p.enabled]
    
    def list_plugins(self) -> List[BasePlugin]:
        """列出所有插件"""
        return list(self._plugins.values())
    
    def enable_plugin(self, name: str) -> bool:
        """启用插件"""
        plugin = self.get_plugin(name)
        if plugin:
            plugin.enable()
            return True
        return False
    
    def disable_plugin(self, name: str) -> bool:
        """禁用插件"""
        plugin = self.get_plugin(name)
        if plugin:
            plugin.disable()
            return True
        return False
