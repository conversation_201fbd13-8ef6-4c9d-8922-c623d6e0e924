# 飞书表格监控系统 v2.0

现代化的飞书表格监控系统，支持自动从GitLab合并请求中提取信息并更新飞书表格。

## 特性

- 🚀 **异步架构** - 基于asyncio的高性能异步处理
- 🔧 **模块化设计** - 清晰的分层架构，易于维护和扩展
- 🔌 **插件系统** - 支持自定义URL解析器和内容提取器
- ⚙️ **灵活配置** - 支持环境变量、配置文件等多种配置方式
- 📊 **结构化日志** - 完善的日志系统，支持文件轮转
- 🛡️ **错误处理** - 完善的异常处理和重试机制
- 🧪 **测试覆盖** - 完整的单元测试和集成测试

## 项目结构

```
feishu-monitor/
├── src/                    # 源代码
│   ├── config/            # 配置管理
│   ├── models/            # 数据模型
│   ├── services/          # 服务层
│   ├── core/              # 核心业务逻辑
│   ├── plugins/           # 插件系统
│   ├── utils/             # 工具模块
│   └── main.py            # 主入口
├── tests/                 # 测试文件
├── config.example.yml     # 配置文件示例
├── .env.example          # 环境变量示例
├── requirements.txt      # 依赖列表
├── feishu.py            # 向后兼容的入口文件
└── README.md            # 项目文档
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置系统

#### 方式一：使用配置文件（推荐）

```bash
cp config.example.yml config.yml
# 编辑 config.yml 填入你的配置信息
```

#### 方式二：使用环境变量

```bash
cp .env.example .env
# 编辑 .env 文件填入你的配置信息
```

### 3. 运行程序

```bash
# 使用新的异步版本
python -m src.main

# 或使用向后兼容的入口
python feishu.py
```

## 配置说明

### 飞书配置

- `FEISHU_APP_ID`: 飞书应用ID
- `FEISHU_APP_SECRET`: 飞书应用密钥
- `FEISHU_APP_TOKEN`: 飞书应用Token
- `FEISHU_TABLE_ID`: 飞书表格ID

### GitLab配置

- `GITLAB_URL`: GitLab实例URL
- `GITLAB_TOKEN`: GitLab访问令牌
- `GITLAB_TIMEOUT`: API请求超时时间（秒）

### 轮询配置

- `POLL_INTERVAL`: 轮询间隔（秒）
- `MAX_RETRIES`: 最大重试次数
- `RETRY_DELAY`: 重试延迟（秒）
- `BATCH_SIZE`: 批处理大小

## 架构设计

### 服务层架构

- **FeishuService**: 飞书API交互服务
- **GitLabService**: GitLab API交互服务  
- **ContentProcessor**: 内容处理服务

### 插件系统

支持自定义插件扩展功能：

- **URLParserPlugin**: URL解析插件
- **ContentExtractorPlugin**: 内容提取插件

### 依赖注入

使用ServiceContainer实现依赖注入，提高代码的可测试性和可维护性。

## 开发指南

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_config.py

# 运行测试并生成覆盖率报告
pytest --cov=src
```

### 代码格式化

```bash
# 格式化代码
black src/ tests/

# 检查代码风格
flake8 src/ tests/

# 类型检查
mypy src/
```

### 创建自定义插件

1. 继承相应的插件基类
2. 实现必要的方法
3. 在ServiceContainer中注册插件

示例：

```python
from src.plugins.base import BasePlugin

class MyCustomPlugin(BasePlugin):
    @property
    def name(self) -> str:
        return "my_custom_plugin"
    
    @property  
    def version(self) -> str:
        return "1.0.0"
    
    # 实现具体功能...
```

## 性能优化

- 使用异步I/O提高并发性能
- 实现连接池和请求缓存
- 支持批量操作减少API调用
- 使用信号量控制并发数量

## 监控和日志

- 结构化日志输出
- 支持日志文件轮转
- 完善的错误追踪
- 性能指标监控

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
