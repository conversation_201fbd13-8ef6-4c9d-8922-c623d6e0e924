import logging
import re
from datetime import timedelta
from io import BytesIO

from django.conf import settings
from django.utils.http import urlencode
from matplotlib import rcParams, pyplot as plt

from report.feishu.generator.impl.img_handler import ImgHandler

import numpy as np


date_regexp = r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
date_pattern = re.compile(date_regexp)
vin_regexp = r'[A-Z0-9]{17}'
vin_pattern = re.compile(vin_regexp)


class LineChartImage:
    def __init__(self, block, feishu_api):
        self.block = block
        self.feishu_api = feishu_api

    def generate_img(self, chart_data, indicator, title,legend_colors=None):
        # 设置默认字体为支持中文的字体
        rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体
        rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题
        # 创建折线图
        fig, ax = plt.subplots(1, 1)
        fig.set_size_inches(16, 7)
        colors = plt.rcParams['axes.prop_cycle'].by_key()['color']
        if legend_colors is None:
            legend_colors = colors
        for i, val in enumerate(indicator):
            color = legend_colors[i % len(legend_colors)]
            label = val
            if chart_data['yAxis'][val] and len(chart_data['yAxis'][val]) > 0:
                chart_np = np.array(chart_data['yAxis'][val], dtype=np.float64)
                label = f"{label}  P99:{np.nanpercentile(chart_np, 99, interpolation='nearest')} AVG:{round(np.nanmean(chart_np), 2)} MIN:{np.nanmin(chart_np)} MAX:{np.nanmax(chart_np)}"
            plt.plot(chart_data['xAxis'], chart_data['yAxis'][val], label=label, color=color)
        # 设置图表属性
        plt.title(title)  # 设置图表标题
        desired_labels_per_screen = 16
        x_axis_length = len(list(set(chart_data['xAxis'])))
        step = x_axis_length // desired_labels_per_screen
        x_ticks = range(0, x_axis_length, 1 if step == 0 else step)
        plt.xticks(x_ticks, size=14, rotation=25, ha="right")
        plt.legend(loc='upper right')  # 显示图例
        fig.tight_layout()
        image_bytes = BytesIO()
        plt.savefig(image_bytes, format='png')
        image_bytes.seek(0)
        plt.close()
        return image_bytes

    def write_feishu(self, image_bytes):
        """
        写到飞书里面
        """
        ImgHandler(image_bytes, self.block, self.feishu_api).apply()


    def reform_chart_data(self, chart_data, column):
        """
        同时查询主板和从板的数据，会有如下的数据变化，因此需要重组数据，将横轴拉平，如果没有数据的按照前一个数据向后传递
        - - - - - - - -- --- ----   - - -
        - -  - -  - - - - -- -- --- -- --
        """
        result = {
            "xAxis": [],
            "yAxis": {}
        }
        result["yAxis"]["master"] = []
        result["yAxis"]["slave"] = []
        if chart_data is None or len(chart_data) == 0:
            return result

        x_axis = []
        master = [None]
        slave = [None]
        before = None
        for item in chart_data:
            if item['record_time'] == before:
                # 如果当前的时间戳与前面的时间戳相等，则进行更新即可
                if "master" == item['origin']:
                    master[-1] = item[column]
                else:
                    slave[-1] = item[column]
                continue
            current = item['record_time']
            if before is None:
                before = current
            diff = (current - before).seconds
            if diff > 30:
                for idx in range(0, int(diff / 30)):
                    before = before + timedelta(seconds=30)
                    x_axis.append(before.strftime("%Y-%m-%d %H:%M:%S"))
                    master.append(None)
                    slave.append(None)
            x_axis.append(current.strftime("%Y-%m-%d %H:%M:%S"))
            if item["origin"] == "master":
                master.append(item[column])
                slave.append(slave[-1])
            else:
                slave.append(item[column])
                master.append(master[-1])

        result['xAxis'] = x_axis
        result["yAxis"]["master"] = master[1:]
        result["yAxis"]["slave"] = slave[1:]
        return result

    def get_resource_url(self, params, suffix="/dataview/onlinetop"):
        if settings.TEST_PLATFORM_ENV == 'local':
            url_pre = f"http://localhost:5555{suffix}"
        else:
            url_pre = f"https://megtool-frontend-megtools.mc.machdrive.cn/#{suffix}"
        url = f"{url_pre}?" + urlencode(params, doseq=True)
        return url

