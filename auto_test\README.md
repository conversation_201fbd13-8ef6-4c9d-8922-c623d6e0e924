# 自动测试报告生成工具

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/status-Active-brightgreen.svg)]()

## 📋 项目简介

自动测试报告生成工具是一个基于 Python 开发的智能化测试报告生成系统，专门用于自动驾驶测试场景。该工具能够从 Jira 系统自动获取测试问题数据，结合预定义的测试路线信息，生成详细的测试分析报告，并支持多种输出格式。

## ✨ 核心功能

### 🔄 数据集成
- **Jira 集成**：自动连接 Jira 系统，获取测试问题和缺陷数据
- **灵活查询**：支持自定义 JQL 查询语句，精确筛选相关问题
- **算法标签识别**：智能识别和分类算法相关问题

### 🛣️ 路线管理
- **配置化路线**：通过配置文件管理测试路线信息
- **场景描述**：支持详细的测试场景描述和里程统计
- **交互式选择**：提供友好的路线选择界面

### 📊 智能分析
- **问题密度计算**：精确计算每百公里问题密度
- **质量评分系统**：基于问题密度的智能质量评分（参考自动驾驶安全标准，5个/百公里为优秀水平）
- **统计置信度**：基于测试里程和问题数量的置信度分析
- **多维度统计**：按类型、状态、路线等多维度统计分析

### 📄 报告生成
- **本地 Markdown 报告**：生成详细的本地报告文件
- **飞书文档集成**：支持直接发布到飞书云文档
- **多标签问题追踪**：特别标注包含多个算法标签的复杂问题
- **可视化表格**：清晰的表格展示和数据可视化

## 🚀 快速开始

### 环境要求

- Python 3.8+
- 网络访问权限（用于连接 Jira 和飞书 API）

### 安装依赖

```bash
# 克隆仓库
git clone https://git-core.megvii-inc.com/t-majingmiao/auto_test_report.git
cd auto_test_report

# 安装依赖
pip install -r requirements.txt
```

### 配置文件

复制并编辑配置文件：

```bash
cp config.ini.example config.ini
```

编辑 `config.ini` 文件，配置以下信息：

```ini
[Jira]
url = https://your-jira-instance.com
user = your-username
password = your-password-or-token
jql = your-custom-jql-query
timeout = 20
max_results = 100

[Feishu]
app_id = your-feishu-app-id
app_secret = your-feishu-app-secret
folder_token = your-folder-token

[Routes]
route_list = [{"name": "路线1", "distance": 10.5, "scenario": "城市道路测试"}, {"name": "路线2", "distance": 25.0, "scenario": "高速公路测试"}]

[Report]
output_dir = ./reports
file_format = md
include_timestamp = true
auto_cleanup = false
```

## 📖 使用指南

### 基本使用

```bash
# 运行主程序
python run.py

# 或者使用带参数的方式
python src/main.py --config config.ini
```

### 测试路线配置

程序启动后会显示可用的测试路线：

```
📋 可用测试路线:
================================================================================
序号   路线名称            公里数      场景描述
--------------------------------------------------------------------------------
1    路线12            10.1     24个红绿灯,24个路口,4个路口左转,6个路口右转,1个右转专用道,1个辅路
2    路线14            11.0     30个红绿灯,30个路口,4个路口左转,5个路口右转,1右转专用道,2个辅路
3    高架              45.8     隧道、分流、合流
================================================================================
```

选择测试路线：
- 输入序号选择单条路线：`1`
- 输入多个序号选择多条路线：`1,2,3`
- 输入 `all` 选择所有路线
- 输入 `q` 退出程序

### 报告输出

生成的报告包含以下部分：

1. **测试路线信息**：总里程、路线详情
2. **算法问题分类统计**：按算法标签分类的问题统计
3. **测试路线统计**：详细的路线统计信息
4. **质量评估指标**：包括质量评分、问题密度等
5. **问题详细列表**：所有问题的详细信息

## 🏗️ 项目结构

```
auto_test/
├── src/                          # 源代码目录
│   ├── clients/                  # 外部客户端
│   │   ├── base_client.py       # 客户端基类
│   │   ├── jira_client.py       # Jira 客户端
│   │   └── feishu_client.py     # 飞书客户端
│   ├── core/                     # 核心模块
│   │   ├── config_manager.py    # 配置管理器
│   │   └── route_manager.py     # 路线管理器
│   ├── generators/               # 报告生成器
│   │   ├── base_generator.py    # 生成器基类
│   │   └── local_generator.py   # 本地报告生成器
│   ├── utils/                    # 工具模块
│   │   ├── logger.py            # 日志工具
│   │   └── exceptions.py        # 异常定义
│   └── main.py                   # 主程序入口
├── reports/                      # 报告输出目录
├── config.ini                    # 配置文件
├── requirements.txt              # 依赖列表
├── run.py                       # 启动脚本
└── README.md                    # 项目说明
```

## 📊 质量评分算法 (优化版)

本工具使用优化的质量评分算法，参考自动驾驶安全标准和软件测试行业最佳实践：

```
问题密度 = 0：                质量评分 = 100分 (满分)
问题密度 ≤ 5个/百公里：       质量评分 = 100 - (密度/5) × 15     (85-100分)
问题密度 5-10个/百公里：      质量评分 = 85 - ((密度-5)/5) × 25  (60-85分)
问题密度 10-20个/百公里：     质量评分 = 60 - ((密度-10)/10) × 30 (30-60分)
问题密度 > 20个/百公里：      质量评分 = max(0, 30 - (密度-20) × 1.5)
```

**评分标准** (参考ISO 26262和ASPICE标准)：
- 🟢 85-100分：优秀 (A级) - 符合高安全标准，可商用部署
- 🔵 70-84分：良好 (B级) - 达到商用标准，需持续监控
- 🟡 50-69分：合格 (C级) - 基本可接受，需要改进
- 🟠 30-49分：需改进 (D级) - 存在安全风险，需要重点优化
- 🔴 0-29分：不合格 (F级) - 不可接受，必须修复

## 🧪 测试

项目包含多个测试脚本：

```bash
# 测试路线配置
python test_routes.py

# 测试转换状态
python test_conversion_fix.py

# 测试链接显示
python test_display_link.py
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📝 更新日志

### v1.3.0 (2024-12-21)
- ✨ 重大优化：质量评分算法全面升级，参考ISO 26262和ASPICE标准
- 📊 优化评分标准：5个/百公里为优秀水平，更符合自动驾驶安全要求
- 🔧 改进置信度计算：考虑问题分布合理性和统计学意义
- 📈 新增A-F等级评分体系，更直观的质量评估

### v1.2.0 (2024-12-20)
- ✨ 优化质量评分算法，调整正常问题密度标准为20个/百公里
- 🐛 修复路线配置读取问题，确保正确使用配置文件中的路线
- 📊 增强报告格式，添加多标签问题追踪功能

### v1.1.0 (2024-12-19)
- ✨ 添加算法标签自动识别功能
- 📊 增强统计分析能力
- 🔧 优化配置管理系统

### v1.0.0 (2024-12-18)
- 🎉 初始版本发布
- ⚡ 基础的 Jira 集成和报告生成功能

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📚 参考标准

**汽车行业**: ISO 26262 (功能安全), Automotive SPICE (过程改进)
**软件测试**: IEEE 829 (测试文档), IEEE 1044 (异常分类)
**质量管理**: ISO 9001 (质量管理), CMMI (能力成熟度)

## 📞 支持与反馈

如有问题或建议，请：

1. 提交 Issue：[创建 Issue](https://git-core.megvii-inc.com/t-majingmiao/auto_test_report/issues)
2. 联系维护者：<EMAIL>

---

**Made with ❤️ by 自动驾驶测试团队** 