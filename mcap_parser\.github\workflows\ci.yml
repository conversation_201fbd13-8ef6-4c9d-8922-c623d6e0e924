name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11"]
        exclude:
          # 排除一些组合以减少CI时间
          - os: macos-latest
            python-version: "3.8"
          - os: windows-latest
            python-version: "3.8"
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-xdist black isort mypy flake8
    
    - name: Lint with flake8
      run: |
        # 停止构建如果有Python语法错误或未定义的名称
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # 退出零表示所有检查通过，GitHub将其解释为成功
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Check code formatting with Black
      run: |
        black --check --diff .
    
    - name: Check import sorting with isort
      run: |
        isort --check-only --diff .
    
    - name: Type checking with MyPy
      run: |
        mypy src/ mcap_parser/ --ignore-missing-imports
      continue-on-error: true  # MyPy检查失败不阻止构建
    
    - name: Run unit tests
      run: |
        pytest tests/test_core.py tests/test_config.py -v --cov=mcap_parser --cov-report=xml
    
    - name: Run CLI tests
      run: |
        pytest tests/test_cli.py -v
      continue-on-error: true  # CLI测试可能因为缺少依赖而失败
    
    - name: Run tools tests
      run: |
        pytest tests/test_tools.py -v
      continue-on-error: true
    
    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.python-version == '3.10'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  integration-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' || github.event_name == 'pull_request'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .
        pip install -r requirements.txt
        pip install pytest
    
    - name: Run integration tests
      run: |
        pytest tests/test_integration.py -v -m "integration and not performance"
      continue-on-error: true  # 集成测试可能因为缺少示例数据而失败

  performance-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .
        pip install -r requirements.txt
        pip install pytest psutil
    
    - name: Run performance tests
      run: |
        pytest tests/test_performance.py -v -m "performance and not benchmark"
      continue-on-error: true
    
    - name: Run benchmark tests
      run: |
        pytest tests/test_performance.py -v -m "benchmark"
      continue-on-error: true

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install safety bandit
    
    - name: Run safety check
      run: |
        safety check --json || true
    
    - name: Run bandit security scan
      run: |
        bandit -r src/ mcap_parser/ -f json || true

  build-package:
    runs-on: ubuntu-latest
    needs: [test, integration-test]
    if: github.event_name == 'push' || github.event_name == 'release'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
    
    - name: Build package
      run: |
        python -m build
    
    - name: Check package
      run: |
        twine check dist/*
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist-packages
        path: dist/
        retention-days: 30

  publish-package:
    runs-on: ubuntu-latest
    needs: build-package
    if: github.event_name == 'release' && github.event.action == 'published'
    environment: release
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist-packages
        path: dist/
    
    - name: Install twine
      run: |
        python -m pip install --upgrade pip
        pip install twine
    
    - name: Publish to PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        twine upload dist/*

  docker-build:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Docker Hub
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: mcap-parser
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
    
    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  documentation:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install documentation dependencies
      run: |
        python -m pip install --upgrade pip
        pip install sphinx sphinx-rtd-theme myst-parser
    
    - name: Build documentation
      run: |
        # 如果有docs目录，构建文档
        if [ -d "docs" ]; then
          cd docs
          make html
        else
          echo "No docs directory found, skipping documentation build"
        fi
    
    - name: Deploy to GitHub Pages
      if: success()
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/_build/html
      continue-on-error: true

  notify:
    runs-on: ubuntu-latest
    needs: [test, integration-test, performance-test, security-scan, build-package]
    if: always()
    
    steps:
    - name: Notify on success
      if: ${{ needs.test.result == 'success' }}
      run: |
        echo "✅ CI/CD Pipeline completed successfully!"
        echo "Test results: ${{ needs.test.result }}"
        echo "Integration tests: ${{ needs.integration-test.result }}"
        echo "Performance tests: ${{ needs.performance-test.result }}"
    
    - name: Notify on failure
      if: ${{ needs.test.result == 'failure' }}
      run: |
        echo "❌ CI/CD Pipeline failed!"
        echo "Test results: ${{ needs.test.result }}"
        exit 1