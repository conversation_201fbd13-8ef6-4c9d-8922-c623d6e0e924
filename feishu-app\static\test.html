<html lang="zh-CN"><head>
  <script src='https://lf-package-cn.feishucdn.com/obj/feishu-static/op/fe/devtools_frontend/remote-debug-0.0.1-alpha.6.js'></script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试报告生成配置</title>
  <!-- 引入Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- 引入Font Awesome -->
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- 配置Tailwind自定义颜色和字体 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#3B82F6',
            secondary: '#10B981',
            neutral: '#64748B',
            dark: '#1E293B',
            success: '#10B981',
            error: '#EF4444',
          },
          fontFamily: {
            sans: ['Inter', 'system-ui', 'sans-serif'],
          },
          animation: {
            'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'fade-in': 'fadeIn 0.5s ease-in-out',
            'slide-up': 'slideUp 0.5s ease-out',
            'slide-in-right': 'slideInRight 0.3s ease-out',
            'slide-out-right': 'slideOutRight 0.3s ease-in',
            'scale-in': 'scaleIn 0.3s ease-out',
            'scale-out': 'scaleOut 0.3s ease-in',
            'bounce-subtle': 'bounceSubtle 0.5s ease-in-out',
          },
          keyframes: {
            fadeIn: {
              '0%': { opacity: '0' },
              '100%': { opacity: '1' },
            },
            slideUp: {
              '0%': { transform: 'translateY(20px)', opacity: '0' },
              '100%': { transform: 'translateY(0)', opacity: '1' },
            },
            slideInRight: {
              '0%': { transform: 'translateX(100%)', opacity: '0' },
              '100%': { transform: 'translateX(0)', opacity: '1' },
            },
            slideOutRight: {
              '0%': { transform: 'translateX(0)', opacity: '1' },
              '100%': { transform: 'translateX(100%)', opacity: '0' },
            },
            scaleIn: {
              '0%': { transform: 'scale(0.95)', opacity: '0' },
              '100%': { transform: 'scale(1)', opacity: '1' },
            },
            scaleOut: {
              '0%': { transform: 'scale(1)', opacity: '1' },
              '100%': { transform: 'scale(0.95)', opacity: '0' },
            },
            bounceSubtle: {
              '0%, 100%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-5px)' },
            }
          }
        },
      }
    }
  </script>
  
  <!-- 自定义工具类 -->
  <style type="text/tailwindcss">
    @layer utilities {
      .form-input-focus {
        @apply focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none transition-all duration-200;
      }
      .card-shadow {
        @apply shadow-lg hover:shadow-xl transition-shadow duration-300;
      }
      .btn-effect {
        @apply transform hover:-translate-y-1 hover:shadow-lg active:translate-y-0 transition-all duration-200;
      }
      .text-hover {
        @apply hover:text-primary transition-colors duration-200;
      }
      .input-focus {
        @apply focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200;
      }
      .fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }
      .slide-up {
        animation: slideUp 0.5s ease-out;
      }
      .btn-3d {
        @apply relative overflow-hidden;
      }
      .btn-3d::before {
        content: '';
        @apply absolute inset-0 bg-white/10 pointer-events-none;
      }
      .ripple {
        @apply absolute rounded-full transform scale-0 opacity-75 bg-white/50 transition-all duration-500 ease-out;
      }
      .history-item {
        @apply flex justify-between items-center p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200;
      }
      .history-item:last-child {
        @apply border-b-0;
      }
      .sidebar-shadow {
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
      }
      .sidebar-enter {
        animation: slideInRight 0.3s ease-out forwards;
      }
      .sidebar-leave {
        animation: slideOutRight 0.3s ease-in forwards;
      }
      .overlay-enter {
        animation: fadeIn 0.3s ease-out forwards;
      }
      .overlay-leave {
        animation: fadeIn 0.3s ease-in reverse forwards;
      }
      .btn-pulse {
        animation: pulse 2s infinite;
      }
      .history-item-enter {
        animation: scaleIn 0.3s ease-out forwards;
      }
    }
  </style>
</head>
<body class="bg-gray-50 font-sans text-dark min-h-screen overflow-x-hidden">
  <!-- 右上角历史记录按钮 -->
  <div class="fixed top-4 right-4 z-40">
    <button id="toggleHistoryBtn" class="bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary/90 transition-all duration-300 transform hover:scale-110 hover:rotate-6 flex items-center group">
      <i class="fa fa-history group-hover:scale-125 transition-transform duration-300"></i>
      <span class="ml-2 hidden md:inline transition-all duration-300 group-hover:scale-110">历史记录</span>
      <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden opacity-0 transition-opacity duration-300" id="historyBadge">0</span>
    </button>
  </div>
  
  <!-- 遮罩层 -->
  <div id="sidebarOverlay" class="fixed inset-0 bg-black/50 z-40 hidden opacity-0 transition-opacity duration-300"></div>
  
  <!-- 历史记录侧边栏 -->
  <div id="historySidebar" class="fixed top-0 right-0 w-full md:w-96 h-full bg-white z-50 transform translate-x-full transition-transform duration-300 ease-in-out sidebar-shadow">
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold flex items-center">
          <i class="fa fa-history text-primary mr-2"></i>
          查询历史记录
        </h2>
        <button id="closeSidebarBtn" class="text-neutral hover:text-dark transition-colors hover:scale-110 transition-transform duration-200">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>
      
      <!-- 清空历史按钮 -->
      <div class="mb-4 flex justify-end">
        <button id="clearHistoryBtn" class="text-sm text-neutral hover:text-error transition-colors duration-200 flex items-center hover:scale-105 transition-transform">
          <i class="fa fa-trash-o mr-1"></i>
          <span>清空历史记录</span>
        </button>
      </div>
      
      <!-- 历史记录列表 -->
      <div id="historyList" class="divide-y divide-gray-100 h-[calc(100vh-180px)] overflow-y-auto">
        <!-- 历史记录将通过JavaScript动态添加 -->
        <div id="noHistoryMessage" class="py-8 text-center text-neutral opacity-0 transition-opacity duration-300">
          <i class="fa fa-folder-open-o text-3xl mb-2 opacity-30"></i>
          <p>暂无查询历史记录</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 页面容器 -->
  <div class="container mx-auto px-4 py-8 max-w-5xl animate-fade-in">
    <!-- 页面标题 -->
    <header class="text-center mb-10 slide-up">
      <h1 class="text-[clamp(1.8rem,5vw,2.5rem)] font-bold text-dark mb-3 group">
        <i class="fa fa-cogs text-primary mr-2 transform group-hover:rotate-12 transition-transform duration-300"></i>
        <span class="text-hover">测试报告生成配置</span>
      </h1>
      <p class="text-neutral max-w-2xl mx-auto">请填写以下配置信息，完成后点击"生成报告"按钮生成测试报告</p>
    </header>
    
    <!-- 表单卡片 -->
    <div class="bg-white rounded-xl p-6 md:p-8 card-shadow mb-8 transform hover:scale-[1.005] transition-all duration-300">
      <form id="configForm" class="space-y-6">
        <!-- DeepSeek API配置 -->
        <div class="border-b border-gray-200 pb-6">
          <h2 class="text-xl font-semibold mb-4 flex items-center group">
            <i class="fa fa-code-fork text-primary mr-2 group-hover:scale-110 transition-transform duration-200"></i>
            <span class="text-hover">DeepSeek API配置</span>
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-1 gap-6">
            <div class="transform hover:translate-y-[-2px] transition-transform duration-200">
              <label for="api_key" class="block text-sm font-medium text-gray-700 mb-1">
                API Key <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input type="password" id="api_key" name="api_key" required="" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition duration-200" placeholder="请输入DeepSeek API Key">
                <i class="fa fa-key absolute right-3 top-3 text-gray-400"></i>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Jira配置 -->
        <div class="border-b border-gray-200 pb-6">
          <h2 class="text-xl font-semibold mb-4 flex items-center group">
            <i class="fa fa-ticket text-primary mr-2 group-hover:scale-110 transition-transform duration-200"></i>
            <span class="text-hover">Jira配置</span>
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="transform hover:translate-y-[-2px] transition-transform duration-200">
              <label for="jira_user" class="block text-sm font-medium text-gray-700 mb-1">
                用户名 <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input type="text" id="jira_user" name="jira_user" required="" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition duration-200" placeholder="请输入Jira用户名">
                <i class="fa fa-user absolute right-3 top-3 text-gray-400"></i>
              </div>
            </div>
            
            <div class="transform hover:translate-y-[-2px] transition-transform duration-200">
              <label for="jira_password" class="block text-sm font-medium text-gray-700 mb-1">
                API Token/密码 <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input type="password" id="jira_password" name="jira_password" required="" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition duration-200" placeholder="请输入Jira API Token或密码">
                <i class="fa fa-lock absolute right-3 top-3 text-gray-400"></i>
              </div>
            </div>
            
            <div class="transform hover:translate-y-[-2px] transition-transform duration-200">
              <label for="jql" class="block text-sm font-medium text-gray-700 mb-1">
                JQL查询语句 <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input type="text" id="jql" name="jql" required="" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition duration-200" placeholder="project = &quot;测试项目&quot; AND status in (&quot;已完成&quot;, &quot;已解决&quot;)">
                <i class="fa fa-search absolute right-3 top-3 text-gray-400"></i>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 飞书配置 -->
        <div class="border-b border-gray-200 pb-6">
          <h2 class="text-xl font-semibold mb-4 flex items-center group">
            <i class="fa fa-paper-plane text-primary mr-2 group-hover:scale-110 transition-transform duration-200"></i>
            <span class="text-hover">飞书配置</span>
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-1 gap-6">
            <div class="transform hover:translate-y-[-2px] transition-transform duration-200">
              <label for="folder_token" class="block text-sm font-medium text-gray-700 mb-1">
                文件夹Token <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input type="text" id="folder_token" name="folder_token" required="" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition duration-200" placeholder="请输入飞书文档文件夹Token">
                <i class="fa fa-folder absolute right-3 top-3 text-gray-400"></i>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分析配置 -->
        <div class="space-y-6">
          <h2 class="text-xl font-semibold mb-4 flex items-center group">
            <i class="fa fa-bar-chart text-primary mr-2 group-hover:scale-110 transition-transform duration-200"></i>
            <span class="text-hover">分析配置</span>
          </h2>
          
          <div class="transform hover:translate-y-[-2px] transition-transform duration-200">
            <label for="system_prompt" class="block text-sm font-medium text-gray-700 mb-1">
              系统提示词 <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <textarea id="system_prompt" name="system_prompt" rows="4" required="" class="w-full px-4 py-2 border border-gray-300 rounded-lg input-focus transition duration-200" placeholder="请输入用于分析的系统提示词..."></textarea>
              <i class="fa fa-comment absolute right-3 top-3 text-gray-400"></i>
            </div>
          </div>
        </div>
        
        <!-- 提交按钮 -->
        <div class="pt-4 flex justify-center">
          <button type="submit" id="generateReportBtn" class="bg-gradient-to-br from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white font-medium px-8 py-3 rounded-xl 
                   shadow-[0_4px_12px_rgba(59,130,246,0.4),0_2px_6px_rgba(59,130,246,0.2)] 
                   hover:shadow-[0_8px_24px_rgba(59,130,246,0.5),0_4px_12px_rgba(59,130,246,0.3)] 
                   active:shadow-[0_2px_4px_rgba(59,130,246,0.4)] 
                   active:translate-y-[2px] transform transition-all duration-200 ease-in-out flex items-center group btn-3d">
            <!-- 立体效果增强：添加顶部高光 -->
            <div class="absolute inset-0 bg-white/10 pointer-events-none"></div>
            
            <!-- 按钮图标 -->
            <i class="fa fa-file-text-o mr-2 group-hover:rotate-[-5deg] transition-transform duration-200"></i>
            <span>生成测试报告</span>
            
            <!-- 点击波纹效果 -->
            <span class="ripple absolute rounded-full transform scale-0 opacity-75 bg-white/50"></span>
          </button>
        </div>
      </form>
    </div>
    
    <!-- 结果展示区域 (初始隐藏) -->
    <div id="resultArea" class="hidden bg-white rounded-xl p-6 md:p-8 card-shadow animate-fade-in">
      <div id="loadingIndicator" class="hidden">
        <div class="flex flex-col items-center py-8">
          <div class="w-16 h-16 border-4 border-primary/30 border-t-primary rounded-full animate-spin mb-4"></div>
          <p class="text-neutral mb-4">正在生成测试报告，请稍候...</p>
          <button id="stopReportBtn" type="button" class="mt-2 px-6 py-2 bg-error text-white rounded-lg shadow hover:bg-error/90 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-error focus:ring-offset-2">
            <i class="fa fa-stop mr-2"></i>停止生成
          </button>
        </div>
      </div>
      
      <!-- 只显示聚焦的报告链接 -->
      <div id="reportContent" class="hidden">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fa fa-check-circle text-success mr-2 animate-pulse-slow"></i>
          测试报告生成结果
        </h2>
        <!-- 只显示报告链接 -->
        <div class="flex flex-col items-center justify-center py-8">
          <a id="reportLink" href="#" target="_blank" class="text-primary text-lg font-bold underline hover:text-primary/80 focus:ring-2 focus:ring-primary focus:outline-none transition-colors duration-200" style="font-size:1.2rem;">
            <i class="fa fa-external-link mr-2"></i>
            <span>点击查看飞书云文档</span>
          </a>
        </div>
      </div>
      
      <div id="errorContent" class="hidden">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fa fa-exclamation-circle text-error mr-2 animate-pulse-slow"></i>
          生成失败
        </h2>
        <div class="mb-4 p-4 rounded-lg bg-red-50 border border-red-200 text-red-700">
          <p><i class="fa fa-times-circle mr-2"></i><span id="errorMessage">生成过程中出现错误。</span></p>
        </div>
      </div>
    </div>
    
    <!-- 页脚 -->
    <footer class="text-center text-neutral text-sm mt-12">
      <p>© 2023 测试报告生成系统</p>
    </footer>
  </div>
  
  <!-- JavaScript -->
  <script>
    // 获取表单和相关元素
    const configForm = document.getElementById('configForm');
    const generateReportBtn = document.getElementById('generateReportBtn');
    const resultArea = document.getElementById('resultArea');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const reportContent = document.getElementById('reportContent');
    const errorContent = document.getElementById('errorContent');
    const statusMessage = document.getElementById('statusMessage');
    const errorMessage = document.getElementById('errorMessage');
    const errorDetails = document.getElementById('errorDetails');
    const reportLink = document.getElementById('reportLink');
    const historyList = document.getElementById('historyList');
    const noHistoryMessage = document.getElementById('noHistoryMessage');
    const clearHistoryBtn = document.getElementById('clearHistoryBtn');
    const toggleHistoryBtn = document.getElementById('toggleHistoryBtn');
    const historySidebar = document.getElementById('historySidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    const closeSidebarBtn = document.getElementById('closeSidebarBtn');
    const historyBadge = document.getElementById('historyBadge');
    const stopReportBtn = document.getElementById('stopReportBtn');
    
    // 侧边栏状态
    let sidebarOpen = false;
    
    // 初始化 - 加载历史记录
    document.addEventListener('DOMContentLoaded', () => {
      loadHistoryRecords();
      
      // 为页面元素添加进入动画
      const pageElements = document.querySelectorAll('.container > div');
      pageElements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        
        setTimeout(() => {
          el.style.opacity = '1';
          el.style.transform = 'translateY(0)';
        }, 100 + index * 100);
      });
      
      // 侧边栏切换
      toggleHistoryBtn.addEventListener('click', toggleSidebar);
      closeSidebarBtn.addEventListener('click', toggleSidebar);
      sidebarOverlay.addEventListener('click', toggleSidebar);
      
      // 点击页面其他区域关闭侧边栏
      document.addEventListener('click', (e) => {
        if (sidebarOpen && 
            !historySidebar.contains(e.target) && 
            e.target !== toggleHistoryBtn && 
            !toggleHistoryBtn.contains(e.target)) {
          toggleSidebar();
        }
      });
      
      // 为历史记录徽章添加动画效果
      animateHistoryBadge();
    });
    
    // 切换侧边栏
    function toggleSidebar() {
      sidebarOpen = !sidebarOpen;
      
      if (sidebarOpen) {
        // 打开侧边栏
        historySidebar.classList.remove('translate-x-full');
        historySidebar.classList.add('translate-x-0');
        sidebarOverlay.classList.remove('hidden');
        
        // 触发动画
        setTimeout(() => {
          sidebarOverlay.classList.remove('opacity-0');
          historySidebar.classList.add('sidebar-enter');
          
          // 为历史记录项添加动画
          animateHistoryItems();
        }, 10);
        
        // 防止背景滚动
        document.body.style.overflow = 'hidden';
        
        // 按钮动画
        toggleHistoryBtn.classList.add('scale-95');
        setTimeout(() => {
          toggleHistoryBtn.classList.remove('scale-95');
        }, 200);
      } else {
        // 关闭侧边栏
        historySidebar.classList.remove('translate-x-0');
        sidebarOverlay.classList.add('opacity-0');
        historySidebar.classList.add('sidebar-leave');
        
        setTimeout(() => {
          historySidebar.classList.add('translate-x-full');
          sidebarOverlay.classList.add('hidden');
          historySidebar.classList.remove('sidebar-leave');
        }, 300);
        
        // 恢复背景滚动
        document.body.style.overflow = '';
      }
    }
    
    // 为历史记录项添加进入动画
    function animateHistoryItems() {
      const items = historyList.querySelectorAll('.history-item');
      
      // 隐藏无历史记录消息
      if (items.length > 0 && !noHistoryMessage.classList.contains('hidden')) {
        noHistoryMessage.classList.add('hidden');
      }
      
      items.forEach((item, index) => {
        // 重置动画
        item.style.opacity = '0';
        item.style.transform = 'translateX(20px)';
        
        // 添加延迟动画
        setTimeout(() => {
          item.style.opacity = '1';
          item.style.transform = 'translateX(0)';
          item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        }, 50 + index * 50);
      });
    }
    
    // 历史记录徽章动画
    function animateHistoryBadge() {
      if (historyBadge.textContent > 0 && !historyBadge.classList.contains('hidden')) {
        historyBadge.classList.add('animate-bounce-subtle');
        setTimeout(() => {
          historyBadge.classList.remove('animate-bounce-subtle');
        }, 1000);
      }
    }
    
    // 波纹效果实现
    generateReportBtn.addEventListener('click', function(e) {
      // 获取按钮内的波纹元素
      const ripple = this.querySelector('.ripple');
      
      // 计算波纹起始位置（点击位置）
      const x = e.clientX - e.target.getBoundingClientRect().left;
      const y = e.clientY - e.target.getBoundingClientRect().top;
      
      // 设置波纹大小（使用按钮对角线长度）
      const size = Math.max(e.target.offsetWidth, e.target.offsetHeight);
      
      // 应用波纹样式
      ripple.style.width = `${size * 2}px`;
      ripple.style.height = `${size * 2}px`;
      ripple.style.left = `${x - size}px`;
      ripple.style.top = `${y - size}px`;
      
      // 重置波纹动画
      ripple.style.transform = 'scale(0)';
      ripple.style.opacity = '0.75';
      
      // 触发动画
      setTimeout(() => {
        ripple.style.transform = 'scale(1)';
        ripple.style.opacity = '0';
      }, 10);
    });
    
    // 表单提交处理
    configForm.addEventListener('submit', async function(e) {
      e.preventDefault();

      // 重置结果区域
      resultArea.classList.remove('hidden');
      loadingIndicator.classList.remove('hidden');
      reportContent.classList.add('hidden');
      errorContent.classList.add('hidden');

      // 收集表单数据
      const formData = new FormData(configForm);
      const configData = {};
      formData.forEach((value, key) => {
        configData[key] = value;
      });

      // 1. 先保存配置到后端
      let saveResp;
      try {
        saveResp = await fetch('/save-config', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(configData)
        });
        const saveResult = await saveResp.json();
        if (saveResult.status !== 'success') throw new Error(saveResult.message || '配置保存失败');
      } catch (err) {
        loadingIndicator.classList.add('hidden');
        errorContent.classList.remove('hidden');
        errorMessage.textContent = '配置保存失败：';
        errorDetails.textContent = err.message;
        resultArea.scrollIntoView({ behavior: 'smooth', block: 'start' });
        return;
      }

      // 2. 配置保存成功后，调用后端运行脚本
      try {
        const runResp = await fetch('/run-report', { method: 'POST' });
        const runResult = await runResp.json();
        loadingIndicator.classList.add('hidden');
        if (runResult.status === 'success' && runResult.doc_link) {
          reportContent.classList.remove('hidden');
          // 只显示聚焦的报告链接
          reportLink.href = runResult.doc_link;
          reportLink.style.display = '';
          reportLink.focus();
          // 保存到历史记录（只保存成功的）
          saveToHistory({
            id: runResult.doc_link,
            title: `测试报告 - ${new Date().toLocaleString()}`,
            url: runResult.doc_link,
            timestamp: new Date().getTime(),
            configSummary: {
              jql: configData.jql || '未设置',
              generatedAt: new Date().toLocaleString()
            }
          });
        } else {
          errorContent.classList.remove('hidden');
          let errMsg = runResult.error || '未知错误';
          // 新增：如果是 HTML 格式（如 <pre>），用 innerHTML 渲染
          if (errMsg.startsWith('<pre')) {
            errorMessage.innerHTML = '生成测试报告失败：' + errMsg;
            // 解析纯文本内容用于高亮输入框
            const plain = errMsg.replace(/<[^>]+>/g, '');
            highlightInputByError(plain);
          } else {
            errorMessage.textContent = '生成测试报告失败：' + errMsg.replace(/^CONFIG_ERROR:/, '').trim();
            highlightInputByError(errMsg);
          }
          errorDetails.textContent = '';
        }
        resultArea.scrollIntoView({ behavior: 'smooth', block: 'center' });
      } catch (err) {
        loadingIndicator.classList.add('hidden');
        errorContent.classList.remove('hidden');
        errorMessage.textContent = '生成测试报告失败：';
        errorDetails.textContent = err.message;
        resultArea.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    });
    
    // 高亮输入框
    function highlightInputByError(configError) {
      // 关键字与input映射
      const map = [
        { key: 'DeepSeek', id: 'api_key' },
        { key: 'Jira', id: 'jira_user' },
        { key: 'Jira', id: 'jira_password' },
        { key: 'Jira', id: 'jql' },
        { key: 'Feishu', id: 'folder_token' }
      ];
      let firstInput = null;
      let matched = false;
      map.forEach(item => {
        if (configError.includes(item.key)) {
          matched = true;
          const input = document.getElementById(item.id);
          if (input) {
            input.classList.add('border-red-500', 'animate-pulse');
            if (!firstInput) firstInput = input;
            setTimeout(() => {
              input.classList.remove('border-red-500', 'animate-pulse');
            }, 2000);
          }
        }
      });
      // 聚焦第一个有问题的输入框
      if (firstInput) {
        firstInput.focus();
        // 滚动到输入框位置
        firstInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      // 仅当确实是配置相关错误时弹窗
      if (matched) {
        alert('配置错误：' + configError);
      }
    }
    
    // 模拟报告生成（实际应用中需要替换为真实API调用）
    async function simulateReportGeneration(configData) {
      // 实际应用中，这里应该是一个fetch请求，调用后端API
      return new Promise((resolve, reject) => {
        // 模拟网络延迟
        setTimeout(() => {
          // 随机决定成功或失败，实际应用中根据真实API响应处理
          if (Math.random() > 0.1) { // 90%概率成功
            resolve({
              status: 'success',
              message: '测试报告生成成功',
              report: {
                generatedAt: new Date().toISOString(),
                analysis: {
                  totalIssues: 125,
                  resolved: 98,
                  unresolved: 27,
                  resolutionRate: '78.4%',
                  mainProblems: ['位置错误', '曲率错误', '实例断连'],
                  trends: {
                    lastWeek: {
                      created: 23,
                      resolved: 31
                    },
                    thisWeek: {
                      created: 18,
                      resolved: 24
                    }
                  }
                },
                recommendations: [
                  "优化nms删除车道线逻辑",
                  "提高删线阈值",
                  "增加分叉判断"
                ],
                nextSteps: [
                  "进行集成测试",
                  "准备发布",
                  "通知相关团队"
                ]
              }
            });
          } else { // 10%概率失败，模拟网络错误或服务器问题
            reject({
              status: 'error',
              message: '连接到Python后端失败，请检查配置和服务状态',
              details: '无法访问http://localhost:5000/generate-report，确保后端服务已启动'
            });
          }
        }, 2000); // 模拟2秒的处理时间
      });
    }
    
    // 添加表单验证反馈
    const requiredInputs = configForm.querySelectorAll('[required]');
    requiredInputs.forEach(input => {
      input.addEventListener('invalid', function() {
        this.classList.add('border-red-500');
        this.classList.add('animate-pulse');
        
        setTimeout(() => {
          this.classList.remove('border-red-500');
          this.classList.remove('animate-pulse');
        }, 2000);
      });
    });
    
    // 历史记录相关函数
    function generateReportId() {
      // 生成唯一报告ID
      return 'report_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
    }
    
    function saveToHistory(record) {
      // 从localStorage获取现有历史记录
      let history = JSON.parse(localStorage.getItem('reportHistory') || '[]');
      
      // 添加新记录
      history.unshift(record); // 添加到开头
      
      // 限制历史记录数量（最多20条）
      if (history.length > 20) {
        history = history.slice(0, 20);
      }
      
      // 保存回localStorage
      localStorage.setItem('reportHistory', JSON.stringify(history));
      
      // 更新历史记录显示
      loadHistoryRecords();
      
      // 更新徽章计数并添加动画
      updateHistoryBadge(history.length);
      animateHistoryBadge();
    }
    
    function loadHistoryRecords() {
      // 从localStorage获取历史记录
      const history = JSON.parse(localStorage.getItem('reportHistory') || '[]');
      
      // 清空现有列表
      historyList.innerHTML = '';
      
      // 检查是否有历史记录
      if (history.length === 0) {
        historyList.appendChild(noHistoryMessage);
        noHistoryMessage.classList.remove('hidden');
        noHistoryMessage.style.opacity = '1';
        return;
      }
      
      // 隐藏无历史记录消息
      if (!noHistoryMessage.classList.contains('hidden')) {
        noHistoryMessage.style.opacity = '0';
        setTimeout(() => {
          noHistoryMessage.classList.add('hidden');
        }, 300);
      }
      
      // 添加历史记录项
      history.forEach(record => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item opacity-0 transform translate-x-20 transition-all duration-300';
        
        // 格式化日期
        const date = new Date(record.timestamp);
        const formattedDate = date.toLocaleString();
        
        historyItem.innerHTML = `
          <div>
            <p class="font-medium text-gray-800">${record.title}</p>
            <p class="text-sm text-neutral mt-1">查询: ${record.configSummary.jql.substring(0, 50)}${record.configSummary.jql.length > 50 ? '...' : ''}</p>
            <p class="text-xs text-neutral/70 mt-1">${formattedDate}</p>
          </div>
          <a href="${record.url}" target="_blank" class="text-primary hover:text-primary/80 transition-colors flex items-center hover:scale-105 transition-transform">
            <span>查看</span>
            <i class="fa fa-angle-right ml-1"></i>
          </a>
        `;
        
        historyList.appendChild(historyItem);
        
        // 添加进入动画
        setTimeout(() => {
          historyItem.classList.remove('opacity-0', 'translate-x-20');
        }, 50);
      });
      
      // 更新徽章计数
      updateHistoryBadge(history.length);
    }
    
    // 更新历史记录徽章
    function updateHistoryBadge(count) {
      if (count > 0) {
        historyBadge.textContent = count;
        historyBadge.classList.remove('hidden');
        historyBadge.style.opacity = '1';
      } else {
        historyBadge.style.opacity = '0';
        setTimeout(() => {
          historyBadge.classList.add('hidden');
        }, 300);
      }
    }
    
    // 清空历史记录
    clearHistoryBtn.addEventListener('click', () => {
      if (confirm('确定要清空所有查询历史记录吗？此操作不可恢复。')) {
        localStorage.removeItem('reportHistory');
        
        // 为现有历史记录项添加退出动画
        const items = historyList.querySelectorAll('.history-item');
        items.forEach((item, index) => {
          item.style.opacity = '0';
          item.style.transform = 'translateX(20px)';
          item.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
        });
        
        // 显示无历史记录消息
        setTimeout(() => {
          historyList.innerHTML = '';
          historyList.appendChild(noHistoryMessage);
          noHistoryMessage.classList.remove('hidden');
          noHistoryMessage.style.opacity = '0';
          
          setTimeout(() => {
            noHistoryMessage.style.opacity = '1';
            noHistoryMessage.style.transition = 'opacity 0.3s ease';
          }, 50);
        }, 300);
        
        // 更新徽章计数
        updateHistoryBadge(0);
        
        // 显示提示
        const notification = document.createElement('div');
        notification.className = 'fixed bottom-4 right-4 bg-dark text-white px-4 py-2 rounded-lg shadow-lg z-50 opacity-0 transform translate-y-4';
        notification.textContent = '历史记录已清空';
        document.body.appendChild(notification);
        
        // 显示通知动画
        setTimeout(() => {
          notification.style.opacity = '1';
          notification.style.transform = 'translateY(0)';
          notification.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        }, 50);
        
        // 隐藏通知动画
        setTimeout(() => {
          notification.style.opacity = '0';
          notification.style.transform = 'translateY(4px)';
          notification.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
          
          setTimeout(() => notification.remove(), 300);
        }, 2000);
      }
    });
    
    // 调试相关功能
    window.debugMode = true;
    
    // 添加调试信息
    if (window.debugMode) {
      console.log('调试模式已启用');
      
      // 模拟报告数据供调试
      window.simulateReport = function() {
        const mockReport = {
          status: 'success',
          message: '测试报告生成成功',
          report: {
            generatedAt: new Date().toISOString(),
            analysis: {
              totalIssues: 125,
              resolved: 98,
              unresolved: 27,
              resolutionRate: '78.4%',
              mainProblems: ['位置错误', '曲率错误', '实例断连'],
              trends: {
                lastWeek: {
                  created: 23,
                  resolved: 31
                },
                thisWeek: {
                  created: 18,
                  resolved: 24
                }
              }
            },
            recommendations: [
              "优化nms删除车道线逻辑",
              "提高删线阈值",
              "增加分叉判断"
            ],
            nextSteps: [
              "进行集成测试",
              "准备发布",
              "通知相关团队"
            ]
          }
        };
        
        // 生成报告ID和链接
        const reportId = generateReportId();
        const reportUrl = `report.html?id=${reportId}`;
        
        // 保存到历史记录
        saveToHistory({
          id: reportId,
          title: `测试报告 - ${new Date().toLocaleString()}`,
          url: reportUrl,
          timestamp: new Date().getTime(),
          configSummary: {
            jql: document.getElementById('jql').value || '未设置',
            generatedAt: new Date().toLocaleString()
          }
        });
        
        // 更新报告链接
        reportLink.href = reportUrl;
        
        // 显示成功结果
        resultArea.classList.remove('hidden');
        loadingIndicator.classList.remove('hidden');
        
        setTimeout(() => {
          loadingIndicator.classList.add('hidden');
          reportContent.classList.remove('hidden');
          statusMessage.textContent = '测试报告生成成功！';
          reportContent.classList.add('animate-fade-in');
          resultArea.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 1000);
      };
      
      // 添加调试按钮
      const debugBtn = document.createElement('button');
      debugBtn.className = 'fixed bottom-4 left-4 bg-neutral text-white px-4 py-2 rounded-lg shadow-lg z-50';
      debugBtn.textContent = '模拟生成报告';
      debugBtn.onclick = window.simulateReport;
      document.body.appendChild(debugBtn);
    }
    
    // 停止生成按钮事件监听
    if (stopReportBtn) {
      stopReportBtn.addEventListener('click', async function() {
        stopReportBtn.disabled = true;
        stopReportBtn.textContent = '正在停止...';
        try {
          const resp = await fetch('/stop-report', { method: 'POST' });
          const result = await resp.json();
          loadingIndicator.classList.add('hidden');
          if (result.status === 'success') {
            errorContent.classList.remove('hidden');
            errorMessage.textContent = '已终止生成';
            // 可选：自动隐藏错误详情
          } else {
            errorContent.classList.remove('hidden');
            errorMessage.textContent = '终止失败：' + (result.message || '未知错误');
          }
        } catch (err) {
          loadingIndicator.classList.add('hidden');
          errorContent.classList.remove('hidden');
          errorMessage.textContent = '终止失败：' + err.message;
        }
        stopReportBtn.disabled = false;
        stopReportBtn.textContent = '停止生成';
      });
    }
  </script>

  </body></html>