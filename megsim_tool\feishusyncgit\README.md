# 多项目工具仓库

这是一个包含多个实用工具项目的仓库，每个项目都有独立的功能和用途。

## 项目列表

### 1. 飞书同步Git工具 (feishu_sync_git)

现代化的飞书表格监控系统，支持自动从GitLab合并请求中提取信息并更新飞书表格。

**特性：**
- 🚀 异步架构 - 基于asyncio的高性能异步处理
- 🔧 模块化设计 - 清晰的分层架构，易于维护和扩展
- 🔌 插件系统 - 支持自定义URL解析器和内容提取器
- ⚙️ 灵活配置 - 支持环境变量、配置文件等多种配置方式
- 📊 结构化日志 - 完善的日志系统，支持文件轮转

**位置：** `projects/feishu_sync_git/`

### 2. Jira克隆SDK (jira_clone_sdk)

现代化的Jira问题克隆SDK，提供灵活的API和命令行工具。

**特性：**
- 🔄 单个/批量克隆 - 支持克隆单个问题或批量克隆
- 🎯 JQL查询支持 - 通过JQL查询批量克隆问题
- ⚙️ 字段处理 - 智能处理基础字段、系统字段和自定义字段
- ✏️ 数据编辑 - 克隆后可自由编辑问题数据
- 🔧 配置管理 - 支持灵活的字段配置和排除规则
- 🔄 重试机制 - 内置重试逻辑提高成功率
- 🔗 链接创建 - 自动创建原问题与克隆问题的关联

**位置：** `projects/jira_clone_sdk/`

## 仓库结构

```
多项目工具仓库/
├── projects/                    # 项目目录
│   ├── feishu_sync_git/        # 飞书同步Git工具
│   │   ├── src/               # 源代码
│   │   ├── tests/             # 测试文件
│   │   ├── config.example.yml # 配置示例
│   │   ├── requirements.txt   # 依赖列表
│   │   └── README.md         # 项目文档
│   └── jira_clone_sdk/        # Jira克隆SDK
│       ├── jira_clone_sdk/    # SDK源代码
│       ├── examples/          # 使用示例
│       ├── tests/             # 测试文件
│       ├── launcher.py        # 命令行启动器
│       ├── requirements.txt   # 依赖列表
│       └── README.md         # 项目文档
├── .gitignore                 # Git忽略文件
└── README.md                 # 仓库说明文档
```

## 快速开始

### 使用飞书同步Git工具

```bash
cd projects/feishu_sync_git
pip install -r requirements.txt
cp config.example.yml config.yml
# 编辑config.yml配置文件
python -m src.main
```

### 使用Jira克隆SDK

```bash
cd projects/jira_clone_sdk
pip install -r requirements.txt

# 克隆单个问题
python launcher.py clone PROJ-123 --url https://your-domain.atlassian.net --token your-token

# 批量克隆
python launcher.py batch --jql "project = PROJ" --url https://your-domain.atlassian.net --token your-token
```

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请创建 Issue 或联系维护者。
