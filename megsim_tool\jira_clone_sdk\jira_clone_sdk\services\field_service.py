"""字段过滤服务"""

import logging
from typing import Dict, Any, List, Set
from ..config import CloneConfig
from ..processors import ProcessorRegistry
from ..exceptions import FieldProcessingException


class FieldFilterService:
    """字段过滤服务"""
    
    def __init__(self, processor_registry: ProcessorRegistry = None):
        self.processor_registry = processor_registry or ProcessorRegistry()
        self.logger = logging.getLogger(__name__)
        
        # 系统字段，通常不应该被复制
        self.system_fields: Set[str] = {
            'id', 'key', 'self', 'created', 'updated', 'creator', 'reporter',
            'status', 'resolution', 'resolutiondate', 'worklog', 'comment',
            'votes', 'watches', 'issuelinks', 'subtasks', 'parent',
            'aggregatetimeoriginalestimate', 'aggregatetimeestimate',
            'aggregatetimespent', 'timeoriginalestimate', 'timeestimate',
            'timespent', 'workratio', 'lastViewed', 'progress'
        }
    
    def filter_fields(self, source_fields: Dict[str, Any], config: CloneConfig, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """过滤和处理字段"""
        if context is None:
            context = {}
        
        context.update({
            'clone_config': config,
            'source_fields': source_fields,
            'field_mapping': config.custom_field_mapping
        })
        
        filtered_fields = {}
        
        for field_name, field_value in source_fields.items():
            try:
                # 跳过系统字段
                if field_name in self.system_fields:
                    self.logger.debug(f"跳过系统字段: {field_name}")
                    continue
                
                # 跳过排除字段
                if field_name in config.exclude_fields:
                    self.logger.debug(f"跳过排除字段: {field_name}")
                    continue
                
                # 只处理安全字段或自定义字段
                if not self._is_safe_field(field_name, config.safe_fields):
                    self.logger.debug(f"跳过非安全字段: {field_name}")
                    continue
                
                # 验证字段
                if not self.processor_registry.validate_field(field_name, field_value):
                    self.logger.warning(f"字段验证失败: {field_name}")
                    continue
                
                # 处理字段
                processed_value = self.processor_registry.process_field(field_name, field_value, context)
                
                # 如果处理结果为None，跳过该字段
                if processed_value is not None:
                    filtered_fields[field_name] = processed_value
                    self.logger.debug(f"处理字段: {field_name}")
                
            except FieldProcessingException as e:
                self.logger.error(f"字段处理失败: {e}")
                # 根据配置决定是否继续
                continue
            except Exception as e:
                self.logger.error(f"字段处理异常: {field_name} - {str(e)}")
                continue
        
        self.logger.info(f"字段过滤完成，保留 {len(filtered_fields)} 个字段")
        return filtered_fields
    
    def _is_safe_field(self, field_name: str, safe_fields: List[str]) -> bool:
        """判断是否为安全字段"""
        # 基础安全字段
        if field_name in safe_fields:
            return True
        
        # 自定义字段
        if field_name.startswith('customfield_'):
            return True
        
        return False
    
    def add_processor(self, processor):
        """添加字段处理器"""
        self.processor_registry.register(processor)
    
    def remove_processor(self, processor_class: type):
        """移除字段处理器"""
        self.processor_registry.unregister(processor_class)
    
    def get_supported_fields(self) -> List[str]:
        """获取支持的字段列表"""
        # 这里可以返回所有处理器支持的字段
        # 简化实现，返回常见字段
        return [
            'project', 'summary', 'issuetype', 'description', 'assignee',
            'priority', 'labels', 'environment', 'duedate', 'components'
        ]