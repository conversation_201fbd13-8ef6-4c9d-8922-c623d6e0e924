# Jira Clone SDK

一个现代化的Jira问题克隆SDK，提供灵活的克隆配置和数据编辑功能。

## 特性

- 🚀 现代化SDK设计
- 🔧 灵活的字段处理器
- 📝 可编辑的克隆数据
- 🔗 支持问题链接创建
- 📦 模块化架构
- 🛡️ 完善的错误处理

## 安装

```bash
pip install -r requirements.txt
```

## 快速开始

```python
from jira_clone_sdk import JiraCloneSDK, CloneConfig

# 初始化SDK
sdk = JiraCloneSDK(
    base_url="https://your-jira-instance.com",
    bearer_token="your-token"
)

# 配置克隆选项
config = CloneConfig(
    clone_prefix="CLONE - ",
    create_clone_link=True
)

# 克隆单个问题
cloned_key = sdk.clone_issue("PROJ-123", config)
print(f"克隆成功: {cloned_key}")

# 批量克隆
successful, failed = sdk.clone_issues_batch(["PROJ-123", "PROJ-124"], config)
```

## 项目结构

```
jira_clone_sdk/
├── jira_clone_sdk/          # SDK核心包
│   ├── __init__.py
│   ├── client.py            # Jira API客户端
│   ├── config.py            # 配置类
│   ├── processors/          # 字段处理器
│   ├── services/            # 核心服务
│   └── exceptions.py        # 异常定义
├── examples/                # 使用示例
├── tests/                   # 测试文件
├── launcher.py              # 启动程序
├── requirements.txt         # 依赖文件
└── README.md               # 说明文档
```

## 许可证

MIT License