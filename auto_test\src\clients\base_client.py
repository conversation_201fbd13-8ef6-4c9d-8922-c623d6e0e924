from abc import ABC, abstractmethod
from typing import Optional, Any
from src.core.config_manager import ConfigManager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class BaseClient(ABC):
    """基础客户端抽象类"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.client = None
        self._initialize_client()
    
    @abstractmethod
    def _initialize_client(self) -> None:
        """初始化客户端连接"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """检查连接状态"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """测试连接"""
        pass
    
    def get_config(self, section: str, option: str, default: str = '') -> str:
        """获取配置值"""
        return self.config.get(section, option, default)
    
    def get_config_int(self, section: str, option: str, default: int = 0) -> int:
        """获取整数配置值"""
        return self.config.get_int(section, option, default)
    
    def get_config_boolean(self, section: str, option: str, default: bool = False) -> bool:
        """获取布尔配置值"""
        return self.config.get_boolean(section, option, default) 