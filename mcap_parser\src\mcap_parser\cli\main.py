"""
主命令行接口
优化版本，提供更好的用户体验和性能
"""

import argparse
import sys
from pathlib import Path
from typing import List, Optional

from ..core.parser_manager import ParserManager
from ..utils.logger import get_logger, setup_logging
from ..utils.performance import PerformanceMonitor

logger = get_logger(__name__)


# 消息类型分类
MESSAGE_CATEGORIES = {
    "perception": {
        "name": "感知数据",
        "types": ["PerceptionResult", "RadarObjectArray", "RadarObjectMessage", "AEBObstacleArray", "TrafficDetectData"],
        "description": "感知融合、雷达目标、AEB障碍物、交通检测等"
    },
    "lane_map": {
        "name": "车道线地图",
        "types": ["LaneArrayv2", "EnvLaneArray", "ReferenceLines", "EntranceArray", "LocalMap", "AdasisMap", "GlobalRouting"],
        "description": "车道线、环境车道、参考线、入口、地图等"
    },
    "localization": {
        "name": "定位导航",
        "types": ["LocalizationEstimate", "GnssBestPose", "Ins"],
        "description": "定位估计、GNSS、惯性导航等"
    },
    "planning_control": {
        "name": "规划控制",
        "types": ["PlanningResult", "ControlResult", "ControlMonitorMsg", "MdriverResult"],
        "description": "路径规划、控制指令、监控消息等"
    },
    "sensors": {
        "name": "传感器",
        "types": ["RawImu", "CorrectedImu", "PointCloud2", "CompressedVideo"],
        "description": "IMU、点云、视频等传感器数据"
    },
    "vehicle": {
        "name": "车辆状态",
        "types": ["VehicleReportCommon", "FreespaceMatrix", "VirtualWallArray"],
        "description": "车辆报告、自由空间、虚拟墙等"
    },
    "visualization": {
        "name": "可视化",
        "types": ["MarkerArray", "AdsMarkerArrayVec"],
        "description": "可视化标记和显示数据"
    },
    "specialized": {
        "name": "专用消息",
        "types": [
            "EnvInfo", "ExceptionMonitor", "FusionInfoForAEB", "MdriverRefLines",
            "NaviAction", "NaviSocketStream", "OCCVisualization", "ObstacleTimestamp",
            "PlanningLog", "SFFusionTFLListNOA", "SFFusionTSListNOA", "Serialize",
            "SerializeProto", "StateContext", "String"
        ],
        "description": "环境信息、异常监控、融合信息等专用消息"
    }
}


def parse_type_specification(type_spec: str) -> List[str]:
    """解析类型规范"""
    if type_spec == "all":
        # 返回所有类型
        all_types = []
        for category in MESSAGE_CATEGORIES.values():
            all_types.extend(category["types"])
        return all_types
    elif type_spec in MESSAGE_CATEGORIES:
        # 返回类别中的所有类型
        return MESSAGE_CATEGORIES[type_spec]["types"]
    else:
        # 逗号分隔的类型列表
        return [t.strip() for t in type_spec.split(",")]


def list_available_types():
    """列出所有可用的消息类型"""
    print("📋 支持的消息类型分类:")
    print("=" * 80)
    
    total_types = 0
    
    for category_key, category in MESSAGE_CATEGORIES.items():
        print(f"\n🔸 {category['name']} ({category_key}):")
        print(f"   描述: {category['description']}")
        print(f"   类型数量: {len(category['types'])}")
        
        for msg_type in category['types']:
            print(f"     • {msg_type}")
        
        total_types += len(category['types'])
    
    print(f"\n📊 统计:")
    print(f"   总消息类型: {total_types} 种")
    print(f"   类别数量: {len(MESSAGE_CATEGORIES)} 个")
    
    print(f"\n💡 使用提示:")
    print(f"   --types all          # 解析所有类型")
    print(f"   --types perception   # 解析感知数据类别")
    print(f"   --types lane_map     # 解析车道线地图类别")
    print(f"   --types LaneArrayv2,RawImu  # 解析指定类型")


def parse_file_command(args):
    """解析文件命令"""
    mcap_file = Path(args.file)
    
    if not mcap_file.exists():
        logger.error(f"MCAP文件不存在: {mcap_file}")
        return 1
    
    # 解析类型规范
    message_types = parse_type_specification(args.types)
    
    if not message_types:
        logger.error(f"无效的类型规范: {args.types}")
        print("使用 --list 查看所有支持的类型")
        return 1
    
    logger.info(f"开始解析文件: {mcap_file.name}")
    logger.info(f"解析类型: {len(message_types)} 种")
    logger.info(f"最大消息数: {args.max}")
    
    # 创建解析器管理器
    with ParserManager(
        max_workers=args.workers,
        enable_async=args.async_mode,
        enable_error_recovery=not args.strict
    ) as parser_manager:
        
        try:
            # 解析文件
            results = list(parser_manager.parse_file(
                mcap_file=mcap_file,
                message_types=message_types,
                max_messages=args.max,
                batch_size=args.batch_size
            ))
            
            # 显示统计信息
            if not args.quiet:
                stats = parser_manager.get_statistics()
                print_statistics(stats)
            
            logger.info(f"解析完成，处理了 {len(results)} 条消息")
            return 0
        
        except KeyboardInterrupt:
            logger.info("用户中断解析")
            return 130
        except Exception as e:
            logger.error(f"解析失败: {e}")
            if args.debug:
                import traceback
                traceback.print_exc()
            return 1


def analyze_file_command(args):
    """分析文件命令"""
    mcap_file = Path(args.file)
    
    if not mcap_file.exists():
        logger.error(f"MCAP文件不存在: {mcap_file}")
        return 1
    
    logger.info(f"开始分析文件: {mcap_file.name}")
    
    try:
        from ..core.sdk import McapAutoDriveSDK
        
        with McapAutoDriveSDK() as sdk:
            analysis = sdk.analyze_mcap_file(mcap_file)
            
            if analysis:
                print_file_analysis(analysis)
                return 0
            else:
                logger.error("文件分析失败")
                return 1
    
    except Exception as e:
        logger.error(f"分析失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1


def print_statistics(stats):
    """打印统计信息"""
    print("\n" + "=" * 80)
    print("📊 解析统计信息")
    print("=" * 80)
    
    print(f"总解析数: {stats.get('total_parsed', 0):,}")
    print(f"成功解析: {stats.get('successful_parsed', 0):,}")
    print(f"失败解析: {stats.get('failed_parsed', 0):,}")
    print(f"成功率: {stats.get('success_rate', 0):.1%}")
    print(f"平均处理时间: {stats.get('avg_processing_time', 0)*1000:.1f}ms")
    
    # 解析器统计
    parser_stats = stats.get('parser_stats', {})
    if parser_stats:
        print(f"\n📋 解析器统计:")
        for msg_type, type_stats in parser_stats.items():
            success_rate = type_stats['success_count'] / type_stats['count'] if type_stats['count'] > 0 else 0
            avg_time = type_stats['total_time'] / type_stats['count'] if type_stats['count'] > 0 else 0
            print(f"   {msg_type}: {type_stats['count']} 条 ({success_rate:.1%} 成功, {avg_time*1000:.1f}ms 平均)")
    
    # 性能统计
    if 'current_memory_mb' in stats:
        print(f"\n⚡ 性能统计:")
        print(f"   当前内存: {stats['current_memory_mb']:.1f} MB")
        print(f"   CPU使用率: {stats.get('current_cpu_percent', 0):.1f}%")


def print_file_analysis(analysis):
    """打印文件分析结果"""
    print("\n" + "=" * 80)
    print("📊 文件分析结果")
    print("=" * 80)
    
    print(f"文件大小: {analysis.get('file_size_mb', 0):.1f} MB")
    print(f"总消息数: {analysis.get('total_messages', 0):,}")
    print(f"话题数量: {analysis.get('total_topics', 0)}")
    print(f"消息类型数: {analysis.get('total_message_types', 0)}")
    print(f"数据时长: {analysis.get('duration', 0):.2f} 秒")
    
    supported_types = analysis.get('supported_types', [])
    unsupported_types = analysis.get('unsupported_types', [])
    
    print(f"\n✅ 支持的类型 ({len(supported_types)}):")
    for msg_type in supported_types[:10]:
        print(f"   • {msg_type}")
    if len(supported_types) > 10:
        print(f"   ... 还有 {len(supported_types) - 10} 种类型")
    
    if unsupported_types:
        print(f"\n⚪ 不支持的类型 ({len(unsupported_types)}):")
        for msg_type in unsupported_types[:5]:
            print(f"   • {msg_type}")
        if len(unsupported_types) > 5:
            print(f"   ... 还有 {len(unsupported_types) - 5} 种类型")


def create_parser():
    """创建命令行解析器"""
    parser = argparse.ArgumentParser(
        description="MCAP自动驾驶数据解析器 v3.0.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s parse data.mcap --types LaneArrayv2 --max 20
  %(prog)s parse data.mcap --types perception --max 50
  %(prog)s analyze data.mcap
  %(prog)s list
        """
    )
    
    # 全局选项
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--quiet", "-q", action="store_true", help="静默模式")
    parser.add_argument("--log-file", type=Path, help="日志文件路径")
    
    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # parse 命令
    parse_parser = subparsers.add_parser("parse", help="解析MCAP文件")
    parse_parser.add_argument("file", type=str, help="MCAP文件路径")
    parse_parser.add_argument("--types", "-t", default="LaneArrayv2", 
                             help="消息类型规范 (all/category/type1,type2...)")
    parse_parser.add_argument("--max", "-m", type=int, default=50, help="最大处理消息数")
    parse_parser.add_argument("--workers", "-w", type=int, default=4, help="工作线程数")
    parse_parser.add_argument("--batch-size", "-b", type=int, default=100, help="批处理大小")
    parse_parser.add_argument("--async-mode", action="store_true", help="启用异步模式")
    parse_parser.add_argument("--strict", action="store_true", help="严格模式(遇到错误停止)")
    
    # analyze 命令
    analyze_parser = subparsers.add_parser("analyze", help="分析MCAP文件")
    analyze_parser.add_argument("file", type=str, help="MCAP文件路径")
    
    # list 命令
    list_parser = subparsers.add_parser("list", help="列出支持的消息类型")
    
    return parser


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 设置日志
    log_level = "DEBUG" if args.debug else "INFO"
    if args.quiet:
        log_level = "WARNING"
    
    setup_logging(level=log_level, log_file=args.log_file)
    
    # 执行命令
    try:
        if args.command == "parse":
            exit_code = parse_file_command(args)
        elif args.command == "analyze":
            exit_code = analyze_file_command(args)
        elif args.command == "list":
            list_available_types()
            exit_code = 0
        else:
            parser.print_help()
            exit_code = 1
    
    except KeyboardInterrupt:
        logger.info("用户中断")
        exit_code = 130
    except Exception as e:
        logger.error(f"程序异常: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        exit_code = 1
    
    # 如果不是成功退出，抛出SystemExit
    if exit_code != 0:
        sys.exit(exit_code)
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
