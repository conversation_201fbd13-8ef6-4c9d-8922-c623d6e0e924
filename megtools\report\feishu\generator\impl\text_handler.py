import logging
from enum import Enum
from urllib.parse import quote

from report.feishu.generator.handler import Handler


class TEXT_COLOR(Enum):
    """字体颜色"""
    RED = (1, "红色")
    ORIGIN = (2, "橙色")
    YELLOW = (3, "黄色")
    GREEN = (4, "绿色")
    BLUE = (5, "蓝色")
    PURPLE = (6, "紫色")
    GRAY = (7, "灰色")

    @property
    def code(self):
        """获取状态码"""
        return self.value[0]

class BACK_COLOR(Enum):
    """背景颜色"""
    LightRed = (1, "浅红色")
    LightOrange = (2, "浅橙色")
    LightYellow = (3, "浅黄色")
    LightGreen = (4, "浅绿色")
    LightBlue = (5, "浅蓝色")
    LightPurple = (6, "浅紫色")
    MiddleGray = (7, "中灰色")
    Red = (8, "红色")
    Orange = (9, "橙色")
    Yellow = (10, "黄色")
    Green = (11, "绿色")
    Blue = (12, "蓝色")
    Purple = (13, "紫色")
    Gray = (14, "灰色")
    LightGray = (15, "浅灰色")

    @property
    def code(self):
        """获取状态码"""
        return self.value[0]


class TextHandler(Handler):

    def __init__(self, data, block, feishu_api):
        super().__init__(data, block, feishu_api)

    def apply(self):
        pass

    def create_link(self, url, index, text_color=None, back_color=None):
        """
        创建链接
        """
        block = [
            {
                "block_type": 2,
                "text": {
                    "style": {
                        "align": 2
                    },
                    "elements": [{
                        'text_run': {
                            'content': self.data,
                            "text_element_style": {
                                "link": {
                                    "url": quote(url, safe='')
                                },
                            }
                        }
                    }],
                }
            }
        ]
        if text_color:
            block[0]["text"]["elements"][0]["text_run"]["text_element_style"]["text_color"] = text_color
        if back_color:
            block[0]["text"]["elements"][0]["text_run"]["text_element_style"]["background_color"] = back_color
        self.feishu_api.create_document_block(self.block.get("parent_id"), {
            'index': index,
            'children': block
        })

    def create_block(self, index, params):
        elements = []
        for info in params:
            text_run = self.create_text_run(content=info["content"], url=info.get("url")
                                            , text_color=info.get("text_color", '')
                                            , back_color=info.get("back_color", ''))
            elements.append(text_run)
        body = {
          "index": index,
          "children": [
            {
              "block_type": 2,
              "text": {
                "elements": elements,
                "style": {
                  "align": 1
                }
              }
            }
          ]
        }
        logging.debug(f'===body{body}')
        logging.debug(f'=======self.target_file_token{self.feishu_api.target_file_token}')

        self.feishu_api.create_document_block(self.block.get("parent_id"), body)

    @staticmethod
    def create_text_run(content, url, text_color=None, back_color=None):

        text_run = {
            "content": content,
        }
        text_element_style = {}
        if url:
            text_element_style["link"] = {"url": quote(url, safe='')}
        if text_color:
            text_element_style["text_color"] = text_color
        if back_color:
            text_element_style["background_color"] = back_color
        if len(text_element_style) > 0:
            text_run["text_element_style"] = text_element_style

        return {"text_run": text_run}
