#!/usr/bin/env python3
"""
MCAP数据类定义
包含SDK使用的所有数据类和结构

Author: MCAP AutoDrive SDK Team
Created: 2025-01-30
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union


@dataclass
class MessageData:

    data: Any
    timestamp: float
    topic: str
    message_type: str
    structured_data: Optional[Any] = None

    def has_structured_data(self) -> bool:
        """检查是否包含结构化数据"""
        return self.structured_data is not None


@dataclass
class TopicInfo:

    topic_name: str
    message_type: str
    message_count: int = 0
    first_timestamp: Optional[float] = None
    last_timestamp: Optional[float] = None
    duration: Optional[float] = None


@dataclass
class TimeRange:

    start_time: float
    end_time: float

    def __post_init__(self):
        """验证时间范围的有效性"""
        if self.start_time >= self.end_time:
            raise ValueError("开始时间必须小于结束时间")

    def contains(self, timestamp: float) -> bool:
        """检查时间戳是否在范围内"""
        return self.start_time <= timestamp <= self.end_time

    def get_duration(self) -> float:
        """获取时间范围的持续时间"""
        return self.end_time - self.start_time


@dataclass
class ProcessingStats:

    total_messages: int = 0
    processed_messages: int = 0
    filtered_messages: int = 0
    processing_time: float = 0.0
    start_time: Optional[float] = None
    end_time: Optional[float] = None

    def get_processing_rate(self) -> float:
        """计算处理速率 (消息/秒)"""
        if self.processing_time > 0:
            return self.processed_messages / self.processing_time
        return 0.0

    def get_filter_ratio(self) -> float:
        """计算过滤比率"""
        if self.total_messages > 0:
            return self.processed_messages / self.total_messages
        return 0.0


@dataclass
class AnalysisResult:

    file_path: str = ""
    file_size: int = 0
    total_messages: int = 0
    total_topics: int = 0
    total_message_types: int = 0
    duration: float = 0.0
    topics: Dict[str, TopicInfo] = field(default_factory=dict)
    message_types: Dict[str, int] = field(default_factory=dict)
    supported_types: List[str] = field(default_factory=list)
    unsupported_types: List[str] = field(default_factory=list)
    processing_time: float = 0.0

    def get_support_ratio(self) -> float:
        """计算支持的消息类型比率"""
        total_types = len(self.supported_types) + len(self.unsupported_types)
        if total_types > 0:
            return len(self.supported_types) / total_types
        return 0.0

    def get_message_rate(self) -> float:
        """计算平均消息频率 (消息/秒)"""
        if self.duration > 0:
            return self.total_messages / self.duration
        return 0.0

    def get_most_active_topic(self) -> Optional[str]:
        """获取消息数量最多的话题"""
        if not self.topics:
            return None
        return max(self.topics.keys(), key=lambda t: self.topics[t].message_count)

    def get_most_common_message_type(self) -> Optional[str]:
        """获取最常见的消息类型"""
        if not self.message_types:
            return None
        return max(self.message_types.keys(), key=lambda t: self.message_types[t])


@dataclass
class StreamConfig:

    message_types: Optional[List[str]] = None
    topics: Optional[List[str]] = None
    time_range: Optional[TimeRange] = None
    max_messages: Optional[int] = None
    enable_structured_data: bool = False
    batch_size: int = 1000
    progress_callback: Optional[callable] = None
    processing_mode: str = "auto"
    fast_mode_threshold: int = 1000

    def should_process_message(self, msg: MessageData) -> bool:
        """判断是否应该处理该消息"""
        # 检查消息类型过滤
        if self.message_types and msg.message_type not in self.message_types:
            return False

        # 检查话题过滤
        if self.topics and msg.topic not in self.topics:
            return False

        # 检查时间范围过滤
        if self.time_range and not self.time_range.contains(msg.timestamp):
            return False

        return True
