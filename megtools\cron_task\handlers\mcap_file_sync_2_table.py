import datetime
import re

from basic.utils import OssUtils
from cron_task.models import OssFileList
from django.core.cache import cache


def apply(task_config):
    SyncMcapInfo(task_config).apply()

class SyncMcapInfo:
    def __init__(self, task_config):
        self.task_config = task_config
        # 需要记录今天和昨天处理的最后一条数据
        self.bucket_name = self.task_config.get("bucket_name")
        self.ossUtil = OssUtils(self.bucket_name)
        self.monitor_config = self.task_config.get("monitor_config")
        self.car_list = self.task_config.get("car_list", [])

    def apply(self):
        for vin in self.car_list:
            today = datetime.datetime.now()
            self.list_objects(f"{vin}/{today.strftime('%Y%m%d')}", vin)
            yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
            self.list_objects(f"{vin}/{yesterday.strftime('%Y%m%d')}", vin)
            self.ossUtil.close()

    def list_objects(self, prefix, vin):
        file_list, dir_list = self.ossUtil.ls_dir(prefix)
        objects = []
        for item in dir_list:
            # 如果存在planning, 则读取planning， 否则读取当前层级
            sub_file_list, sub_dir_list = self.ossUtil.ls_dir(item.get("Prefix", ""))
            for path in sub_dir_list:
                if path.get("Prefix", "").endswith("planning/"):
                    sub_file_list, sub_dir_list = self.ossUtil.ls_dir(f'{item.get("Prefix", "")}planning/')
                    break
            objects.extend(sub_file_list)

        if len(objects) == 0:
            return
        oss_path_list = {}
        for item in objects:
            parse_type = self.checkObject(item.get("Key"))
            if not parse_type:
                continue
            entity = {
                "bucket_name": self.bucket_name,
                "oss_path": item.get("Key"),
                "file_size": item.get("Size"),
                "file_update_time": item.get("LastModified"),
                "vin": vin[4:],
                "parse_type": parse_type[1],
                "current_status": '0',
                "create_time": datetime.datetime.now(),
                "update_time": datetime.datetime.now(),
            }
            oss_path_list[item.get("Key")] = OssFileList(**entity)
        # 校验是否存在，如果存在需要删除掉
        db_result = OssFileList.objects.filter(oss_path__in=list(oss_path_list.keys())).all()
        db_oss_path = [item.oss_path for item in db_result]
        insert_array = []
        for key, value in oss_path_list.items():
            if key in db_oss_path:
                continue
            insert_array.append(value)
        OssFileList.objects.bulk_create(insert_array)

    def checkObject(self, oss_path: str):
        for item in self.monitor_config:
            if item.get("contains", "~~") in oss_path and oss_path.endswith(item.get("suffix", "~~")):
                return item.get("contains"), item.get("parse_type")
            elif item.get("contains") in oss_path and "suffix" not in item:
                return item.get("contains"), item.get("parse_type")
            elif "reg" in item:
                pattern = item.get("reg")
                if re.match(pattern, oss_path):
                    return item.get("re"), item.get("parse_type")
        return None

# r'car_z\d+/\d{8}/ppl_bag_\d{8}_\d{6}/ppl_bag_\d{8}_\d{6}__\d{6}_\d{6}_\d+\.mcap$'