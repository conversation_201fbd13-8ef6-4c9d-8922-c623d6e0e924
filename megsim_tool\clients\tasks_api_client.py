import requests
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

class MegSimTasksClient:
    """
    MegSim 任务API客户端
    提供任务创建和查询功能
    """
    
    def __init__(self, base_url: str = "https://megsim.mc.machdrive.cn"):
        self.base_url = base_url.rstrip('/')
        self.tasks_endpoint = f"{self.base_url}/api/tasks/"
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
    
    def create_task(self, 
                   name: str,
                   project: int,
                   car_type: str = "Z10",
                   priority: int = 4,
                   task_type: int = 20,
                   run_metric: bool = True,
                   run_test: bool = True,
                   tag_ids: Optional[str] = None,
                   reinjection_task_type: Optional[int] = None,
                   **kwargs) -> Dict[str, Any]:
        """
        创建新任务
        
        Args:
            name: 任务名称
            project: 项目ID
            car_type: 车型，默认Z10
            priority: 优先级，默认4
            task_type: 任务类型，默认20
            run_metric: 是否运行指标，默认True
            run_test: 是否运行测试，默认True
            tag_ids: 标签ID列表，JSON字符串格式
            reinjection_task_type: 重注入任务类型
            **kwargs: 其他参数
        
        Returns:
            创建的任务信息
        """
        payload = {
            "name": name,
            "project": project,
            "car_type": car_type,
            "priority": priority,
            "type": task_type,
            "run_metric": run_metric,
            "run_test": run_test,
            "bp_delay_enable": False,
            "end_early_enable": False,
            "reinjection_scene_type": None,
            "sync_jira_status": True,
            "case_status": "",
            "case_time": "",
            "scene_set_enable": False,
            "ego_cruise_speed": None,
            "evaluation_checkers": None
        }
        
        if tag_ids:
            payload["tag_ids"] = tag_ids
        if reinjection_task_type:
            payload["reinjection_task_type"] = reinjection_task_type
            
        # 添加其他参数
        payload.update(kwargs)
        
        try:
            response = self.session.post(self.tasks_endpoint, json=payload, timeout=30)
            response.raise_for_status()
            return {
                "success": True,
                "status_code": response.status_code,
                "data": response.json()
            }
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": str(e),
                "status_code": getattr(response, 'status_code', None) if 'response' in locals() else None
            }
    
    def get_tasks(self, 
                  limit: int = 10,
                  skip: int = 0,
                  order: str = "-create_at",
                  search: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        获取任务列表
        
        Args:
            limit: 返回记录数限制
            skip: 跳过记录数
            order: 排序字段，默认按创建时间倒序
            search: 搜索条件字典
        
        Returns:
            任务列表信息
        """
        params = {
            "limit": limit,
            "skip": skip,
            "order": order,
            "search": json.dumps(search or {})
        }
        
        try:
            response = self.session.get(self.tasks_endpoint, params=params, timeout=30)
            response.raise_for_status()
            return {
                "success": True,
                "status_code": response.status_code,
                "data": response.json()
            }
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": str(e),
                "status_code": getattr(response, 'status_code', None) if 'response' in locals() else None
            }
    
    def search_tasks_by_name(self, name: str, limit: int = 10) -> Dict[str, Any]:
        """
        按名称搜索任务
        
        Args:
            name: 任务名称
            limit: 返回记录数限制
        
        Returns:
            匹配的任务列表
        """
        search_criteria = {"name": name}
        return self.get_tasks(limit=limit, search=search_criteria)
    
    def search_tasks_by_project(self, project_id: int, limit: int = 10) -> Dict[str, Any]:
        """
        按项目ID搜索任务
        
        Args:
            project_id: 项目ID
            limit: 返回记录数限制
        
        Returns:
            匹配的任务列表
        """
        search_criteria = {"project": project_id}
        return self.get_tasks(limit=limit, search=search_criteria)
    
    def get_recent_tasks(self, limit: int = 10) -> Dict[str, Any]:
        """
        获取最近创建的任务
        
        Args:
            limit: 返回记录数限制
        
        Returns:
            最近的任务列表
        """
        return self.get_tasks(limit=limit, order="-create_at")
    
    def generate_curl_command(self, method: str, endpoint: str, data: Optional[Dict] = None) -> str:
        """
        生成等效的cURL命令
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
        
        Returns:
            cURL命令字符串
        """
        url = f"{self.base_url}{endpoint}"
        curl_cmd = f"curl -X {method.upper()} \\\n  '{url}' \\\n"
        
        for header, value in self.session.headers.items():
            curl_cmd += f"  -H '{header}: {value}' \\\n"
        
        if data and method.upper() in ['POST', 'PUT', 'PATCH']:
            curl_cmd += f"  -d '{json.dumps(data)}'"
        
        return curl_cmd

def main():
    """
    测试任务API客户端
    """
    client = MegSimTasksClient()
    
    print("=== MegSim 任务API客户端测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试创建任务
    print("1. 测试创建任务")
    create_result = client.create_task(
        name="API客户端测试任务",
        project=4,
        car_type="Z10",
        tag_ids="[7736]",
        reinjection_task_type=150291
    )
    
    if create_result["success"]:
        print("✅ 任务创建成功")
        print(f"状态码: {create_result['status_code']}")
        if "data" in create_result:
            task_data = create_result["data"]
            print(f"任务ID: {task_data.get('id', 'N/A')}")
            print(f"任务名称: {task_data.get('name', 'N/A')}")
    else:
        print("❌ 任务创建失败")
        print(f"错误: {create_result['error']}")
    
    print()
    
    # 测试获取任务列表
    print("2. 测试获取任务列表")
    get_result = client.get_recent_tasks(limit=5)
    
    if get_result["success"]:
        print("✅ 任务列表获取成功")
        print(f"状态码: {get_result['status_code']}")
        if "data" in get_result:
            data = get_result["data"]
            if isinstance(data, list):
                tasks = data
            elif isinstance(data, dict) and "results" in data:
                tasks = data["results"]
            else:
                tasks = []
            print(f"返回任务数: {len(tasks)}")
            for i, task in enumerate(tasks[:3], 1):
                print(f"  {i}. {task.get('name', 'N/A')} (ID: {task.get('id', 'N/A')})")
    else:
        print("❌ 任务列表获取失败")
        print(f"错误: {get_result['error']}")
    
    print()
    
    # 生成cURL示例
    print("3. 等效cURL命令示例")
    curl_create = client.generate_curl_command(
        "POST", 
        "/api/tasks/", 
        {
            "name": "test",
            "project": 4,
            "car_type": "Z10",
            "priority": 4,
            "type": 20
        }
    )
    print("创建任务:")
    print(curl_create)

if __name__ == "__main__":
    main()