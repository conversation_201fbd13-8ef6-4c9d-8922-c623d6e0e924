@echo off
REM Jira Clone SDK Windows 启动脚本

setlocal enabledelayedexpansion

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查是否在正确目录
if not exist "launcher.py" (
    echo 错误: 未找到launcher.py，请确保在正确目录运行此脚本
    pause
    exit /b 1
)

REM 显示帮助信息
if "%1"=="" (
    echo Jira Clone SDK 启动脚本
    echo.
    echo 使用方法:
    echo   run.bat clone ^<issue-key^> [选项]
    echo   run.bat batch [选项]
    echo   run.bat edit ^<issue-key^> [选项]
    echo.
    echo 示例:
    echo   run.bat clone PROJ-123 --url https://your-domain.atlassian.net --token your-token
    echo   run.bat batch --jql "project = PROJ" --url https://your-domain.atlassian.net --token your-token
    echo   run.bat edit PROJ-124 --data examples/edit_data.json --url https://your-domain.atlassian.net --token your-token
    echo.
    echo 更多帮助:
    echo   run.bat help
    pause
    exit /b 0
)

REM 显示详细帮助
if "%1"=="help" (
    python launcher.py --help
    pause
    exit /b 0
)

REM 运行Python脚本
python launcher.py %*

REM 如果有错误，暂停显示
if errorlevel 1 (
    echo.
    echo 执行完成，按任意键退出...
    pause >nul
)