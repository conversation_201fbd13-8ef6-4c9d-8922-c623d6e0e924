# Jira Clone SDK PowerShell 启动脚本

param(
    [Parameter(Position=0)]
    [string]$Command,
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$RemainingArgs
)

# 检查Python是否安装
try {
    $pythonVersion = python --version 2>$null
    if (-not $pythonVersion) {
        throw "Python not found"
    }
} catch {
    Write-Host "错误: 未找到Python，请先安装Python 3.8+" -ForegroundColor Red
    exit 1
}

# 检查是否在正确目录
if (-not (Test-Path "launcher.py")) {
    Write-Host "错误: 未找到launcher.py，请确保在正确目录运行此脚本" -ForegroundColor Red
    exit 1
}

# 显示帮助信息
if (-not $Command -or $Command -eq "help") {
    if ($Command -eq "help") {
        python launcher.py --help
    } else {
        Write-Host "Jira Clone SDK 启动脚本" -ForegroundColor Green
        Write-Host ""
        Write-Host "使用方法:"
        Write-Host "  .\run.ps1 clone <issue-key> [选项]"
        Write-Host "  .\run.ps1 batch [选项]"
        Write-Host "  .\run.ps1 edit <issue-key> [选项]"
        Write-Host ""
        Write-Host "示例:"
        Write-Host "  .\run.ps1 clone PROJ-123 --url https://your-domain.atlassian.net --token your-token"
        Write-Host "  .\run.ps1 batch --jql 'project = PROJ' --url https://your-domain.atlassian.net --token your-token"
        Write-Host "  .\run.ps1 edit PROJ-124 --data examples/edit_data.json --url https://your-domain.atlassian.net --token your-token"
        Write-Host ""
        Write-Host "更多帮助:"
        Write-Host "  .\run.ps1 help"
    }
    exit 0
}

# 构建参数列表
$args = @($Command) + $RemainingArgs

# 运行Python脚本
try {
    python launcher.py @args
    $exitCode = $LASTEXITCODE
    if ($exitCode -ne 0) {
        Write-Host ""
        Write-Host "执行完成，退出码: $exitCode" -ForegroundColor Yellow
    }
    exit $exitCode
} catch {
    Write-Host "执行失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}