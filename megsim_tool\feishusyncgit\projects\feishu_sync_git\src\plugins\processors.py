"""处理器插件"""

import re
from typing import Optional, Tuple
from abc import abstractmethod

from .base import BasePlugin
from ..models.gitlab import GitLabMR


class URLParserPlugin(BasePlugin):
    """URL解析插件基类"""
    
    @property
    def name(self) -> str:
        return "url_parser"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @abstractmethod
    def can_parse(self, url: str) -> bool:
        """判断是否能解析此URL"""
        pass
    
    @abstractmethod
    def parse_url(self, url: str) -> Optional[GitLabMR]:
        """解析URL"""
        pass


class GitLabURLParserPlugin(URLParserPlugin):
    """GitLab URL解析插件"""
    
    def __init__(self, settings):
        super().__init__(settings)
        self.patterns = [
            re.compile(r"https?://[^/]+/([^/-]+/[^/-]+)/-/(merge_requests|merge_requests)/(\d+)"),
            re.compile(r"https?://[^/]+/([^/]+/[^/]+)/(merge_requests|merge_requests)/(\d+)"),
            re.compile(r"https?://[^/]+/([^/-]+/[^/-]+)/-/(merge_requests|merge_requests)/(\d+)/.*")
        ]
    
    @property
    def name(self) -> str:
        return "gitlab_url_parser"
    
    @property
    def description(self) -> str:
        return "GitLab合并请求URL解析器"
    
    def can_parse(self, url: str) -> bool:
        """判断是否为GitLab URL"""
        return any(pattern.search(url) for pattern in self.patterns)
    
    def parse_url(self, url: str) -> Optional[GitLabMR]:
        """解析GitLab URL"""
        try:
            for pattern in self.patterns:
                match = pattern.search(url)
                if match:
                    return GitLabMR(project_path=match.group(1), mr_id=int(match.group(3)))
            return None
        except Exception as e:
            self.logger.error(f"URL解析失败: {url}", exc_info=e)
            return None


class ContentExtractorPlugin(BasePlugin):
    """内容提取插件基类"""
    
    @property
    def name(self) -> str:
        return "content_extractor"
    
    @property
    def version(self) -> str:
        return "1.0.0"
    
    @abstractmethod
    def extract_content(self, description: str) -> Tuple[Optional[str], Optional[str]]:
        """提取内容"""
        pass


class DefaultContentExtractorPlugin(ContentExtractorPlugin):
    """默认内容提取插件"""
    
    def __init__(self, settings):
        super().__init__(settings)
        self.content_regex = re.compile(
            r'(?:##\s*)?更新内容【\\?\*?必填】\s*\n?(.*?)(?=##|###|所属模块)',
            re.DOTALL | re.IGNORECASE
        )
        self.method_regex = re.compile(
            r'(?:##\s*)?测试方法【\\?\*必填】\s*\n(.*?)(?=##|###|自测结果)',
            re.DOTALL | re.IGNORECASE
        )
    
    @property
    def name(self) -> str:
        return "default_content_extractor"
    
    @property
    def description(self) -> str:
        return "默认的更新内容和测试方法提取器"
    
    def extract_content(self, description: str) -> Tuple[Optional[str], Optional[str]]:
        """提取更新内容和测试方法"""
        if not description:
            return None, None
        
        update_content = self._extract_section(description, self.content_regex)
        test_method = self._extract_section(description, self.method_regex)
        
        return update_content, test_method
    
    def _extract_section(self, text: str, pattern: re.Pattern) -> Optional[str]:
        """提取文本段落"""
        match = pattern.search(text)
        if match:
            content = match.group(1).strip()
            return re.sub(r'\s+', ' ', content)
        return None


class CustomContentExtractorPlugin(ContentExtractorPlugin):
    """自定义内容提取插件示例"""
    
    def __init__(self, settings):
        super().__init__(settings)
        # 可以从配置中读取自定义正则表达式
        self.custom_patterns = getattr(settings, 'custom_patterns', {})
    
    @property
    def name(self) -> str:
        return "custom_content_extractor"
    
    @property
    def description(self) -> str:
        return "自定义内容提取器，支持配置化的正则表达式"
    
    def extract_content(self, description: str) -> Tuple[Optional[str], Optional[str]]:
        """使用自定义规则提取内容"""
        # 这里可以实现更复杂的提取逻辑
        # 例如：基于AI的内容提取、多语言支持等
        return None, None
