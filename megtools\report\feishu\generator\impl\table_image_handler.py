import logging
import time
from report.feishu.generator.handler import Handler


class TableImageHandler(Handler):
    def __init__(self, data, block, feishu_api, column_width=None, table_header=None):
        super().__init__(data, block, feishu_api)
        if column_width is None:
            column_width = []
        if table_header is None:
            table_header = []
        self.update_cache = []
        self.column_width = column_width
        self.table_header = table_header
        self.header_row = 1 if len(self.table_header) > 0 else 0

    def apply(self):
        table_cells, block_id = self.create_table()
        for idx in range(len(self.table_header), len(table_cells)):
            row = idx // len(self.data[0])
            col = idx % len(self.data[0])
            if col > (len(self.data[row - self.header_row]) - 1):
                continue
            val = self.data[row - self.header_row][col]
            self.create_image(table_cells[idx].get("block_id"), val['image'])
            self.feishu_api.create_document_block(table_cells[idx].get("block_id"), {
                'index': 1,
                'children': [{
                    "block_type": 2,
                    "text": {
                        "elements": [
                            {
                                "text_run": {
                                    "content": val['name']
                                }
                            }
                        ],
                    }
                }]
            })
            self.feishu_api.delete_document_block(table_cells[idx].get("block_id"), {'start_index': 2,
                                                                                     'end_index': 3})
        return block_id

    def create_table(self):
        """
        生成一个空的表格，准备数据填充！
        """
        rows = len(self.data) + self.header_row
        cols = len(self.data[0])
        gen_rows = rows if rows <= 9 else 9
        gen_cols = cols if cols <= 9 else 9
        block = [
            {
                "block_type": 31,
                "table": {
                    "property": {
                        "row_size": gen_rows,
                        "column_size": gen_cols,
                        "header_row": self.header_row > 0
                    }
                }
            }
        ]
        if len(self.column_width) > 0:
            block[0]["table"]["property"]["column_width"] = self.column_width

        parent_id = self.block.get("parent_id")
        data = self.feishu_api.create_document_block(parent_id, {
            'index': self.block.get("index", 0) + 1,
            'children': block
        })
        block_id = data.get('data', {}).get("children")[0].get('block_id', "")
        self.append_row_col("insert_table_row", "row_index", rows - gen_rows, block_id)
        self.append_row_col("insert_table_column", "column_index", cols - gen_cols, block_id)

        # # 获取parent_id 下所有的子块
        return self.feishu_api.get_block_all_blocks(block_id), block_id

    def append_row_col(self, type, index_type, size, block_id):
        """
        生成行列，  行：row_index  列：col_index
        """
        if size <= 0:
            return
        data = None
        for i in range(0, size):
            body = {type: {index_type: -1}}
            data = self.feishu_api.update_document_block(block_id, body)
        return data

    def find_consecutive_ranges(self,arr):
        if not arr:
            return []

        ranges = []
        start_index = 0
        for i in range(1, len(arr)):
            if arr[i] != arr[i - 1]:
                if i - start_index > 1:
                    ranges.append((start_index, i - 1))
                start_index = i
        if len(arr) - start_index > 1:
            ranges.append((start_index, len(arr) - 1))
        return ranges

    def create_image(self, parent_id, data):
        # 1. 创建图片块
        block_id = self.create_img_block(parent_id)
        # 2. 上传文件
        img_token = self.upload_image(block_id, data)
        # 3. 替换文件
        self.replace_img(block_id, img_token)

    def create_img_block(self, parent_id):
        new_block = self.feishu_api.create_document_block(parent_id, {
            'index': 0,
            'children': [{
                "block_type": 27,
                "image": {
                    "align": 2,
                }
            }]
        })
        return new_block['data']['children'][0]['block_id']

    def upload_image(self, block_id, data):
        return self.feishu_api.upload_document_img(data, f"{time.time_ns()}.png", block_id)

    def replace_img(self, block_id, img_token):
        self.feishu_api.update_document_block(block_id, {
            "replace_image": {
                "height": 511,
                "token": img_token,
                "width": 1280
            }
        })