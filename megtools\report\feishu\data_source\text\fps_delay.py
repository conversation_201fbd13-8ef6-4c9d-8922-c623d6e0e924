from basic.utils import SqlUtil
from report.feishu.data_source.image.line_chart_image import LineChartImage
from report.feishu.generator.impl.text_handler import TextHandler, BACK_COLOR


class FpsDelay(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.handle_conditions(variables, params)

    def apply(self):
        result = self.query_data()
        if len(result) > 0:
            result = result[0]
            # return f"{result.get("delay") }~{result.get("maxdelay", 0)}"

        # 下面再放一个描述
        url = self.get_resource_url({
            'queryKey': f"log_view:fps:{self.condition['table_name'][9:]}",
            'vin': self.condition['vin'],
            'record_time_start': self.condition['start_time'],
            'record_time_end': self.condition['end_time'],
            'indicator': "delay,maxdelay",
        }, f"/dataview/fps/SingleFpsShow")
        if "limit" in self.condition and result.get('maxdelay', ''):
            maxdelay = float(result.get('maxdelay', ''))
            if maxdelay > self.condition['limit']:
                TextHandler(f"{result.get('delay', '')}~{result.get('maxdelay', '')}", self.block,
                            self.feishu_api).create_link(url, self.block.get("index", 0) + 1, back_color=BACK_COLOR.LightRed.code)
                return

        # 指定字段生成。
        if "field_name" in self.condition and len(self.condition["field_name"]) == 1:
            if self.condition["field_name"][0] == "delay":
                return f"{result.get('delay', '')}"
            if self.condition["field_name"][0] == "maxdelay":
                return f"{result.get('maxdelay', '')}"
        if  "field_name" in self.condition and len(self.condition["field_name"]) == 2:
            return f"{result.get('maxdelay', '')}~{result.get('maxdelay', '')}"

        TextHandler(f"{result.get('delay', '')}~{result.get('maxdelay', '')}", self.block,
                    self.feishu_api).create_link(url, self.block.get("index", 0)+1)


    def query_data(self):
        sql = f""" select ROUND(avg(delay) * 1000 ,2) delay, ROUND(avg(maxdelay) * 1000,2) maxdelay from {self.condition['table_name']} 
        where delay < 10 and maxdelay < 10 and vin = %s and record_time >= %s and record_time <= %s"""
        result = SqlUtil.query_all_dict(sql, (self.condition['vin'],
                                              self.condition['start_time'],
                                              self.condition['end_time'],))
        return result

    def handle_conditions(self, variables, params):
        params = params.split(",")
        # 如果没有指定VIN，直接生成图片
        self.condition["vin"] = variables.get('vin', '')
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.condition['table_name'] = params[0]
        if len(params) == 2 and params[1].isnumeric():
            self.condition['limit'] = float(params[1])
        else:
            self.condition['field_name'] = params[1:]
