# 飞书报告触发工具集

本文件夹包含了用于触发MegTool Backend飞书报告生成的所有脚本和工具。

## 文件说明

### 核心脚本

- **`simple_trigger.py`** - 主要的Python脚本，专门用于其他程序调用
  - 支持模块导入和命令行调用
  - 提供标准JSON返回格式
  - 最轻量化，适合集成到其他系统

### 平台特定脚本

- **`trigger_report.ps1`** - PowerShell脚本
  - 跨平台支持
  - 丰富的参数选项
  - 支持批量模式和JSON输出

- **`trigger_report.bat`** - Windows批处理脚本
  - 交互式菜单界面
  - 用户友好的操作方式

### 配置和文档

- **`requirements_trigger.txt`** - Python依赖包列表
  - 安装命令：`pip install -r requirements_trigger.txt`

- **`usage_examples.md`** - 详细使用示例
  - 包含多种编程语言的调用示例
  - API参数说明和返回格式

- **`README.md`** - 本说明文件

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements_trigger.txt
```

### 2. 基本使用

**Python模块调用：**
```python
from simple_trigger import trigger_report
result = trigger_report(report_id=1, vin="TEST123")
```

**命令行调用：**
```bash
python simple_trigger.py --report-id 1 --vin TEST123
```

**PowerShell调用：**
```powershell
.\trigger_report.ps1 -ReportId 1 -Vin "TEST123"
```

### 3. 使用配置文件（可选）
```bash
# 创建示例配置文件
python simple_trigger.py --help
```

## 核心参数

- `report_id` - 报告ID（必填）
- `vin` - 车辆识别码
- `start_time` - 开始时间（格式：YYYY-MM-DD HH:MM:SS）
- `end_time` - 结束时间（格式：YYYY-MM-DD HH:MM:SS）
- `custom_params` - 其他自定义参数
- `status` - 触发状态（默认：4-手动触发）

## 返回格式

**成功：**
```json
{
    "success": true,
    "data": {
        "status": 200,
        "message": "报告触发成功",
        "data": {...}
    }
}
```

**失败：**
```json
{
    "success": false,
    "error": "错误信息"
}
```

## 注意事项

1. 确保MegTool Backend服务正在运行
2. 检查网络连接和认证信息
3. 时间格式必须为 `YYYY-MM-DD HH:MM:SS`
4. 建议在生产环境中使用 `simple_trigger.py`
5. 详细使用方法请参考 `usage_examples.md`

## 技术支持

如有问题，请查看：
1. `usage_examples.md` - 详细使用示例
2. 日志文件 `trigger_report.log`
3. MegTool Backend的API文档