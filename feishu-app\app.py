from flask import Flask, request, send_from_directory, jsonify
import configparser
import os
import subprocess
import psutil

app = Flask(__name__, static_folder='static')

CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'config', 'config.ini')
REPORT_SCRIPT = os.path.join(os.path.dirname(__file__), 'AutoTestReportGenerator.py')

@app.route('/')
def index():
    return app.send_static_file('test.html')

@app.route('/save-config', methods=['POST'])
def save_config():
    data = request.json
    config = configparser.ConfigParser()
    config['DeepSeek'] = {
        'api_key': data.get('api_key', ''),
        'api_url': 'https://api.deepseek.com/v1/chat/completions'
    }
    config['Jira'] = {
        'url': 'https://jira.mach-drive-inc.com',
        'user': data.get('jira_user', ''),
        'password': data.get('jira_password', ''),
        'jql': data.get('jql', '')
    }
    config['Feishu'] = {
        'app_id': 'cli_a8d028a01ed8900b',
        'app_secret': 'fA8FpGvqdoZvxBe9tTqiDbp6KkRGWmEF',
        'folder_token': data.get('folder_token', '')
    }
    config['Analysis'] = {
        'system_prompt': data.get('system_prompt', '')
    }
    with open(CONFIG_PATH, 'w', encoding='utf-8') as f:
        config.write(f)
    return jsonify({'status': 'success', 'message': '配置已保存'})

@app.route('/run-report', methods=['POST'])
def run_report():
    try:
        proc = subprocess.run(
            ['python', 'AutoTestReportGenerator.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            encoding='utf-8',
            errors='replace',
            timeout=600
        )
        output = proc.stdout or ''
        # 只捕获和用户输入有关的错误
        error_prefixes = [
            'CONFIG_ERROR:', 'JIRA_ERROR:', 'FEISHU_ERROR:', 'DEEPSEEK_ERROR:', 'USER_ERROR:'
        ]
        user_errors = []
        for line in output.splitlines():
            for prefix in error_prefixes:
                if line.strip().startswith(prefix):
                    user_errors.append(line.split(prefix, 1)[1].strip())
        if user_errors:
            err_msg = '\n'.join(user_errors)
            # 用pre标签包裹，前端可直接高亮
            return jsonify({'status': 'error', 'error': f'<pre style="color:red">{err_msg}</pre>'})
        # 检查飞书文档链接
        for line in output.splitlines():
            if line.strip().startswith('FEISHU_DOC_LINK:'):
                doc_link = line.split('FEISHU_DOC_LINK:')[1].strip()
                return jsonify({'status': 'success', 'doc_link': doc_link})
        # 检查是否有 Traceback 或 Exception
        if 'Traceback' in output or 'Exception' in output:
            return jsonify({'status': 'error', 'error': f'<pre style="color:red">{output}</pre>'})
        # 其它情况
        return jsonify({'status': 'error', 'error': output})
    except Exception as e:
        return jsonify({'status': 'error', 'error': f'<pre style="color:red">{str(e)}</pre>'})

@app.route('/stop-report', methods=['POST'])
def stop_report():
    killed = 0
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if not cmdline:
                    continue
                if any('AutoTestReportGenerator.py' in str(arg) for arg in cmdline):
                    proc.kill()
                    killed += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        if killed > 0:
            return jsonify({'status': 'success', 'message': f'已终止{killed}个进程'})
        else:
            return jsonify({'status': 'error', 'message': '未找到正在运行的AutoTestReportGenerator.py进程'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/static/<path:filename>')
def static_files(filename):
    return send_from_directory(app.static_folder, filename)

if __name__ == '__main__':
    app.run(debug=True, port=5000)