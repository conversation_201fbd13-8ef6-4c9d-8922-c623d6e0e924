
drop table if exists dataview_top_overview;
create table dataview_top_overview
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '车的ID',
    record_time     datetime(6) DEFAULT CURRENT_TIMESTAMP(6) not null comment '记录时间',
    mem_total          double       not null comment '总内存',
    mem_free           double       not null comment '空闲内存',
    mem_used           double       not null comment '使用内存',
    mem_buff_cache     double       not null comment '用作内核缓存的内存',
    swap_total         double       not null comment '交换区总量',
    swap_free          double       not null comment '空闲交换区总量',
    swap_used          double       not null comment '使用交换区总量',
    swap_avail_mem     double       not null comment '缓冲的交换区总量'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'Top日志 - Overview 信息';


drop table if exists dataview_top_cpu;
create table dataview_top_cpu
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)  not null comment '记录时间',
    cpu_idx            varchar(16)  not null comment 'CPU编号',
    cpu_us             double       not null comment '用户空间占用CPU百分比',
    cpu_sy             double       not null comment '内核空间占用CPU百分比',
    cpu_ni             double       not null comment '用户进程空间内改变过优先级的进程占用CPU百分比',
    cpu_id             double       not null comment '空闲CPU百分比',
    cpu_wa             double       not null comment '等待输入输出的CPU时间百分比',
    cpu_hi             double       not null comment '硬件中断的CPU时间百分比',
    cpu_si             double       not null comment '软件中断的CPU时间百分比',
    cpu_st             double       not null comment '虚拟机进程在物理CPU上等待其CPU时间的时间百分比'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'Top日志 - CPU 信息';

drop table if exists dataview_top_progress;
create table dataview_top_progress
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    pid             int          not null comment '进程ID',
    user            varchar(50)  not null comment '用户名',
    priority        int          not null comment '优先级',
    nice_value      int          not null comment '负值表示高优先级，正值表示低优先级',
    virtual_image   double          not null comment '进程使用的虚拟内存总量(kb)',
    resident_size   double          not null comment '进程使用的、未被换出的物理内存大小(kb)',
    shared_mem_size double          not null comment '共享内存大小(kb)',
    process_status  varchar(20)  not null comment '进程状态 不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程',
    cpu_usage       double       not null comment '上次更新到现在的CPU时间占用百分比',
    mem_usage       double       not null comment '进程使用的物理内存百分比',
    cpu_time        double       not null comment '进程使用的CPU时间总计 单位1/100秒',
    command         varchar(512) not null comment '命令名/命令行'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'Top日志 - 进程信息';




drop table if exists dataview_gpu_nvidia_smi;
create table dataview_gpu_nvidia_smi
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    memory_total_1   double          not null comment '总内存',
    memory_used_1   double          not null comment '使用内存',
    memory_free_1   double          not null comment '剩余内存',
    utilization_gpu_1   double          not null comment 'Gpu百分比',
    utilization_memory_1   double          not null comment '进程使用的虚拟内存总量(kb)',
    temperature_gpu_1   double          not null comment '进程使用的虚拟内存总量(kb)',
    memory_total_2   double          not null comment '总内存',
    memory_used_2   double          not null comment '使用内存',
    memory_free_2   double          not null comment '剩余内存',
    utilization_gpu_2   double          not null comment 'Gpu百分比',
    utilization_memory_2   double          not null comment '进程使用的虚拟内存总量(kb)',
    temperature_gpu_2   double          not null comment '进程使用的虚拟内存总量(kb)'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'Gpu日志 - GPU 信息';


drop table if exists dataview_gpu_tegrastats_monitor;
create table dataview_gpu_tegrastats_monitor
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    memory_total_1   double          not null comment '总内存',
    memory_used_1   double          not null comment '使用内存',
    utilization_gpu_1   double          not null comment 'Gpu百分比',
    temperature_gpu_1   double          not null comment '进程使用的虚拟内存总量(kb)'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'Gpu日志 - Thor GPU 信息';

alter table dataview_gpu_tegrastats_monitor add column cpu_0 double comment 'cpu_0';
alter table dataview_gpu_tegrastats_monitor add column cpu_1 double comment 'cpu_1';
alter table dataview_gpu_tegrastats_monitor add column cpu_2 double comment 'cpu_2';
alter table dataview_gpu_tegrastats_monitor add column cpu_3 double comment 'cpu_3';
alter table dataview_gpu_tegrastats_monitor add column cpu_4 double comment 'cpu_4';
alter table dataview_gpu_tegrastats_monitor add column cpu_5 double comment 'cpu_5';
alter table dataview_gpu_tegrastats_monitor add column cpu_6 double comment 'cpu_6';
alter table dataview_gpu_tegrastats_monitor add column cpu_7 double comment 'cpu_7';
alter table dataview_gpu_tegrastats_monitor add column cpu_8 double comment 'cpu_8';
alter table dataview_gpu_tegrastats_monitor add column cpu_9 double comment 'cpu_9';
alter table dataview_gpu_tegrastats_monitor add column cpu_10 double comment 'cpu_10';
alter table dataview_gpu_tegrastats_monitor add column cpu_11 double comment 'cpu_11';


drop table if exists dataview_e2e_perceptor_fps_traffic_light;
create table dataview_e2e_perceptor_fps_traffic_light
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';


drop table if exists dataview_e2e_perceptor_fps_obstacle_timestamp;
create table dataview_e2e_perceptor_fps_obstacle_timestamp
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';


drop table if exists dataview_e2e_perceptor_fps_obstacle_rviz;
create table dataview_e2e_perceptor_fps_obstacle_rviz
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';








drop table if exists dataview_e2e_perceptor_fps_lane_array;
create table dataview_e2e_perceptor_fps_lane_array
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_radar0;
create table dataview_e2e_perceptor_fps_radar0
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_radar1;
create table dataview_e2e_perceptor_fps_radar1
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_radar2;
create table dataview_e2e_perceptor_fps_radar2
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_radar3;
create table dataview_e2e_perceptor_fps_radar3
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_radar4;
create table dataview_e2e_perceptor_fps_radar4
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_obstacle_array;
create table dataview_e2e_perceptor_fps_obstacle_array
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_cam_front_120;
create table dataview_e2e_perceptor_fps_cam_front_120
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_cam_front_left_100;
create table dataview_e2e_perceptor_fps_cam_front_left_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_cam_front_30;
create table dataview_e2e_perceptor_fps_cam_front_30
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_cam_back_70;
create table dataview_e2e_perceptor_fps_cam_back_70
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_cam_front_right_100;
create table dataview_e2e_perceptor_fps_cam_front_right_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_front_rslidar;
create table dataview_e2e_perceptor_fps_front_rslidar
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_cam_back_left;
create table dataview_e2e_perceptor_fps_cam_back_left
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';



drop table if exists dataview_e2e_perceptor_fps_cam_back_right;
create table dataview_e2e_perceptor_fps_cam_back_right
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';


drop table if exists dataview_e2e_perceptor_fps_static_obstacle;
create table dataview_e2e_perceptor_fps_static_obstacle
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';


drop table if exists dataview_e2e_perceptor_fps_localization_estimate;
create table dataview_e2e_perceptor_fps_localization_estimate
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';


drop table if exists dataview_mdriver_fps_mworld_array;
create table dataview_mdriver_fps_mworld_array
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'mdriver fps 日志分析';

drop table if exists dataview_mdriver_fps_mworld_array_from_cam;
create table dataview_mdriver_fps_mworld_array_from_cam
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'mdriver fps 日志分析';


drop table if exists dataview_mdriver_fps_planning;
create table dataview_mdriver_fps_planning
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'mdriver fps 日志分析';


drop table if exists dataview_mdriver_fps_obtacle_array;
create table dataview_mdriver_fps_obtacle_array
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'mdriver fps 日志分析';


drop table if exists dataview_all_fps_cam_back_left_100;
create table dataview_all_fps_cam_back_left_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';

drop table if exists dataview_all_fps_cam_front_right_100;
create table dataview_all_fps_cam_front_right_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';


drop table if exists dataview_all_fps_cam_front_left_100;
create table dataview_all_fps_cam_front_left_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';


drop table if exists dataview_all_fps_cam_back_70;
create table dataview_all_fps_cam_back_70
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';


drop table if exists dataview_all_fps_cam_front_30;
create table dataview_all_fps_cam_front_30
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';


drop table if exists dataview_all_fps_cam_back_right_100;
create table dataview_all_fps_cam_back_right_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';


drop table if exists dataview_all_fps_cam_left_200;
create table dataview_all_fps_cam_left_200
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';


drop table if exists dataview_all_fps_cam_right_200;
create table dataview_all_fps_cam_right_200
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';


drop table if exists dataview_all_fps_cam_front_200;
create table dataview_all_fps_cam_front_200
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';


drop table if exists dataview_all_fps_cam_front_120;
create table dataview_all_fps_cam_front_120
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';


drop table if exists dataview_all_fps_cam_back_200;
create table dataview_all_fps_cam_back_200
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'all fps 日志分析';

drop table if exists dataview_control_fps_vehicle_report;
create table dataview_control_fps_vehicle_report
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'control fps 日志分析';




drop table if exists dataview_gnss_fps_ins_pose;
create table dataview_gnss_fps_ins_pose
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'gnss fps 日志分析';

drop table if exists dataview_gnss_fps_raw_imu;
create table dataview_gnss_fps_raw_imu
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'gnss fps 日志分析';

drop table if exists dataview_gnss_fps_corr_imu;
create table dataview_gnss_fps_corr_imu
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'gnss fps 日志分析';

drop table if exists dataview_gnss_fps_gps;
create table dataview_gnss_fps_gps
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'gnss fps 日志分析';

drop table if exists dataview_gnss_fps_gps;
create table dataview_gnss_fps_gps
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'gnss fps 日志分析';

drop table if exists dataview_proxy_radar_fps_0;
create table dataview_proxy_radar_fps_0
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'proxy radar fps 日志分析';

drop table if exists dataview_proxy_radar_fps_1;
create table dataview_proxy_radar_fps_1
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'proxy radar fps 日志分析';

drop table if exists dataview_proxy_radar_fps_2;
create table dataview_proxy_radar_fps_2
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'proxy radar fps 日志分析';

drop table if exists dataview_proxy_radar_fps_3;
create table dataview_proxy_radar_fps_3
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'proxy radar fps 日志分析';

drop table if exists dataview_proxy_radar_fps_4;
create table dataview_proxy_radar_fps_4
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'proxy radar fps 日志分析';






drop table if exists dataview_innovusion_lidar_front_lidar_points;
create table dataview_innovusion_lidar_front_lidar_points
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_innovusion_lidar_front_lidar_points fps /sensor/front_lidar_points 日志分析';

drop table if exists dataview_localization_localization_estimate;
create table dataview_localization_localization_estimate
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_localization_localization_estimate fps /localization/localization_estimate 日志分析';

drop table if exists dataview_localization_corr_imu;
create table dataview_localization_corr_imu
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_localization_corr_imu fps /sensor/corr_imu 日志分析';

drop table if exists dataview_localization_gps;
create table dataview_localization_gps
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_localization_gps fps /sensor/gps 日志分析';

drop table if exists dataview_localization_ins_pose;
create table dataview_localization_ins_pose
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_localization_ins_pose fps /sensor/ins_pose 日志分析';

drop table if exists dataview_localization_raw_imu;
create table dataview_localization_raw_imu
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_localization_raw_imu fps /sensor/raw_imu 日志分析';

drop table if exists dataview_localization_vehicle_report;
create table dataview_localization_vehicle_report
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_localization_vehicle_report fps /sensor/vehicle_report_common 日志分析';




drop table if exists dataview_host_all_cpu_mpstat;
create table dataview_host_all_cpu_mpstat
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)  not null comment '记录时间',
    cpu_idx            varchar(16)  not null comment 'CPU编号',
    cpu_usr             double       not null comment '用户空间占用CPU百分比',
    cpu_nice             double       not null comment '内核空间占用CPU百分比',
    cpu_sys             double       not null comment '用户进程空间内改变过优先级的进程占用CPU百分比',
    cpu_iowait             double       not null comment '空闲CPU百分比',
    cpu_irq             double       not null comment '等待输入输出的CPU时间百分比',
    cpu_soft             double       not null comment '硬件中断的CPU时间百分比',
    cpu_steal             double       not null comment '软件中断的CPU时间百分比',
    cpu_guest             double       not null comment '软件中断的CPU时间百分比',
    cpu_gnice             double       not null comment '软件中断的CPU时间百分比',
    cpu_idle             double       not null comment '虚拟机进程在物理CPU上等待其CPU时间的时间百分比'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'host_cpu_all_mpstat_usage - CPU 信息';



drop table if exists dataview_host_process_inner_e2e;
create table dataview_host_process_inner_e2e
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    module_name            varchar(64)  not null comment '模块名称',
    pid             int          not null comment '进程ID',
    user            varchar(50)  not null comment '用户名',
    priority        int          not null comment '优先级',
    nice_value      int          not null comment '负值表示高优先级，正值表示低优先级',
    virtual_image   double          not null comment '进程使用的虚拟内存总量(kb)',
    resident_size   double          not null comment '进程使用的、未被换出的物理内存大小(kb)',
    shared_mem_size double          not null comment '共享内存大小(kb)',
    process_status  varchar(20)  not null comment '进程状态 不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程',
    cpu_usage       double       not null comment '上次更新到现在的CPU时间占用百分比',
    mem_usage       double       not null comment '进程使用的物理内存百分比',
    cpu_time        double       not null comment '进程使用的CPU时间总计 单位1/100秒',
    command         varchar(512) not null comment '命令名/命令行'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '线程日志分析';


drop table if exists dataview_host_process_inner_mdriver;
create table dataview_host_process_inner_mdriver
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    module_name            varchar(64)  not null comment '模块名称',
    pid             int          not null comment '进程ID',
    user            varchar(50)  not null comment '用户名',
    priority        int          not null comment '优先级',
    nice_value      int          not null comment '负值表示高优先级，正值表示低优先级',
    virtual_image   double          not null comment '进程使用的虚拟内存总量(kb)',
    resident_size   double          not null comment '进程使用的、未被换出的物理内存大小(kb)',
    shared_mem_size double          not null comment '共享内存大小(kb)',
    process_status  varchar(20)  not null comment '进程状态 不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程',
    cpu_usage       double       not null comment '上次更新到现在的CPU时间占用百分比',
    mem_usage       double       not null comment '进程使用的物理内存百分比',
    cpu_time        double       not null comment '进程使用的CPU时间总计 单位1/100秒',
    command         varchar(512) not null comment '命令名/命令行'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '线程日志分析';

drop table if exists dataview_host_process_inner_env;
create table dataview_host_process_inner_env
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    module_name            varchar(64)  not null comment '模块名称',
    pid             int          not null comment '进程ID',
    user            varchar(50)  not null comment '用户名',
    priority        int          not null comment '优先级',
    nice_value      int          not null comment '负值表示高优先级，正值表示低优先级',
    virtual_image   double          not null comment '进程使用的虚拟内存总量(kb)',
    resident_size   double          not null comment '进程使用的、未被换出的物理内存大小(kb)',
    shared_mem_size double          not null comment '共享内存大小(kb)',
    process_status  varchar(20)  not null comment '进程状态 不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程',
    cpu_usage       double       not null comment '上次更新到现在的CPU时间占用百分比',
    mem_usage       double       not null comment '进程使用的物理内存百分比',
    cpu_time        double       not null comment '进程使用的CPU时间总计 单位1/100秒',
    command         varchar(512) not null comment '命令名/命令行'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '线程日志分析';

drop table if exists dataview_host_process_inner_pilot;
create table dataview_host_process_inner_pilot
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    module_name            varchar(64)  not null comment '模块名称',
    pid             int          not null comment '进程ID',
    user            varchar(50)  not null comment '用户名',
    priority        int          not null comment '优先级',
    nice_value      int          not null comment '负值表示高优先级，正值表示低优先级',
    virtual_image   double          not null comment '进程使用的虚拟内存总量(kb)',
    resident_size   double          not null comment '进程使用的、未被换出的物理内存大小(kb)',
    shared_mem_size double          not null comment '共享内存大小(kb)',
    process_status  varchar(20)  not null comment '进程状态 不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程',
    cpu_usage       double       not null comment '上次更新到现在的CPU时间占用百分比',
    mem_usage       double       not null comment '进程使用的物理内存百分比',
    cpu_time        double       not null comment '进程使用的CPU时间总计 单位1/100秒',
    command         varchar(512) not null comment '命令名/命令行'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '线程日志分析';

drop table if exists dataview_host_process_inner_control;
create table dataview_host_process_inner_control
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    module_name            varchar(64)  not null comment '模块名称',
    pid             int          not null comment '进程ID',
    user            varchar(50)  not null comment '用户名',
    priority        int          not null comment '优先级',
    nice_value      int          not null comment '负值表示高优先级，正值表示低优先级',
    virtual_image   double          not null comment '进程使用的虚拟内存总量(kb)',
    resident_size   double          not null comment '进程使用的、未被换出的物理内存大小(kb)',
    shared_mem_size double          not null comment '共享内存大小(kb)',
    process_status  varchar(20)  not null comment '进程状态 不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程',
    cpu_usage       double       not null comment '上次更新到现在的CPU时间占用百分比',
    mem_usage       double       not null comment '进程使用的物理内存百分比',
    cpu_time        double       not null comment '进程使用的CPU时间总计 单位1/100秒',
    command         varchar(512) not null comment '命令名/命令行'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '线程日志分析';

drop table if exists dataview_host_process_inner_perception;
create table dataview_host_process_inner_perception
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    module_name            varchar(64)  not null comment '模块名称',
    pid             int          not null comment '进程ID',
    user            varchar(50)  not null comment '用户名',
    priority        int          not null comment '优先级',
    nice_value      int          not null comment '负值表示高优先级，正值表示低优先级',
    virtual_image   double          not null comment '进程使用的虚拟内存总量(kb)',
    resident_size   double          not null comment '进程使用的、未被换出的物理内存大小(kb)',
    shared_mem_size double          not null comment '共享内存大小(kb)',
    process_status  varchar(20)  not null comment '进程状态 不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程',
    cpu_usage       double       not null comment '上次更新到现在的CPU时间占用百分比',
    mem_usage       double       not null comment '进程使用的物理内存百分比',
    cpu_time        double       not null comment '进程使用的CPU时间总计 单位1/100秒',
    command         varchar(512) not null comment '命令名/命令行'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '线程日志分析';

drop table if exists dataview_host_process_inner_loc;
create table dataview_host_process_inner_loc
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    module_name            varchar(64)  not null comment '模块名称',
    pid             int          not null comment '进程ID',
    user            varchar(50)  not null comment '用户名',
    priority        int          not null comment '优先级',
    nice_value      int          not null comment '负值表示高优先级，正值表示低优先级',
    virtual_image   double          not null comment '进程使用的虚拟内存总量(kb)',
    resident_size   double          not null comment '进程使用的、未被换出的物理内存大小(kb)',
    shared_mem_size double          not null comment '共享内存大小(kb)',
    process_status  varchar(20)  not null comment '进程状态 不可中断的睡眠状态,R=运行,S=睡眠,T=跟踪/停止,Z=僵尸进程',
    cpu_usage       double       not null comment '上次更新到现在的CPU时间占用百分比',
    mem_usage       double       not null comment '进程使用的物理内存百分比',
    cpu_time        double       not null comment '进程使用的CPU时间总计 单位1/100秒',
    command         varchar(512) not null comment '命令名/命令行'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment '线程日志分析';


drop table if exists one_frame_e2e_obstacle;
create table one_frame_e2e_obstacle
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    process_time   double          not null comment 'obstacle_process_time'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'one_frame_e2e_obstacle 单帧 日志分析';


drop table if exists one_frame_e2e_traffic_light;
create table one_frame_e2e_traffic_light
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    process_time   double          not null comment 'traffic_light_process_time'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'one_frame_e2e_traffic_light 单帧 日志分析';


drop table if exists one_frame_mdriver_process_time;
create table one_frame_mdriver_process_time
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    process_time   double          not null comment 'process_time'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'one_frame_mdriver_process_time 单帧 日志分析';



drop table if exists one_frame_control_process_time;
create table one_frame_control_process_time
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    process_time   double          not null comment 'process_time'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'one_frame_control_process_time 单帧 日志分析';



drop table if exists one_frame_environment_process_time;
create table one_frame_environment_process_time
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    process_time   double          not null comment 'process_time'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'one_frame_control_process_time 单帧 日志分析';


drop table if exists one_frame_pilot_planning_process_time;
create table one_frame_pilot_planning_process_time
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    process_time   double          not null comment 'process_time'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'one_frame_control_process_time 单帧 日志分析';


drop table if exists one_frame_pilot_planning_sensor2planning;
create table one_frame_pilot_planning_sensor2planning
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    process_time   double          not null comment 'process_time'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'one_frame_control_process_time 单帧 日志分析';



drop table if exists one_frame_pilot_planning_prediction;
create table one_frame_pilot_planning_prediction
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    process_time   double          not null comment 'process_time'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'one_frame_control_process_time 单帧 日志分析';




drop table if exists dataview_cpu_temperature;
create table dataview_cpu_temperature
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment 'VIN',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    package   double          not null comment 'package',
    core_0   double          not null comment 'core_0',
    core_4   double          not null comment 'core_4',
    core_8   double          not null comment 'core_8',
    core_12  double          not null comment 'core_12',
    core_16  double          not null comment 'core_16',
    core_20  double          not null comment 'core_20',
    core_24  double          not null comment 'core_24',
    core_28  double          not null comment 'core_28',
    core_32  double          not null comment 'core_32',
    core_33  double          not null comment 'core_33',
    core_34  double          not null comment 'core_34',
    core_35  double          not null comment 'core_35',
    core_36  double          not null comment 'core_36',
    core_37  double          not null comment 'core_37',
    core_38  double          not null comment 'core_38',
    core_39  double          not null comment 'core_39',
    core_40  double          not null comment 'core_40',
    core_41  double          not null comment 'core_41',
    core_42  double          not null comment 'core_42',
    core_43  double          not null comment 'core_43',
    core_44  double          not null comment 'core_44',
    core_45  double          not null comment 'core_45',
    core_46  double          not null comment 'core_46',
    core_47  double          not null comment 'core_47'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'CPU 温度监控';



drop table if exists dataview_fusion_radar_rear_left;
create table dataview_fusion_radar_rear_left
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fusion_radar_rear_left fps /sensor/radar_rear_left 日志分析';

drop table if exists dataview_fusion_radar_rear_right;
create table dataview_fusion_radar_rear_right
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fusion_radar_rear_right fps /sensor/radar_rear_right 日志分析';

drop table if exists dataview_fusion_radar_front_left;
create table dataview_fusion_radar_front_left
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fusion_radar_front_left fps /sensor/radar_front_right 日志分析';

drop table if exists dataview_fusion_radar_front_right;
create table dataview_fusion_radar_front_right
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fusion_radar_front_right fps /sensor/radar_front_left 日志分析';

drop table if exists dataview_fusion_radar_localization_estimate;
create table dataview_fusion_radar_localization_estimate
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fusion_radar_localization_estimate fps /localization/localization_estimate 日志分析';

drop table if exists dataview_fusion_fusion_obstacle_array;
create table dataview_fusion_fusion_obstacle_array
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fusion_fusion_obstacle_array fps /perception/fusion/obstacle_array_result 日志分析';

drop table if exists dataview_fusion_radar_front_middle;
create table dataview_fusion_radar_front_middle
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fusion_radar_front_middle fps /sensor/radar_front_middle 日志分析';

drop table if exists dataview_fusion_vehicle_report_common;
create table dataview_fusion_vehicle_report_common
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fusion_vehicle_report_common fps /sensor/vehicle_report_common 日志分析';

drop table if exists dataview_fusion_detection_obstacle_array;
create table dataview_fusion_detection_obstacle_array
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fusion_detection_obstacle_array fps /perception/detection/obstacle_array_result 日志分析';


drop table if exists dataview_occ_static_obstacle_result;
create table dataview_occ_static_obstacle_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_static_obstacle_result fps /perception/detection/static_obstacle_result 日志分析';


drop table if exists dataview_occ_static_obstacle_result_second;
create table dataview_occ_static_obstacle_result_second
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_static_obstacle_result_second fps /perception/detection/static_obstacle_result_second 日志分析';


drop table if exists dataview_occ_front_rslidar;
create table dataview_occ_front_rslidar
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_front_rslidar fps /sensor/front_rslidar_points 日志分析';

drop table if exists dataview_occ_localization;
create table dataview_occ_localization
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_localization fps /localization/localization_estimate 日志分析';

drop table if exists dataview_occ_cam_back_right;
create table dataview_occ_cam_back_right
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_cam_back_right fps /sensor/cam_back_right_100/h264 日志分析';

drop table if exists dataview_occ_cam_back_left;
create table dataview_occ_cam_back_left
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_cam_back_left fps /sensor/cam_back_left_100/h264 日志分析';

drop table if exists dataview_occ_cam_front_right;
create table dataview_occ_cam_front_right
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_cam_front_right fps /sensor/cam_front_right_100/h264 日志分析';

drop table if exists dataview_occ_cam_front_left;
create table dataview_occ_cam_front_left
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_cam_front_left fps /sensor/cam_front_left_100/h264 日志分析';

drop table if exists dataview_occ_cam_back_70;
create table dataview_occ_cam_back_70
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_cam_back_70 fps /sensor/cam_back_70/h264 日志分析';

drop table if exists dataview_occ_visualization;
create table dataview_occ_visualization
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_visualization fps /perception/occ/visualization 日志分析';

drop table if exists dataview_occ_visualization_3d;
create table dataview_occ_visualization_3d
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_visualization_3d fps /perception/occ/visualization_3d 日志分析';

drop table if exists dataview_occ_cam_front_30;
create table dataview_occ_cam_front_30
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_cam_front_30 fps /sensor/cam_front_30/h264 日志分析';

drop table if exists dataview_occ_cam_front_120;
create table dataview_occ_cam_front_120
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_cam_front_120 fps /sensor/cam_front_120/h264 日志分析';


drop table if exists dataview_pilot_perception_map_fps_merger;
drop table if exists dataview_pilot_perception_map_fps_perception_map;
drop table if exists dataview_innovusion_lidar_se;


drop table if exists dataview_pilot_planngin_result;
create table dataview_pilot_planngin_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_pilot_planngin_result fps /planning/planning_result 日志分析';

drop table if exists dataview_env_fps_traffic_light;
create table dataview_env_fps_traffic_light
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_env_fps_traffic_light fps /perception/traffic_lights_3in1_result 日志分析';

drop table if exists dataview_env_fps_env_info;
create table dataview_env_fps_env_info
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_env_fps_env_info fps /planning/env_info 日志分析';



drop table if exists dataview_planning_thread_speed;
create table dataview_planning_thread_speed
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    curr   double          not null comment 'curr',
    average   double          not null comment 'average',
    max   double          not null comment 'max'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'PilotPlanning PlanningThread time spend';


drop table if exists dataview_vehicle_software_info;
create table dataview_vehicle_software_info
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment 'VIN',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    version_info   text          not null comment '版本信息'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'PilotPlanning PlanningThread time spend';

drop table if exists dataview_e2e_perceptor_fps_map_input_sync;
create table dataview_e2e_perceptor_fps_map_input_sync
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_fps_map_input_sync fps /debug/profile/map_input_sync 日志分析';



drop table if exists dataview_occ_input_timestamps;
create table dataview_occ_input_timestamps
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_input_timestamps fps /perception/occ/input_timestamps 日志分析';


drop table if exists dataview_pilot_planning_lane_array;
create table dataview_pilot_planning_lane_array
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_pilot_planning_lane_array fps /debug/env_info/lane_array 日志分析';


drop table if exists dataview_e2e_failsafe_result;
drop table if exists dataview_e2e_failsafe_cam_front_120;
drop table if exists dataview_e2e_failsafe_cam_back_70;
create table dataview_e2e_failsafe_cam_back_70
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_cam_back_70 fps /failsafe/cam_back_70 日志分析';

drop table if exists dataview_e2e_failsafe_cam_back_left_100;
create table dataview_e2e_failsafe_cam_back_left_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_cam_back_left_100 fps /failsafe/cam_back_left_100 日志分析';

drop table if exists dataview_e2e_failsafe_cam_back_right_100;
create table dataview_e2e_failsafe_cam_back_right_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_cam_back_right_100 fps /failsafe/cam_back_right_100 日志分析';

drop table if exists dataview_e2e_failsafe_cam_front_120;
create table dataview_e2e_failsafe_cam_front_120
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_cam_front_120 fps /failsafe/cam_front_120 日志分析';

drop table if exists dataview_e2e_failsafe_cam_front_30;
create table dataview_e2e_failsafe_cam_front_30
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_cam_front_30 fps /failsafe/cam_front_30 日志分析';

drop table if exists dataview_e2e_failsafe_cam_front_left_100;
create table dataview_e2e_failsafe_cam_front_left_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_cam_front_left_100 fps /failsafe/cam_front_left_100 日志分析';

drop table if exists dataview_e2e_failsafe_cam_front_right_100;
create table dataview_e2e_failsafe_cam_front_right_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_cam_front_right_100 fps /failsafe/cam_front_right_100 日志分析';

drop table if exists dataview_e2e_failsafe_sensor_back_70;
create table dataview_e2e_failsafe_sensor_back_70
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_sensor_back_70 fps /sensor/cam_back_70/h264 日志分析';

drop table if exists dataview_e2e_failsafe_sensor_back_left_100;
create table dataview_e2e_failsafe_sensor_back_left_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_sensor_back_left_100 fps /sensor/cam_back_left_100/h264 日志分析';

drop table if exists dataview_e2e_failsafe_sensor_back_right_100;
create table dataview_e2e_failsafe_sensor_back_right_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_sensor_back_right_100 fps /sensor/cam_back_right_100/h264 日志分析';

drop table if exists dataview_e2e_failsafe_sensor_front_120;
create table dataview_e2e_failsafe_sensor_front_120
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_sensor_front_120 fps /sensor/cam_front_120/h264 日志分析';

drop table if exists dataview_e2e_failsafe_sensor_front_30;
create table dataview_e2e_failsafe_sensor_front_30
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_sensor_front_30 fps /sensor/cam_front_30/h264 日志分析';

drop table if exists dataview_e2e_failsafe_sensor_front_left_100;
create table dataview_e2e_failsafe_sensor_front_left_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_sensor_front_left_100 fps /sensor/cam_front_left_100/h264 日志分析';

drop table if exists dataview_e2e_failsafe_sensor_front_right_100;
create table dataview_e2e_failsafe_sensor_front_right_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_failsafe_sensor_front_right_100 fps /sensor/cam_front_right_100/h264 日志分析';


drop table if exists dataview_e2e_perceptor_decode_back70;
create table dataview_e2e_perceptor_decode_back70
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_decode_back70 fps decode/sensor/cam_back_70/h264 日志分析';

drop table if exists dataview_e2e_perceptor_decode_back_left_100;
create table dataview_e2e_perceptor_decode_back_left_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_decode_back_left_100 fps decode/sensor/cam_back_left_100/h264 日志分析';

drop table if exists dataview_e2e_perceptor_decode_back_right_100;
create table dataview_e2e_perceptor_decode_back_right_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_decode_back_right_100 fps decode/sensor/cam_back_right_100/h264 日志分析';

drop table if exists dataview_e2e_perceptor_decode_front_120;
create table dataview_e2e_perceptor_decode_front_120
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_decode_front_120 fps decode/sensor/cam_front_120/h264 日志分析';

drop table if exists dataview_e2e_perceptor_decode_front_30;
create table dataview_e2e_perceptor_decode_front_30
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_decode_front_30 fps decode/sensor/cam_front_30/h264 日志分析';

drop table if exists dataview_e2e_perceptor_decode_front_left_100;
create table dataview_e2e_perceptor_decode_front_left_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_decode_front_left_100 fps decode/sensor/cam_front_left_100/h264 日志分析';

drop table if exists dataview_e2e_perceptor_decode_front_right_100;
create table dataview_e2e_perceptor_decode_front_right_100
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_decode_front_right_100 fps decode/sensor/cam_front_right_100/h264 日志分析';







drop table if exists dataview_occ_dynamic_obstacle_result;
create table dataview_occ_dynamic_obstacle_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment 'VIN',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_dynamic_obstacle_result fps /perception/detection/dynamic_obstacle_result 日志分析';

drop table if exists dataview_occ_dynamic_obstacle_result_second;
create table dataview_occ_dynamic_obstacle_result_second
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment 'VIN',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_occ_dynamic_obstacle_result_second fps /perception/detection/dynamic_obstacle_result_second 日志分析';



drop table if exists dataview_one_frame_vlm_llmnode;
create table dataview_one_frame_vlm_llmnode
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    llmnode   double          comment 'llmnode'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_one_frame_vlm_llmnode 单帧分析';

drop table if exists dataview_one_frame_vlm_precess;
create table dataview_one_frame_vlm_precess
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    precess   double          comment 'precess'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_one_frame_vlm_precess 单帧分析';

drop table if exists dataview_one_frame_vlm_generate_infer;
create table dataview_one_frame_vlm_generate_infer
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    generate_infer   double          comment 'generate_infer'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_one_frame_vlm_generate_infer 单帧分析';


drop table if exists dataview_fps_vlm_node_fps;
create table dataview_fps_vlm_node_fps
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          comment 'fps'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_fps_vlm_node_fps FPS';


drop table if exists cron_jira_e2e_2_mysql;
create table cron_jira_e2e_2_mysql
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    `key` varchar(16)   comment 'jira_key',
    summary varchar(256)   comment '概述',
    issuetype varchar(16)   comment '类型',
    status varchar(16)   comment '状态',
    priority varchar(16)   comment '优先级',
    components varchar(128)   comment '模块',
    labels varchar(512)   comment '标签',
    customfield_13000 varchar(256)   comment '算法问题标签',
    customfield_11706 varchar(64)   comment 'MegSim报告人',
    customfield_12306 varchar(16)   comment '车型',
    customfield_11701 varchar(32)   comment '测试车号',
    assignee varchar(32)   comment '经办人',
    reporter varchar(32)   comment '报告人',
    created    datetime(6)   comment '创建时间',
    updated    datetime(6)   comment '更新时间',
    customfield_12001    datetime(6)   comment '预计完成时间',
    customfield_11502    datetime(6)   comment '实际修复时间',
    point_time    datetime(6)   comment 'summary 中提取的时间',
    customfield_10211            varchar(256)   comment 'mcap链接',
    lat   double         comment 'lat',
    lon   double    comment 'lon'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'Jira 数据同步';

drop table if exists dataview_aeb_lidar_obstacle;
create table dataview_aeb_lidar_obstacle
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_lidar_obstacle fps /perception/aeb_lidar_obstacle_array 日志分析';

drop table if exists dataview_aeb_rv_obstacle;
create table dataview_aeb_rv_obstacle
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_rv_obstacle fps /perception/aeb_rv_obstacle_array 日志分析';

drop table if exists dataview_aeb_front_rslidar_points;
create table dataview_aeb_front_rslidar_points
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_front_rslidar_points fps /sensor/front_rslidar_points 日志分析';

drop table if exists dataview_aeb_cam_front_120;
create table dataview_aeb_cam_front_120
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_cam_front_120 fps /sensor/cam_front_120/h264 日志分析';



drop table if exists dataview_aeb_lidar_obstacle_array;
create table dataview_aeb_lidar_obstacle_array
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_lidar_obstacle_array fps /perception/aeb_lidar_obstacle_arry 日志分析';
insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time)
values ('log_view:fps:aeb_lidar_obstacle_array',
        '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebLidarObstacleArray","alias":"Topic: /perception/aeb_lidar_obstacle_array","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}',
        '', 'sys', now(), 'sys', now());



drop table if exists one_frame_env_public_admap;
create table one_frame_env_public_admap
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment 'VIN',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    process_time   double          not null comment 'obstacle_process_time'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'one_frame_env_public_admap 单帧 日志分析';



drop table if exists dataview_env_map_admap_l0;
create table dataview_env_map_admap_l0
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment 'VIN',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析';


drop table if exists dataview_e2e_perceptor_fps_debug_input_sync;
create table dataview_e2e_perceptor_fps_debug_input_sync
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment 'VIN',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'e2e_perceptor fps 日志分析 /debug/mc_algo_context/input_sync_7v';

drop table if exists dataview_e2e_perceptor_fps_traffic_signs;
create table dataview_e2e_perceptor_fps_traffic_signs
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_fps_traffic_signs fps /perception/traffic_signs_result 日志分析';

drop table if exists dataview_e2e_perceptor_fps_traffic_raw;
create table dataview_e2e_perceptor_fps_traffic_raw
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_e2e_perceptor_fps_traffic_raw fps /perception/traffic_raw_result 日志分析';

insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:e2e_perceptor_fps_traffic_signs', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsE2ePerceptorTrafficSigns","alias":"Topic: /perception/traffic_signs_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now());
insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:e2e_perceptor_fps_traffic_raw', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsE2ePerceptorTrafficRaw","alias":"Topic: /perception/traffic_raw_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now());

drop table if exists dataview_aeb_rv_dyn_result;
create table dataview_aeb_rv_dyn_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_rv_dyn_result fps /perception/aeb_rv_dyn_result 日志分析';

drop table if exists dataview_aeb_rv_result;
create table dataview_aeb_rv_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_rv_result fps /perception/aeb_rv_result 日志分析';

drop table if exists dataview_aeb_rv_obstacle_result;
create table dataview_aeb_rv_obstacle_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_rv_obstacle_result fps /perception/aeb_rv_obstacle_array 日志分析';

drop table if exists dataview_rv_static_node_fps_rvstatic_result;
create table dataview_rv_static_node_fps_rvstatic_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_rv_static_node_fps_rvstatic_result fps /perception/rvstatic_result 日志分析';

drop table if exists dataview_rv_static_node_fps_lane_array_result;
create table dataview_rv_static_node_fps_lane_array_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_rv_static_node_fps_lane_array_result fps /perception/lane_array_result 日志分析';

drop table if exists dataview_rv_static_node_fps_localization;
create table dataview_rv_static_node_fps_localization
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_rv_static_node_fps_localization fps /localization/localization_estimate 日志分析';

drop table if exists dataview_rv_static_node_fps_aeb_rv_result;
create table dataview_rv_static_node_fps_aeb_rv_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_rv_static_node_fps_aeb_rv_result fps /perception/aeb_rv_result 日志分析';

insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:rv_static_node_fps_rvstatic_result', '{"name":"rv_static_node fps monitor","chart_config":[{"name":"FpsRvStaticNodeRvStaticResult","alias":"Topic: /perception/rvstatic_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now());
insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:rv_static_node_fps_lane_array_result', '{"name":"rv_static_node fps monitor","chart_config":[{"name":"FpsRvStaticNodeLaneArrayResult","alias":"Topic: /perception/lane_array_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now());
insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:rv_static_node_fps_localization', '{"name":"rv_static_node fps monitor","chart_config":[{"name":"FpsRvStaticNodeLocalization","alias":"Topic: /localization/localization_estimate","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now());
insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:rv_static_node_fps_aeb_rv_result', '{"name":"rv_static_node fps monitor","chart_config":[{"name":"FpsRvStaticNodeAebRvResult","alias":"Topic: /perception/aeb_rv_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now());

drop table if exists dataview_rv_static_node_fps_rvstatic_word_result;
create table dataview_rv_static_node_fps_rvstatic_word_result
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_rv_static_node_fps_rvstatic_word_result fps /perception/rvstatic_word_result 日志分析';
insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:rv_static_node_fps_rvstatic_word_result', '{"name":"rv_static_node fps monitor","chart_config":[{"name":"FpsRvStaticNodeRvStaticWordResult","alias":"Topic: /perception/rvstatic_word_result","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now())




drop table if exists dataview_aeb_lights;
create table dataview_aeb_lights
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_lights fps /perception/aeb_lights 日志分析';

drop table if exists dataview_aeb_adb;
create table dataview_aeb_adb
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_adb fps /perception/aeb_adb 日志分析';


insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:aeb_lights', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebLights","alias":"Topic: /perception/aeb_lights","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now())
insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:aeb_adb', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebAdb","alias":"Topic: /perception/aeb_adb","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now())



drop table if exists dataview_lidarabs_sensor_front_rslidar_points;
create table dataview_lidarabs_sensor_front_rslidar_points
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_lidarabs_sensor_front_rslidar_points fps /sensor/front_rslidar_points 日志分析';

insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:lidarabs_sensor_front_rslidar_points', '{"name":"lidarabs_ fps monitor","chart_config":[{"name":"FpsLidarAbsFrontRsLidarPoints","alias":"Topic: /sensor/front_rslidar_points","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now())


drop table if exists dataview_aeb_rv_dyn_subclass;
create table dataview_aeb_rv_dyn_subclass
(
    id bigint unsigned auto_increment primary key comment '主键ID',
    vin            varchar(17)  not null comment '用户名',
    record_time    datetime(6) DEFAULT CURRENT_TIMESTAMP(6)   not null comment '记录时间',
    fps   double          not null comment 'fps',
    delay   double          not null comment 'delay',
    maxdelay   double          not null comment 'maxdelay'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci comment 'dataview_aeb_rv_dyn_subclass fps /perception/aeb_rv_dyn_subclass 日志分析';

insert into basic_key_value (name, value, remark, create_by, create_time, update_by, update_time) values ('log_view:fps:aeb_rv_dyn_subclass', '{"name":"aeb_perceptor fps monitor","chart_config":[{"name":"FpsAebRvDynSubclass","alias":"Topic: /perception/aeb_rv_dyn_subclass","yAxis":[{"name":"fps","comment":"FPS"},{"name":"delay","comment":"Delay(s)"},{"name":"maxdelay","comment":"MaxDelay(s)"}]}]}', '', 'sys', now(), 'sys', now())
