from django.urls import path
from basic.views import *

urlpatterns = [
    # path('v1/passport/userInfo', userInfo),
    path('user/info', userInfo),
    path('auth/login', login),
    path('auth/codes', auth_codes),

    #  KEY-VALUE 接口查询配置
    path('kvPage', kvPage),
    path('modifyKey', modifyKey),
    path('delByKey', delByKey),
    path('queryKey', queryKey),

    # 用户信息
    path('userinfoPageApi', userinfoPage),
    path('modifyUserApi', modifyUser),
    path('delUserinfoApi', delUserinfo),

    # 告警分组
    path('alarmGroupPageApi', alarmGroupPage),
    path('modifyAlarmGroupApi', modifyAlarmGroup),
    path('delAlarmGroupApi', delAlarmGroup),
    path('getUserNameListApi', getUserNameList),

    # 查询Jira 的数据
    path('query<PERSON><PERSON>', query<PERSON><PERSON>),
    # 转换 oss 中的数据！
    path('transOss/<path:dynamic_path>', transOss),

    path('send_feishu_message', send_feishu_message),
]