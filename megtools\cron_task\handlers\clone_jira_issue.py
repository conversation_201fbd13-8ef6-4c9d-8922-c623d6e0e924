import json

from basic.services import Send<PERSON><PERSON>age<PERSON>pi
from cron_task.services import C<PERSON><PERSON>ssue


def apply(task_config):
    CloneJiraIssue(task_config).apply()

class CloneJiraIssue:
    def __init__(self, task_config):
        self.task_config = task_config
        self.jql = task_config.get("jql", "")

    def apply(self):
        req = {
            "password": self.task_config.get("password"),
            "username": self.task_config.get("username"),
            "jql": self.task_config.get("jql"),
        }
        result = CloneIssue(req).apply()
        self.alarm(result)

    def alarm(self, result):
        open_ids = self.task_config.get("alarm", "")
        if not open_ids:
            return
        message = ""
        for item in result:
            if item[1] == "Clone 条件不满足，可能已经Clone，请检查!":
                continue
            else:
                message = f"{message}\n{item[0]}: [{item[1][11:]}](https://jira.mach-drive-inc.com/browse/{item[1][11:]})"
        if message.strip() == "":
            return
        for item in open_ids.split(";"):
            SendMessageApi(f""" **{self.task_config.get("task_name", "CloneJiraIssue")}**:{message} """).send_message(item)