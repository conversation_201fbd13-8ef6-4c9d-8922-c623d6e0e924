"""
通用解析器
处理没有专用解析器的消息类型
"""

from typing import Any, Dict, List, Optional
import time

from .base import BaseParser, ParseResult
from ..utils.logger import get_logger

logger = get_logger(__name__)


class GenericParser(BaseParser):
    """
    通用解析器
    用于处理没有专用解析器的消息类型
    """
    
    def __init__(self):
        super().__init__("Generic", ["*"])  # 支持所有类型
        self.message_type_stats = {}  # 每种消息类型的统计
    
    def parse_message(self, ros_msg: Any, timestamp: float, topic: str, message_type: str) -> bool:
        """
        解析消息 - 通用实现
        
        Args:
            ros_msg: ROS消息对象
            timestamp: 时间戳
            topic: 话题名称
            message_type: 消息类型
            
        Returns:
            bool: 解析是否成功
        """
        try:
            # 更新消息类型统计
            if message_type not in self.message_type_stats:
                self.message_type_stats[message_type] = {
                    'count': 0,
                    'first_seen': timestamp,
                    'last_seen': timestamp,
                    'topics': set()
                }
            
            stats = self.message_type_stats[message_type]
            stats['count'] += 1
            stats['last_seen'] = timestamp
            stats['topics'].add(topic)
            
            # 显示通用解析信息
            self._display_generic_info(ros_msg, timestamp, topic, message_type)
            
            return True
        
        except Exception as e:
            logger.error(f"通用解析器处理消息失败: {message_type} - {e}")
            return False
    
    def parse(self, ros_msg: Any, timestamp: float, topic: str, message_type: str) -> bool:
        """
        简化的解析接口，直接返回bool
        用于与旧版本兼容
        """
        try:
            return self.parse_message(ros_msg, timestamp, topic, message_type)
        except Exception as e:
            logger.error(f"通用解析器解析失败: {e}")
            return False
    
    def _display_generic_info(self, ros_msg: Any, timestamp: float, topic: str, message_type: str):
        """显示通用解析信息"""
        simple_type = message_type.split('.')[-1]
        
        print(f"\n📋 {simple_type} @ {timestamp:.2f}s")
        print(f"   话题: {topic}")
        print(f"   消息类型: {message_type}")
        print(f"   ⚪ 使用通用解析器")
        
        # 显示消息属性
        attributes = self._get_message_attributes(ros_msg)
        if attributes:
            print(f"   属性: {', '.join(attributes[:10])}")  # 只显示前10个属性
            if len(attributes) > 10:
                print(f"   ... 还有{len(attributes)-10}个属性")
        
        # 显示基本统计
        if message_type in self.message_type_stats:
            stats = self.message_type_stats[message_type]
            print(f"   📊 统计: 第{stats['count']}次解析")
    
    def _get_message_attributes(self, ros_msg: Any) -> List[str]:
        """获取消息属性列表"""
        try:
            if hasattr(ros_msg, '__dict__'):
                # 对象属性
                return [attr for attr in ros_msg.__dict__.keys() if not attr.startswith('_')]
            elif hasattr(ros_msg, '__slots__'):
                # 槽属性
                return list(ros_msg.__slots__)
            else:
                # 使用dir()获取属性
                return [attr for attr in dir(ros_msg) if not attr.startswith('_')]
        except Exception as e:
            logger.debug(f"获取消息属性失败: {e}")
            return []
    
    def supports_type(self, message_type: str) -> bool:
        """通用解析器支持所有类型"""
        return True
    
    def get_message_type_statistics(self) -> Dict[str, Dict[str, Any]]:
        """获取消息类型统计信息"""
        stats = {}
        for message_type, type_stats in self.message_type_stats.items():
            stats[message_type] = {
                'count': type_stats['count'],
                'first_seen': type_stats['first_seen'],
                'last_seen': type_stats['last_seen'],
                'duration': type_stats['last_seen'] - type_stats['first_seen'],
                'topics': list(type_stats['topics']),
                'topic_count': len(type_stats['topics'])
            }
        return stats
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取详细统计信息"""
        base_stats = super().get_statistics()
        
        # 添加消息类型统计
        base_stats['message_types'] = self.get_message_type_statistics()
        base_stats['unique_message_types'] = len(self.message_type_stats)
        base_stats['total_topics'] = len(set().union(*[
            stats['topics'] for stats in self.message_type_stats.values()
        ]))
        
        return base_stats
    
    def reset_statistics(self):
        """重置统计信息"""
        super().reset_statistics()
        self.message_type_stats.clear()
        logger.info("通用解析器统计信息已重置")


class DetailedGenericParser(GenericParser):
    """
    详细通用解析器
    提供更详细的消息分析
    """
    
    def __init__(self, max_depth: int = 3, max_items: int = 5):
        super().__init__()
        self.name = "DetailedGeneric"
        self.max_depth = max_depth
        self.max_items = max_items
    
    def _display_generic_info(self, ros_msg: Any, timestamp: float, topic: str, message_type: str):
        """显示详细的通用解析信息"""
        simple_type = message_type.split('.')[-1]
        
        print(f"\n📋 {simple_type} @ {timestamp:.2f}s")
        print(f"   话题: {topic}")
        print(f"   消息类型: {message_type}")
        print(f"   🔍 使用详细通用解析器")
        
        # 递归分析消息结构
        structure = self._analyze_message_structure(ros_msg, depth=0)
        if structure:
            print(f"   消息结构:")
            for line in structure:
                print(f"     {line}")
        
        # 显示统计信息
        if message_type in self.message_type_stats:
            stats = self.message_type_stats[message_type]
            print(f"   📊 统计: 第{stats['count']}次解析, 话题数: {len(stats['topics'])}")
    
    def _analyze_message_structure(self, obj: Any, depth: int = 0, name: str = "") -> List[str]:
        """递归分析消息结构"""
        if depth > self.max_depth:
            return [f"{'  ' * depth}... (深度限制)"]
        
        lines = []
        prefix = "  " * depth
        
        try:
            if hasattr(obj, '__dict__'):
                # 对象属性
                items = list(obj.__dict__.items())[:self.max_items]
                for attr_name, attr_value in items:
                    type_name = type(attr_value).__name__
                    
                    if self._is_simple_type(attr_value):
                        lines.append(f"{prefix}{attr_name}: {type_name} = {self._format_value(attr_value)}")
                    elif hasattr(attr_value, '__len__') and not isinstance(attr_value, str):
                        try:
                            length = len(attr_value)
                            lines.append(f"{prefix}{attr_name}: {type_name}[{length}]")
                            if length > 0 and depth < self.max_depth:
                                # 显示第一个元素的结构
                                first_item = attr_value[0] if hasattr(attr_value, '__getitem__') else next(iter(attr_value))
                                sub_lines = self._analyze_message_structure(first_item, depth + 1, f"{attr_name}[0]")
                                lines.extend(sub_lines[:3])  # 限制子结构显示
                        except:
                            lines.append(f"{prefix}{attr_name}: {type_name}")
                    else:
                        lines.append(f"{prefix}{attr_name}: {type_name}")
                        if depth < self.max_depth - 1:
                            sub_lines = self._analyze_message_structure(attr_value, depth + 1, attr_name)
                            lines.extend(sub_lines[:2])  # 限制子结构显示
                
                if len(obj.__dict__) > self.max_items:
                    lines.append(f"{prefix}... 还有{len(obj.__dict__) - self.max_items}个属性")
            
            elif hasattr(obj, '__slots__'):
                # 槽对象
                for slot_name in obj.__slots__[:self.max_items]:
                    if hasattr(obj, slot_name):
                        attr_value = getattr(obj, slot_name)
                        type_name = type(attr_value).__name__
                        lines.append(f"{prefix}{slot_name}: {type_name}")
        
        except Exception as e:
            lines.append(f"{prefix}分析失败: {e}")
        
        return lines
    
    def _is_simple_type(self, value: Any) -> bool:
        """判断是否为简单类型"""
        return isinstance(value, (int, float, str, bool, type(None)))
    
    def _format_value(self, value: Any) -> str:
        """格式化值显示"""
        if isinstance(value, str):
            return f'"{value[:50]}{"..." if len(value) > 50 else ""}"'
        elif isinstance(value, float):
            return f"{value:.3f}"
        else:
            return str(value)
