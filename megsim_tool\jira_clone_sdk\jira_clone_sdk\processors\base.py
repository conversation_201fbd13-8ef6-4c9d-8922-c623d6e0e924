"""字段处理器基类"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from ..config import FieldConfig, FieldAction
from ..exceptions import FieldProcessingException


class FieldProcessor(ABC):
    """字段处理器基类"""
    
    def __init__(self, priority: int = 0):
        self.priority = priority
    
    @abstractmethod
    def can_process(self, field_name: str, field_value: Any = None) -> bool:
        """判断是否可以处理该字段"""
        pass
    
    @abstractmethod
    def process(self, field_name: str, field_value: Any, context: Dict[str, Any]) -> Any:
        """处理字段值"""
        pass
    
    def validate_field(self, field_name: str, field_value: Any) -> bool:
        """验证字段值"""
        return True
    
    def transform_field(self, field_name: str, field_value: Any, config: FieldConfig, context: Dict[str, Any]) -> Any:
        """根据配置转换字段"""
        try:
            if config.action == FieldAction.KEEP:
                return field_value
            elif config.action == FieldAction.SKIP:
                return None
            elif config.action == FieldAction.REPLACE:
                return config.value
            elif config.action == FieldAction.TRANSFORM and config.transformer:
                return config.transformer(field_value, context)
            else:
                return self.process(field_name, field_value, context)
        except Exception as e:
            raise FieldProcessingException(field_name, str(e), field_value)
    
    def __lt__(self, other):
        """用于排序，优先级高的先处理"""
        return self.priority > other.priority