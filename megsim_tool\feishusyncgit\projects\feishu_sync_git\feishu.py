"""
飞书表格监控守护进程 - 主入口文件
用于向后兼容，实际功能已迁移到模块化架构中
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.main import main as async_main

def main():
    """主入口函数 - 向后兼容"""
    try:
        asyncio.run(async_main())
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()


