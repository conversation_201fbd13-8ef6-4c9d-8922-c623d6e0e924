import datetime

from basic.services import Send<PERSON>essageApi
from report.feishu.doc_parser import FeishuDocParser
from report.models import ReportFeishuInfo, ReportFeishuInfoLog
from report.services import FeishuReportGenerator


def apply(task_config):
    GenerateReportTemplate(task_config).apply()

class GenerateReportTemplate:
    def __init__(self, task_config):
        self.config = task_config

    def apply(self):
        reportConfig = ReportFeishuInfo.objects.filter(name=self.config.get('report_name',-1)).first()
        if reportConfig is None:
            raise Exception(f"cannot find report id : {self.config.get('id',-1)}")
        result, entity = FeishuReportGenerator(reportConfig).prepare()
        target_token = entity["target_token"]
        del entity["target_token"]
        if not result:
            raise Exception(f"generate template fail: {entity.get('remark', '')}")
        entity['status'] = '5'
        entity['params'] = reportConfig.params
        entity['remark'] = reportConfig.remark
        ReportFeishuInfoLog(**entity).save()
        if "init" in self.config:
            init_method = self.config['init'].get('init_method', [])
            variables = self.config['init'].get('variables', {})
            self.init_variables(variables)
            FeishuDocParser(target_token, variables, exec_method=init_method).execute()
        self.sendMessage(entity)

    def init_variables(self, variables):
        # 初始化变量
        for item in variables:
            value = variables[item]
            if value.startswith('time('):
                value = self.handle_time(value)
            variables[item] = value

    def handle_time(self, value):
        params = value[5:-1]
        params = params.split(',')
        result = datetime.datetime.now()
        if len(params) > 0:
            if params[0].endswith('s'):
                result = result + datetime.timedelta(seconds=int(params[0][:-1]))
            elif params[0].endswith('m'):
                result = result + datetime.timedelta(minutes=int(params[0][:-1]))
            elif params[0].endswith('d'):
                result = result + datetime.timedelta(days=int(params[0][:-1]))
            else:
                result = result + datetime.timedelta(seconds=int(params[0]))
        if len(params) > 1:
            result = result.strftime(params[1])
        else:
            result = result.strftime("%Y-%m-%d %H:%M:%S")
        return result

    def sendMessage(self, entity):
        if "alarm" not in self.config:
            return
        message = f"**报告模板已生成** :[点击前往]({entity.get('report_url', '')})"
        if 'at' in self.config:
            message = f"{message}\n"
            for item in self.config.get("at", []):
                message = f'{message} <at id="{item}"></at> '
        for item in self.config.get("alarm", "").split(";"):
            SendMessageApi(message).send_message(item)
