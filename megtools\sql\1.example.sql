##### DORIS
drop table if exists feeling_strategy;
create table feeling_strategy (
    version varchar(16) not null  comment '版本号',
    enviroment varchar(16) not null  comment '环境标识',
    vin varchar(17) not null  comment 'vin',
    request_time decimal(20,6) not null  comment '请求时间',
    dt DATE not null comment '时间分区字段，来自于request_time',
    event_name varchar(64) not null  comment '事件类型',
    parse_time DATETIME not null  comment '解析请求时间',
    base_line varchar(16) not null  comment '基线版本',
    vid varchar(32) not null  comment 'vid',
    domain varchar(32) not null  comment 'domain',
    link_class varchar(32) not null  comment '道路区分',
    last_diff_state_type varchar(32) comment '功能类型',
    last_diff_state_type_real varchar(32) comment '功能类型',
    last_diff_state_time decimal(20,6) not null  comment '请求时间',
    state_type varchar(32)   comment '状态类型',
    strategy_msg_status varchar(16)   comment '功能降级状态',
    strategy_msg varchar(128)  comment '降级消息',
    descript varchar(512)   comment '描述'
)
ENGINE=OLAP
UNIQUE KEY(`version`, `enviroment`, `vin`, `request_time`, `dt`, event_name)
comment 'FeelingStrategy表'
PARTITION BY RANGE(dt) ()
DISTRIBUTED BY HASH(`version`, `enviroment`,`vin`, `dt` )
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"dynamic_partition.enable" = "true",
"dynamic_partition.prefix" = "p",
"dynamic_partition.time_unit" = "DAY",
"dynamic_partition.create_history_partition" = "true",
"dynamic_partition.start" = "-365",
"dynamic_partition.end" = "1",
"dynamic_partition.buckets" = "3"
);



####### MYSQL
DROP TABLE if EXISTS screen_release_detail;
create table screen_release_detail (
    `id` bigint auto_increment primary key comment '主键ID',
    `acu_release` varchar(50) not null default '' comment 'ACU版本号',
    `hback_result` tinyint  not null  DEFAULT 0 comment '版本打回',
    `module_test_result` tinyint  not null  DEFAULT 0 comment '模块测试结果',
    `integration_pack_result` tinyint  not null  DEFAULT 0 comment '集成打包结果',
    `smoke_test_result` tinyint  not null  DEFAULT 0 comment '冒烟测试结果',
    `smoke_test_detail` varchar(1000) not null default '' comment '冒烟测试结果详情',
    `auto_report_s` varchar(1000) not null default '' comment '冒烟测试自动化报告',
    `auto_report_f` varchar(1000) not null default '' comment '功能测试自动化报告',
    `auto_report_g` varchar(1000) not null default '' comment '泛化测试自动化报告',
    `function_special_result` tinyint  not null  DEFAULT 0 comment '功能验证结果',
    `function_special_detail` varchar(1000) not null default '' comment '功能验证结果详情',
    `genera_test_result` tinyint  not null  DEFAULT 0 comment '泛化测试结果',
    `genera_test_detail` varchar(1000) not null default '' comment '泛化测试结果详情',
    `result` tinyint  not null  DEFAULT 0 comment '版本结果',
    `basic_information` json not null comment '基础信息',
    `detail`json not null comment '模块详情',
    `create_time` datetime(6) not null DEFAULT CURRENT_TIMESTAMP(6) comment '创建时间',
    `update_time` datetime(6) not null DEFAULT CURRENT_TIMESTAMP(6) comment '更新时间'
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci comment '大屏展示版本数据详情';
