#!/usr/bin/env python3
"""
自动生成API文档
使用docstring和类型注解生成专业文档
"""

import inspect
import json
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, get_type_hints


@dataclass
class ParameterDoc:
    """参数文档"""
    name: str
    type_hint: str
    description: str
    default_value: Any = None
    required: bool = True


@dataclass
class MethodDoc:
    """方法文档"""
    name: str
    description: str
    parameters: List[ParameterDoc] = field(default_factory=list)
    return_type: str = "None"
    examples: List[str] = field(default_factory=list)
    raises: List[str] = field(default_factory=list)


@dataclass
class ClassDoc:
    """类文档"""
    name: str
    description: str
    methods: List[MethodDoc] = field(default_factory=list)
    attributes: List[ParameterDoc] = field(default_factory=list)
    examples: List[str] = field(default_factory=list)


class APIDocGenerator:
    """API文档生成器"""
    
    def __init__(self):
        self.classes: List[ClassDoc] = []
    
    def extract_class_doc(self, cls: Type) -> ClassDoc:
        """提取类文档"""
        class_doc = ClassDoc(
            name=cls.__name__,
            description=self._clean_docstring(cls.__doc__ or "")
        )
        
        # 提取方法文档
        for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
            if not name.startswith('_'):  # 跳过私有方法
                method_doc = self._extract_method_doc(method)
                class_doc.methods.append(method_doc)
        
        # 提取属性文档（如果有类型注解）
        if hasattr(cls, '__annotations__'):
            for attr_name, attr_type in cls.__annotations__.items():
                attr_doc = ParameterDoc(
                    name=attr_name,
                    type_hint=str(attr_type),
                    description=f"{attr_name}属性"
                )
                class_doc.attributes.append(attr_doc)
        
        return class_doc
    
    def _extract_method_doc(self, method) -> MethodDoc:
        """提取方法文档"""
        method_doc = MethodDoc(
            name=method.__name__,
            description=self._clean_docstring(method.__doc__ or "")
        )
        
        # 提取参数信息
        sig = inspect.signature(method)
        type_hints = get_type_hints(method)
        
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
            
            param_doc = ParameterDoc(
                name=param_name,
                type_hint=str(type_hints.get(param_name, param.annotation)),
                description=f"{param_name}参数",
                default_value=param.default if param.default != inspect.Parameter.empty else None,
                required=param.default == inspect.Parameter.empty
            )
            method_doc.parameters.append(param_doc)
        
        # 提取返回类型
        if 'return' in type_hints:
            method_doc.return_type = str(type_hints['return'])
        
        return method_doc
    
    def _clean_docstring(self, docstring: str) -> str:
        """清理docstring"""
        if not docstring:
            return ""
        
        lines = docstring.strip().split('\n')
        # 移除缩进
        min_indent = min(len(line) - len(line.lstrip()) 
                        for line in lines[1:] if line.strip())
        
        cleaned_lines = [lines[0]]
        for line in lines[1:]:
            if line.strip():
                cleaned_lines.append(line[min_indent:])
            else:
                cleaned_lines.append("")
        
        return '\n'.join(cleaned_lines)
    
    def generate_markdown(self, output_file: Path):
        """生成Markdown文档"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# MCAP自动驾驶SDK API文档\n\n")
            f.write("本文档由代码自动生成，包含所有公共API的详细说明。\n\n")
            
            for class_doc in self.classes:
                self._write_class_markdown(f, class_doc)
    
    def _write_class_markdown(self, f, class_doc: ClassDoc):
        """写入类的Markdown文档"""
        f.write(f"## {class_doc.name}\n\n")
        f.write(f"{class_doc.description}\n\n")
        
        # 属性
        if class_doc.attributes:
            f.write("### 属性\n\n")
            for attr in class_doc.attributes:
                f.write(f"- **{attr.name}** (`{attr.type_hint}`): {attr.description}\n")
            f.write("\n")
        
        # 方法
        if class_doc.methods:
            f.write("### 方法\n\n")
            for method in class_doc.methods:
                self._write_method_markdown(f, method)
    
    def _write_method_markdown(self, f, method_doc: MethodDoc):
        """写入方法的Markdown文档"""
        f.write(f"#### {method_doc.name}\n\n")
        f.write(f"{method_doc.description}\n\n")
        
        # 参数
        if method_doc.parameters:
            f.write("**参数:**\n\n")
            for param in method_doc.parameters:
                required_text = "必需" if param.required else f"可选，默认值: `{param.default_value}`"
                f.write(f"- **{param.name}** (`{param.type_hint}`): {param.description} ({required_text})\n")
            f.write("\n")
        
        # 返回值
        if method_doc.return_type != "None":
            f.write(f"**返回值:** `{method_doc.return_type}`\n\n")
        
        # 示例
        if method_doc.examples:
            f.write("**示例:**\n\n")
            for example in method_doc.examples:
                f.write(f"```python\n{example}\n```\n\n")
        
        f.write("---\n\n")


def generate_comprehensive_docs():
    """生成完整的API文档"""
    
    # 示例：为McapAutoDriveSDK生成文档
    doc_generator = APIDocGenerator()
    
    # 这里应该导入实际的类
    # from mcap_core_sdk import McapAutoDriveSDK
    # class_doc = doc_generator.extract_class_doc(McapAutoDriveSDK)
    # doc_generator.classes.append(class_doc)
    
    # 生成示例文档结构
    example_class = ClassDoc(
        name="McapAutoDriveSDK",
        description="MCAP自动驾驶数据解析SDK的主要类，提供高性能的MCAP文件解析功能。",
        methods=[
            MethodDoc(
                name="stream_data",
                description="流式解析MCAP文件中的消息数据",
                parameters=[
                    ParameterDoc("mcap_file", "Union[str, Path]", "MCAP文件路径"),
                    ParameterDoc("message_types", "Optional[List[str]]", "要解析的消息类型列表", None, False),
                    ParameterDoc("max_messages", "Optional[int]", "最大消息数量限制", None, False)
                ],
                return_type="Iterator[MessageData]",
                examples=[
                    "sdk = McapAutoDriveSDK()\nfor msg in sdk.stream_data('data.mcap', ['LaneArrayv2']):\n    print(msg.timestamp)"
                ]
            )
        ]
    )
    
    doc_generator.classes.append(example_class)
    
    # 生成文档
    output_dir = Path("docs/api")
    output_dir.mkdir(parents=True, exist_ok=True)
    doc_generator.generate_markdown(output_dir / "api_reference.md")
    
    print("✅ API文档生成完成")


if __name__ == "__main__":
    generate_comprehensive_docs()
