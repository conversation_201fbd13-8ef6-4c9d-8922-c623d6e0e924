# MegTool Backend 使用说明

## 快速开始

### 1. 环境准备

确保您的开发环境已安装以下依赖：
- Python 3.8+
- Django 3.2+
- 相关Python包（见 requirements.txt）

### 2. 获取访问权限

联系系统管理员获取：
- 系统访问地址
- 用户名和密码
- API访问权限

### 3. 首次登录

```bash
# 使用cURL进行登录测试
curl -X POST "http://your-domain.com/api/basic/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your-username",
    "password": "your-password"
  }'
```

成功登录后，您将获得访问Token，请保存此Token用于后续API调用。

## 常见使用场景

### 场景1：系统监控和性能分析

**目标**：监控车辆系统性能，分析CPU、内存使用情况

**步骤**：
1. 登录获取Token
2. 查询系统概览数据
3. 查询具体CPU/进程数据
4. 分析性能趋势

**示例代码**：
```python
import requests
import json
from datetime import datetime, timedelta

class MegToolClient:
    def __init__(self, base_url, username, password):
        self.base_url = base_url
        self.token = None
        self.login(username, password)
    
    def login(self, username, password):
        url = f"{self.base_url}/api/basic/auth/login"
        data = {"username": username, "password": password}
        response = requests.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            self.token = result['data']['token']
            print("登录成功")
        else:
            raise Exception("登录失败")
    
    def get_headers(self):
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
    
    def monitor_system_performance(self, vin, hours=24):
        """监控系统性能"""
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        # 查询系统概览
        url = f"{self.base_url}/api/dataview/queryTopOverview"
        data = {
            "vin": vin,
            "record_time_start": start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "record_time_end": end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "indicator": ["cpu_usage", "memory_usage"]
        }
        
        response = requests.post(url, json=data, headers=self.get_headers())
        return response.json()

# 使用示例
client = MegToolClient("http://your-domain.com", "admin", "password")
performance_data = client.monitor_system_performance("TESTVIN123")
print(json.dumps(performance_data, indent=2, ensure_ascii=False))
```

### 场景2：定时任务管理

**目标**：管理系统定时任务，包括创建、修改、监控任务执行状态

**步骤**：
1. 查询现有定时任务
2. 创建新的定时任务
3. 监控任务执行状态
4. 必要时重新运行任务

**示例代码**：
```python
class CronTaskManager:
    def __init__(self, client):
        self.client = client
    
    def list_tasks(self, page=1, size=10):
        """查询定时任务列表"""
        url = f"{self.client.base_url}/api/cron/cronTaskPage"
        data = {"page": page, "size": size}
        response = requests.post(url, json=data, headers=self.client.get_headers())
        return response.json()
    
    def create_task(self, task_name, cron_exp, handler_class, description=""):
        """创建定时任务"""
        url = f"{self.client.base_url}/api/cron/modifyCronTask"
        data = {
            "task_name": task_name,
            "cron_exp": cron_exp,
            "handler_class": handler_class,
            "is_active": True,
            "description": description
        }
        response = requests.post(url, json=data, headers=self.client.get_headers())
        return response.json()
    
    def rerun_task(self, task_id):
        """重新运行任务"""
        url = f"{self.client.base_url}/api/cron/rerunTask"
        data = {"id": task_id}
        response = requests.post(url, json=data, headers=self.client.get_headers())
        return response.json()

# 使用示例
task_manager = CronTaskManager(client)

# 查询任务列表
tasks = task_manager.list_tasks()
print("当前定时任务：", tasks)

# 创建新任务（每天凌晨2点执行）
result = task_manager.create_task(
    task_name="日报生成任务",
    cron_exp="0 2 * * *",
    handler_class="DailyReportHandler",
    description="每日自动生成系统报告"
)
print("任务创建结果：", result)
```

### 场景3：报告生成和管理

**目标**：自动生成飞书报告，管理报告模板和执行日志

**步骤**：
1. 配置报告模板
2. 设置定时生成规则
3. 手动触发报告生成
4. 查看生成日志

**示例代码**：
```python
class ReportManager:
    def __init__(self, client):
        self.client = client
    
    def create_report_config(self, name, template_id, target_token, cron_exp):
        """创建报告配置"""
        url = f"{self.client.base_url}/api/report/modifyFeishuReport"
        data = {
            "name": name,
            "template_id": template_id,
            "target_token": target_token,
            "cron_exp": cron_exp,
            "is_active": True
        }
        response = requests.post(url, json=data, headers=self.client.get_headers())
        return response.json()
    
    def generate_report(self, report_id, params=""):
        """手动生成报告"""
        url = f"{self.client.base_url}/api/report/generateFeishuReportApi"
        data = {
            "id": report_id,
            "params": params,
            "status": "4"
        }
        response = requests.post(url, json=data, headers=self.client.get_headers())
        return response.json()
    
    def get_report_logs(self, report_id=None, page=1, size=10):
        """查询报告日志"""
        url = f"{self.client.base_url}/api/report/getFeishuReportLogPageApi"
        data = {"page": page, "size": size}
        if report_id:
            data["report_id"] = report_id
        response = requests.post(url, json=data, headers=self.client.get_headers())
        return response.json()

# 使用示例
report_manager = ReportManager(client)

# 创建周报配置（每周一上午9点生成）
weekly_report = report_manager.create_report_config(
    name="系统周报",
    template_id="template_123",
    target_token="feishu_doc_token_456",
    cron_exp="0 9 * * 1"
)
print("周报配置创建结果：", weekly_report)

# 手动生成报告
if weekly_report['code'] == 0:
    report_id = weekly_report['data']['id']
    generate_result = report_manager.generate_report(report_id)
    print("报告生成结果：", generate_result)
```

### 场景4：车辆轨迹和问题分析

**目标**：分析车辆行驶轨迹，关联Jira问题，进行问题定位

**步骤**：
1. 查询车辆轨迹数据
2. 查询相关Jira问题
3. 分析问题发生位置
4. 生成分析报告

**示例代码**：
```python
class VehicleAnalyzer:
    def __init__(self, client):
        self.client = client
    
    def analyze_vehicle_issues(self, vin, start_time, end_time):
        """分析车辆问题"""
        # 查询车辆轨迹
        trajectory = self.get_vehicle_trajectory(vin, start_time, end_time)
        
        # 查询相关Jira问题
        issues = self.get_related_issues(vin, start_time, end_time)
        
        # 查询问题位置
        if issues and 'data' in issues:
            issue_keys = [issue['key'] for issue in issues['data']]
            issue_locations = self.get_issue_locations(issue_keys, start_time, end_time)
            
            return {
                "trajectory": trajectory,
                "issues": issues,
                "issue_locations": issue_locations
            }
        
        return {"trajectory": trajectory, "issues": issues}
    
    def get_vehicle_trajectory(self, vin, start_time, end_time):
        """获取车辆轨迹"""
        url = f"{self.client.base_url}/api/amap/queryCarSite"
        data = {
            "vin": vin,
            "record_time_start": start_time,
            "record_time_end": end_time
        }
        response = requests.post(url, json=data, headers=self.client.get_headers())
        return response.json()
    
    def get_related_issues(self, vin, start_time, end_time):
        """获取相关问题"""
        url = f"{self.client.base_url}/api/amap/queryJiraIssues"
        data = {
            "vin": vin,
            "record_time_start": start_time,
            "record_time_end": end_time
        }
        response = requests.post(url, json=data, headers=self.client.get_headers())
        return response.json()
    
    def get_issue_locations(self, jira_list, start_time, end_time):
        """获取问题位置"""
        url = f"{self.client.base_url}/api/amap/queryJiraSite"
        data = {
            "jira_list": jira_list,
            "record_time_start": start_time,
            "record_time_end": end_time
        }
        response = requests.post(url, json=data, headers=self.client.get_headers())
        return response.json()

# 使用示例
analyzer = VehicleAnalyzer(client)

# 分析最近24小时的车辆问题
end_time = datetime.now()
start_time = end_time - timedelta(hours=24)

analysis_result = analyzer.analyze_vehicle_issues(
    vin="TESTVIN123",
    start_time=start_time.strftime("%Y-%m-%d %H:%M:%S"),
    end_time=end_time.strftime("%Y-%m-%d %H:%M:%S")
)

print("车辆问题分析结果：")
print(json.dumps(analysis_result, indent=2, ensure_ascii=False))
```

## 最佳实践

### 1. 错误处理

```python
def safe_api_call(func, *args, **kwargs):
    """安全的API调用包装器"""
    try:
        response = func(*args, **kwargs)
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                return result['data']
            else:
                print(f"API错误: {result.get('message', '未知错误')}")
                return None
        else:
            print(f"HTTP错误: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"网络错误: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None
```

### 2. Token管理

```python
class TokenManager:
    def __init__(self, client):
        self.client = client
        self.token_expire_time = None
    
    def ensure_valid_token(self):
        """确保Token有效"""
        if self.token_expire_time and datetime.now() > self.token_expire_time:
            # Token过期，重新登录
            self.client.login(self.client.username, self.client.password)
            # 假设Token有效期为2小时
            self.token_expire_time = datetime.now() + timedelta(hours=2)
```

### 3. 批量操作

```python
def batch_process_vehicles(client, vin_list, operation_func):
    """批量处理车辆数据"""
    results = []
    for vin in vin_list:
        try:
            result = operation_func(vin)
            results.append({"vin": vin, "result": result, "status": "success"})
        except Exception as e:
            results.append({"vin": vin, "error": str(e), "status": "failed"})
    return results
```

### 4. 配置管理

```python
import configparser

class ConfigManager:
    def __init__(self, config_file="config.ini"):
        self.config = configparser.ConfigParser()
        self.config.read(config_file)
    
    def get_api_config(self):
        return {
            "base_url": self.config.get('api', 'base_url'),
            "username": self.config.get('api', 'username'),
            "password": self.config.get('api', 'password'),
            "timeout": self.config.getint('api', 'timeout', fallback=30)
        }
```

## 故障排除

### 常见问题

1. **401 未授权错误**
   - 检查Token是否有效
   - 确认用户权限是否足够
   - 重新登录获取新Token

2. **404 接口不存在**
   - 检查API路径是否正确
   - 确认API版本是否匹配

3. **500 服务器错误**
   - 检查请求参数格式
   - 查看服务器日志
   - 联系系统管理员

4. **超时错误**
   - 增加请求超时时间
   - 检查网络连接
   - 分批处理大量数据

### 调试技巧

```python
import logging

# 启用详细日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def debug_api_call(url, data, headers):
    """调试API调用"""
    logger.debug(f"请求URL: {url}")
    logger.debug(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    logger.debug(f"请求头: {headers}")
    
    response = requests.post(url, json=data, headers=headers)
    
    logger.debug(f"响应状态码: {response.status_code}")
    logger.debug(f"响应数据: {response.text}")
    
    return response
```

## 性能优化建议

1. **连接池使用**
```python
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

session = requests.Session()
retry_strategy = Retry(
    total=3,
    backoff_factor=1,
    status_forcelist=[429, 500, 502, 503, 504]
)
adapter = HTTPAdapter(max_retries=retry_strategy)
session.mount("http://", adapter)
session.mount("https://", adapter)
```

2. **异步处理**
```python
import asyncio
import aiohttp

async def async_api_call(session, url, data):
    async with session.post(url, json=data) as response:
        return await response.json()

async def batch_async_calls(urls_and_data):
    async with aiohttp.ClientSession() as session:
        tasks = [async_api_call(session, url, data) for url, data in urls_and_data]
        return await asyncio.gather(*tasks)
```

3. **缓存策略**
```python
from functools import lru_cache
from datetime import datetime, timedelta

class CachedClient:
    def __init__(self, client):
        self.client = client
        self.cache = {}
    
    def get_cached_data(self, key, fetch_func, cache_duration=300):
        """获取缓存数据"""
        now = datetime.now()
        if key in self.cache:
            data, timestamp = self.cache[key]
            if now - timestamp < timedelta(seconds=cache_duration):
                return data
        
        # 缓存过期或不存在，重新获取
        data = fetch_func()
        self.cache[key] = (data, now)
        return data
```

## 安全注意事项

1. **Token安全**
   - 不要在代码中硬编码Token
   - 使用环境变量或配置文件存储敏感信息
   - 定期更换Token

2. **数据验证**
   - 验证输入参数
   - 过滤敏感数据
   - 使用HTTPS传输

3. **访问控制**
   - 遵循最小权限原则
   - 记录API访问日志
   - 监控异常访问行为

## 联系支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查API文档中的接口说明
3. 联系技术支持团队
4. 提供详细的错误信息和复现步骤

---

**注意**: 本使用说明基于当前API版本编写，如有更新请及时查看最新文档。