#!/usr/bin/env python3
"""
交互式工具
提供用户友好的交互界面
"""

import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
import json


class InteractiveMenu:
    """交互式菜单系统"""
    
    def __init__(self, title: str = "MCAP自动驾驶SDK"):
        self.title = title
        self.options: List[Tuple[str, str, callable]] = []
        self.current_file: Optional[Path] = None
    
    def add_option(self, key: str, description: str, handler: callable):
        """添加菜单选项"""
        self.options.append((key, description, handler))
    
    def display_menu(self):
        """显示菜单"""
        self._clear_screen()
        print(f"🚗 {self.title}")
        print("=" * 50)
        
        if self.current_file:
            print(f"📁 当前文件: {self.current_file.name}")
            print("-" * 50)
        
        for key, description, _ in self.options:
            print(f"  {key}. {description}")
        
        print("\n  q. 退出")
        print("=" * 50)
    
    def run(self):
        """运行交互式菜单"""
        while True:
            self.display_menu()
            
            try:
                choice = input("\n请选择操作: ").strip().lower()
                
                if choice == 'q':
                    print("👋 再见！")
                    break
                
                # 查找对应的处理函数
                handler = None
                for key, _, func in self.options:
                    if choice == key:
                        handler = func
                        break
                
                if handler:
                    try:
                        handler()
                    except KeyboardInterrupt:
                        print("\n⚠️  操作被用户中断")
                    except Exception as e:
                        print(f"❌ 操作失败: {e}")
                    
                    input("\n按回车键继续...")
                else:
                    print("❌ 无效选择，请重试")
                    input("按回车键继续...")
            
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except EOFError:
                break
    
    def _clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')


class McapInteractiveTool:
    """MCAP交互式工具"""
    
    def __init__(self):
        self.menu = InteractiveMenu()
        self.current_file: Optional[Path] = None
        self.recent_files: List[Path] = []
        self._setup_menu()
    
    def _setup_menu(self):
        """设置菜单选项"""
        self.menu.add_option("1", "选择MCAP文件", self._select_file)
        self.menu.add_option("2", "分析文件信息", self._analyze_file)
        self.menu.add_option("3", "解析车道线数据", self._parse_lane_data)
        self.menu.add_option("4", "解析其他消息类型", self._parse_other_messages)
        self.menu.add_option("5", "性能测试", self._performance_test)
        self.menu.add_option("6", "数据验证", self._validate_data)
        self.menu.add_option("7", "生成报告", self._generate_report)
        self.menu.add_option("8", "配置设置", self._configure_settings)
    
    def _select_file(self):
        """选择MCAP文件"""
        print("📁 选择MCAP文件")
        print("-" * 30)
        
        # 显示当前目录的MCAP文件
        current_dir = Path.cwd()
        mcap_files = list(current_dir.glob("*.mcap"))
        
        if mcap_files:
            print("当前目录中的MCAP文件:")
            for i, file in enumerate(mcap_files, 1):
                size_mb = file.stat().st_size / 1024 / 1024
                print(f"  {i}. {file.name} ({size_mb:.1f} MB)")
            
            print(f"  {len(mcap_files) + 1}. 输入自定义路径")
            
            try:
                choice = int(input("\n请选择文件: "))
                if 1 <= choice <= len(mcap_files):
                    self.current_file = mcap_files[choice - 1]
                    self.menu.current_file = self.current_file
                    print(f"✅ 已选择文件: {self.current_file.name}")
                elif choice == len(mcap_files) + 1:
                    self._input_custom_path()
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")
        else:
            print("当前目录没有找到MCAP文件")
            self._input_custom_path()
    
    def _input_custom_path(self):
        """输入自定义路径"""
        path_str = input("请输入MCAP文件路径: ").strip()
        if path_str:
            file_path = Path(path_str)
            if file_path.exists() and file_path.suffix == '.mcap':
                self.current_file = file_path
                self.menu.current_file = self.current_file
                print(f"✅ 已选择文件: {self.current_file.name}")
            else:
                print("❌ 文件不存在或不是MCAP文件")
    
    def _analyze_file(self):
        """分析文件信息"""
        if not self._check_file_selected():
            return
        
        print("📊 分析文件信息...")
        print("-" * 30)
        
        try:
            # 这里应该调用实际的SDK分析功能
            # from mcap_core_sdk import McapAutoDriveSDK
            # sdk = McapAutoDriveSDK()
            # analysis = sdk.analyze_mcap_file(self.current_file)
            
            # 模拟分析结果
            file_size = self.current_file.stat().st_size
            print(f"文件大小: {file_size / 1024 / 1024:.1f} MB")
            print(f"文件路径: {self.current_file}")
            print("消息类型: 正在分析...")
            print("话题列表: 正在分析...")
            print("时间范围: 正在分析...")
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    def _parse_lane_data(self):
        """解析车道线数据"""
        if not self._check_file_selected():
            return
        
        print("🛣️  解析车道线数据")
        print("-" * 30)
        
        # 获取用户参数
        try:
            max_messages = input("最大消息数 (默认10): ").strip()
            max_messages = int(max_messages) if max_messages else 10
            
            print(f"开始解析车道线数据，最多 {max_messages} 条消息...")
            
            # 这里应该调用实际的解析功能
            # 模拟解析过程
            import time
            for i in range(min(3, max_messages)):
                print(f"📍 解析消息 {i+1}...")
                time.sleep(0.5)  # 模拟处理时间
                print(f"   发现 {20+i*2} 条车道线")
            
            print("✅ 车道线解析完成")
            
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 解析失败: {e}")
    
    def _parse_other_messages(self):
        """解析其他消息类型"""
        if not self._check_file_selected():
            return
        
        print("📡 解析其他消息类型")
        print("-" * 30)
        
        # 显示可用的消息类型
        message_types = [
            "AEBObstacleArray",
            "EntranceArray", 
            "LocalizationEstimate",
            "ControlResult",
            "PerceptionResult"
        ]
        
        print("可用的消息类型:")
        for i, msg_type in enumerate(message_types, 1):
            print(f"  {i}. {msg_type}")
        
        try:
            choice = int(input("请选择消息类型: "))
            if 1 <= choice <= len(message_types):
                selected_type = message_types[choice - 1]
                print(f"开始解析 {selected_type} 消息...")
                
                # 模拟解析
                print(f"✅ {selected_type} 解析完成")
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
    
    def _performance_test(self):
        """性能测试"""
        if not self._check_file_selected():
            return
        
        print("⚡ 性能测试")
        print("-" * 30)
        
        print("1. 快速模式测试")
        print("2. 标准模式测试")
        print("3. 内存使用测试")
        print("4. 吞吐量测试")
        
        try:
            choice = int(input("请选择测试类型: "))
            
            if choice == 1:
                print("🚀 执行快速模式测试...")
                # 模拟性能测试
                import time
                start_time = time.time()
                time.sleep(2)  # 模拟处理
                duration = time.time() - start_time
                print(f"✅ 快速模式测试完成，耗时 {duration:.2f}s")
                print(f"   处理速度: 1000 msg/s")
                print(f"   内存使用: 50 MB")
            
            elif choice == 2:
                print("📊 执行标准模式测试...")
                # 类似的测试逻辑
                print("✅ 标准模式测试完成")
            
            else:
                print("❌ 无效选择")
                
        except ValueError:
            print("❌ 请输入有效数字")
    
    def _validate_data(self):
        """数据验证"""
        if not self._check_file_selected():
            return
        
        print("🔍 数据验证")
        print("-" * 30)
        
        print("开始验证数据质量...")
        
        # 模拟验证过程
        print("✅ 坐标范围检查: 通过")
        print("✅ 置信度检查: 通过")
        print("⚠️  发现 3 个轻微问题")
        print("✅ 数据验证完成")
    
    def _generate_report(self):
        """生成报告"""
        if not self._check_file_selected():
            return
        
        print("📋 生成报告")
        print("-" * 30)
        
        report_types = [
            "数据摘要报告",
            "性能分析报告", 
            "数据质量报告",
            "完整分析报告"
        ]
        
        print("报告类型:")
        for i, report_type in enumerate(report_types, 1):
            print(f"  {i}. {report_type}")
        
        try:
            choice = int(input("请选择报告类型: "))
            if 1 <= choice <= len(report_types):
                selected_type = report_types[choice - 1]
                output_file = f"report_{self.current_file.stem}_{choice}.json"
                
                print(f"生成 {selected_type}...")
                print(f"✅ 报告已保存到: {output_file}")
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
    
    def _configure_settings(self):
        """配置设置"""
        print("⚙️  配置设置")
        print("-" * 30)
        
        settings = {
            "快速模式": True,
            "详细日志": False,
            "数据验证": True,
            "性能监控": True
        }
        
        print("当前设置:")
        for i, (key, value) in enumerate(settings.items(), 1):
            status = "启用" if value else "禁用"
            print(f"  {i}. {key}: {status}")
        
        print(f"  {len(settings) + 1}. 重置为默认设置")
        
        try:
            choice = int(input("请选择要修改的设置 (0=返回): "))
            if choice == 0:
                return
            elif 1 <= choice <= len(settings):
                key = list(settings.keys())[choice - 1]
                settings[key] = not settings[key]
                status = "启用" if settings[key] else "禁用"
                print(f"✅ {key} 已{status}")
            elif choice == len(settings) + 1:
                print("✅ 设置已重置为默认值")
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
    
    def _check_file_selected(self) -> bool:
        """检查是否已选择文件"""
        if not self.current_file:
            print("❌ 请先选择MCAP文件")
            return False
        return True
    
    def run(self):
        """运行交互式工具"""
        print("🚗 欢迎使用MCAP自动驾驶SDK交互式工具")
        print("本工具提供友好的图形界面来操作MCAP文件")
        input("按回车键开始...")
        
        self.menu.run()


def main():
    """主函数"""
    tool = McapInteractiveTool()
    tool.run()


if __name__ == "__main__":
    main()
