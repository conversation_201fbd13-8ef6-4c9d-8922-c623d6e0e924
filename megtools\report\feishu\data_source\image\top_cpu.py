import json

from basic.utils import SqlUtil
from dataview.services import reform_acu_top_chart
from report.feishu.data_source.image.line_chart_image import LineChartImage
from report.feishu.generator.impl.text_handler import TextHandler


class TopCpu(LineChartImage):
    def __init__(self, block, params, variables, feishu_api):
        super().__init__(block, feishu_api)
        # 如果参数不全需要进行补齐
        self.condition = {}
        self.handle_conditions(variables, params)

    def apply(self):
        # 查询生成图表的数据
        chart_data = self.query_imgdata()
        # 生成图片后返回图片路径
        image_bytes = self.generate_img(chart_data,  self.condition['indicator'], "Cpu使用情况")
        # 写到飞书里面
        self.write_feishu(image_bytes)
        # 下面再放一个描述
        url = self.get_resource_url({
            'vin': self.condition['vin'],
            'record_time_start': self.condition['start_time'],
            'record_time_end': self.condition['end_time'],
            'indicator': json.dumps([{},{'cpu': '%Cpu(s)', 'indicator': self.condition['indicator']},{}]),
        })
        # 生成飞书链接
        TextHandler('资源详情', self.block, self.feishu_api).create_link(url, self.block.get("index", 0) + 2)

    def query_imgdata(self):
        sql_pattern = ""
        for item in self.condition['indicator']:
            sql_pattern = f"{sql_pattern} , round(avg({item}), 4) as {item}"
        sql = f""" select record_time {sql_pattern} from dataview_top_cpu 
        where vin = %s and record_time >= %s and record_time <= %s
        group by record_time order by record_time """
        result = SqlUtil.query_all_dict(sql, (self.condition['vin'],
                                            self.condition['start_time'],
                                            self.condition['end_time'],))
        result = reform_acu_top_chart(result, self.condition['indicator'])
        return result


    def handle_conditions(self, variables, params):
        params = params.split(",")
        # 如果没有指定VIN，直接生成图片
        self.condition["vin"] = variables.get('vin', '')
        # 根据report_name 查询开始时间和结束时间
        self.condition['start_time'] = variables.get('start_time', '')
        self.condition['end_time'] = variables.get('end_time', '')
        self.condition['indicator'] = params

